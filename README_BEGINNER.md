# 🚀 Externe Services integrieren - <PERSON><PERSON><PERSON>fänger

## 🤔 Was ist das überhaupt?

Stell dir vor, du baust ein Ha<PERSON> (deine App) und brauchst Strom von außen (externer Service). Du könntest einfach ein Kabel reinlegen, aber das wäre chaotisch. Stattdessen baust du eine saubere Steckdose (Client-Module) - so bleibt alles ordentlich und austauschbar.

## 🎯 Das Ziel: Saubere Integration

**Schlecht:** Deine App ruft direkt externe APIs auf

```kotlin
// NICHT SO! 🚫
val response = httpClient.post("https://rela.com/api/appointments") {
    body = """{"WerkstattterminDatum": "2024-01-01", "KFZKennzeichen": "S-PL 1234"}"""
}
```

**Gut:** Deine App nutzt saubere Business-Funktionen

```kotlin
// SO IST ES RICHTIG! ✅
val appointment = RelaAppointmentRequest(
    appointmentDate = tomorrow,
    vehicleLicensePlate = "S-PL 1234"
)
val result = createRelaAppointment.createAppointment(appointment)
```

## 🏗️ Die Architektur (wie ein Haus bauen)

```
🏠 Dein Haus (Business Logic)
├── 🚪 Eingangstür (Ports = Was kann ich?)
├── 🔌 Steckdose (Adapter = Wie mache ich es?)
└── 🔌 Kabel nach draußen (External API)
```

### Die 3 Schichten:

1. **Business Layer (Innen)** = Was will ich machen?
2. **Port Layer (Grenze)** = Welche Funktionen brauche ich?
3. **Adapter Layer (Außen)** = Wie rufe ich externe Services auf?

## 📚 Schritt-für-Schritt Anleitung

### **Schritt 1: API Specification verstehen**

**Was ist das?** Eine Datei, die beschreibt, wie ein externer Service funktioniert.

**Wo liegt sie?** `api/[service-name].yaml`

**Beispiel für RELA:**

```yaml
# api/rela.yaml
paths:
  /appointments:
    post:
      summary: "Termin erstellen"
      requestBody:
        schema:
          properties:
            WerkstattterminDatum: string # ← Deutsche Feldnamen!
            KFZKennzeichen: string
```

**Was passiert damit?** OpenAPI Generator erstellt automatisch Kotlin-Code daraus.

### **Schritt 2: Code generieren lassen**

**Was machen wir?**

```bash
./gradlew generateRelaClient
./gradlew setupGeneratedSources
```

**Was passiert?**

- OpenAPI Generator liest `api/rela.yaml`
- Erstellt Kotlin-Klassen in `src/generated/`
- Diese haben deutsche Feldnamen und sind schwer zu benutzen

**Was bekommen wir?**

```kotlin
// Generiert: CreateAppointmentRequestDto.kt
data class CreateAppointmentRequestDto(
    val werkstattterminDatum: OffsetDateTime,  // ← Deutsch!
    val kfZKennzeichen: String,                // ← Schwer zu lesen!
    // ...
)
```

### **Schritt 3: Business Layer erstellen (Was will ich?)**

**Das ist der wichtigste Teil!** Hier definierst du, was deine App können soll - in einfachen, verständlichen Worten.

#### **3.1 Port Interfaces (Die Eingangstür)**

**Was ist ein Port?** Ein Vertrag, der sagt: "Ich kann X machen"

```kotlin
// CreateRelaAppointment.kt
fun interface CreateRelaAppointment {
    fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse
}
```

**Warum `fun interface`?**

- Einfacher zu verstehen (nur eine Funktion)
- Einfacher zu testen
- Klare Verantwortlichkeit

#### **3.2 Business DTOs (Verständliche Datenstrukturen)**

**Was ist ein DTO?** Data Transfer Object = Ein Container für Daten

```kotlin
// RelaAppointmentRequest.kt - ENGLISCH und verständlich!
data class RelaAppointmentRequest(
    val appointmentDate: OffsetDateTime,        // Statt "werkstattterminDatum"
    val vehicleLicensePlate: String,            // Statt "kfZKennzeichen"
    val vehicleVin: String,                     // Statt "FIN"
    val customerLastName: String,               // Statt "name"
    val customerFirstName: String               // Statt "vorname"
)
```

**Warum eigene DTOs?**

- **Verständlich:** Englische Namen statt deutsche API-Namen
- **Stabil:** Wenn sich die externe API ändert, bleibt dein Code gleich
- **Sauber:** Nur die Felder, die du wirklich brauchst

#### **3.3 Exceptions (Was kann schiefgehen?)**

```kotlin
// RelaExceptions.kt
class RelaAppointmentCreationException(message: String?, cause: Throwable?)
class RelaServiceUnavailableException(message: String?, cause: Throwable?)
```

**Warum eigene Exceptions?**

- **Spezifisch:** "Termin konnte nicht erstellt werden" ist klarer als "HTTP 500"
- **Behandelbar:** Verschiedene Fehler können unterschiedlich behandelt werden

### **Schritt 4: Adapter Layer (Wie mache ich es?)**

**Das ist der Übersetzer zwischen deiner App und der externen API.**

#### **4.1 WebClient Interface (Die saubere Schnittstelle)**

```kotlin
// RelaWebClient.kt
interface RelaWebClient {
    @PostExchange(url = "/rela-rest-ws/luckyneutron/appointments")
    fun createAppointment(@RequestBody request: CreateAppointmentRequestDto): CreateAppointmentResponseDto
}
```

**Was macht das?**

- Spring erstellt automatisch eine Implementation
- Macht HTTP-Aufrufe für dich
- Ist einfach zu testen (mocken)

#### **4.2 Client Implementation (Der Übersetzer)**

```kotlin
// RelaClient.kt
@Component
class RelaClient(private val relaWebClient: RelaWebClient) : CreateRelaAppointment {

    override fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse {
        // 1. Übersetze Business-DTO zu API-DTO
        val apiRequest = CreateAppointmentRequestDto(
            werkstattterminDatum = request.appointmentDate,     // Englisch → Deutsch
            kfZKennzeichen = request.vehicleLicensePlate,       // Verständlich → API
            // ...
        )

        // 2. Rufe externe API auf
        val apiResponse = relaWebClient.createAppointment(apiRequest)

        // 3. Übersetze API-Response zu Business-Response
        return RelaAppointmentResponse(orderNumber = apiResponse.auftragsnummer)
    }
}
```

**Was passiert hier?**

1. **Übersetzen:** Deine verständlichen Daten → API-Format
2. **Aufrufen:** HTTP-Request an externe API
3. **Zurückübersetzen:** API-Response → Deine verständlichen Daten

#### **4.3 Configuration (Die Verkabelung)**

```kotlin
// RelaClientConfiguration.kt
@Configuration
@ConditionalOnProperty(name = ["rela.enabled"], havingValue = "true")  // ← Feature Flag!
class RelaConfiguration

@Configuration
@ConditionalOnBean(RelaConfiguration::class)  // ← Nur wenn aktiviert!
class RelaClientConfiguration {

    @Bean
    fun relaServiceProxyFactory(@Value("\${rela.base-url}") baseUrl: String): HttpServiceProxyFactory {
        val webClient = WebClient.builder()
            .baseUrl(baseUrl)                    // ← Wo ist die API?
            .filter(errorHandler)                // ← Was bei Fehlern?
            .build()

        return HttpServiceProxyFactory.builderFor(WebClientAdapter.create(webClient)).build()
    }

    @Bean
    fun getRelaWebClient(factory: HttpServiceProxyFactory): RelaWebClient {
        return factory.createClient(RelaWebClient::class.java)  // ← Spring Magie!
    }
}
```

**Was macht das?**

- **Feature Flag:** `@ConditionalOnProperty` = Nur wenn `rela.enabled=true`
- **Dependency Injection:** Spring verbindet alles automatisch
- **WebClient Setup:** Konfiguriert HTTP-Client mit Base-URL und Fehlerbehandlung

#### **4.4 Exception Handler (Fehlerbehandlung)**

```kotlin
// RelaClientExceptionHandler.kt
@Component
class RelaClientExceptionHandler {

    fun clientErrorResponseProcessor(response: ClientResponse): Mono<ClientResponse> {
        return if (response.statusCode().isError) {
            when (response.statusCode()) {
                HttpStatus.BAD_REQUEST -> {
                    // HTTP 400 → Verständliche Exception
                    throw RelaValidationException("Ungültige Daten gesendet")
                }
                HttpStatus.SERVICE_UNAVAILABLE -> {
                    // HTTP 503 → Service nicht verfügbar
                    throw RelaServiceUnavailableException("RELA Service ist nicht erreichbar")
                }
                // ...
            }
        } else {
            Mono.just(response)  // Alles OK
        }
    }
}
```

**Warum das?**

- **Übersetzung:** HTTP-Status-Codes → Verständliche Fehlermeldungen
- **Zentral:** Alle Fehler werden an einer Stelle behandelt
- **Logging:** Strukturierte Logs für besseres Debugging

### **Schritt 5: Configuration (Einstellungen)**

#### **5.1 Application.yml (Die Einstellungsdatei)**

```yaml
# application.yml
rela:
  enabled: ${RELA_ENABLED:false} # Feature Flag (Standard: aus)
  base-url: ${RELA_BASE_URL:http://localhost:8080} # Wo ist die API?
  timeout-seconds: ${RELA_TIMEOUT_SECONDS:30} # Wie lange warten?
  retry-attempts: ${RELA_RETRY_ATTEMPTS:3} # Wie oft wiederholen?
```

**Warum Environment Variables (`${...}`)？**

- **Flexibilität:** Dev, Test, Prod haben verschiedene URLs
- **Sicherheit:** Keine Secrets im Code
- **12-Factor App:** Best Practice für Cloud-Apps

#### **5.2 Properties Class (Typsichere Konfiguration)**

```kotlin
// In RelaClientConfiguration.kt
@ConfigurationProperties("rela")
data class RelaProperties(
    val enabled: Boolean = false,
    val baseUrl: String,
    val timeoutSeconds: Long = 30,
    val retryAttempts: Int = 3
)
```

**Warum das?**

- **Typsicherheit:** `Long` statt `String`
- **IDE-Unterstützung:** Autocompletion in YAML
- **Validierung:** Kann Validierungs-Annotations haben

## 🧪 Testing (Testen ohne externe API)\*\*

```kotlin
// RelaClientTest.kt
class RelaClientTest {
    private val relaWebClient = mockk<RelaWebClient>()  // ← Fake WebClient
    private val relaClient = RelaClient(relaWebClient)

    @Test
    fun `should create appointment successfully`() {
        // Given (Vorbereitung)
        val request = RelaAppointmentRequest(...)
        val expectedResponse = CreateAppointmentResponseDto(auftragsnummer = "12345")
        every { relaWebClient.createAppointment(any()) } returns expectedResponse

        // When (Ausführung)
        val result = relaClient.createAppointment(request)

        // Then (Überprüfung)
        assertEquals("12345", result.orderNumber)
        verify { relaWebClient.createAppointment(any()) }  // ← Wurde aufgerufen?
    }
}
```

**Warum Mocking?**

- **Schnell:** Keine echten HTTP-Aufrufe
- **Zuverlässig:** Externe API kann offline sein
- **Kontrolliert:** Du bestimmst, was zurückkommt

## 🎯 Das Kochrezept für neue Services

### **Zutaten (was du brauchst):**

1. API Specification (`.yaml` Datei)
2. 5 Standard-Dateien (siehe unten)
3. Configuration in `application.yml`
4. Tests

### **Rezept für Service "XYZ":**

#### **1. API Spec hinzufügen**

```
api/xyz.yaml  ← OpenAPI Specification
```

#### **2. Gradle Task hinzufügen**

```gradle
// build.gradle
tasks.register("generateXyzClient", GenerateTask) {
    inputSpec.set("$projectDir/$apiFolder/xyz.yaml")
    apiPackage.set("com.fleetmanagement.modules.xyz.adapter.out.rest")
    modelPackage.set("com.fleetmanagement.modules.xyz.adapter.out.rest.model")
    // ...
}
```

#### **3. Die 5 Standard-Dateien erstellen:**

**Business Layer:**

```kotlin
// 1. XyzPort.kt (Was kann ich?)
fun interface CreateXyzSomething {
    fun doSomething(request: XyzRequest): XyzResponse
}

// 2. XyzDTOs.kt (Verständliche Daten)
data class XyzRequest(val name: String, val value: Int)
data class XyzResponse(val id: String)
```

**Adapter Layer:**

```kotlin
// 3. XyzWebClient.kt (HTTP Interface)
interface XyzWebClient {
    @PostExchange(url = "/xyz/api/something")
    fun doSomething(@RequestBody request: XyzRequestDto): XyzResponseDto
}

// 4. XyzClient.kt (Übersetzer)
@Component
@ConditionalOnBean(XyzConfiguration::class)
class XyzClient(private val xyzWebClient: XyzWebClient) : CreateXyzSomething {
    override fun doSomething(request: XyzRequest): XyzResponse {
        val apiRequest = mapToDto(request)  // ← Übersetzen
        val apiResponse = xyzWebClient.doSomething(apiRequest)
        return mapFromDto(apiResponse)      // ← Zurückübersetzen
    }
}

// 5. XyzClientConfiguration.kt (Verkabelung)
@Configuration
@ConditionalOnProperty(name = ["xyz.enabled"], havingValue = "true")
class XyzConfiguration

@Configuration
@ConditionalOnBean(XyzConfiguration::class)
class XyzClientConfiguration {
    @Bean
    fun xyzWebClient(): XyzWebClient { /* ... */ }
}
```

#### **4. Configuration hinzufügen**

```yaml
# application.yml
xyz:
  enabled: ${XYZ_ENABLED:false}
  base-url: ${XYZ_BASE_URL:http://localhost:8080}
```

#### **5. Tests schreiben**

```kotlin
// XyzClientTest.kt
class XyzClientTest {
    private val xyzWebClient = mockk<XyzWebClient>()
    private val xyzClient = XyzClient(xyzWebClient)

    @Test
    fun `should do something successfully`() {
        // Given, When, Then...
    }
}
```

## 🚨 Häufige Fehler und wie du sie vermeidest

### **1. Direkte API-Aufrufe im Business Code**

```kotlin
// FALSCH 🚫
class SomeService {
    fun doSomething() {
        val response = httpClient.post("https://external-api.com/...")  // ← Direkt!
    }
}

// RICHTIG ✅
class SomeService(private val createSomething: CreateSomething) {
    fun doSomething() {
        val result = createSomething.create(request)  // ← Über Port!
    }
}
```

### **2. Generierte DTOs im Business Code verwenden**

```kotlin
// FALSCH 🚫
class SomeService {
    fun doSomething(request: CreateAppointmentRequestDto) {  // ← Generierte DTO!
        // ...
    }
}

// RICHTIG ✅
class SomeService {
    fun doSomething(request: RelaAppointmentRequest) {  // ← Business DTO!
        // ...
    }
}
```

### **3. Keine Feature Flags**

```kotlin
// FALSCH 🚫
@Component
class XyzClient {  // ← Immer aktiv!
}

// RICHTIG ✅
@Component
@ConditionalOnBean(XyzConfiguration::class)  // ← Nur wenn aktiviert!
class XyzClient {
}
```

### **4. Hardcoded URLs**

```yaml
# FALSCH 🚫
xyz:
  base-url: https://prod-api.xyz.com  # ← Hardcoded!

# RICHTIG ✅
xyz:
  base-url: ${XYZ_BASE_URL:http://localhost:8080}  # ← Environment Variable!
```

## 🎉 Zusammenfassung

**Das Wichtigste in einem Satz:** Erstelle saubere Business-Interfaces (Ports) und übersetze zwischen deinen verständlichen DTOs und den externen API-DTOs in Adaptern.

**Die 3 goldenen Regeln:**

1. **Business Layer:** Was will ich machen? (Englisch, verständlich)
2. **Port Layer:** Welche Funktionen brauche ich? (Interfaces)
3. **Adapter Layer:** Wie rufe ich externe APIs auf? (Übersetzer)

**Dein Cheat Sheet:**

1. API Spec → Code generieren
2. Business DTOs und Ports erstellen
3. WebClient Interface und Client Implementation
4. Configuration mit Feature Flags
5. Tests mit Mocking

## 🔍 Debugging Tipps

### **Wenn etwas nicht funktioniert:**

#### **1. Feature Flag prüfen**

```yaml
# application.yml
rela:
  enabled: true # ← Muss auf true stehen!
```

#### **2. Logs anschauen**

```kotlin
// In deinem Client
private val log = LoggerFactory.getLogger(RelaClient::class.java)

override fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse {
    log.info("Creating RELA appointment for VIN: ${request.vehicleVin}")  // ← Debug-Info
    // ...
}
```

#### **3. HTTP-Aufrufe debuggen**

```yaml
# application.yml
logging:
  level:
    org.springframework.web.reactive.function.client: DEBUG # ← HTTP-Logs
```

#### **4. Exception-Handling testen**

```kotlin
@Test
fun `should handle API errors gracefully`() {
    // Given
    every { relaWebClient.createAppointment(any()) } throws RuntimeException("API Error")

    // When & Then
    assertThrows<RelaAppointmentCreationException> {
        relaClient.createAppointment(request)
    }
}
```

## 🎯 Erweiterte Patterns

### **1. Retry Logic (Wiederholung bei Fehlern)**

```kotlin
@Retryable(value = [RelaServiceUnavailableException::class], maxAttempts = 3)
override fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse {
    // ...
}
```

### **2. Circuit Breaker (Schutz vor überlasteten Services)**

```kotlin
@CircuitBreaker(name = "rela", fallbackMethod = "createAppointmentFallback")
override fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse {
    // ...
}

fun createAppointmentFallback(request: RelaAppointmentRequest, ex: Exception): RelaAppointmentResponse {
    log.warn("RELA service unavailable, using fallback")
    throw RelaServiceUnavailableException("Service temporarily unavailable")
}
```

### **3. Metrics (Überwachung)**

```kotlin
@Timed(name = "rela.appointment.creation", description = "Time taken to create RELA appointment")
override fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse {
    // ...
}
```

## 📖 Weiterführende Ressourcen

### **Spring Boot Dokumentation:**

- [WebClient](https://docs.spring.io/spring-framework/docs/current/reference/html/web-reactive.html#webflux-client)
- [Configuration Properties](https://docs.spring.io/spring-boot/docs/current/reference/html/features.html#features.external-config.typesafe-configuration-properties)
- [Conditional Beans](https://docs.spring.io/spring-boot/docs/current/reference/html/features.html#features.developing-auto-configuration.condition-annotations)

### **Architektur Patterns:**

- [Hexagonal Architecture](https://alistair.cockburn.us/hexagonal-architecture/)
- [Ports and Adapters](https://herbertograca.com/2017/09/14/ports-adapters-architecture/)

### **Testing:**

- [MockK Documentation](https://mockk.io/)
- [Spring Boot Testing](https://docs.spring.io/spring-boot/docs/current/reference/html/features.html#features.testing)

## 🎉 Du hast es geschafft!

Jetzt kannst du jeden externen Service sauber integrieren! 🚀

**Remember:**

- Immer Business-Interfaces definieren
- Externe APIs in Adaptern verstecken
- Feature Flags für alles verwenden
- Tests mit Mocking schreiben
- Verständliche Namen verwenden

**Happy Coding!** 💻✨
