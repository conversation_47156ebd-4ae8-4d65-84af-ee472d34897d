# RELA Tier Change Appointments

## Getting started

Generate a http-client.private.env.json to safe the credentials on your local machine:
{
"rela-staging": {
"rela_auth_token": "<auth_token>"
}
}

### Credentials

All credentials are available on GitLab variables:
- Staging:
    - RELA_STAGING_AUTH_TOKEN
- Prod:
    - RELA_PROD_AUTH_TOKEN

### Environments
- QAS: 
- PRD: 

### Documentation
- See folder 'documentation'

### Contact Person
- <PERSON> (f.jena<PERSON>@sdnord.de)
- <PERSON><PERSON><PERSON> (VMP3)

### Context
POST https://rela.sdnord.de/rela-rest-ws/luckyneutron/appointments
{
  "WerkstattterminDatum": "2025-07-31T00:00:00", => MS-Bookings
  "WerkstattterminUhrzeit": "2025-07-31T10:00:00", => MS-Bookings
  "Buehne": 1, => MS-Bookings (technischer User)
  "Dienstleistung": 2, => MS-Bookings (Winter- / Sommerreifenwechsel)
  "KFZKennzeichen": "S-P1", => MS-Bookings (Identifier?)
  "FIN": "WP0ZZZ999SS123456", => FVM
  "Name": "Piehl", => FVM
  "Vorname": "Silke", => FVM
  "TypCode": "992642", => FVM
  "TypBeschreibung": "911 Carrera 4 GTS Cabriolet", => FVM
  "PCCBCode": "", => ??? (Keramikbremse) => value/null => Ausstattungschlüssel "1LQ", Ausstattungsfamilie "BAV"
  "WheelCode": "58Y", => ??? (Sommerradsatz) => Ausstattungsschlüssel "C2Q", Ausstattungsfamilie "RAD"
  "RDKCode": "1PJ", => ??? (Zentralverschluss) => value/null  :  Wir können das umbenennen (CWL) => Ausstattungsschlüssel "1PJ", Ausstattungsfamilie "ABR"
  "RASCode": null, => ??? (Hinterachslenkung) => value/null => Ausstattungschlüssel "ON5", Ausstatungsfamilie "HIA"
  "PCCBBeschreibung": "", => ??? (nicht relevant) => Ausstattungschlüssel "1LQ", Ausstattungsfamilie "BAV"
  "ReifenbeschreibungDemontage": "CARRERA GTS SCHMIEDERAD", => ??? (Label im Einlagerungsbeleg) (nicht relevant) Ausstattungsschlüssel "H6F" , Ausstattungsfamilie "REI"
  "VentilbeschreibungDemontage": "ZENTRALVERSCHLUSS", => ??? (nicht relevant) => Ausstattungschlüssel "UX7", Ausstatungsfamilie "RBS"
  "ReifenbeschreibungMontage": "", => ??? (nicht relevant) => Winterreifen-Pool?
  "VentilbeschreibungMontage": "", => ??? (nicht relevant) => Winterreifen-Pool?
  "bestelltVon": <EMAIL>, => ??? (Leasingnehmer / Person der den Termin gebucht hat)
  "bestelltAmDatum": "2025-07-18T00:00:00", => ??? Kann man zusammenlegen mit Uhrzeit (bestelltAm)
  "bestelltAmUhrzeit": "2025-07-18T16:00:00" => ???
}