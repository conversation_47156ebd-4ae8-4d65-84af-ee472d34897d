import org.jetbrains.kotlin.gradle.internal.KaptGenerateStubsTask
import org.openapitools.generator.gradle.plugin.tasks.GenerateTask

plugins {
    id 'org.springframework.boot' version '3.5.3'
    id 'io.spring.dependency-management' version '1.1.7'
    id 'org.liquibase.gradle' version '2.2.0'
    id 'org.jetbrains.kotlin.jvm' version '2.2.0'
    id 'org.jetbrains.kotlin.plugin.spring' version '2.2.0'
    id 'org.jetbrains.kotlin.plugin.jpa' version '2.2.0'
    id 'org.jetbrains.kotlin.kapt' version '2.2.0'
    id 'com.github.ben-manes.versions' version '0.52.0'
    id 'org.openapi.generator' version '7.13.0'
    id 'jacoco'
    id("org.jlleitschuh.gradle.ktlint") version "12.3.0"
}

ext['spring-security.version'] = '6.5.1'

group = 'com.fleetmanagement'
version = '0.0.1-SNAPSHOT'

def generatedSourcesPath = "src/generated/"
def apiFolder = "/api"

java {
    sourceCompatibility = JavaVersion.VERSION_21
    targetCompatibility = JavaVersion.VERSION_21
}

sourceSets {
    main {
        java {
            srcDir("src/main/kotlin")
            srcDir(generatedSourcesPath + "kotlin")
        }
        kotlin { srcDir("$generatedSourcesPath/kotlin") }
        resources { srcDir("src/main/kotlin") }
    }
    test { resources { srcDir("src/test/kotlin") } }
}

ktlint {
    version.set("1.6.0") // Specify the Ktlint version
    android.set(false)
    outputToConsole.set(true)
    outputColorName.set("RED") // Optional: Color for lint output
    ignoreFailures.set(false) // Fails build on lint issues
    filter {
        exclude("**/generated/**")
    }
}


tasks.named('runKtlintCheckOverMainSourceSet') {
    dependsOn(tasks.setupGeneratedSources, tasks.generateAvro)
}

/**
 * Provide personal access token to global gradle properties <user>\.gradle\gradle.properties
 *
 * ```
 * REGISTRY_TOKEN=<token_value>
 * ```
 *
 */

def repositoryCredentialsTokenName = System.getenv("CI_JOB_TOKEN") == null ? "Private-Token" : "Job-Token"
def repositoryCredentialsTokenValue = System.getenv("CI_JOB_TOKEN") ?: findProperty("REGISTRY_TOKEN").toString()

repositories {
    mavenCentral()
    maven {
        url = uri("https://cicd.skyway.porsche.com/api/v4/projects/31402/packages/maven")
        name = "Shared-S3-Client"
        credentials(HttpHeaderCredentials) {
            name = repositoryCredentialsTokenName
            value = repositoryCredentialsTokenValue
        }
        authentication {
            header(HttpHeaderAuthentication)
        }
    }

    maven {
        url = uri("https://cicd.skyway.porsche.com/api/v4/projects/33934/packages/maven")
        name = "PVH"
        credentials(HttpHeaderCredentials) {
            name = repositoryCredentialsTokenName
            value = repositoryCredentialsTokenValue
        }
        authentication {
            header(HttpHeaderAuthentication)
        }
    }

    maven {
        url = uri("https://cicd.skyway.porsche.com/api/v4/projects/20718/packages/maven")
        name = "Fuhrpark-API"
        credentials(HttpHeaderCredentials) {
            name = repositoryCredentialsTokenName
            value = repositoryCredentialsTokenValue
        }
        authentication {
            header(HttpHeaderAuthentication)
        }
    }

    maven {
        url = uri("https://cicd.skyway.porsche.com/api/v4/projects/23878/packages/maven")
        name = "Space-EMHFXP"
        credentials(HttpHeaderCredentials) {
            name = repositoryCredentialsTokenName
            value = repositoryCredentialsTokenValue
        }
        authentication {
            header(HttpHeaderAuthentication)
        }
    }

    maven {
        name = "Aspose Repository"
        url = uri("https://releases.aspose.com/java/repo")
    }

    maven {
        name = "Kafka Confluent"
        url 'https://packages.confluent.io/maven/'
    }
}

configurations {
    resolvableApi {
        canBeResolved = true
        extendsFrom(api)
    }
    avroTools
}

dependencies {
    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    developmentOnly 'org.springframework.boot:spring-boot-devtools'
    developmentOnly 'org.springframework.boot:spring-boot-docker-compose'

    implementation 'org.springframework.boot:spring-boot-starter-actuator'
    implementation 'org.springframework.boot:spring-boot-starter-web'
    implementation 'org.springframework.boot:spring-boot-starter-webflux'
    implementation 'org.springframework.boot:spring-boot-starter-data-jpa'
    implementation 'org.springframework.boot:spring-boot-starter-cache'
    implementation 'org.springframework.boot:spring-boot-starter-validation'
    implementation 'org.springframework.boot:spring-boot-starter-security'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign:4.3.0'
    implementation 'com.fasterxml.jackson.module:jackson-module-kotlin'
    implementation 'com.fasterxml.jackson.datatype:jackson-datatype-jsr310'
    implementation 'org.springframework.boot:spring-boot-starter-oauth2-client'

    implementation 'io.github.openfeign:feign-okhttp:13.6'
    implementation 'io.github.openfeign:feign-jackson:13.6'
    implementation 'org.jetbrains.kotlin:kotlin-reflect'
    implementation 'io.micrometer:micrometer-core'
    implementation 'io.micrometer:micrometer-registry-cloudwatch2'
    implementation 'org.postgresql:postgresql'
    implementation 'org.liquibase:liquibase-core:4.32.0'
    implementation 'ch.qos.logback.contrib:logback-json-classic:0.1.5'
    implementation 'ch.qos.logback.contrib:logback-jackson:0.1.5'
    implementation 'de.porsche.pvh:pvh-async-processing-api:FINAL-0.3.4.20250122083359.0c796671.890.38194017'

    implementation 'io.swagger.core.v3:swagger-annotations:2.2.34'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-ui:2.8.9'
    implementation 'org.springdoc:springdoc-openapi-starter-webmvc-api:2.8.9'

    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-core:1.10.2'
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-reactor:1.10.2'

    implementation 'com.emh.shared:s3-storage-client:2.1.0-dev'
    implementation 'io.awspring.cloud:spring-cloud-aws-starter-s3:3.4.0'
    implementation 'org.springframework.boot:spring-boot-starter-quartz'

    implementation 'com.auth0:java-jwt:4.5.0'
    implementation 'com.auth0:jwks-rsa:0.22.2'
    implementation 'com.google.guava:guava:33.4.8-jre'

    implementation 'org.springframework.kafka:spring-kafka'
    implementation 'org.mapstruct:mapstruct:1.6.3'
    kapt 'org.mapstruct:mapstruct-processor:1.6.3'

    implementation 'io.opentelemetry.instrumentation:opentelemetry-instrumentation-annotations:2.16.0'

    implementation 'software.amazon.awssdk:rds:2.31.69'

    implementation 'io.hypersistence:hypersistence-utils-hibernate-63:3.10.1'

    implementation("com.aspose:aspose-words:25.6:jdk17")
    implementation("com.aspose:aspose-email:21.5:jdk16")
    implementation("com.aspose:aspose-barcode:25.5")
    // https://mvnrepository.com/artifact/app.getxray/xray-junit-extensions
    implementation 'app.getxray:xray-junit-extensions:0.10.0'
    implementation 'com.opencsv:opencsv:5.11.2'
    implementation("org.apache.poi:poi:5.4.1")
    implementation("org.apache.poi:poi-ooxml:5.4.1")
    implementation("org.reflections:reflections:0.10.2")
    implementation("io.confluent:kafka-json-schema-serializer:8.0.0")
    implementation 'io.confluent:kafka-avro-serializer:8.0.0'
    implementation 'org.apache.avro:avro:1.12.0'
    avroTools 'org.apache.avro:avro-tools:1.12.0'

    //API
    api 'com.emh:space-fuhrpark:5.0.0-SNAPSHOT'
    api 'com.emh:space-emhfxp:14.0.0-dev'

    testImplementation 'com.tngtech.archunit:archunit-junit5:1.4.1'

    testImplementation 'org.wiremock:wiremock-standalone:3.13.1'
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    testImplementation 'org.springframework.security:spring-security-test'
    testImplementation 'io.mockk:mockk:1.14.4'
    testImplementation 'com.ninja-squad:springmockk:4.0.2'

    testImplementation 'org.testcontainers:postgresql:1.21.2'
    testImplementation 'org.testcontainers:junit-jupiter:1.21.2'
    testImplementation 'org.testcontainers:testcontainers:1.21.2'
    testImplementation 'org.springframework.boot:spring-boot-testcontainers'

    testImplementation 'au.com.dius.pact.consumer:junit5:4.6.17'
    testImplementation 'au.com.dius.pact.provider:junit5spring:4.6.17'
    testImplementation 'org.springframework.kafka:spring-kafka-test:3.3.7'
    testImplementation 'org.awaitility:awaitility-kotlin:4.3.0'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.10.2'
}

tasks.named('test', Test) {
    useJUnitPlatform({ excludeTags("bdd") }) // Make use of Junit5
    maxParallelForks = 4 // Make use of resources of the machine it is running on. Test runs will finish faster
    systemProperty(
            "junit.platform.execution.listeners.deactivate",
            "app.getxray.xray.junit.customjunitxml.EnhancedLegacyXmlReportGeneratingListener"
    )
}

task runBddTests(type: Test) {
    useJUnitPlatform({ includeTags("bdd") })
}

jacoco {
    toolVersion = "0.8.11"
}

tasks.jacocoTestReport {
    reports {
        xml.required = true
        html.required = true
    }

    onlyIf {
        gradle.taskGraph.hasTask(tasks.getByName("build"))
    }
    dependsOn(tasks.test)
}

tasks.jacocoTestCoverageVerification {
    violationRules {
        rule {
            limit {
                minimum = "0.7".toBigDecimal()
            }
        }
    }

    onlyIf {
        gradle.taskGraph.hasTask(tasks.getByName("build"))
    }
    dependsOn(tasks.test)
}

tasks.named("build") {
    finalizedBy(tasks.jacocoTestReport, tasks.jacocoTestCoverageVerification)
}

tasks.compileKotlin {
    kotlinOptions {
        jvmTarget = "21"
        languageVersion = "1.9"
        apiVersion = "1.9"
    }
    dependsOn("setupGeneratedSources", "generateAvro")
}

tasks.withType(KaptGenerateStubsTask).configureEach {
    dependsOn("setupGeneratedSources", "generateAvro")
}

bootRun {
    jvmArgs = ["-Xms256m", "-Xmx1024m"]
}

test {
    jvmArgs = ["-Xms2048m", "-Xmx4096m"]
}

def isNonStable = { String version ->
    def stableKeyword = ['RELEASE', 'FINAL', 'GA'].any { it -> version.toUpperCase().contains(it) }
    def regex = /^[0-9,.v-]+(-r)?$/
    return !stableKeyword && !(version ==~ regex)
}

tasks.named("dependencyUpdates").configure {
    rejectVersionIf {
        isNonStable(it.candidate.version) && !isNonStable(it.currentVersion)
    }
}

// B2B Avro schema generation
tasks.register("generateAvro", JavaExec) {
    def inputDir = file("src/main/resources/avro/b2b")
    def outputDir = file("$generatedSourcesPath/kotlin")

    inputs.dir inputDir
    outputs.dir outputDir
    outputs.upToDateWhen { false }

    classpath = configurations.avroTools
    mainClass.set("org.apache.avro.tool.Main")
    args = ["compile", "schema", inputDir.absolutePath, outputDir.absolutePath]
    dependsOn(setupGeneratedSources)
}

tasks.register("unpackApi", Copy) {
    duplicatesStrategy = DuplicatesStrategy.INCLUDE
    outputs.upToDateWhen { false }
    configurations.named("resolvableApi").get().forEach {
        from(zipTree(it))
        into("$projectDir/$apiFolder")
    }
}

//ECC adapter server code generation
tasks.register("generateEccServer", GenerateTask) {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin-spring")
    inputSpec.set("$projectDir/$apiFolder/ecc.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.fleetmanagement.modules.ecc")
    modelPackage.set("com.fleetmanagement.modules.ecc.adapter.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
            [
                    serviceInterface     : true,
                    serviceImplementation: false,
                    exceptionHandler     : false,
                    apiSuffix            : "",
                    useBeanValidation    : false,
                    serializationLibrary : "jackson",
                    annotationLibrary    : "none",
                    documentationProvider: "none",
                    useSwaggerUI         : false
            ]
    )
    dependsOn("unpackApi")
}

//vtstamm adapter server code generation
tasks.register("generateVTStammServer", GenerateTask) {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin-spring")
    inputSpec.set("$projectDir/$apiFolder/vtstamm.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.fleetmanagement.modules.vtstamm.adapter.incoming.rest")
    modelPackage.set("com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
            [
                    serviceInterface     : true,
                    serviceImplementation: false,
                    exceptionHandler     : false,
                    apiSuffix            : "",
                    useBeanValidation    : false,
                    serializationLibrary : "jackson",
                    annotationLibrary    : "none",
                    documentationProvider: "none",
                    useSwaggerUI         : false,
                    useTags              : true
            ]
    )
    templateDir.set("${projectDir.absolutePath}/src/main/resources/generator-templates")
    dependsOn("unpackApi")
}

//PACE internal order number adapter client code generation
tasks.register("generatePaceInternalOrderNumberClient", GenerateTask) {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin")
    inputSpec.set("$projectDir/src/main/resources/pace/pace-internalordernumber.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.fleetmanagement.modules.paceinternalordernumber.generated.adapter.out.rest")
    modelPackage.set("com.fleetmanagement.modules.paceinternalordernumber.generated.adapter.out.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
            [
                    useSpringBoot3      : true,
                    dateLibrary         : "java8",
                    library             : "jvm-spring-webclient",
                    apiSuffix           : "Api",
                    serializationLibrary: "jackson",
                    enumPropertyNaming  : "original",
                    omitGradleWrapper   : true
            ]
    )
    dependsOn("unpackApi")
}

//PACE balance sheet adapter client code generation
tasks.register("generatePaceBalanceSheetClient", GenerateTask) {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin")
    inputSpec.set("$projectDir/src/main/resources/pace/pace-balancesheet.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest")
    modelPackage.set("com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
            [
                    useSpringBoot3      : true,
                    dateLibrary         : "java8",
                    library             : "jvm-spring-webclient",
                    apiSuffix           : "Api",
                    serializationLibrary: "jackson",
                    enumPropertyNaming  : "original",
                    omitGradleWrapper   : true
            ]
    )
    dependsOn("unpackApi")
}

//employee client code generation
tasks.register("generateEmployeeClient", GenerateTask) {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin")
    inputSpec.set("$projectDir/$apiFolder/employees.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.fleetmanagement.modules.vehicleperson.integration.userservice.rest")
    modelPackage.set("com.fleetmanagement.modules.vehicleperson.integration.userservice.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
            [
                    useSpringBoot3      : true,
                    dateLibrary         : "java8",
                    library             : "jvm-spring-webclient",
                    apiSuffix           : "Api",
                    serializationLibrary: "jackson",
                    enumPropertyNaming  : "UPPERCASE",
                    omitGradleWrapper   : true
            ]
    )
    dependsOn("unpackApi")
}

//employee history client code generation
tasks.register("generateEmployeeHistoryClient", GenerateTask) {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin")
    inputSpec.set("$projectDir/$apiFolder/employee-history.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.fleetmanagement.modules.vehicleperson.integration.userservice.rest")
    modelPackage.set("com.fleetmanagement.modules.vehicleperson.integration.userservice.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
            [
                    useSpringBoot3      : true,
                    dateLibrary         : "java8",
                    library             : "jvm-spring-webclient",
                    apiSuffix           : "Api",
                    serializationLibrary: "jackson",
                    enumPropertyNaming  : "UPPERCASE",
                    omitGradleWrapper   : true
            ]
    )
    dependsOn("unpackApi")
}

//DMS vehicle migration server model code generation
tasks.register("generateDMSVehicleMigrationServer", GenerateTask) {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin-spring")
    inputSpec.set("$projectDir/$apiFolder/dms-vehicle-migration.yaml")
    outputDir.set("$projectDir/generated")
    modelPackage.set("com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
            [
                    serviceInterface     : true,
                    serviceImplementation: false,
                    exceptionHandler     : false,
                    apiSuffix            : "",
                    useBeanValidation    : false,
                    serializationLibrary : "jackson",
                    annotationLibrary    : "none",
                    documentationProvider: "none",
                    useSwaggerUI         : false
            ]
    )
    dependsOn("unpackApi")
}


//Carsync trip invoice code generation
tasks.register("generateCarsyncTripInvoiceClient", GenerateTask) {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin")
    inputSpec.set("$projectDir/src/main/resources/carsync/carsync-tripinvoice.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.fleetmanagement.modules.carsync.generated.adapter.out.rest")
    modelPackage.set("com.fleetmanagement.modules.carsync.generated.adapter.out.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
            [
                    useSpringBoot3      : true,
                    dateLibrary         : "java8",
                    library             : "jvm-spring-webclient",
                    apiSuffix           : "Api",
                    serializationLibrary: "jackson",
                    enumPropertyNaming  : "original",
                    omitGradleWrapper   : true
            ]
    )
    dependsOn("unpackApi")
}

//rela adapter client code generation
tasks.register("generateRelaClient", GenerateTask) {
    outputs.upToDateWhen { false }
    validateSpec.set(true)
    generatorName.set("kotlin")
    inputSpec.set("$projectDir/$apiFolder/rela.yaml")
    outputDir.set("$projectDir/generated")
    apiPackage.set("com.fleetmanagement.modules.rela.adapter.out.rest")
    modelPackage.set("com.fleetmanagement.modules.rela.adapter.out.rest.model")
    modelNameSuffix.set("Dto")
    additionalProperties.set(
            [
                    useSpringBoot3      : true,
                    dateLibrary         : "java8",
                    library             : "jvm-spring-webclient",
                    apiSuffix           : "Api",
                    serializationLibrary: "jackson",
                    enumPropertyNaming  : "original",
                    omitGradleWrapper   : true
            ]
    )
    dependsOn("unpackApi")
}

tasks.register("setupGeneratedSources", Copy) {
    doFirst { delete(generatedSourcesPath) }
    from("generated/src/main") {
        exclude(
                "resources",
                "**/Serializer.kt",
                "*/org",
                "**/ApiUtil*",
                "**/PaceApi.kt",
                "**/DmsApi.kt",
                "**/EmployeeApi.kt",
                "**/HistoryApi.kt",
                "**/AuthApi.kt",
                "**/TripApi.kt"
        )
    }
    into(generatedSourcesPath)
    includeEmptyDirs = false
    dependsOn(
            "generateEccServer",
            "generatePaceInternalOrderNumberClient",
            "generatePaceBalanceSheetClient",
            "generateDMSVehicleMigrationServer",
            "generateEmployeeClient",
            "generateEmployeeHistoryClient",
            "generateCarsyncTripInvoiceClient",
            "generateVTStammServer",
            "generateRelaClient"
    )
    doLast { delete("generated/") }
}
