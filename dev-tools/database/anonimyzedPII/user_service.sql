-- Create a function to generate a random string
CREATE OR REPLACE FUNCTION random_string(length INT)
RETURNS TEXT AS $$
DECLARE
chars TEXT := 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    result TEXT := '';
    i INT;
BEGIN
FOR i IN 1..length LOOP
        result := result || substr(chars, floor(random() * length(chars) + 1)::int, 1);
END LOOP;
RETURN result;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION random_first_name()
RETURNS TEXT AS $$
DECLARE
first_names TEXT[] := ARRAY[
        '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>',
        '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>eran', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>n',
        '<PERSON>', '<PERSON><PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', 'Sam', 'Indigo', 'Sky', '<PERSON>', '<PERSON>'
    ];
BEG<PERSON>
RETUR<PERSON> first_names[1 + floor(random() * array_length(first_names, 1))::int];
END;
$$ LANGUAGE plpgsql;

-- 2. <PERSON>ction: Generate a random last name
CREATE OR REPLACE FUNCTION random_last_name()
RETURNS TEXT AS $$
DECLARE
last_names TEXT[] := ARRAY[
        'Smith', 'Johnson', 'Brown', 'Taylor', 'Anderson', 'Clark', 'Lewis', 'Walker', 'Hall', 'Young',
        'Allen', 'Wright', 'King', 'Scott', 'Green', 'Adams', 'Baker', 'Gonzalez', 'Nelson', 'Carter',
        'Mitchell', 'Perez', 'Roberts', 'Turner', 'Phillips', 'Campbell', 'Parker', 'Evans', 'Edwards', 'Collins',
        'Stewart', 'Sanchez', 'Morris', 'Rogers', 'Reed', 'Cook', 'Morgan', 'Bell', 'Murphy', 'Bailey',
        'Rivera', 'Cooper', 'Richardson', 'Cox', 'Howard', 'Ward', 'Torres', 'Peterson', 'Gray', 'Ramirez'
    ];
BEGIN
RETURN last_names[1 + floor(random() * array_length(last_names, 1))::int];
END;
$$ LANGUAGE plpgsql;

-- create city
CREATE OR REPLACE FUNCTION random_city()
RETURNS TEXT AS $$
DECLARE
cities TEXT[] := ARRAY[
        'Berlin', 'Hamburg', 'Munich', 'Cologne', 'Frankfurt', 'Stuttgart', 'Duesseldorf', 'Dortmund', 'Essen', 'Leipzig',
        'Bremen', 'Dresden', 'Hanover', 'Nuremberg', 'Duisburg', 'Bochum', 'Wuppertal', 'Bielefeld', 'Bonn', 'Mannheim',
        'Karlsruhe', 'Wiesbaden', 'Augsburg', 'Gelsenkirchen', 'Moenchengladbach', 'Braunschweig', 'Chemnitz', 'Kiel', 'Aachen', 'Halle',
        'Magdeburg', 'Freiburg', 'Krefeld', 'Luebeck', 'Mainz', 'Erfurt', 'Oberhausen', 'Rostock', 'Kassel', 'Hagen',
        'Saarbruecken', 'Hamm', 'Muelheim', 'Potsdam', 'Ludwigshafen', 'Oldenburg', 'Leverkusen', 'Osnabrueck', 'Solingen', 'Heidelberg'
    ];
BEGIN
RETURN cities[1 + FLOOR(RANDOM() * ARRAY_LENGTH(cities, 1))::INT];
END;
$$ LANGUAGE plpgsql;

   -- create city
CREATE OR REPLACE FUNCTION random_postalcode()
RETURNS TEXT AS $$
DECLARE
BEGIN
RETURN '_' || FLOOR(RANDOM() * 90000 + 10000)::TEXT;
END;
$$ LANGUAGE plpgsql;


UPDATE emh_user.employee e
SET
    first_name = data.fname,
    last_name = data.lname,
    company_email = '_' || lower(data.fname) || '.' || lower(data.lname) || '@company.com'
    FROM (
    SELECT
        id,
        random_first_name() AS fname,
        random_last_name() AS lname
    FROM emh_user.employee
) AS data
WHERE e.id = data.id;

UPDATE emh_user.pag_employee e
SET
    street = '_' || data.lname || 'str',
    city = random_city(),
    private_email = '_' || lower(data.fname) || '.' || lower(data.lname) || '@private.com',
    internal_company_phone_number = '+49-' || LPAD((FLOOR(RANDOM() * 900 + 100))::TEXT, 3, '0') || '-' || LPAD((FLOOR(RANDOM() * ********))::TEXT, 7, '0'),
    company_mobile_number = '+49-' || LPAD((FLOOR(RANDOM() * 900 + 100))::TEXT, 3, '0') || '-' || LPAD((FLOOR(RANDOM() * ********))::TEXT, 7, '0'),
    postal_code = random_postalcode(),
    personnel_clerk = random_string(5),
    accounting_clerk = random_string(5),
    employee_group = random_string(5),
    cost_center = random_string(5),
    personal_area = random_string(5),
    id_card_number = random_string(5),
    private_mobile_number = '+49-' || LPAD((FLOOR(RANDOM() * 900 + 100))::TEXT, 3, '0') || '-' || LPAD((FLOOR(RANDOM() * ********))::TEXT, 7, '0')
FROM (
    SELECT
    id,
    random_first_name() AS fname,
    random_last_name() AS lname
    FROM emh_user.pag_employee
    ) AS data
WHERE e.id = data.id;



UPDATE emh_user.subsidiary_employee e
SET
    street = '_' || data.lname || 'str',
    city = random_city(),
    private_email = '_' || lower(data.fname) || '.' || lower(data.lname) || '@private.com',
    internal_company_phone_number = '+49-' || LPAD((FLOOR(RANDOM() * 900 + 100))::TEXT, 3, '0') || '-' || LPAD((FLOOR(RANDOM() * ********))::TEXT, 7, '0'),
    company_mobile_number = '+49-' || LPAD((FLOOR(RANDOM() * 900 + 100))::TEXT, 3, '0') || '-' || LPAD((FLOOR(RANDOM() * ********))::TEXT, 7, '0'),
    postal_code = random_postalcode(),
    personnel_clerk = random_string(5),
    accounting_clerk = random_string(5),
    employee_group = random_string(5),
    cost_center = random_string(5),
    personal_area = random_string(5),
    id_card_number = random_string(5),
    private_mobile_number = '+49-' || LPAD((FLOOR(RANDOM() * 900 + 100))::TEXT, 3, '0') || '-' || LPAD((FLOOR(RANDOM() * ********))::TEXT, 7, '0')
FROM (
    SELECT
    id,
    random_first_name() AS fname,
    random_last_name() AS lname
    FROM emh_user.subsidiary_employee
    ) AS data
WHERE e.id = data.id;


-- delete all data form the employee_history
DELETE FROM emh_user.employee_history;
