UPDATE vehicle.production_info
SET
    production_number = NULL;

UPDATE vehicle.order_info
SET
    commission_number = NULL,
    invoice_number = NULL;

UPDATE vehicle.return_info
SET
    factory_car_preparation_order_number = NULL;

DELETE FROM vehicle.vehicle
WHERE embargo_info_id IN (
    SELECT id FROM vehicle.embargo_info WHERE in_embargo IS TRUE OR in_embargo IS NULL
);

DELETE FROM vehicle.vehicle
WHERE embargo_info_id IN (
    SELECT id FROM vehicle.embargo_info WHERE in_embargo IS TRUE OR in_embargo IS NULL
);

