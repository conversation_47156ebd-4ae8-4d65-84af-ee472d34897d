#!/bin/bash

commit_msg=$(head -n1 "$1")

# Regex pattern: type(scope): message
pattern="(Merge\s.*|Revert\s.*|(feat|fix)(\((docs|infra|refactor|\w+-\w+)\))?(!)?:\s.*)"


if echo "$commit_msg" | grep -Eq "$pattern"; then
  exit 0
else
  echo The following commit message does not follow guidelines
  echo "$commit_msg"
  echo "❌ Commit message must follow the format: type(scope): message"
  echo "✅ Allowed types: feat, fix"
  echo "✅ Allowed scopes: docs, infra, refactor, ci, lint, chore OR a JIRA ticket (must contain a hyphen)"
  echo "Examples:"
  echo "1. feat(FPT9-123): add user login feature"
  echo "2. feat: something to test"
  echo "3. Revert \"feat: test\""
  echo "4. fix(FTP3-101): something happened"
  echo "5. feat(refactor): Renaming variable"
  echo "6. feat(FTP1-1000): feature commit"
  echo "7. feat(FTP5-6231)!: breaking change in this commit"
  echo "8. feat!: breaking without scope"
  echo "9. Merge branch 'main' into FTP1-1000"
fi
