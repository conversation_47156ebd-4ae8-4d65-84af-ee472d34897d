#!/bin/bash
set -eo pipefail
SCRIPT_DIR=$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" &>/dev/null && pwd)
pushd "$SCRIPT_DIR" >/dev/null 2>&1 || exit 1
source ./initialize_kafka_config.sh

# check parameters
if [ -z "$1" ]; then
    echo "Please specify the topic as first parameter"
    echo "Usage:"
    echo "     ./consume-topic.sh <topic name> <optional: max messages>"
    echo ""
    echo "Sample Usage:"
    echo "    ./consume-topic.sh FRA_emhs_fleet_master_vehicle_dev"
    exit 1
fi
TOPIC=$1
MAX_MESSAGES=${2:-5}
GROUP_ID="${TOPIC}-local-test"

# execute the command
echo "Consuming ${MAX_MESSAGES} messages from topic ${1}"
../../bin/kafka/bin/kafka-console-consumer.sh \
    --bootstrap-server $BROKER \
    --consumer.config=$KAFKA_AUTH_CONFIG_FILE \
    --topic $TOPIC \
    --group $GROUP_ID \
    --property print.key=true \
    --property print.headers=true \
    --from-beginning \
    --max-messages $MAX_MESSAGES

popd >/dev/null 2>&1 || exit 1
