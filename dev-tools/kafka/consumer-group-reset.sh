#!/bin/bash
set -eo pipefail
SCRIPT_DIR=$(cd -- "$(dirname -- "${BASH_SOURCE[0]}")" &>/dev/null && pwd)
pushd "$SCRIPT_DIR" >/dev/null 2>&1 || exit 1
source ./initialize_kafka_config.sh


if [ -z "$1" ] || [ -z "$2" ]; then
    echo "Please specify the topic as first parameter and group as second parameter"
    echo "Usage:"
    echo "     ./consume-group-reset.sh <topic name> <group-name>"
    echo ""
    echo "Sample Usage:"
    echo "    ./consume-group-reset.sh FRA_emhs_fleet_master_vehicle_dev test-group-1"
    exit 1
fi
TOPIC=$1
GROUP_ID=$2

# execute the command
echo "Resetting consumer group offset for group ${GROUP_ID} on topic ${TOPIC}"
"../../bin/kafka/bin/kafka-consumer-groups.sh" \
    --bootstrap-server $BROKER \
    --command-config $KAFKA_AUTH_CONFIG_FILE \
    --group $GROUP_ID \
    --topic $TOPIC \
    --reset-offsets --to-earliest \
    --execute

"../../bin/kafka/bin/kafka-consumer-groups.sh" \
    --bootstrap-server $BROKER \
    --command-config $KAFKA_AUTH_CONFIG_FILE \
    --describe --group $GROUP_ID

popd >/dev/null 2>&1 || exit 1
