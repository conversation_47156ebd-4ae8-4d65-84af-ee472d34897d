if [ ! -d ../../bin/kafka ]; then
  echo "ERROR: Kafka not found!"
  echo "Please download latest kafka from https://kafka.apache.org/ and place it under <vehicle-service repo root}/bin/kafka"
  exit 1
fi

export BROKER="pkc-rxgnk.eu-west-1.aws.confluent.cloud:9092"
export KAFKA_USERNAME="SW42RJER6HGHBWY6"
export KAFKA_PASSWORD=$(aws ssm get-parameters --name "/vehicle-service/external/streamzilla/kafka_password" --with-decryption --query "Parameters[0].Value" --output text)
if [ "$KAFKA_PASSWORD" = "None" ]; then echo "Could not get Streamzilla password from aws parameter store."; exit 1; fi
if [ "$KAFKA_PASSWORD" = "" ]; then echo "Could not get Streamzilla password from aws parameter store."; exit 1; fi
export KAFKA_AUTH_CONFIG_FILE="../../bin/kafka/consumer.properties"

cat > $KAFKA_AUTH_CONFIG_FILE << EOF
security.protocol=SASL_SSL
sasl.mechanism=PLAIN
sasl.jaas.config=org.apache.kafka.common.security.plain.PlainLoginModule required username="$KAFKA_USERNAME" password="$KAFKA_PASSWORD";
EOF
