# Replay Kafka messages

This tool is to emit Kafka messages to a topic. It was used for PVH originally, please feel free to generify.

## How to use
### Setup
you need Python installed and the following libraries
```
pip install pandas kafka-python 
```
you need the local Kafka setup running (`docker-compose up` in root)

### Publish a message
Config:
1. in `replay_message_locally.py` you can change the TOPIC_NAME
2. in kafka_messages.csv you can change the messages to be published

```
python replay_messages_locally.py 
```
