#
# This code is protected by intellectual property rights.
# Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
# © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
#
import pandas as pd
from kafka import KafkaProducer
import json
import time
from datetime import datetime

# Configuration
TOPIC_NAME = 'FRA_pvh_Vehicle_Embargo'
KAFKA_BROKER = 'localhost:29092'
CSV_FILE_PATH = 'kafka_messages.csv'

# Create a Kafka producer
producer = KafkaProducer(
    bootstrap_servers=KAFKA_BROKER,
    value_serializer=lambda v: json.dumps(v).encode('utf-8'),  # Serialize JSON
    key_serializer=lambda k: json.dumps(k).encode('utf-8')      # Serialize JSON keys if needed
)
# Read the CSV file
df = pd.read_csv(CSV_FILE_PATH, )

for index, row in df.iterrows():
    key = row['key']
    headers_from_csv = json.loads(row['headerString'])
    value = json.loads(row['bodyString'])
    timestamp = int(datetime.strptime(row['timestamp'], "%Y-%m-%d").timestamp())*1000
    keys_to_pick = ['VIN', 'Database-Action', 'Is-Embargo', 'Is-Synthetic']
    headers_for_kafka = [(key, headers_from_csv[key].encode('utf-8')) for key in keys_to_pick]

    producer.send(TOPIC_NAME, value=value, key=key, headers=headers_for_kafka, timestamp_ms=timestamp)

    time.sleep(2)

producer.flush()
producer.close()

print("Messages sent to Kafka successfully.")
