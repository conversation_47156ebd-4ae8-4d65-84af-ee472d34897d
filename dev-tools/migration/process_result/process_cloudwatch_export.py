import os

import pandas as pd
input_file = 'logs-insights-export.csv'
# Define the error patterns to count
error_patterns = [
    "vehicleType must not be null",
    "Error creating registration order \[ProblemDetail\[type='error.registration.invalidField.vin', title='null', status=0, detail='vin: Required field VIN is invalid",
    "ObjectOptimisticLockingFailureException\: Batch update returned unexpected row count from update \[0\]; actual row count: 0; expected: 1; statement executed: update vehicletransfer.vehicle_transfer",
    "OptimisticLockException: Batch update returned unexpected row count from update \[0\]; actual row count: 0; expected: 1;",
    "NoSuchElementException: No value present.*VehicleRegistrationChangeCaptureService.captureChanges",
    "Unexpected exception occurred invoking async method: public void com.fleetmanagement.modules.vehicletransfer.application.HistoryCaptureService.handleVehicleTransferEvents",
    "Unexpected exception occurred invoking async method: public void com.fleetmanagement.modules.vehicletransfer.application.CurrentVehicleTransferService.updateCurrentVehicleTransferOnVehicleTransferEvent",
    "Unexpected exception occurred invoking async method: public void com.fleetmanagement.modules.vehicletransfer.application.CurrentVehicleTransferService.updateCurrentVehicleTransferOnPlannedVehicleTransferEvent",
    "VSHikariCP \- Connection is not available, request timed out after",
    "Unable to acquire JDBC Connection",
    "Invoice cannot be created for vehicle status: S200",
    "Invoice cannot be created for vehicle status: SX98",
    "Error while setting SOLD_DATE. The data field SOLD_DATE cannot be maintained, because the vehicle is marked for scrapping.",
    "Could not open JPA EntityManager for transaction",
    "Error while setting APPROVED_FOR_SCRAPPING_DATE. The data field APPROVED_FOR_SCRAPPING_DATE cannot be maintained, because the vehicle is not blocked for sale.",
    "Error while setting VEHICLE_SENT_TO_SALES_DATE. The data field VEHICLE_SENT_TO_SALES_DATE cannot be maintained, because the vehicle is blocked for sale.",
    "Error while setting SCRAPPED_DATE. The data field SCRAPPED_DATE cannot be maintained, because the vehicle is not marked for scrapping.",
    "Error while setting SCRAPPED_DATE. The data field SCRAPPED_DATE cannot be maintained, because the vehicle is not blocked for sale.",
    "Error while setting APPROVED_FOR_SCRAPPING_DATE. The data field APPROVED_FOR_SCRAPPING_DATE cannot be maintained, because the vehicle is not marked for scrapping.",
    "Error while setting SOLD_DATE. The data field SOLD_DATE cannot be maintained, because the vehicle is blocked for sale.",
    "Error while setting PREPARATION_DONE_DATE. The data field PREPARATION_DONE_DATE cannot be maintained, because the vehicle is blocked for sale.",
    "Error while setting SOLD_DATE. The data field SOLD_DATE cannot be maintained, because the vehicle is marked for scrapping."
    "Invoice cannot be created for vehicle status: .*",
    "Parking lot .* is not configured for location ZWZ",
    "hsn: Value can be empty or upto 4 digit number",
]

def process_csv(file_path):
    # Read the CSV file

    df = pd.read_csv(file_path, sep=',', header=0, names=['@timestamp', '@message'])

    # Convert 'error' column to string type
    df['error'] = df['@message'].astype(str)

    # Count occurrences of each pattern
    pattern_counts = {}
    for pattern in error_patterns:
        # Escape special regex characters in the pattern to treat them as literals
        # Replace any explicit wildcards (.*) that were escaped
        regex_pattern = pattern.replace('\\.\\\*', '.*')

        pattern_counts[pattern] = df['error'].str.contains(regex_pattern, regex=True).sum()
        df = df[~df['error'].str.contains(regex_pattern, regex=True)]

    # Create a dataframe with the counts
    result_df = pd.DataFrame({
        'error_pattern': list(pattern_counts.keys()),
        'count': list(pattern_counts.values())
    })

    # Sort by count in descending order
    result_df = result_df.sort_values(by='count', ascending=False)

    # Save the processed DataFrame to a new CSV file
    output_file_path = os.path.splitext(file_path)[0] + '_processed.csv'
    df.to_csv(output_file_path, index=False)

    # Save the processed DataFrame to a new CSV file
    output_file_path = os.path.splitext(file_path)[0] + '_error_count.csv'
    result_df.to_csv(output_file_path, index=False)

    return output_file_path, result_df


if __name__ == "__main__":
    # Example usage
    processed_file, result_df = process_csv(input_file)
    print(f"Error count summary saved as: {processed_file}")
    print("\nError pattern counts:")
    print(result_df)
