import os

import pandas as pd
input_file = 'migration_report.csv'
# Define the error patterns to count
error_patterns = [
    "No matching depreciation cost center found for vehicleUsage \[Mobilitätspool <PERSON>\] and vehicle type \[TRAILER\]",
    "No matching depreciation cost center found for vehicleUsage \[Entwicklung\] and vehicle type \[MOTORCYCLE\]",
    "No matching depreciation cost center found for vehicleUsage \[Mobilitätspool <PERSON>\] and vehicle type \[TRANSPORTER\]",
    "No matching usage group found for vehicle usage 'Berufsausbildung'",
    "success",
    "No matching vehicle usage found for vehicle usage with value \[null\] found",
    "No matching vehicle responsible person found for employee number \[.*\]",
    "Could not determine depreciation relevant cost center, as either vehicleUsage and/or vehicleType information are missing.",
    "FMS registration date cannot be null",
    "Transfers count \([\d]+\) did not match count of maintenance order numbers \([\d]+\)",
    "Order numbers are incorrectly assigned, latest value attached to FMS vehicle did not match newest in the list.",
    "No manufacturer found for null",
    "No vehicle responsible person provided for vehicle transfer migration",
    "Unable to acquire JDBC Connection",
    "Could not open JPA EntityManager for transaction",
    "Error while setting APPROVED_FOR_SCRAPPING_DATE. The data field APPROVED_FOR_SCRAPPING_DATE cannot be maintained, because the vehicle is not blocked for sale.",
    "Error while setting VEHICLE_SENT_TO_SALES_DATE. The data field VEHICLE_SENT_TO_SALES_DATE cannot be maintained, because the vehicle is blocked for sale.",
    "Error while setting SCRAPPED_DATE. The data field SCRAPPED_DATE cannot be maintained, because the vehicle is not marked for scrapping.",
    "Error while setting SCRAPPED_DATE. The data field SCRAPPED_DATE cannot be maintained, because the vehicle is not blocked for sale.",
    "Error while setting APPROVED_FOR_SCRAPPING_DATE. The data field APPROVED_FOR_SCRAPPING_DATE cannot be maintained, because the vehicle is not marked for scrapping.",
    "Error while setting SOLD_DATE. The data field SOLD_DATE cannot be maintained, because the vehicle is blocked for sale.",
    "vehicleType must not be null",
    "Planned appointment's responsible person did not match the latest vehicle transfer's responsible person!",
    "VehicleTransfer .* has a usage group which force using cost center to be not nullable",
    "Invoice cannot be created for vehicle status: S200",
    "Invoice cannot be created for vehicle status: SX98",
    "Cannot resolve vehicle-id from vehicle-data module",
    "Parking lot .* is not configured for location ZWZ",
    "Error while setting PREPARATION_DONE_DATE. The data field PREPARATION_DONE_DATE cannot be maintained, because the vehicle is blocked for sale.",
    "Error while setting SOLD_DATE. The data field SOLD_DATE cannot be maintained, because the vehicle is marked for scrapping."
]

def process_csv(file_path):
    # Read the CSV file
    # headers: vin;product_guid;error
    df = pd.read_csv(file_path, sep=';', header=0, names=['vin', 'product_guid', 'error'])

    # Convert 'error' column to string type
    df['error'] = df['error'].astype(str)

    # Prepare results
    pattern_counts = {}
    pattern_vins = {}
    df_remaining = df.copy()
    for pattern in error_patterns:
        # Escape special regex characters in the pattern to treat them as literals
        regex_pattern = pattern.replace('\\.\\*', '.*')
        mask = df_remaining['error'].str.contains(regex_pattern, regex=True)
        pattern_counts[pattern] = mask.sum()
        vins = df_remaining.loc[mask, 'vin'].tolist()
        # Cut vins to maximum of 20
        pattern_vins[pattern] = ';'.join(vins[:20])
        df_remaining = df_remaining[~mask]

    # Create a dataframe with the counts and vins
    result_df = pd.DataFrame({
        'error_pattern': list(pattern_counts.keys()),
        'count': list(pattern_counts.values()),
        'vins': list(pattern_vins.values())
    })

    # Sort by count in descending order
    result_df = result_df.sort_values(by='count', ascending=False)

    # Save the processed DataFrame to a new CSV file
    output_file_path = os.path.splitext(file_path)[0] + '_processed.csv'
    df_remaining.to_csv(output_file_path, index=False)

    # Optionally, save the result_df with pattern summary
    summary_file_path = os.path.splitext(file_path)[0] + '_error_summary.csv'
    result_df.to_csv(summary_file_path, index=False)

    return output_file_path, result_df


if __name__ == "__main__":
    # Example usage
    processed_file, result_df = process_csv(input_file)
    print(f"Error count summary saved as: {processed_file}")
    print("\nError pattern counts:")
    print(result_df)
