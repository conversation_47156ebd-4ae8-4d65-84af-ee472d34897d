# Clean up specific vehicles from database

This Script is intended for dev/staging only. Never remove anything from production.
This script will generate SQL DELETE statements to remove a vehicle from the database.

## Usage

Execute the following SQL statement in the query editor to remove the test vehicles from the database:

```sql
CREATE TABLE test_vehicle_ids AS
SELECT 
    id,
    color_info_id,
    country_info_id,
    delivery_info_id,
    embargo_info_id,
    consumption_info_id,
    fleet_info_id,
    model_info_id,
    order_info_id,
    pmp_data_id,
    price_info_id,
    production_info_id,
    return_info_id,
    technical_info_id,
    tire_set_change_id
FROM vehicle.vehicle
WHERE vin IN (
    'BP0ZZZ95ZLLB74056','BP0ZZZ95ZLLB64522','WP0ZZZWA5TS100007','WP0ZZZWA7TS200075',
    'WP0ZZZWA4TS200048','WP0ZZZWA7TS200030','WP0ZZZWA7TS200044','WP0ZZZWA6TS200052',
    'WP0Z<PERSON><PERSON>WA6TS200018','WP0ZZZWA6TS200004','WP0ZZZWA8SS000515','WP0ZZZWA4SS030014',
    'WP0ZZZWA8SS030002','WP0ZZZWA8SS030016','WP0ZZZWA7SS030007','WP0ZZZWA7SS060060',
    'WP0ZZZWA6SS000514','WP0ZZZY16RSA56611','WP0ZZZWA4SS060050','WP0ZZZWA8SS060018',
    'WP0ZZZY16RSA77247','WP0ZZZY16RSA77118'
);

DELETE FROM vehiclesales.vehicle_sale WHERE vehicle_id IN (SELECT id FROM test_vehicle_ids);
DELETE FROM vehiclesales.vehicle_invoice WHERE vehicle_id IN (SELECT id FROM test_vehicle_ids);

DELETE FROM predelivery.pre_delivery_inspection WHERE vehicle_id IN (SELECT id FROM test_vehicle_ids);

DELETE FROM vehiclehistory.change_event_delta WHERE vehicle_id IN (SELECT id FROM test_vehicle_ids);

DELETE FROM vehicleregistration.order WHERE vehicle_id IN (SELECT id FROM test_vehicle_ids);

DELETE FROM vehicletransfer.vehicle_transfer WHERE vehicle_id IN (SELECT id FROM test_vehicle_ids);

DELETE FROM vehicle.vehicle WHERE id IN (SELECT id FROM test_vehicle_ids);

DELETE FROM vehicle.color_info WHERE id IN (SELECT color_info_id FROM test_vehicle_ids WHERE color_info_id IS NOT NULL);
DELETE FROM vehicle.country_info WHERE id IN (SELECT country_info_id FROM test_vehicle_ids WHERE country_info_id IS NOT NULL);
DELETE FROM vehicle.delivery_info WHERE id IN (SELECT delivery_info_id FROM test_vehicle_ids WHERE delivery_info_id IS NOT NULL);
DELETE FROM vehicle.embargo_info WHERE id IN (SELECT embargo_info_id FROM test_vehicle_ids WHERE embargo_info_id IS NOT NULL);
DELETE FROM vehicle.consumption_info WHERE id IN (SELECT consumption_info_id FROM test_vehicle_ids WHERE consumption_info_id IS NOT NULL);
DELETE FROM vehicle.fleet_info WHERE id IN (SELECT fleet_info_id FROM test_vehicle_ids WHERE fleet_info_id IS NOT NULL);
DELETE FROM vehicle.model_info WHERE id IN (SELECT model_info_id FROM test_vehicle_ids WHERE model_info_id IS NOT NULL);
DELETE FROM vehicle.order_info WHERE id IN (SELECT order_info_id FROM test_vehicle_ids WHERE order_info_id IS NOT NULL);
DELETE FROM vehicle.price_info WHERE id IN (SELECT price_info_id FROM test_vehicle_ids WHERE price_info_id IS NOT NULL);
DELETE FROM vehicle.production_info WHERE id IN (SELECT production_info_id FROM test_vehicle_ids WHERE production_info_id IS NOT NULL);
DELETE FROM vehicle.return_info WHERE id IN (SELECT return_info_id FROM test_vehicle_ids WHERE return_info_id IS NOT NULL);
DELETE FROM vehicle.technical_info WHERE id IN (SELECT technical_info_id FROM test_vehicle_ids WHERE technical_info_id IS NOT NULL);
DELETE FROM vehicle.tire_set_change WHERE id IN (SELECT tire_set_change_id FROM test_vehicle_ids WHERE tire_set_change_id IS NOT NULL);

DROP TABLE test_vehicle_ids;
```
