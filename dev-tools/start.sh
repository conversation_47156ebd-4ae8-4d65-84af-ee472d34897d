export AWS_REGION=eu-west-1

export PVH_CLIENT_ID=f8223a51-ab32-42cb-9fc7-03261d6ba15e@apps_vw-dilab_com
export KAFKA_USER=SW42RJER6HGHBWY6
export PVH_CLIENT_SECRET=$(aws ssm get-parameters --name "/vehicle-service/external/pvh/oauth_m2m_client_secret" --with-decryption --query "Parameters[0].Value" --output text)
export KAFKA_PASSWORD=$(aws ssm get-parameters --name "/vehicle-service/external/streamzilla/kafka_password" --with-decryption --query "Parameters[0].Value" --output text)

export DMS_VEHICLE_MIGRATION_ENABLED=false
export VEHICLE_CAMPAIGNS_ENABLED=false
export DMSI_OAUTH2_CLIENT_ID=DEUP50050_OTHER
export DMSI_OAUTH2_CLIENT_SECRET=$(aws ssm get-parameters --name "/vehicle-service/secret/rwil-idp/dmsi_oauth2_client_secret" --with-decryption --query "Parameters[0].Value" --output text)
export RWIL_IDP_HOST=idp.rwil.qa.eu.bp.aws.cloud.vwgroup.com

export ENTRA_ID_OAUTH2_CLIENT_SECRET=$(aws ssm get-parameters --name "/vehicle-service/secret/azure-idp/entra_id_oauth2_client_secret" --with-decryption --query "Parameters[0].Value" --output text)

export SECURITY_ENABLED=false
export VEHICLE_LOCATION_SYNC_STRATEGY=disabled
export SECURITY_CORS_ENABLED=true
export SERVER_PORT=8081
export ENABLE_CARSYNC=false
./gradlew bootRun
