# Cloudwatch Queries for future reference 

## Response times 

### Slowest API calls

```
fields message, `mdc.x-trace-id`
| filter @message like 'RequestTimingFilter'
| parse @message  'took *ms to respond with' as responseTime
| sort by responseTime desc
```

### Percentile response time of a specific API call

```
fields message, `mdc.x-trace-id`
| filter @message like 'RequestTimingFilter'
| filter message  not like '/search'
| parse @message  'took *ms to respond with' as responseTime
| stats pct(responseTime, 99)
```

