# Commit Message Format

Our project requires a consistent, clear, and enforceable commit message standard to allow for:
- Change-Management at Porsche (Trace-ability of changes to JIRA tickets)
- Possible future Automation (e.g., changelogs, versioning)
    
## Commit Message Header Format

```shell
<type>[!][(scope)]: <description> [| FPT-1234]
```
| Element                                             | Description                                                                                                    |
|-----------------------------------------------------|----------------------------------------------------------------------------------------------------------------|
| **type**                                            | One of `feat`, `fix`                                                                           |
| **scope**                                           | Optional: For feat, use the JIRA ticket ID (with hyphen) or one of  (`refactor`, `ci`, `docs`, `lint`, `infra`) |
| **description**                                     | Concise summary of the change                                                                                  |

### Rationale
- A subset of Conventional Commits 1.0, so we can make use of existing tools for automated actions (semantic-versioning, changelog generation etc)
- Types can be used to signal version changes as per semantic versioning
- JIRA ticket integration improves traceability between commits and issue tracking

## Commit Body

**For bigger changes ensure the **why** of a certain code change is documented.** 

This will help future maintainers understand the thought-process, considerations and environment that had been there when the code change was made.

https://gist.github.com/finalfantasia/bd0070673ca27e5f7473 - this is where we want to be.

## Commit Authors

When pairing, use `Co-Authored-By` to indicate who paired with you on the commit.

https://gitlab.com/gitlab-org/gitlab-foss/-/issues/31640

## Enforcement 

This would be checked as a "push-rule" in Gitlab
https://docs.gitlab.com/user/project/repository/push_rules/

A regex for the commit-message format would be

```
^(Merge.*|Revert.*|#.*|(?:feat|fix|chore)\(((ci|docs|lint|infra|refactor)|[^()\s]+-[^()\s]+)\)!?: .+)$
```

### 🔧 Installation of local Git Hooks

> **Note:** Local Git hooks are and will not be **enforced** through tooling. It is up to the individual developer to install client-side hooks. 


Your local Git repository and your workflows are entirely under your control. We prefer not enforce the installation of Git hooks, and no hooks will be automatically added or required through build scripts.

To ensure that validations are **non-bypassable**, all essential checks—such as commit message formatting and code standards—are performed exclusively in the Continuous Integration (CI) pipeline and via server-enforced push rules. This approach guarantees prevents individual developers from inadvertently or intentionally skipping validations, and is actually the only place that these checks need to be made,

That said, you could install optional Git hooks (such as `pre-commit` or `pre-push`) if you’d like to:

- Validate commit message formatting
- Run linters, formatters, or tests automatically

so that you don't have to wait for the CI to check them.

#### Mac / Linux users 
To install hooks, use the Bash installer under `./dev-tools/install-pre-commit-hook.sh` (for pre-commit hook) or `./dev-tools/install-pre-push-hook.sh` (for pre-push hook)

#### For Windows users 
TODO
