# Configuration 

## Golden Rules 

1. **No configuration in the code.**  

## Levels of Configuration 

### Common Configuration 
1. All common configuration, that is not environment-specific goes into `application.yml` in the root directory

### Environment Specific Configuration 
1. To reduce the effect of cascading configurations, we decided to not use Spring Profile based configuration in our code. 
2. All environment based configuration is to be injected through Environment Variables.
3. The application.yml configuration will contain references to these Environment Variables 
4. The application.yml configuration can specify default values for these Environment Variable based properties, but they MUST be used only for the local-development environment.

Example: 
```yaml
spring:
  datasource:
    url: ${SPRING_DATASOURCE_URL:***********************************************}
    username: ${SPRING_DATASOURCE_USERNAME:application_user}
    password: ${SPRING_DATASOURCE_PASSWORD:Password1}
```
### Secrets 
1. Secrets are passed in through Environment Variables as shown in the example above.

### Feature-Toggling 
1. The project must be organized in a package-by-feature manner. 
2. Features can then be toggled on or off with the use of @Configuration classes and a @ConditionalOnProperty 
3. All feature flags configuration is specified in the `application.yml` file, under the `feature.flags` property at the root-level.
4. There should be no other places where the feature flag configuration is available 
5. Do NOT re-use feature-toggles. Always create new ones and delete un-used feature-toggles.
6. Feature-Toggles MUST not be long-lived. 

Example: 
```kotlin
@Configuration
@ConditionalOnProperty("feature-flags.kafka-enable-pvh-ingestion", havingValue = "true", matchIfMissing = false)
class KafkaConfiguration(
    private val config: KafkaConfigurationProperties,
    private val meterRegistry: MeterRegistry
) {
    // Kafka Bean Configuration
}
```
