# Database Integration 

- We only have one database for the entire application 
- Each application-module has it own database schema
- An application-module must NOT use the database-schema from another application module. **This is not enforced today through tooling, but will be in the near future.**
- **JPA Entities must not be a part of Module API** 
- Spring Data JPA is used for reading and writing data to the database 

## Reference Implementation 

- All `vehicledata` database logic is available at the module-level root package `vehicledata/repository`
