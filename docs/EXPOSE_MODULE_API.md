# Application Module API 

- Modules hide all internal implementation detail and expose data/functionality through a well-defined and well-thought-out interfacee.

## How to expose a Application Module API 

- Every Application Module should have an `api` package at it's package-root. 
- An `api` package will contain the necessary Kotlin Interfaces and DTO objects that make up it's Application Module API 

### Reference Implementation 
- `vehicle-data` Application Module

