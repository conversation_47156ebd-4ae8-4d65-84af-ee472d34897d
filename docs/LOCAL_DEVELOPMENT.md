# Local Development

## Prerequisites

### JDK 17 
You will need JDK 17 to build and run this project.

Recommend using https://sdkman.io/ to manage your versions.

```shell
curl -s "https://get.sdkman.io" | bash
sdk install java 17.0.8-amzn
```

### AWS CLI V2  
Check if aws-cli v2 is already installed with command `aws --version`. If not, install it from
here: https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html

### Docker Runtime
**If you are using [colima](https://github.com/abiosoft/colima) as your container runtime**, you need to additionally do the following steps to make use of test containers:

```shell
colima status
colima start
sudo ln -s $HOME/.colima/default/docker.sock /var/run/docker.sock
```

For other Docker runtimes (eg: with Docker-Desktop) you should ideally not have to do anything specific. 

If facing trouble with Test-Containers and colima is not your runtime, please refer to the documentation here: https://java.testcontainers.org/features/configuration/

### PostgreSQL DB
A `compose.yml` file is available with the project - this should spin up a new PostgreSQL container when the application starts. 

https://spring.io/blog/2023/06/21/docker-compose-support-in-spring-boot-3-1

The database container is available only during application runtime.

**Using an existing PostgreSQL installation**

If you would like to use your own PostgreSQL DB installation, you will have to do the initial setup manually.

Please refer to the `compose.yml` file for the database-names and the database initialization scripts under [here](../dev-tools/database/initdb/initial_database_setup.sql)

### Other tools (used in the README)

- HTTPie for making HTTP requests

You could use `curl` or another HTTP client if you prefer instead

- ktlint for code formatting
Install ktlint plugin in IntelliJ and enable it on Save. In case you don't have IntelliJ, you can install from [source](https://pinterest.github.io/ktlint/1.5.0/install/cli/)

## Build and Run

### Setup package-registry 
The project contains shared libraries that needs to be pulled in from GitLab and to pull these libraries locally, we need to have a PAT(personal access token) from Gitlab.
The below steps will ensure that the dependencies can be pulled properly:
 
Create *Personal Access Token* here: https://cicd.skyway.porsche.com/-/user_settings/personal_access_tokens  

Configure global gradle properties <user>\.gradle\gradle.properties  

```
  REGISTRY_TOKEN=<personal_access_token>
 ```

### Run application locally
```shell
./dev-tools/start.sh
```

Expected response:

```shell
Started VehicleApplication in 1.505 seconds (process running for 1.783)
```

Test if the application is running

```shell
http GET http://localhost:8081/api/vs/actuator/health
```

Expected response:

```shell
HTTP/1.1 200
Connection: keep-alive
Content-Type: application/vnd.spring-boot.actuator.v3+json
Date: Tue, 25 Jul 2023 11:19:11 GMT
Keep-Alive: timeout=60
Transfer-Encoding: chunked

{
    "status": "UP"
}
```

## Testing

To run all tests in the application

```
./gradlew test
```

## Running all the applications on your local machine 

There are three applications you will need to start up 

- Vehicle Service 
- Vehicle Registration Service 
- Fleet Management UI 

The following steps need to be automated so that manual changes are not required. 

### Vehicle Service
Please follow the instructions above under "Build and Run"

### Vehicle-Registration Service (optional)

In a separate tab (or) terminal window 

#### 1. Set Server Port 

```sh
  export SERVER_PORT=8082
```

#### 2. Enable Cross Origin Requests 

```sh
export SECURITY_CORS_ENABLED=true
```

#### 3. Run the Vehicle-Registration Service 
Please follow the instructions in the vehicle-registration repository. There should be a markdown file called LOCAL_DEVELOPMENT.md 

Ensure that the environment variables required for the application to run are in place before you run the application. The secrets don't matter if you are not going to make calls to remote systems - security is usually disabled when running our applications locally. 

```sh
export VS_CLIENT_SECRET=[logbook/m2m-oauth/client-secret]
export VS_SCOPE=1d612e2b-317e-4d59-80f5-f80b3e3a33c0/.default
export VS_CLIENT_ID=4093fa22-f5d7-462e-8722-c15a2f328d76
export VS_BASE_URL=http://localhost:8081/api/vs
export ENABLE_SECURITY=false
```

### Fleet Management UI 

Before starting the UI application, change the following configuration

In file `config.local.json`
```
"API_BASE_URL": "http://localhost:8081/api",
```

And then run the UI application 

```
    npm start 
```

### Quartz job configuration for Archival

The Quartz job configuration for the Archival service is designed to periodically trigger the archival of vehicle data. This ensures that data will be efficiently moved to s3 buckets and removed from operational database fulfilling the compliance and GoBD regulations

To enable this locally, simply set the following flag to true and add the M2M client credentials to connect to vehicle-registration service 

```sh
export ENABLE_ARCHIVAL_SCHEDULER=true
export VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_ID=d594f124-a777-41ca-b09b-9af3def7379a
export VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_SECRET=`/vehicle-service/secret/azure-idp/vehicle_registration_m2m_client_secret`
export VEHICLE_REGISTRATION_ARCHIVE_API_SCOPE=app://cfb99f74-19b0-4747-b96f-81989c59bb4a/.default
```
