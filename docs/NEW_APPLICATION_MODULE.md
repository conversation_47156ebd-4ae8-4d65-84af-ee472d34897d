# Application Modules 

An Application Module is an Kotlin package that encapsulates a specific aspect of Fleet-Vehicle-Mangement.

An Application Module must hide it's internal details and expose only what is necessary through a well-defined interface. It must have it's own data separated into it's own schema.

An Application Module, together with library-packages should be easily decomposable from this deployment unit into a separate service.

## How to create a new Application Module 

### Location
* An Application-Module is created under the `/src/main/kotlin/com/fleetmanagement/modules`


### Configuration
* An Application-Module `application.yaml` properties must be specified under a key at the root-level with the same name as that of the Application-Module. 


### Database Setup

#### Creating a new Schema 
* Update (./dev-tools/database/initdb/initial_database_setup.sql) to include new schema
* Create a schema  (use `./dev-tools/database/rerun-initial-database-setup.sh`) in your local development environment, and manually in the remote environment. 
* We use the same DB user for the time being for access to all schemas. There is no schema-level DB authentication - BUT - data-isolation between modules MUST be respected until tooling is introduced for this.

#### Code changes
* At the root of the Application-Module, setup a Liquibase DB Migration Bean. (Refer `vehicle-data` module)
* Create a database change-log file within the resources directory. Use the following format `db.changelog-<module_name>.yml`

### Package Structure

* For simple application modules, prefer a Package-By-Layer style. (Refer `legalhold` module)
* For application modules containing more that 2 distinct features, prefer a Package-By-Feature style. (Refer `vehicle-data` module)
