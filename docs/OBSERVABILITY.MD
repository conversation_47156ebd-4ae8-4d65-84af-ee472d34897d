# Open Telemetry Local Setup

This application sends telemetry data to an OpenTelemetry distribution using an ADOT (Amazon Distribution of OpenTelemetry) collector.
You can refer to the collector [here](https://cicd.skyway.porsche.com/FP20/fleet-management/adot).

## Running Locally with OpenTelemetry Tracing and Logs

1. **Download OpenTelemetry Java Agent JAR**:

   Download the `opentelemetry-javaagent.jar` from the Releases section of the [opentelemetry-java-instrumentation](https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest) repository.
   This JAR file contains the agent and all automatic instrumentation packages.

   ```bash
   curl -L -O https://github.com/open-telemetry/opentelemetry-java-instrumentation/releases/latest/download/opentelemetry-javaagent.jar
   ```

2. **Set Environment Variables**:

    ```shell
    export JAVA_TOOL_OPTIONS="-javaagent:PATH/TO/opentelemetry-javaagent.jar" \
      OTEL_TRACES_EXPORTER=logging \
      OTEL_METRICS_EXPORTER=none \
      OTEL_LOGS_EXPORTER=logging \
      OTEL_METRIC_EXPORT_INTERVAL=15000
    ```

   Replace PATH/TO/opentelemetry-javaagent.jar with the actual path to the downloaded opentelemetry-javaagent.jar file.

3. **Start the application**:

   ```shell
   ./gradlew bootRun
   ```

4. **Expected output**:

   The application will start and telemetry data will be sent to the OpenTelemetry distribution using the specified exporters. 
   The output will resemble the following:

   ```
   Picked up JAVA_TOOL_OPTIONS: -javaagent:opentelemetry-javaagent.jar
   OpenJDK 64-Bit Server VM warning: Sharing is only supported for boot loader classes because bootstrap classpath has been appended
   [otel.javaagent 2024-04-16 14:30:54:081 +0200] [main] INFO io.opentelemetry.javaagent.tooling.VersionLogger - opentelemetry-javaagent - version: 2.3.0
   ...
   ...
   ...
   
   [otel.javaagent 2024-04-16 14:32:28:960 +0200] [http-nio-8081-exec-1] INFO io.opentelemetry.exporter.logging.LoggingSpanExporter - 'read.vehicle' : 14e5c05b99a61e4f82707aa979588340 b382bf0e0c821559 
   INTERNAL [tracer: io.opentelemetry.opentelemetry-instrumentation-annotations-1.16:2.3.0-alpha] 
   AttributesMap{data={thread.name=http-nio-8081-exec-1, vin=WP1ZZZ95ZNLB02704, code.function=byVinNumber, thread.id=36, code.namespace=com.fleetmanagement.modules.vehicledata.internal.controller.VehicleReadController}, capacity=128, totalAddedValues=5}
   ...
   ...
   ```
## Example Usage 

Annotate methods that require a custom span in the following format 

```kotlin
    @WithSpan("read.vehicle")
    @GetMapping("/vehicleData")
    fun byVinNumber(
        @SpanAttribute("vin") @RequestHeader(name = "FP20-Vin-Number", required = true) vin: String
    ): ResponseEntity<VehicleFullRepresentation> {
        //code
    }
```

Please refer to https://opentelemetry.io/docs/zero-code/java/agent/annotations/ for more details

