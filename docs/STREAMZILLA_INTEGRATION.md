# Streamzilla (Kafka) Integration 

- The application has integration with the Streamzilla Platform - using a pre-configured Streamzilla credentials

## To consume messages from a Topic 

### Reference Implementation
-  `vehicledata/integrations/pvh/streamzilla` 

### Tasks
- Copy over class containing the `@KafkaListener` from the `vehicledata` module to your own
- Check if you might require a Kafka Filter - the `vehicledata` module has a vehicle-filter. If you do not need this, please omit this for your use-case.

