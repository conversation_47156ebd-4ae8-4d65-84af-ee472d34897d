# Testing Strategy 

## General Principles 

- If the tests pass, then the functionality should not break in production
- Don't test for the sake of a coverage metric[^1]
- Test-Driven-Development is not only encouraged, but required
- Prefer Classicist styles of testing, over the Mockist style - within a single architectural layer[^2]

## Levels of testing 

The project at the moment only has two levels of testing 
- **Controller Tests** to test exposed API 
- **API Client Tests** to test web-clients making calls to remote systems

### Testing the Controllers
1) Controllers are tested using an `@WebMVCTest`. 
2) We should make calls to the API endpoint under test using plain JSON strings, never objects. This ensures that the `ObjectMapper` settings in the project are as expected.
3) Security features can be disabled in Controller Tests 


### Testing API Clients
Since mocking the framework is considered a bad idea[^3] we chose to use Wiremock to test all of our API clients. 
This allows us flexibility to change the API client library in the future and the tests can serve as a safety net.

Considered options 
1) Wiremock 
2) MockServer 

Wiremock was chosen over MockServer due to 

1) it's style of expectation matching 
2) and it's descriptive error-message when expectations are not met. 

### Contract Testing 
Our APIs are not versioned, since 

1) We only have consumers within EMH (the Agile Release Train) 
2) Since all of our consumers are known, we would like to maintain contract-tests with our consumers 
3) This enables our team to change API response-formats with full confidence that none of our consumers are affected
4) We would like our API compatibility to be verified through tests that block a breaking-change to production, rather than relying on convention

All pact verification tests are found in the test `VehicleReadPactVerificationTest`

We do not use a Pact Broker, instead, the contracts are checked in directly to our repository.

## References 
[^1]: https://itnext.io/code-coverage-the-metric-that-makes-your-tests-worse-c1dddcc0831
[^2]: https://martinfowler.com/articles/mocksArentStubs.html#ClassicalAndMockistTesting
[^3]: https://testing.googleblog.com/2020/07/testing-on-toilet-dont-mock-types-you.html