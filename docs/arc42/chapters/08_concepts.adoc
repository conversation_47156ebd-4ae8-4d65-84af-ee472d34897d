:jbake-title: Cross-cutting Concepts
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 8
:filename: /chapters/08_concepts.adoc
ifndef::imagesdir[:imagesdir: ../../images]

:toc:



[[section-concepts]]
== Cross-cutting Concepts


ifdef::arc42help[]
[role="arc42help"]
****
.Content
This section describes overall, principal regulations and solution ideas that are relevant in multiple parts (= cross-cutting) of your system.
Such concepts are often related to multiple building blocks.
They can include many different topics, such as

* models, especially domain models
* architecture or design patterns
* rules for using specific technology
* principal, often technical decisions of an overarching (= cross-cutting) nature
* implementation rules


.Motivation
Concepts form the basis for _conceptual integrity_ (consistency, homogeneity) of the architecture. 
Thus, they are an important contribution to achieve inner qualities of your system.

Some of these concepts cannot be assigned to individual building blocks, e.g. security or safety. 


.Form
The form can be varied:

* concept papers with any kind of structure
* cross-cutting model excerpts or scenarios using notations of the architecture views
* sample implementations, especially for technical concepts
* reference to typical usage of standard frameworks (e.g. using Hibernate for object/relational mapping)

.Structure
A potential (but not mandatory) structure for this section could be:

* Domain concepts
* User Experience concepts (UX)
* Safety and security concepts
* Architecture and design patterns
* "Under-the-hood"
* development concepts
* operational concepts

Note: it might be difficult to assign individual concepts to one specific topic
on this list.

image::08-concepts-EN.drawio.png["Possible topics for crosscutting concepts"]


.Further Information

See https://docs.arc42.org/section-8/[Concepts] in the arc42 documentation.
****
endif::arc42help[]

=== _<Concept 1>_

_<explanation>_



=== _<Concept 2>_

_<explanation>_

...

=== _<Concept n>_

_<explanation>_
