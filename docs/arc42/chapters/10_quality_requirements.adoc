:jbake-title: Quality Requirements
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 10
:filename: /chapters/10_quality_requirements.adoc
ifndef::imagesdir[:imagesdir: ../../images]

:toc:



[[section-quality-scenarios]]
== Quality Requirements

=== Guidelines for Ignoring SonarQube Rules

This document provides guidance on when it is appropriate to ignore SonarQube rules while ensuring code quality and maintainability.

==== General Guidelines
Before ignoring any SonarQube rule, consider the following:

✅ If the rule conflicts with existing coding standards or project requirements.

✅ If it produces frequent false positives in the specific codebase.

✅ If ignoring it will not introduce long-term technical debt.

❌ Do not ignore rules that impact security, stability, or maintainability without strong justification.

For official guidance, refer to the SonarQube documentation:

link:++https://docs.sonarsource.com/sonarqube-server/10.7/core-concepts/clean-code/introduction/++[SonarQube Documentation]

link:++https://rules.sonarsource.com/kotlin/++[Rules Documentation]

link:++https://skyway.porsche.com/sonarqube/coding_rules?selected=css%3AS4655 ++[Porsche Sonar Rules]

Clean Code leads to software that is secure, reliable, and maintainable. These three aspects - security, reliability, and maintainability - are called software qualities in the Sonar Solution, and they contribute to the long-term value of your software

==== 1. Reliability (Bugs & Code Smells)
Reliability rules help prevent potential runtime errors. Some can be overly strict or produce false positives.

===== Can Be Ignored:

link:++https://skyway.porsche.com/sonarqube/coding_rules?activation=true&impactSoftwareQualities=RELIABILITY&qprofile=AXCFzFiADFahUi3zZdj1&open=kotlin%3AS1206 ++[equals(Any?)" and "hashCode()" should be overridden in pairs] - Kotlin’s data class automatically generates equals() and hashCode() methods, so no manual override is needed

==== 2. Maintainability (Code Smells & Complexity)
Maintainability refers to the ease with which you can repair, improve and understand software code. Some are opinionated and can be ignored in specific cases.

===== Can Be Ignored:

Examples could be:

link:++https://skyway.porsche.com/sonarqube/coding_rules?activation=true&qprofile=AXCFzFiADFahUi3zZdj1&open=kotlin%3AS104 ++[Files should not have too many lines of code (kotlin:S104)] – If the file is a DTO or a configuration-heavy class.

link:++https://skyway.porsche.com/sonarqube/coding_rules?activation=true&qprofile=AXCFzFiADFahUi3zZdj1&open=kotlin%3AS107 ++[Functions should not have too many parameters (kotlin:S107)] – If the parameters logically belong together (e.g., constructors for DTOs).

link:++https://skyway.porsche.com/sonarqube/coding_rules?activation=true&qprofile=AXCFzFiADFahUi3zZdj1&open=kotlin%3AS1192 ++[Duplicated strings (kotlin:S1192)] – If the duplication improves readability and maintainability (e.g., SQL queries, JSON keys).

==== 3. Security (Vulnerabilities)
Security is the protection of your software from unauthorized access, use, or destruction. Security-related rules prevent potential exploits. These should generally NOT be ignored unless there is a very strong justification.

===== Can Be Ignored (Rarely, With Justification):

Examples could be:

link:++https://skyway.porsche.com/sonarqube/coding_rules?activation=true&qprofile=AXCFzFiADFahUi3zZdj1&open=kotlin%3AS2068[Hardcoded credentials] (kotlin:S2068) ++[Hardcoded credentials (kotlin:S2068)] – If the credentials are test data, which are destroyed after the test execution and is not used in production.

link:++https://skyway.porsche.com/sonarqube/coding_rules?activation=true&qprofile=AXCFzFiADFahUi3zZdj1&open=kotlin%3AS5542 ++[Use of weak hashing algorithms (kotlin:S5542)] – If the weak algorithm is necessary for backward compatibility.

===== Should NOT Be Ignored:

Some examples could be:

SQL Injection - Ignoring this can lead to severe security vulnerabilities.

Unvalidated user input – Can expose the application to attacks like XSS or command injection.

==== Final Recommendations

Document ignored rules and the reason for ignoring them.

Re-evaluate ignored rules periodically.

Apply rule suppressions (@SuppressWarnings or sonar.issue.ignore.multicriteria) sparingly to avoid missing critical issues.

Some ignored rules are currently added link:++https://cicd.skyway.porsche.com/FP20/fleet-management/vehicle-service/-/blob/main/sonar-project.properties?ref_type=heads#L45 ++[here]

ifdef::arc42help[]
[role="arc42help"]
****

.Content
This section contains all quality requirements as quality tree with scenarios. The most important ones have already been described in section 1.2. (quality goals)

Here you can also capture quality requirements with lesser priority,
which will not create high risks when they are not fully achieved.

.Motivation
Since quality requirements will have a lot of influence on architectural
decisions you should know for every stakeholder what is really important to them,
concrete and measurable.


.Further Information

See https://docs.arc42.org/section-10/[Quality Requirements] in the arc42 documentation.

****
endif::arc42help[]

=== Quality Tree

ifdef::arc42help[]
[role="arc42help"]
****
.Content
The quality tree (as defined in ATAM – Architecture Tradeoff Analysis Method) with quality/evaluation scenarios as leafs.

.Motivation
The tree structure with priorities provides an overview for a sometimes large number of quality requirements.

.Form
The quality tree is a high-level overview of the quality goals and requirements:

* tree-like refinement of the term "quality". Use "quality" or "usefulness" as a root
* a mind map with quality categories as main branches

In any case the tree should include links to the scenarios of the following section.


****
endif::arc42help[]

=== Quality Scenarios

ifdef::arc42help[]
[role="arc42help"]
****
.Contents
Concretization of (sometimes vague or implicit) quality requirements using (quality) scenarios.

These scenarios describe what should happen when a stimulus arrives at the system.

For architects, two kinds of scenarios are important:

* Usage scenarios (also called application scenarios or use case scenarios) describe the system’s runtime reaction to a certain stimulus. This also includes scenarios that describe the system’s efficiency or performance. Example: The system reacts to a user’s request within one second.
* Change scenarios describe a modification of the system or of its immediate environment. Example: Additional functionality is implemented or requirements for a quality attribute change.

.Motivation
Scenarios make quality requirements concrete and allow to
more easily measure or decide whether they are fulfilled.

Especially when you want to assess your architecture using methods like
ATAM you need to describe your quality goals (from section 1.2)
more precisely down to a level of scenarios that can be discussed and evaluated.

.Form
Tabular or free form text.
****
endif::arc42help[]
