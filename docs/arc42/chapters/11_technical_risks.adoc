:jbake-title: Risks and Technical Debts
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 11
:filename: /chapters/11_technical_risks.adoc
ifndef::imagesdir[:imagesdir: ../../images]

:toc:



[[section-technical-risks]]
== Risks and Technical Debts


ifdef::arc42help[]
[role="arc42help"]
****
.Contents
A list of identified technical risks or technical debts, ordered by priority

.Motivation
“Risk management is project management for grown-ups” (<PERSON>, Atlantic Systems Guild.) 

This should be your motto for systematic detection and evaluation of risks and technical debts in the architecture, which will be needed by management stakeholders (e.g. project managers, product owners) as part of the overall risk analysis and measurement planning.

.Form
List of risks and/or technical debts, probably including suggested measures to minimize, mitigate or avoid risks or reduce technical debts.


.Further Information

See https://docs.arc42.org/section-11/[Risks and Technical Debt] in the arc42 documentation.

****
endif::arc42help[]
