:jbake-title: Glossary
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 12
:filename: /chapters/12_glossary.adoc
ifndef::imagesdir[:imagesdir: ../../images]

:toc:



[[section-glossary]]
== Glossary

ifdef::arc42help[]
[role="arc42help"]
****
.Contents
The most important domain and technical terms that your stakeholders use when discussing the system.

You can also see the glossary as source for translations if you work in multi-language teams.

.Motivation
You should clearly define your terms, so that all stakeholders

* have an identical understanding of these terms
* do not use synonyms and homonyms


.Form

A table with columns <Term> and <Definition>.

Potentially more columns in case you need translations.


.Further Information

See https://docs.arc42.org/section-12/[Glossary] in the arc42 documentation.

****
endif::arc42help[]

[cols="e,2e" options="header"]
|===
|Term |Definition

|<Term-1>
|<definition-1>

|<Term-2>
|<definition-2>
|===
