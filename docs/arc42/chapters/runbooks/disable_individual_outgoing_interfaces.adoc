ifndef::projectRoot[:projectRoot: ../../../..]
ifndef::sandboxtfvars[:sandboxtfvars: terraform/envs/sandbox.tfvars]
ifndef::sandboxtfvarsPath[:sandboxtfvarsPath: {projectRoot}/terraform/envs/sandbox.tfvars]
ifndef::adr0040[:adr0040: https://skyway.porsche.com/confluence/spaces/FP20/pages/1845446283/EMH+ADR-0040+Disable+outgoing+interfaces]

:toc:
:numbered:
= Disabling Individual Outgoing Interfaces

== Introduction

This is a runbook on how to enable and disable individual outgoing interfaces for the FVM. By  temporarily disabling an
 interface, it is possible to disconnect from downstream systems. The intent of temporarily disabling an interface is to
prevent defects and data inconsistency across systems in case of misbehavior of the FVM.

This runbook also provides instruction on how to update downstream systems for missed updates during isolation.

== Purpose of Temporarily Disabling Outgoing Interfaces

It is expected that a misbehavior of the software (FVM) might be introduced, providing wrong data to downstream systems
that create defects and data inconsistencies across multiple systems that are hard to correct. To prevent the spread of
inconsistencies, it should be possible to switch off interfaces temporarily, until the misbehavior is fixed.

When the misbehavior is fixed, it might be necessary to provide downstream systems with updates that were missed during
isolation.

== How Disabling Individual Outgoing Interfaces Works

FVM supports multiple communication patterns and technical implementations to communicate with downstream systems. A
list of these patterns and the associated systems can be found the underlying ADR link:{adr0040}["EMH ADR-0040 Disable outgoing interfaces"].

The different channels of communication are:

• via event published to Kafka (Streamzilla)
• vie Email
• via calling APIs of downstream systems

To prevent communication via these channels, different approaches are used to disable them. These depend on the technical
implementation, whether it is initialized synchronously on a domain event, asynchronously via a CRON job or other
factors. For disabling, there are the following strategies:

• publish events to an isolated Kafka topic
• send Email to a test user instead of the real recipient
• disable CRON jobs that call external systems via API
• use feature flags when available


To apply these strategies, individually a corresponding variable in a tfvars file needs to be changed. When applied this
overwrites environment variables:

• Kafka topics are replaced by topics that are not consumed by external systems
• CRONs are disabled
• Email recipients are changed to dummy recipients
• feature flags are toggled

== Disable an Outgoing Interface Individually

The best way to individually disable interfaces is, to comment out everything in the
link:{sandboxtfvarsPath}[{sandboxtfvars}],
except the interface(s) that need to be disabled, and then
link:sandbox_mode_to_disable_all_outgoing_interfaces.adoc#runbook-sanboxmode-enable[enable Sandbox Mode]
for the corresponding environment.

The change of the link:{sandboxtfvarsPath}[{sandboxtfvars}] file and .gitlab-ci.yml (see Runbook Sandbox Mode) file needs to be
committed and pushed to main. Deployment to the corresponding environment needs to be triggered.

The following table lists all individual interfaces, their channels of communication, how to set the tfvar to disable it:
[cols="2,1,2,8,4", options="header"]
|===
|Interface|Channel of communication|Strategy to disable/enable| Variables to edit|How to update downstream systems after isolation?
| Archiving / GoBD Jobs |  API | Disable CRON and toggle feature flag  | vehicle_archive_cron_schedule = "0 0 0 1 1 ? 2099" +
vehicle_archive_enable_scheduler = "false"  | No action needed. Archiving will take place when CRON is reactivated

| Cost Center update | API | Disable CRON | cost_center_update_cron_schedule = "0 0 0 1 1 ? 2099" +
cost_center_update_preparation_cron_schedule = "0 0 0 1 1 ? 2099" |

|PDI Ordering Email|Email|Disable CRON that sends emails regularly and set dummy addresses for the recipients | pdi_ordering_email_schedule = "0 0 0 1 1 ? 2099" +
pdi_ordering_recipient_to_email_address = "" +
pdi_ordering_recipient_cc_email_address = "" +
pdi_ordering_sender_email_address       = "" | If email is not sent for a day it has to be created and send manaully.

| MS Bookings | API | Disable CRON and toggle feature flag | ms_booking_appointments_job_schedule = "0 0 0 1 1 ? 2099" +
msbooking_enable = false | No action needed. The appointments will be synced back when CRON is reactivated.

| Order Number Updatea | API | Disable CRON | maintenance_order_number_update_schedule = "0 0 0 1 1 ? 2099" +
factory_car_preparation_order_number_update_schedule = "0 0 0 1 1 ? 2099" |  No action needed. Order Numbers are synced, when CRON is reactivated

| Vehicle Master Data | Kafka Topic | Disable CRON (feature flag and dummy Kafka topic) | fleet_master_data_cron_schedule = "0 0 0 1 1 ? 2099" +
[line-through]#enable_fleet_master_data = false# +
[line-through]#fleet_master_data_topic = "FRA_emhs_fleet_master_vehicle_dev"#| No action needed as long as flag and dummy
Kafka were not used. Master data relies on an outbox pattern, so that all updates are published on reactivation of the CRON

| Tire Management Email | Email | Disable CRON, feature flag and use dummy recipients | tire_management_email_recipient  = ""
tire_management_email_sender = "<EMAIL>" +
tire_management_enabled = false
tire_management_data_export_cron = "0 0 0 1 1 ? 2099"  | Needs to be sent manually.
| TÜV Notification | Email | Use dummy email | tuv_team_recipient_to_email_address = "" +
tuv_team_sender_email_address = "" +
logistics_team_recipient_to_email_address = "" +
logistics_team_sender_email_address = ""  | Needs to be sent manually.
| Balance sheet PACE integration | API | Disable CRONS | synchronize_scrapping_status_update_cron = "0 0 0 1 1 ? 2099"  +
synchronize_blocked_for_sale_update_cron = "0 0 0 1 1 ? 2099" +
prepare_blocked_for_sale_update_cron = "0 0 0 1 1 ? 2099" |  No action needed. Staus are synced, when CRONs are reactivated
| ECC Interface | API provided by FVM | Feature flag | ecc_allow_incoming_traffic | ECC consumer needs to consume the data again
|===


== Verifying Individual Outgoing Interface is Disabled

To verify that an individual interface is disabled (or enable) the following steps are required:

1. login to AWS for the corresponding environment (mobility-services-dev, mobility-services-staging or mobility-services-prod)
2. go to Elastic Container Service
3. select emh-fleet-management cluster
4. select vehicle-service
5. select the Tasks tab
6. click on Task definition for a Running and Healthy task
7. click on the Container name vehicle-service
8. check the Container details if they match the values provided by the link:{sandboxtfvarsPath}[{sandboxtfvars}]

== Enabling an Outgoing Interface and how to Update Downstream Systems


The following applies, if Sandbox Mode is used with uncommented interfaces in the link:{sandboxtfvarsPath}[{sandboxtfvars}] file.


=== Only one Interface is Disabled

If only one interface is disabled, link:sandbox_mode_to_disable_all_outgoing_interfaces.adoc#runbook-sanboxmode-disable[disable Sandbox Mode].

=== Multiple Interfaces are Disabled

Comment the interface that needs to be re-enabled in the link:{sandboxtfvarsPath}[{sandboxtfvars}].

The change of the link:{sandboxtfvarsPath}[{sandboxtfvars}] file needs to be committed and pushed to main. Deployment to the corresponding environment needs to be triggered.


