:toc:
:numbered:

= Rollback for production deployment

== Introduction

This runbook describes two ways on how to roll back a production deployment. The first is to rollback by triggering a
deployment of a specific version. The second is to roll back by updating the ECS task on AWS and using an older docker image.

== Purpose of Rollback Production Deployment

A rollback becomes necessary when a new deployment leads to critical issues in the production environment.
The primary goal of a rollback is to quickly restore a stable and functional state of the application to minimize the
negative impact on users and business operations.

Common reasons for a rollback include:

* *Critical Bugs:* A new feature causes severe errors that impair the core functionality of the application.
* *Performance Issues:* The deployment leads to unacceptably high load times, excessive resource consumption
(CPU, memory), or a general slowdown of the system.
* *High Error Rate:* Monitoring systems show a significant increase in application errors (e.g., HTTP 5xx errors) after the deployment.
* *Failed Health Checks:* The new tasks are flagged as "unhealthy" by the AWS Load Balancer health checks and cannot accept traffic.
* *Security Vulnerabilities:* A newly introduced vulnerability is discovered that must be addressed immediately.

== How Rollback Works

As mentioned in the introduction, there are two primary methods. The first method is always preferred as it is traceable
and follows the standard deployment process.

=== Method 1: Rollback by Redeploying an Older Version (Preferred Method)

This method utilizes our CI/CD pipeline to redeploy a known, stable version of the software.


*Procedure:*

1. *Identify the last stable version:* Find the version number of the last successful deployment. This information can
be found in the CI/CD pipeline's link:https://cicd.skyway.porsche.com/FP20/fleet-management/vehicle-service/-/releases[Releases]
page.
2. *Verify that the rollback is possible:* Check that the difference between the current version and the targeted
rollback version is only a minor or patch version.
3. *Manually trigger the deployment pipeline:* Start the production deployment pipeline and provide the identified
stable version number as a parameter:
.. Goto link:https://cicd.skyway.porsche.com/FP20/fleet-management/vehicle-service/-/pipelines[pipelines] and click
_New Pipeline_
.. Select the targeted version in _Run for branch name or tag_
.. Run deployment by clicking _New Pipeline_

=== Method 2: Manual Rollback via AWS Console (Emergency Method)
This method should only be used if an extremely fast intervention is required.

*Procedure:*

1. *Navigate to the AWS ECS Console:* Log in to the AWS Management Console and open the Elastic Container Service (ECS).
2. *Select the service:* Choose the cluster and then the service you want to roll back.
3. *Identify the Task Definition:* In the "Deployments" or "Tasks" tab, you will see the currently running task
definition (e.g., vehicle-service:3). ECS versions task definitions with a new revision number for each change.
The previous revision (e.g., vehicle-service:2) is typically the stable version.

WARNING: Be sure that the latest deployment did not introduce breaking changes. The difference of the current version and
the targeted rollback version is only a minor or patch.

[start=4]
. *Update the service:*
* Click _Update Service_
* Under _Task Definition Revision_ select the previous, stable revision number (e.g., vehicle-service:2).
* Ensure the _Force new deployment_ option is checked.
* Accept all other settings and complete the update.
. *Monitor the deployment:* AWS ECS will now initiate a rolling update to the selected older task definition.

== Verifying Rollback Worked

After initiating the rollback, its success must be carefully verified.

*Check Deployment Status in ECS:*

* In the AWS ECS Console, go to the service and monitor the "Deployments" tab.
* A new deployment should have started (PRIMARY). The old, faulty deployment should be ACTIVE, but its Running count
should be decreasing to 0, while the new deployment's count increases.
* Once complete, only one deployment should be listed as PRIMARY with the correct running count.

*Check Logs:*

* Open the logs (e.g., in CloudWatch Logs) of the newly started tasks.
* Ensure the application starts successfully and does not log any of the errors that triggered the rollback.

*Monitor Dashboards:*

* Observe the relevant metrics in your monitoring tool (e.g., [Name of Monitoring Tool]).
* The error rate should drop to its normal level.

*Functional Tests:*

* Perform manual tests of core functionalities, especially the area that was affected by the bug.
* Confirm that the application is working as expected for end-users again.

== Important Considerations and Limitations

* *Database Migrations:* This is the most critical point. If the faulty deployment included a non-backward-compatible
database schema change, a code-only rollback will not fix the problem and can lead to massive data corruption.
Database rollbacks must be planned and executed separately and are extremely risky. Always check if a schema change was
part of the deployment before initiating a rollback!
* *Drift from Manual Changes:* If you use Method 2 (manual rollback), the state in AWS will differ from your
infrastructure-as-code (Terraform, CloudFormation, etc.). The code must be reverted to the old state and applied;
otherwise, the next regular deployment will re-introduce the faulty state.
* *"Roll Forward" as an Alternative:* Sometimes it is safer and faster to develop a hotfix for the bug and deploy a
new version instead of rolling back. This is often the better strategy, especially if database changes are involved.
Always weigh the options between "Rollback" and "Roll Forward."
