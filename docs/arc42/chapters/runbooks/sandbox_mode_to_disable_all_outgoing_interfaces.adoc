ifndef::projectRoot[:projectRoot: ../../../..]
ifndef::sandboxtfvars[:sandboxtfvars: terraform/envs/sandbox.tfvars]
ifndef::sandboxtfvarsPath[:sandboxtfvarsPath: {projectRoot}/terraform/envs/sandbox.tfvars]
ifndef::adr0040[:adr0040: https://skyway.porsche.com/confluence/spaces/FP20/pages/1845446283/EMH+ADR-0040+Disable+outgoing+interfaces]

:toc:
:numbered:
= Sandbox Mode

== Introduction
This is a runbook on how to enable and disable Sandbox Mode for the FVM. With Sandbox Mode it is possible to disconnect
from all downstream systems. The intent of Sandbox Mode is to prevent defects and data inconsistency across systems.

== Purpose of Sandbox Mode
The data available on the development and staging environment is not sufficient to test the FVM application to a degree
that provides enough confidence for a go live. FVM has to be tested in the production environment using real data and
being connected to real external (upstream)systems. Until the correct behavior of FVM has been validated, FVM may
consume data from other systems, but it must not provide data (including messages and events) to them. Also, FVM might
be loaded multiple times with fresh data. This process must not affect neighbor systems. The staging environment cannot
be used as there it has no approval for productive data (especially with users) and because the external systems cannot
be switched to our staging environment.

== How Sandbox Mode Works

FVM supports multiple communication patterns and technical implementations to communicate with downstream systems. A
list of these patterns and the associated systems can be found the underlying ADR
link:{adr0040}["EMH ADR-0040 Disable outgoing interfaces"].

The different channels of communication are:

• via event published to Kafka (Streamzilla)
• vie Email
• via calling APIs of downstream systems

To prevent communication via these channels, different approaches are used to disable them. These depend on the
technical implementation, whether it is initialized synchronously on a domain event, asynchronously via a CRON job or
other factors. For disabling, there are the following strategies:

• publish events to an isolated Kafka topic
• send Email to a test user instead of the real recipient
• disable CRON jobs that call external systems via API
• use feature flags when available


To apply these strategies, a special tfvars file is available (link:{sandboxtfvarsPath}[{sandboxtfvars}]). When applied
this overwrites environment variables:

• Kafka topics are replaced by topics that are not consumed by external systems
• CRONs are disabled
• Email recipients are changed to dummy recipients
• feature flags are toggled


== Prerequisites for Enabling Sandbox Mode

Sandbox Mode is intended to be enabled before go-live. Therefore, possible side effects on production data, when
enabling it after go-live, are not considered.

Disabling outgoing interface post go-live is considered individually (see Runbook
link:disable_individual_outgoing_interfaces.adoc[Disabling Individual Outgoing Interfaces]).


[[runbook-sanboxmode-enable]]
== Enable Sandbox Mode

Sandbox Mode can be enabled for all environments (dev, staging and prod) in
link:{projectRoot}/.gitlab-ci.yml[.gitlab-ci.yml] file by editing the corresponding step

* link:{projectRoot}/.gitlab-ci.yml[terraform-dev]
* link:{projectRoot}/.gitlab-ci.yml[terraform-staging]
* link:{projectRoot}/.gitlab-ci.yml[terraform-prod]

To enable Sandbox Mode the variable SANDBOX_MODE needs to be set to true.

[source,yaml]
----
terraform-prod: # [[terraform-prod]]
  # ...
  variables:
    # ...
    SANDBOX_MODE: true

----

The change of the .gitlab-ci.yml file needs to be committed and pushed to main. Deployment to the corresponding
environment needs to be triggered.

== Verifying Sandbox Mode is Active

To verify that Sandbox Mode is active (or inactive) the following steps are required:

1. login to AWS for the corresponding environment (mobility-services-dev, mobility-services-staging or
mobility-services-prod)
2. go to Elastic Container Service
3. select emh-fleet-management cluster
4. select vehicle-service
5. select the Tasks tab
6. click on Task definition for a Running and Healthy task
7. click on the Container name vehicle-service
8. check the Container details, if they match the values provided by the terraform/envs/sandbox.tfvars



[[runbook-sanboxmode-disable]]
== Disable Sandbox Mode

To disable Sandbox Mode for an environment, the corresponding step in the .gitlab-ci.yml file needs to be edited:

* link:{projectRoot}/.gitlab-ci.yml[terraform-dev]
* link:{projectRoot}/.gitlab-ci.yml[terraform-staging]
* link:{projectRoot}/.gitlab-ci.yml[terraform-prod]

To disable Sandbox Mode the variable SANDBOX_MODE needs to be set to false.

[source,yaml]
----
terraform-prod:
  # ...
  variables:
    # ...
    SANDBOX_MODE: false

----

The change of the .gitlab-ci.yml file needs to be committed and pushed to main. Deployment to the corresponding
environment needs to be triggered.

== Important Considerations and Limitations

The Sandbox Mode only disables outgoing interfaces to downstream systems and is not total isolation of the FVM. Data
from upstream systems still get into the FVM.

The Sandbox Mode only disables outgoing interfaces from the FVM (vehicle-service), not from others like the
VLS (vehicle-location-service) or the DMS (damage-management-service).


