ifndef::projectRoot[:projectRoot: ../../../..]
:toc:
:numbered:
= Snapshot of Production Data and Anonymization for Testing Purposes

== Introduction

This runbook provides an operating procedure for creating an anonymized copy of the production database for use in
development or staging environments. The process involves taking a snapshot of the production database, restoring it to
an isolated temporary environment for data anonymization, and then making a final, clean snapshot available to
non-production environments.

Following this procedure ensures that developers and testers can work with realistic, production-scale data without
exposing sensitive user information, thus upholding privacy and compliance standards.

== Purpose and Justification

Testing applications with data that accurately reflects the volume, complexity, and distribution of production data is
critical for identifying performance bottlenecks, bugs, and edge cases before deployment. However, using raw production
data in non-production environments is strictly forbidden.

This process bridges that gap by providing a safe, anonymized dataset that is structurally and referentially
consistent with production data.

== Prerequisites

* *IAM Permissions:* The user or role performing these actions requires appropriate permissions for AWS RDS (creating,
restoring, deleting snapshots and instances)
* *Same Version Deployed:*  The environment receiving the snapshot (dev or staging) should ideally run the same
application version (Git commit hash) as the production environment. This helps prevent database schema mismatches when
the snapshot is restored. While migration scripts can often resolve discrepancies, aligning versions is the best practice.
* *Sandbox Mode Enabled:* The sandbox mode should be enabled to prevent unintended interactions with downstream systems
(see link:sandbox_mode_to_disable_all_outgoing_interfaces.adoc[Sanbox Mode Runbook]).
* *Anonymization Scripts:* Anonymization has to be applied. There is a set of  pre-written, tested, and
 SQL scripts. These scripts are specific to the database schema and are responsible for masking, deleting, or replacing
sensitive data fields. The scripts are located in `dev-tools/database/anonimyzedPII/`. The following scripts are available:

* link:{projectRoot}/dev-tools/database/anonimyzedPII/user_service.sql[user_service.sql]
* link:{projectRoot}/dev-tools/database/anonimyzedPII/vehicle_registraion.sql[vehicle_registraion.sql]
* link:{projectRoot}/dev-tools/database/anonimyzedPII/vehicle_service.sql[vehicle_service.sql]


== Process Overview

The end-to-end process is broken down into six main stages:

1. *Create Production Snapshot (optional):* Take a manual snapshot of the source production database cluster. An existing
snapshot can be used instead.
2. *Restore to Temporary Instance:* Restore the snapshot to a new, isolated temporary database instance. This instance
is where the anonymization will occur. Never run anonymization scripts on the production database.
3. *Anonymize Data:* Connect to the temporary instance and execute scripts to anonymize all sensitive data.
4. *Create Anonymized Snapshot:* Once anonymization is complete and verified, create a final snapshot of the temporary
database instance. This is the "golden" anonymized snapshot.
5. *Share Snapshot:* Since dev and staging are using different AWS accounts, share the anonymized snapshot with
that account.
6. *Restore in Target Environment:* Restore the anonymized snapshot on the destination (e.g., staging or development)
environment.
7. *Update Database Credentials on the Applications:* The database still uses its initial users and credentials (of the source system).
These need to be applied on the target environment.


== Step-by-Step Procedures

=== Create a Snapshot of the Production Database

Using AWS Management Console:

1. Navigate to the RDS service in the AWS Console.
2. In the navigation pane, select Databases and choose the production database instance or cluster.
3. Click the Actions button and select Take snapshot.
4. Enter a descriptive Snapshot name (e.g., prod-db-yyyymmdd-before-anonymization) and select snapshot type "DB cluster"
5. Click Take snapshot.

=== Restore Snapshot to a Temporary Instance

1. Navigate to RDS > Snapshots.
2. Select the snapshot created in the previous step.
3. Click Actions > Restore snapshot.
4. Configure the temporary instance:
    * DB instance identifier: Give it a clear name, like _temp-anonymization-db_.
    * VPC: _emh-infrastructure-fleet-management_
    * DB Subnet Group: _emh-fleet-management-database-cluster-subnet-group_
    * Existing VPC Group: _emh-fleet-management-database-cluster-sg_
    * Public accessibility: Ensure this is set to No.
    * IAM database authentication: Ensure this is checked
    * Click Restore DB Instance. Wait for the instance's status to become available.
    * Enable RDS Data API when the cluster is  available.

=== Anonymize Data on the Temporary Instance

.Connect to the Temporary DB using AWS RDS Query.

1. Navigate to RDS > Query Editor.
2. Select the cluster created in the previous step.
3. As Database Username select Connect with Secrets Manager ARN
4. Paste the ARN vehicle-service/password/rds/app_user from Secrets Manager
5. Paste "vehicleservice" as database name
6. Run anonymization scripts:

.For the user_service

1. Copy all content from  link:{projectRoot}/dev-tools/database/anonimyzedPII/user_service.sql[user_service.sql].
2. Paste it to the query editor.
3. Click Run (be sure nothing is selected in the textarea).

.For vehicle registration:

1. Copy all content from  link:{projectRoot}/dev-tools/database/anonimyzedPII/vehicle_registration.sql[vehicle_registration.sql].
2. Paste it to the query editor.
3. Click Run (be sure nothing is selected in the textarea).

.For vehicle service:

1. Copy all content from  link:{projectRoot}/dev-tools/database/anonimyzedPII/vehicle_service.sql[vehicle_service.sql].
2. Paste it to the query editor.
3. Click Run (be sure nothing is selected in the textarea).

*Verification*: Run SELECT queries to spot-check the anonymized data and confirm that no sensitive data remains.

=== Share Snapshot

For being able to share the snapshot, a KMS Key is needed that is shared between the prod and the target environment:

1. On prod, go to _Key Management Service > Customer managed keys_
2. Click create Key
3. Configure the key:
  * Key type: symmetric
  * Key usage: Encrypt and decrypt
  * give it a meaningful alias and description
  * Key administrators: AWSReservedSSO_Administrator_<id>
  * Key users: AWSReservedSSO_Administrator_<id>

.The key Policy should look like this:
[source, json]
----
{
  "Version": "2012-10-17",
  "Id": "key-test-policy",
  "Statement": [
    {
      "Sid": "Enable IAM User Permissions",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::<source-account-id>:root"
      },
      "Action": "kms:*",
      "Resource": "*"
    },
    {
      "Sid": "Allow access for Key Administrators",
      "Effect": "Allow",
      "Principal": {
        "AWS": "arn:aws:iam::<source-account-id>:role/aws-reserved/sso.amazonaws.com/eu-central-1/AWSReservedSSO_Administrator_<id>"
      },
      "Action": [
        "kms:Create*",
        "kms:Describe*",
        "kms:Enable*",
        "kms:List*",
        "kms:Put*",
        "kms:Update*",
        "kms:Revoke*",
        "kms:Disable*",
        "kms:Get*",
        "kms:Delete*",
        "kms:TagResource",
        "kms:UntagResource",
        "kms:ScheduleKeyDeletion",
        "kms:CancelKeyDeletion",
        "kms:RotateKeyOnDemand"
      ],
      "Resource": "*"
    },
    {
      "Sid": "Allow use of the key",
      "Effect": "Allow",
      "Principal": {
        "AWS": [
          "arn:aws:iam::<source-account-id>:role/aws-reserved/sso.amazonaws.com/eu-central-1/<admin-role>",
          "arn:aws:iam::<target-account-id>:root"
        ]
      },
      "Action": [
        "kms:Encrypt",
        "kms:Decrypt",
        "kms:ReEncrypt*",
        "kms:GenerateDataKey*",
        "kms:DescribeKey"
      ],
      "Resource": "*"
    },
    {
      "Sid": "Allow attachment of persistent resources",
      "Effect": "Allow",
      "Principal": {
        "AWS": [
          "arn:aws:iam::<source-account-id>:role/aws-reserved/sso.amazonaws.com/eu-central-1/<admin-role>",
          "arn:aws:iam::<target-account-id>:root"
        ]
      },
      "Action": [
        "kms:CreateGrant",
        "kms:ListGrants",
        "kms:RevokeGrant"
      ],
      "Resource": "*",
      "Condition": {
        "Bool": {
          "kms:GrantIsForAWSResource": "true"
        }
      }
    }
  ]
}

----

The next step is to create a snapshot:

1. Navigate to the RDS service in the AWS Console.
2. In the navigation pane, select Databases and choose the temporary database instance or cluster.
3. Click the _Actions_ button and select _Take snapshot_.
4. Enter a descriptive snapshot name (e.g., prod-db-yyyymmdd-after-anonymization) and select snapshot type "DB cluster"
5. Click Take snapshot.

Then the snapshot needs to be encrypted with the shared key:

1. Navigate to snapshots and select the anonymized snapshot.
2. Click the _Actions_ button and select _Copy snapshot_.
3. Configure the copy:
  * New DB snapshot identifier: give it a descriptive name (eg. prod-db-yyyymmdd-after-anonymization-for-sharing)
  * Destination Region: Europe (Ireland)
  * AWS KMS key: choose the shared key
4. Click _Copy snapshot_


=== Restore in Target Environment

To restore the anonymized snapshot on the target environment, log in on the target environment with an IAM role that has
sufficient permissions to restore RDS snapshots.

1. Navigate to the RDS service in the AWS Console.
2. In the navigation pane, select Snapshots and select the _Shared with me_ tab. There the shared snapshot should be available.
3. Click Actions > Restore snapshot.
4. Configure the instance:
  * VPC: _emh-infrastructure-fleet-management_
  * DB Subnet Group: _emh-fleet-management-database-cluster-subnet-group_
  * Existing VPC Group: _emh-fleet-management-database-cluster-sg_
  * Public accessibility: Ensure this is set to No.
  * IAM database authentication: Ensure this is checked
  * Click Restore DB Instance. Wait for the instance's status to become available.
  * Enable RDS Data API when the cluster is  available.

=== Update Database Credentials on the Applications

Once the new database instance/cluster is Available, update the application's configuration (e.g., environment variables,
secrets manager) with the new database credentials.

* *Database Host/URL:* Use the endpoint URL of the newly created database instance.
* *Username/Password:* The master username and password will be the same as those from the production database where the snapshot was taken.
