# Database Models 

## Vehicle Data
### Data Characteristics 
- contains data (copied over) from various source-systems (eg: PVH)
- contains mock-data for systems that we don't have an active integration for (eg: EQUI)

### Entity Diagram
```plantuml
@startuml

!theme plain
top to bottom direction
skinparam linetype ortho

class color_info {
   exterior: varchar(32)
   exterior_description: text
   interior: varchar(32)
   interior_description: text
   exterior_description_language: varchar(255)
   interior_description_language: varchar(255)
   id: bigint
}
class country_info {
   bnr_value: varchar(6)
   cnr_value: varchar(6)
   cnr_country_description: varchar(255)
   cnr_country_description_language: varchar(255)
   id: bigint
}
class embargo_info {
   in_embargo: boolean
   id: bigint
}
class engine_info {
   engine_type: varchar(32)
   typification: varchar(32)
   id: bigint
}
class delivery_info {
    id: bigint
    preparationDoneDate: timestamptz
    isPreparationNecessary: boolean
}
class fleet_info {
    id: bigint
    soldDate: timestamptz
    scrappedDate: timestamptz
    stolenDate: timestamptz
    soldCupCarDate: timestamptz
    approvedForScrappingDate: timestamptz
    scrappedVehicleOfferedDate: timestamptz
    vehicleSentToSalesDate: timestamptz
    costEstimationOrderedDate: timestamptz
    isResidualValueMarket: timestamptz
    profitabilityAuditDate: timestamptz
}
class return_info {
    id: bigint
    nextProcess: varchar(255)
    isUsedCar: boolean
}
class model_info {
   model_description: varchar(1024)
   product_id: varchar(32)
   product_code: varchar(32)
   order_type: varchar(32)
   model_description_language: varchar(255)
   model_year: integer
   vehicle_type: varchar
   manufacturer: varchar
   id: bigint
}
class order_info {
   consignee_number: varchar(32)
   trading_partner_number: varchar(32)
   purpose_order_type: varchar(32)
   importer_short_name: varchar(32)
   commission_number: varchar(32)
   invoice_number: varchar(32)
   invoice_date: timestamp(6)
   purchase_order_date: timestamp(6)
   requested_delivery_date: timestamp(6)
   delivery_type: varchar(32)
   primary_status: varchar(32)
   customer_delivery_date: timestamp(6)
   planned_customer_delivery_date: timestamp(6)
   port_code: varchar(255)
   shipping_code: varchar(255)
   vehicle_status_oem: varchar(255)
   department: varchar(32)
   leasing_type: varchar(8)
   preproduction_vehicle: boolean
   blocked_for_sale: boolean
   scrap_vehicle: boolean
   id: bigint
}
class pmp_data {
   odometer: integer
   timestamp: timestamp(6)
   id: bigint
}
class production_info {
   production_number: varchar(32)
   production_end_date: timestamp(6)
   planned_production_end_date: timestamp(6)
   technical_model_year: integer
   factory: varchar(32)
   factory_vw: integer
   quote_month: integer
   quote_year: integer
   gear_box_class: varchar(4)
   production_number_vw: varchar(255)
   id: bigint
}
class vehicle {
   vin: varchar(255)
   vguid: varchar(255)
   engine_info_id: bigint
   requires_refresh: boolean
   order_info_id: bigint
   model_info_id: bigint
   production_info_id: bigint
   country_info_id: bigint
   pmp_data_id: bigint
   embargo_info_id: bigint
   color_info_id: bigint
   fleet_info_id: bigint
   delivery_info_id: bigint
   return_info_id: bigint
   options: jsonb
   last_updated_at: timestamp with time zone
   created_at: timestamp with time zone
   equi_id: varchar(255)
   equipment_number: bigint
   id: uuid
}

vehicle                  -[#595959,plain]-^  color_info              : "color_info_id:id"
vehicle                  -[#595959,plain]-^  country_info            : "country_info_id:id"
vehicle                  -[#595959,plain]-^  embargo_info            : "embargo_info_id:id"
vehicle                  -[#595959,plain]-^  engine_info             : "engine_info_id:id"
vehicle                  -[#595959,plain]-^  model_info              : "model_info_id:id"
vehicle                  -[#595959,plain]-^  order_info              : "order_info_id:id"
vehicle                  -[#595959,plain]-^  pmp_data                : "pmp_data_id:id"
vehicle                  -[#595959,plain]-^  production_info         : "production_info_id:id"
vehicle                  -[#595959,plain]-^  fleet_info              : "fleet_info_id:id"
vehicle                  -[#595959,plain]-^  delivery_info           : "delivery_info_id:id"
vehicle                  -[#595959,plain]-^  return_info             : "return_info_id:id"
@enduml
```

## Vehicle-Registration

### Data Characteristics
 - Data related to vehicle-registration processes

### Entity Diagram

```plantuml
@startuml

!theme plain
top to bottom direction
skinparam linetype ortho

class brieflist {
   vin: varchar(17)
   brief_number: varchar(35)
   created_at: timestamp(6)
   updated_at: timestamp(6)
   updated_by: varchar(255)
   id: bigint
}

class order {
   vin: varchar(17)
   drive_type: varchar(15)
   leasing_type: varchar(15)
   department: varchar(15)
   registration_office: varchar(5)
   planned_licence_plate: varchar(15)
   fuel_type: varchar(15)
   planned_registration_date: timestamp(6)
   commenter: varchar(255)
   order_status: varchar(255)
   created_at: timestamp(6)
   updated_at: timestamp(6)
   model_type: varchar(255)
   registration_id: bigint
   vehicle_id: uuid
   version: bigint
   brieflist_id: bigint
   order_type: bigint
   equi_internal_vehicle_id: varchar(255)
   test_number: bigint
   equipment_number: bigint
   id: bigint
}
class registration {
   storage_location: varchar(15)
   tsn: varchar(32)
   hsn: varchar(4)
   registration_type: integer
   registration_date: timestamp(6)
   licence_plate: varchar(15)
   remark: varchar(255)
   sfme: boolean
   test_vehicle: boolean
   created_at: timestamp(6)
   updated_at: timestamp(6)
   registration_status  /* New registration_status column in registration */: varchar(35)
   first_registration_date: timestamp(6)
   last_registration_date: timestamp(6)
   last_deregistration_date: timestamp(6)
   id: bigint
}

order                  -[#595959,plain]-^  brieflist             : "brieflist_id:id"
order                  -[#595959,plain]-^  registration          : "registration_id:id"
@enduml
```

## Vehicle-Location 

### Data Characteristics
- contains information of the vehicle's last-known location

### Entity Diagram
There is no information of people in this schema.

```plantuml
@startuml

!theme plain
top to bottom direction
skinparam linetype ortho

class last_known_location {
   vehicle_id: uuid
   location_key: varchar
   occurred_on: timestamp with time zone
   event_source: varchar
   event_type: varchar
   location_name: varchar
   building: varchar
   level: varchar
   parking_lot: varchar
   geo_location_latitude: double precision
   geo_location_longitude: double precision
   id: uuid
}
class location {
   location_key: varchar
   name: varchar
   building: jsonb
   parking_lot: varchar
   location_of_interest: boolean
   inactive: boolean
   latitude: double precision
   longitude: double precision
   id: uuid
}
@enduml
```

## Vehicle Transfer 
### Data Characteristics
- contains data around the process of transfering a fleet-managed vehicle from one person/department to another
- contains data around who is responsible for a vehicle at a given point in time

### Entity Diagram

```plantuml
@startuml

!theme plain
top to bottom direction
skinparam linetype ortho

class color_info {
   exterior: varchar(32)
   exterior_description: text
   interior: varchar(32)
   interior_description: text
   exterior_description_language: varchar(255)
   interior_description_language: varchar(255)
   id: bigint
}
class consignee_data {
   consignee: varchar
   description: varchar
   maximum_service_life_months: integer
   vehicle_usage_id: uuid
   vehicle_responsible_person: varchar
   internal_contact_person: varchar
   preproduction_vehicle: boolean
   blocked_for_sale: boolean
   scrapped: boolean
   depreciation_relevant_cost_center_id: uuid
   internal_order_number: varchar
   validation_of_leasing_privileges: varchar
   usage_group_id: uuid
   vehicle_type: varchar
   manufacturer: varchar
   version: integer
   using_cost_center: varchar
   id: uuid
}
class cost_center {
   cost_center_id: varchar
   description: varchar
   version: integer
   id: uuid
}
class country_info {
   bnr_value: varchar(6)
   cnr_value: varchar(6)
   cnr_country_description: varchar(255)
   cnr_country_description_language: varchar(255)
   id: bigint
}

class embargo_info {
   in_embargo: boolean
   id: bigint
}
class engine_info {
   engine_type: varchar(32)
   typification: varchar(32)
   id: bigint
}
class model_info {
   model_description: varchar(1024)
   product_id: varchar(32)
   product_code: varchar(32)
   order_type: varchar(32)
   model_description_language: varchar(255)
   model_year: integer
   vehicle_type: varchar
   manufacturer: varchar
   id: bigint
}
class order_info {
   consignee_number: varchar(32)
   trading_partner_number: varchar(32)
   purpose_order_type: varchar(32)
   importer_short_name: varchar(32)
   commission_number: varchar(32)
   invoice_number: varchar(32)
   invoice_date: timestamp(6)
   purchase_order_date: timestamp(6)
   requested_delivery_date: timestamp(6)
   delivery_type: varchar(32)
   primary_status: varchar(32)
   customer_delivery_date: timestamp(6)
   planned_customer_delivery_date: timestamp(6)
   port_code: varchar(255)
   shipping_code: varchar(255)
   vehicle_status_oem: varchar(255)
   department: varchar(32)
   leasing_type: varchar(8)
   preproduction_vehicle: boolean
   blocked_for_sale: boolean
   scrap_vehicle: boolean
   id: bigint
}
class planned_vehicle_transfer {
   vehicle_id: uuid
   consignee: varchar
   business_partner_id: varchar
   vehicle_responsible_person: varchar
   maximum_service_life_in_months: integer
   internal_contact_person: varchar
   is_preproduction_vehicle: boolean
   is_blocked_for_sale: boolean
   is_scrapped: boolean
   depreciation_relevant_cost_center_id: uuid
   using_cost_center: varchar
   internal_order_number: varchar
   vehicle_usage_id: uuid
   usage_group_id: uuid
   validation_of_leasing_privileges: varchar
   version: integer
   id: uuid
}
class pmp_data {
   odometer: integer
   timestamp: timestamp(6)
   id: bigint
}
class production_info {
   production_number: varchar(32)
   production_end_date: timestamp(6)
   planned_production_end_date: timestamp(6)
   technical_model_year: integer
   factory: varchar(32)
   factory_vw: integer
   quote_month: integer
   quote_year: integer
   gear_box_class: varchar(4)
   production_number_vw: varchar(255)
   id: bigint
}
class usage_group {
   usage_group_id: bigint
   description: varchar
   version: integer
   id: uuid
}
class vehicle {
   vin: varchar(255)
   vguid: varchar(255)
   engine_info_id: bigint
   requires_refresh: boolean
   order_info_id: bigint
   model_info_id: bigint
   production_info_id: bigint
   country_info_id: bigint
   pmp_data_id: bigint
   embargo_info_id: bigint
   color_info_id: bigint
   options: jsonb
   last_updated_at: timestamp with time zone
   created_at: timestamp with time zone
   equi_id: varchar(255)
   equipment_number: bigint
   id: uuid
}
class vehicle_usage {
   usage_id: bigint
   usage: varchar
   version: integer
   id: uuid
}

consignee_data            -[#595959,plain]-^  cost_center              : "depreciation_relevant_cost_center_id:id"
consignee_data            -[#595959,plain]-^  usage_group              : "usage_group_id:id"
consignee_data            -[#595959,plain]-^  vehicle                  : ":id"
consignee_data            -[#595959,plain]-^  vehicle_usage            : "vehicle_usage_id:id"
cost_center               -[#595959,plain]-^  cost_center              : "cost_center_id:id"
planned_vehicle_transfer  -[#595959,plain]-^  cost_center              : "depreciation_relevant_cost_center_id:id"
planned_vehicle_transfer  -[#595959,plain]-^  usage_group              : "usage_group_id:id"
planned_vehicle_transfer  -[#595959,plain]-^  vehicle_usage            : "vehicle_usage_id:id"
usage_group               -[#595959,plain]-^  usage_group              : "usage_group_id:id"
vehicle                   -[#595959,plain]-^  color_info               : "color_info_id:id"
vehicle                   -[#595959,plain]-^  country_info             : "country_info_id:id"
vehicle                   -[#595959,plain]-^  embargo_info             : "embargo_info_id:id"
vehicle                   -[#595959,plain]-^  engine_info              : "engine_info_id:id"
vehicle                   -[#595959,plain]-^  model_info               : "model_info_id:id"
vehicle                   -[#595959,plain]-^  order_info               : "order_info_id:id"
vehicle                   -[#595959,plain]-^  pmp_data                 : "pmp_data_id:id"
vehicle                   -[#595959,plain]-^  production_info          : "production_info_id:id"
@enduml
```

## Fleet Vehicle Management 

### Data Characteristics
- contains data that is used for the Fleet-Vehicle-Management UI application.

### Entity Diagram
- Currently only "Configurable Views" is stored in the FVM schema, in addition to "Data-Managers"
- The "Data-Manager" tables have been omitted from the schema diagram below, since it is covered in it's own documentation.

```plantuml
@startuml

!theme plain
top to bottom direction
skinparam linetype ortho

class view {
   name: varchar(155)
   description: text
   key: varchar(255)
   is_public: boolean
   is_default: boolean
   filter_state: jsonb
   column_state: jsonb
   user_id_sha256: char(64)
   created_at: timestamp(6)
   updated_at: timestamp(6)
   id: bigint
}

@enduml
```
## Vehicle History 
### Data Characteristics
- contains all changes that have been made to a Fleet-Managed Vehicle.

### Entity Diagram

```plantuml
@startuml

!theme plain
top to bottom direction
skinparam linetype ortho

class change_event_delta {
   current_vehicle_change_event_id: bigint
   vehicle_id: uuid
   field: varchar(255)
   old_value: varchar(255)
   new_value: varchar(255)
   modified_by: varchar(255)
   modified_at: timestamp with time zone
   id: bigint
}

class vehicle_change_event {
   vehicle_id: uuid
   vehicle_change_event: text
   modified_by: varchar(255)
   modified_at: timestamp with time zone
   domain: varchar(64)
   domain_id: varchar(64)
   created_at: timestamp with time zone
   id: bigint
}

@enduml
```

## Security
### Data Characteristics
- contains security-related configuration, including role-based access control is stored in this schema

### Entity Diagram

`group-id` is an Azure Entra ID unique identifer for an Azure Group

```plantuml
@startuml

!theme plain
top to bottom direction
skinparam linetype ortho


class group_privilege {
   group_id: uuid
   privilege_id: bigint
   filter: jsonb
}
class privilege {
   resource: varchar(255)
   permission: varchar(255)
   id: bigint
}

group_privilege        -[#595959,plain]-^  privilege             : "privilege_id:id"
@enduml

```
