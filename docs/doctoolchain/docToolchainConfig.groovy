outputPath = 'build'

// If you want to use the Antora integration, set this to true.
// This requires your project to be setup as Antora module.
// You can use `downloadTemplate` task to bootstrap your project.
//useAntoraIntegration = false

// Path where the docToolchain will search for the input files.
// This path is appended to the docDir property specified in gradle.properties
// or in the command line, and therefore must be relative to it.

inputPath = 'docs';

// if you need to register custom Asciidoctor extensions, this is the right place
// configure the name and path to your extension, relative to the root of your project
// (relative to dtcw). For example: 'src/ruby/asciidoctor-lists.rb'.
// this is the same as the `requires`-list of the asciidoctor gradle plugin. The extensions will be
// registered for generateDeck, generateHtml, generatePdf and generateDocbook tasks, only.
// rubyExtensions = []

// the pdfThemeDir config in this file is outdated.
// please check http://doctoolchain.org/docToolchain/v2.0.x/020_tutorial/030_generateHTML.html#_pdf_style for further details
// pdfThemeDir = './src/docs/pdfTheme'

inputFiles = [
        //[file: 'doctoolchain_demo.adoc',       formats: ['html','pdf']],
        //[file: 'arc42-template.adoc',    formats: ['html','pdf']],
	[file: 'arc42/arc42.adoc', formats: ['html','pdf']],
	/** inputFiles **/
]

//folders in which asciidoc will find images.
//these will be copied as resources to ./images
//folders are relative to inputPath
// Hint: If you define an imagepath in your documents like
// :imagesdir: ./whatsoever
// define it conditional like
// ifndef::imagesdir[:imagesdir: ./whatsoever]
// as doctoolchain defines :imagesdir: during generation
imageDirs = [
    'images/.',
	'images/.',
	/** imageDirs **/
]

// whether the build should fail when detecting broken image references
// if this config is set to true all images will be embedded
failOnMissingImages = true

// these are directories (dirs) and files which Gradle monitors for a change
// in order to decide if the docs have to be re-build
taskInputsDirs = [
                    "${inputPath}",
//                  "${inputPath}/src",
//                  "${inputPath}/images",
                 ]

taskInputsFiles = []

//*****************************************************************************************

// Configuration for customTasks
// create a new Task with ./dtcw createTask
customTasks = [
/** customTasks **/
]


//*****************************************************************************************

//Configuration for microsite: generateSite + previewSite

microsite = [:]

// these properties will be set as jBake properties
// microsite.foo will be site.foo in jBake and can be used as config.site_foo in a template
// see https://jbake.org/docs/2.6.4/#configuration for how to configure jBake
// other properties listed here might be used in the jBake templates and thus are not
// documented in the jBake docs but hopefully in the template docs.
microsite.with {
    /** start:microsite **/

    // is your microsite deployed with a context path?
    contextPath = '/'
    // the folder of a site definition (theme) relative to the docDir+inputPath
    //siteFolder = '../site'

    /** end:microsite **/

    //project theme
    //site folder relative to the docs folder
    //see 'copyTheme' for more details
    siteFolder = '../site'

    // the title of the microsite, displayed in the upper left corner
    title = '##site-title##'
    // the next items configure some links in the footer
    //
    // contact eMail
    // example: mailto:<EMAIL>
    footerMail = '##footer-email##'
    //
    // twitter account url
    footerTwitter = '##twitter-url##'
    //
    // Stackoverflow QA
    footerSO = '##Stackoverflow-url##'
    //
    // Github Repository
    footerGithub = '##Github-url##'
    //
    // Slack Channel
    footerSlack = '##Slack-url##'
    //
    // Footer Text
    // example: <small class="text-white">built with docToolchain and jBake <br /> theme: docsy</small>
    footerText = '<small class="text-white">built with <a href="https://doctoolchain.org">docToolchain</a> and <a href="https://jbake.org">jBake</a> <br /> theme: <a href="https://www.docsy.dev/">docsy</a></small>'
    //
    // site title if no other title is given
    title = 'docToolchain'
    //
    // the url to create an issue in github
    // Example: https://github.com/docToolchain/docToolchain/issues/new
    issueUrl = '##issue-url##'
    //
    // the base url for code files in github
    // Example: https://github.com/doctoolchain/doctoolchain/edit/master/src/docs
    branch = System.getenv("DTC_PROJECT_BRANCH")?:'-'
    gitRepoUrl = '##git-repo-url##'

    //
    // the location of the landing page
    landingPage = 'landingpage.gsp'
    // the menu of the microsite. A map of [code:'title'] entries to specify the order and title of the entries.
    // the codes are autogenerated from the folder names or :jbake-menu: attribute entries from the .adoc file headers
    // set a title to '-' in order to remove this menu entry.
    menu = [:]

//tag::additionalConverters[]
/**

if you need support for additional markup converters, you can configure them here
you have three different types of script you can define:

- groovy: just groovy code as string
- groovyFile: path to a groovy script
- bash: a bash command. It will receive the name of the file to be converted as first argument

`groovy` and `groovyFile` will have access to the file and config object

`dtcw:rstToHtml.py` is an internal script to convert restructuredText.
Needs `python3` and `docutils` installed.

**/
    additionalConverters = [
        //'.one': [command: 'println "test"+file.canonicalPath', type: 'groovy'],
        //'.two': [command: 'scripts/convert-md.groovy', type: 'groovyFile'],
        //'.rst': [command: 'dtcw:rstToHtml.py', type: 'bash'],
    ]
//end::additionalConverters[]

    // if you prefer another convention regarding the automatic generation
    // of jBake headers, you can configure a script to modify them here
    // the script has access to
    // - file: the current object
    // - sourceFolder: the copy of the docs-source on which the build operates
    //                 default `/microsite/tmp/site/doc`
    // - config: the config object (this file, but parsed)
    // - headers: already parsed headers to be modified
    /**
    customConvention = """
        System.out.println file.canonicalPath
        headers.title += " - from CustomConvention"
    """.stripIndent()
    **/
}

//*****************************************************************************************

//Configuration for exportChangelog

exportChangelog = [:]

changelog.with {

    // Directory of which the exportChangelog task will export the changelog.
    // It should be relative to the docDir directory provided in the
    // gradle.properties file.
    dir = 'docs'

    // Command used to fetch the list of changes.
    // It should be a single command taking a directory as a parameter.
    // You cannot use multiple commands with pipe between.
    // This command will be executed in the directory specified by changelogDir
    // it the environment inherited from the parent process.
    // This command should produce asciidoc text directly. The exportChangelog
    // task does not do any post-processing
    // of the output of that command.
    //
    // See also https://git-scm.com/docs/pretty-formats
    cmd = 'git log --pretty=format:%x7c%x20%ad%x20%n%x7c%x20%an%x20%n%x7c%x20%s%x20%n --date=short'

}

//*****************************************************************************************

//tag::confluenceConfig[]
//Configuration for publishToConfluence

confluence = [:]

/**
//tag::input-config[]

*input*

is an array of files to upload to Confluence with the ability
to configure a different parent page for each file.

=== Attributes

- `file`: absolute or relative path to the asciidoc generated html file to be exported
- `url`: absolute URL to an asciidoc generated html file to be exported
- `ancestorName` (optional): the name of the parent page in Confluence as string;
                             this attribute has priority over ancestorId, but if page with given name doesn't exist,
                             ancestorId will be used as a fallback
- `ancestorId` (optional): the id of the parent page in Confluence as string; leave this empty
                           if a new parent shall be created in the space

The following four keys can also be used in the global section below

- `spaceKey`: page specific variable for the key of the confluence space to write to
              case sensitive! If the case is not correct, it can be that new page will be
              created but can't be updated in the next run.
- `subpagesForSections` (optional): The number of nested sub-pages to create. Default is '1'.
                                    '0' means creating all on one page.
                                    The following migration for removed configuration can be used.
** `allInOnePage = true` is the same as `subpagesForSections = 0`
** `allInOnePage = false && createSubpages = false` is the same as `subpagesForSections = 1`
** `allInOnePage = false && createSubpages = true` is the same as `subpagesForSections = 2`
- `pagePrefix` (optional): page specific variable, the pagePrefix will be a prefix for the page title and it's sub-pages
                           use this if you only have access to one confluence space but need to store several
                           pages with the same title - a different pagePrefix will make them unique
- `pageSuffix` (optional): same usage as prefix but appended to the title and it's subpages

only 'file' or 'url' is allowed. If both are given, 'url' is ignored

//end::input-config[]
**/

confluence.with {
    input = [
            [ file: "../../build/html5/arc42/arc42.html" ],
    ]

    // endpoint of the confluenceAPI (REST) to be used
    // if you use Confluence Cloud, you can set this value to
    // https://[yourServer]
    // a working example is https://arc42-template.atlassian.net
    // if you use Confluence Server, you may need to set a context:
    // https://[yourServer]/[context]
    // a working example is https://arc42-template.atlassian.net/wiki
    api = 'https://skyway.porsche.com/confluence'

    // requests per second for confluence API calls
    rateLimit = 10

    // if true API V1 only will be used. Default is true.
    // useV1Api = true

    // if true, the new editor v2 will be used. Default is false.
    // enforceNewEditor = false

    //    Additionally, spaceKey, subpagesForSections, pagePrefix and pageSuffix can be globally defined here. The assignment in the input array has precedence

    // the key of the confluence space to write to
    spaceKey = 'FPT3'
    ancestorId='1291095883' // https://skyway.porsche.com/confluence/pages/viewpage.action?pageId=1291095883

    // if true, all pages will be created using the new editor v2
    // enforceNewEditor = false

    // variable to determine how many layers of sub pages should be created
    subpagesForSections = 1

    // the pagePrefix will be a prefix for each page title
    // use this if you only have access to one confluence space but need to store several
    // pages with the same title - a different pagePrefix will make them unique
    pagePrefix = 'FVM | '

    pageSuffix = ''

    // the comment used for the page version
    pageVersionComment = ''

    /*
    WARNING: It is strongly recommended to store credentials securely instead of commiting plain text values to your git repository!!!

    Tool expects credentials that belong to an account which has the right permissions to to create and edit confluence pages in the given space.
    Credentials can be used in a form of:
     - passed parameters when calling script (-PconfluenceUser=myUsername -PconfluencePass=myPassword) which can be fetched as a secrets on CI/CD or
     - gradle variables set through gradle properties (uses the 'confluenceUser' and 'confluencePass' keys)
    Often, same credentials are used for Jira & Confluence, in which case it is recommended to pass CLI parameters for both entities as
    -Pusername=myUser -Ppassword=myPassword
    */

    //optional API-token to be added in case the credentials are needed for user and password exchange.
    //apikey = "[API-token]"

    // HTML Content that will be included with every page published
    // directly after the TOC. If left empty no additional content will be
    // added
    // extraPageContent = '<ac:structured-macro ac:name="warning"><ac:parameter ac:name="title" /><ac:rich-text-body>This is a generated page, do not edit!</ac:rich-text-body></ac:structured-macro>
    extraPageContent = '<ac:structured-macro ac:name="warning"><ac:parameter ac:name="title" /><ac:rich-text-body>This is a generated page, do not edit!</ac:rich-text-body></ac:structured-macro>'

    // enable or disable attachment uploads for local file references
    enableAttachments = false

    // variable to limit number of pages retreived per REST-API call
    pageLimit = 100

    // default attachmentPrefix = attachment - All files to attach will require to be linked inside the document.
    // attachmentPrefix = "attachment"


    // Optional proxy configuration, only used to access Confluence
    // schema supports http and https
    // proxy = [host: 'my.proxy.com', port: 1234, schema: 'http']

    // Optional: specify which Confluence OpenAPI Macro should be used to render OpenAPI definitions
    // possible values: ["confluence-open-api", "open-api", true]. true is the same as "confluence-open-api" for backward compatibility
    // useOpenapiMacro = "confluence-open-api"

    // for exportConfluence-Task
    export = [
        srcDir: 'sample_data',
        destDir: 'docs'
    ]

}
//end::confluenceConfig[]

