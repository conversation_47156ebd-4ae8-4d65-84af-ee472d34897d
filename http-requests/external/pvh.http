### Get Access Token
POST https://{{gravity_idp_host}}/oidc/v1/token HTTP/1.1
Content-Type: application/x-www-form-urlencoded

grant_type=client_credentials
    &client_id={{pvh_client_id}}
    &client_secret={{pvh_client_secret}}

> {%
    client.global.set("gravity_auth_token", response.body.access_token);
%}


### Get Vehicle from PVH by VIN
@vehicle_vin=WP0ZZZ990RS200817
@language =de_DE
GET https://{{gravity_host}}/prod-business-asdcs-vehicle-service/vehicles/v1/vin/{{vehicle_vin}}/language/{{language}} HTTP/1.1
Authorization: Bearer {{gravity_auth_token}}
