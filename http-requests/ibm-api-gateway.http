### Azure IDP Test Token
POST https://login.microsoftonline.com/{{azure_test_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_test_api_client_id}}
&client_secret={{oauth_m2m_test_api_client_secret}}
&grant_type=client_credentials
&scope={{api_test_scope}}/.default

> {%
    client.global.set("auth_test_token", response.body.access_token);
%}

>> ./outputs/response.json

### Azure IDP Token
POST https://login.microsoftonline.com/{{azure_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_api_client_id}}
&client_secret={{oauth_m2m_api_client_secret}}
&grant_type=client_credentials
&scope={{api_scope}}/.default

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response.json

### Load Vehicles by VIN
POST https://{{host}}/porsche-group/{{environment}}/vehicles/load
Authorization: Bearer {{auth_token}}
Authorization-FleetVehicle: Bearer {{auth_test_token}}
X-Porsche-Client-Id: {{api_client_id}}
X-Porsche-Client-Secret: {{api_client_secret}}
accept: application/json
Content-Type: application/json

{
  "vins": [
    "WP0ZZZ99ZNS210519"
  ]
}

>> ./outputs/response.json

### Get Vehicle by VIN
GET https://{{host}}/porsche-group/{{environment}}/vehicles/vin/WP0ZZZ99ZNS210519
Authorization: Bearer {{auth_token}}
Authorization-FleetVehicle: Bearer {{auth_test_token}}
X-Porsche-Client-Id: {{api_client_id}}
X-Porsche-Client-Secret: {{api_client_secret}}
accept: application/json

>> ./outputs/response.json

## Test ECC against IBM Gateway
### Azure IDP Test Token
POST https://login.microsoftonline.com/{{azure_test_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{ecc_oauth_m2m_api_client_id}}
    &client_secret={{ecc_oauth_m2m_api_client_secret}}
    &grant_type=client_credentials
    &scope={{api_test_scope}}/.default

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response.json

### Get damage reports by a date range
GET https://{{host}}/porsche-group/{{environment}}/ecc/00123456
Authorization: Bearer {{auth_token}}
X-Porsche-Client-Id: {{ecc_ibm_api_gateway_client_id}}
X-Porsche-Client-Secret: {{ecc_ibm_api_gateway_client_secret}}
accept: application/json
Content-Type: application/json