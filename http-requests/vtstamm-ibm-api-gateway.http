## Test VTStamm Vehicle API against IBM Gateway
## secrets can be obtained from gitlab CI/CD
## ECC_OAUTH_M2M_API_CLIENT_SECRET, ECC_IBM_API_GATEWAY_CLIENT_SECRET_{ENV}

### Azure IDP Test Token (we are using 'EMH Test ECC Data Consumer' for testing purposes)
POST https://login.microsoftonline.com/{{azure_test_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{ecc_oauth_m2m_api_client_id}}
    &client_secret={{ecc_oauth_m2m_api_client_secret}}
    &grant_type=client_credentials
    &scope={{api_test_scope}}/.default

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response.json

### Create new VTStamm Vehicle
POST https://{{host}}/porsche-group/{{environment}}/vtstamm/vehicle
Authorization: Bearer {{auth_token}}
X-Porsche-Client-Id: {{ecc_ibm_api_gateway_client_id}}
X-Porsche-Client-Secret: {{ecc_ibm_api_gateway_client_secret}}
accept: application/json
Content-Type: application/json

{
  "vin":"1HGCP36818A064693",
  "manufacturer":"Porsche",
  "internalVehicleDescription":"I look like an equiId",
  "internalDesignation":"some internal designation",
  "equipmentNumber": 328023,
  "vehicleType":"PKW",
  "blockedForSale":true,
  "scrapVehicle":false,
  "modelDescription":"model description",
  "modelDescriptionDevelopment":"model description dev",
  "orderType":"order types",
  "tuevAppointment": "2025-01-01",
  "countryInfo": "de",
  "exteriorColor":"yes please",
  "interiorColor":"black",
  "productionEndDate":"2025-05-06",
  "modelRangeDevelopment":"range dev",
  "subjectToConfidentiality":true,
  "confidentialityClassification":"top secret",
  "subjectToConfidentialityStartDate":"2025-01-01",
  "subjectToConfidentialityEndDate":"2028-12-31",
  "recordFactoryExit":true,
  "camouflageRequired":true,
  "typeOfUseVTS":"type",
  "statusVTS":"available"
}

>> ./outputs/response.json

### Update VTStamm Vehicle
PATCH https://{{host}}/porsche-group/{{environment}}/vtstamm/vehicle/1HGCP36818A064692
Authorization: Bearer {{auth_token}}
X-Porsche-Client-Id: {{ecc_ibm_api_gateway_client_id}}
X-Porsche-Client-Secret: {{ecc_ibm_api_gateway_client_secret}}
accept: application/json
Content-Type: application/json

{
  "equipmentNumber": 328023,
  "internalVehicleDescription":"I look like an equiId",
  "internalDesignation":"some other internal designation",
  "tuevAppointment": "2025-01-02",
  "subjectToConfidentiality":false,
  "confidentialityClassification":null,
  "subjectToConfidentialityStartDate":null,
  "subjectToConfidentialityEndDate":null,
  "recordFactoryExit":false,
  "camouflageRequired":false
}

>> ./outputs/response.json

### Update Non-VTStamm Vehicle
PATCH https://{{host}}/porsche-group/{{environment}}/vtstamm/vehicle/nonvtstamm/1HGCP36818A064692
Authorization: Bearer {{auth_token}}
X-Porsche-Client-Id: {{ecc_ibm_api_gateway_client_id}}
X-Porsche-Client-Secret: {{ecc_ibm_api_gateway_client_secret}}
accept: application/json
Content-Type: application/json

{
  "equipmentNumber": 328023,
  "internalVehicleDescription":"I look like an equiId",
  "internalDesignation":"some other internal designation",
  "tuevAppointment": "2025-01-02",
  "subjectToConfidentiality":false,
  "confidentialityClassification":null,
  "subjectToConfidentialityStartDate":null,
  "subjectToConfidentialityEndDate":null,
  "recordFactoryExit":false,
  "camouflageRequired":false
}

>> ./outputs/response.json
