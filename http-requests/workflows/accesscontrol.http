### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
    client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
    client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

>> ./outputs/local/response.json

### Export access control permissions to excel
POST /api/vs/ui/access-control/export/b78d429b-c2e4-46ea-bed4-6b1c9ca6f1c0
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080

{
  "translations": {
    "vehicle": {
      "model": {
        "description": "Model Description"
      },
      "vin_one": "VIN",
      "vin_other": "VINs"
    }
  }
}

### Import access control permissions
POST /api/vs/ui/access-control/import/b78d429b-c2e4-46ea-bed4-6b1c9ca6f1c0
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="import-all-permissions.xlsx"
Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet

< ../../src/test/resources/accesscontrol/import-all-permissions.xlsx
--WebAppBoundary
Content-Disposition: form-data; name="comment"

Import Admin Privileges
--WebAppBoundary--
###

### Get access control history
GET /api/vs/ui/access-control-history
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/response.json
