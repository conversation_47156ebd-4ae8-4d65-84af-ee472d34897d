### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
    client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
    client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

>> ./outputs/local/response.json

### Cost Center
### Get cost center
GET /api/vs/ui/vehicle-transfer-maintenance/cost-center
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/consignee-datasheet/cost-center/response.json

### Create cost center
POST /api/vs/ui/vehicle-transfer-maintenance/cost-center
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "costCenterId": "0QRswqn12aQ",
  "description": "0eXBqWG",
  "vehicleUsageIds": ["a13354e8-cd6a-4db2-b59d-1b75494d7985"],
  "vehicleTypes": ["Car"]
}

>> ./outputs/local/consignee-datasheet/cost-center/response.json

### Update cost center
PUT /api/vs/ui/vehicle-transfer-maintenance/cost-center/03784d9e-7ee0-4d84-8266-91c5c1c43211
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json
version: 2

{
  "costCenterId": "depreciationRelevantCostCenterId",
  "description": "0eXBqWG",
  "vehicleUsageIds": ["a13354e8-cd6a-4db2-b59d-1b75494d7985","a5451773-8799-4d48-8b82-7517e2477370"],
  "vehicleTypes": ["Car", "Truck"]
}

>> ./outputs/local/consignee-datasheet/cost-center/response.json

### Delete cost center
DELETE /api/vs/ui/vehicle-transfer-maintenance/cost-center/6f6e98db-0566-4254-b005-adcfaf6f80de
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080

>> ./outputs/local/consignee-datasheet/cost-center/response.json


### Usage Group

### Get Usage Group
GET /api/vs/ui/vehicle-transfer-maintenance/usage-group
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/consignee-datasheet/usage-group/response.json

### Create Usage Group
POST /api/vs/ui/vehicle-transfer-maintenance/usage-group
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "usageGroupId": 5,
  "description": "0eXBqWG"
}

>> ./outputs/local/consignee-datasheet/usage-group/response.json

### Update Usage Group
PUT /api/vs/ui/vehicle-transfer-maintenance/usage-group/f3c73c7a-4227-4af3-ba1d-da211b8d63ff
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json
version: 0

{
  "usageGroupId": 9,
  "description": "description"
}

>> ./outputs/local/consignee-datasheet/usage-group/response.json

### Delete Usage Group
DELETE /api/vs/ui/vehicle-transfer-maintenance/usage-group/f3c73c7a-4227-4af3-ba1d-da211b8d63ff
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080

>> ./outputs/local/consignee-datasheet/usage-group/response.json

###Vehicle Usage

### Get vehicle usage
GET /api/vs/ui/vehicle-transfer-maintenance/vehicle-usage
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/consignee-datasheet/vehicle-usage/response.json

### Create vehicle usage
POST /api/vs/ui/vehicle-transfer-maintenance/vehicle-usage
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "usageId": 4,
  "usage": "0eXBqWG"
}

>> ./outputs/local/consignee-datasheet/vehicle-usage/response.json

### Update vehicle usage
PUT /api/vs/ui/vehicle-transfer-maintenance/vehicle-usage/a5451773-8799-4d48-8b82-7517e2477370
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json
version: 0

{
  "usageId": 4,
  "usage": "usage"
}

>> ./outputs/local/consignee-datasheet/vehicle-usage/response.json

### Delete vehicle usage
DELETE /api/vs/ui/vehicle-transfer-maintenance/vehicle-usage/a5451773-8799-4d48-8b82-7517e2477370
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080

>> ./outputs/local/consignee-datasheet/vehicle-usage/response.json

### Consignee Data

### Get consignee data
GET /api/vs/ui/vehicle-transfer-maintenance/consignee-data
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/consignee-datasheet/consignee-data/response.json

### Create consignee data
POST /api/vs/ui/vehicle-transfer-maintenance/consignee-data
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "consignee": "0QRswqn12aQ",
  "description": "0eXBqWG",
  "maximumServiceLifeInMonths": 1,
  "vehicleUsageId": "d52f8a0d-09ce-486a-9856-cc67d2b6d191",
  "vehicleResponsiblePerson": "00123456",
  "internalContactPerson": "00786543",
  "isPreproductionVehicle": true,
  "isBlockedForSale": true,
  "isScrapped": true,
  "depreciationRelevantCostCenterId": "6f6e98db-0566-4254-b005-adcfaf6f80de",
  "usingCostCenter": "HwqAK",
  "internalOrderNumber": "ts4WaENv64dazuC9i9Te",
  "validationOfLeasingPrivileges": "A1bAW4",
  "usageGroupId": "34064783-23eb-4e19-9f12-084487cb98ae",
  "vehicleType": "uH4mjsXoj",
  "manufacturer": "s1XeT"
}

>> ./outputs/local/consignee-datasheet/consignee-data/response.json

### Update consignee data
PUT /api/vs/ui/vehicle-transfer-maintenance/consignee-data
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

[
  {
    "id": "5357474e-faf4-4a31-8c21-d736c7ccb555",
    "version": 0,
    "consignee": "consignee",
    "description": "0eXBqWG",
    "maximumServiceLifeInMonths": 1,
    "vehicleUsageId": "a5451773-8799-4d48-8b82-7517e2477370",
    "vehicleResponsiblePerson": "00123456",
    "internalContactPerson": "00786543",
    "isPreproductionVehicle": true,
    "isBlockedForSale": true,
    "isScrapped": true,
    "depreciationRelevantCostCenterId": "6f6e98db-0566-4254-b005-adcfaf6f80de",
    "usingCostCenter": "HwqAK",
    "internalOrderNumber": "ts4WaENv64dazuC9i9Te",
    "validationOfLeasingPrivileges": "A1bAW4",
    "usageGroupId": "f3c73c7a-4227-4af3-ba1d-da211b8d63ff",
    "vehicleType": "uH4mjsXoj",
    "manufacturer": "s1XeT"
  }
]

>> ./outputs/local/consignee-datasheet/consignee-data/response.json

### Delete consignee data
DELETE /api/vs/ui/vehicle-transfer-maintenance/consignee-data/5357474e-faf4-4a31-8c21-d736c7ccb555
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
