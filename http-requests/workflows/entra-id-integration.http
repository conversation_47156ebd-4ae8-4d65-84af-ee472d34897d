### EntraId IDP Token
POST https://{{host_idp}}/{{tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_api_client_id}}
    &client_secret={{oauth_m2m_api_client_secret}}
    &grant_type=client_credentials
    &scope={{scope}}

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response_token.json

### EntraId: Get AdministrativUnit by Id
GET https://graph.microsoft.com/v1.0/directory/administrativeUnits/{{administrativUnitId}}/members
Authorization: Bearer {{auth_token}}
Content-Type: application/json

>> ./outputs/response_entraId_AdministrativUnit.json


### EntraId: Get AppRoleAssignments by Id
GET https://graph.microsoft.com/v1.0/me/appRoleAssignments?$select=principalId&$filter=resourceId eq 6f9c0024-186a-43d9-aa3a-cbcb8c9067d0
Authorization: Bearer {{auth_token}}
Content-Type: application/json

>> ./outputs/response_entraId_AppRoleAssignments.json


### EntraId: Get user details by filter
GET https://graph.microsoft.com/v1.0/users?$select=id&$filter=mail eq '<EMAIL>'
Authorization: Bearer {{auth_token}}
Content-Type: application/json

>> ./outputs/response_entraId_users.json

### EntraId: Get user membership by Id
GET https://graph.microsoft.com/v1.0/users/{{userId}}/memberOf?$select=id
Authorization: Bearer {{auth_token}}
Content-Type: application/json

>> ./outputs/response_entraId_userMemberOf.json
