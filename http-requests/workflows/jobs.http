### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
  client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
  client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

>> ./outputs/local/response.json

### Environment variables
@jobName = VehicleArchiveJob
POST /api/vs/jobs/trigger?jobName={{jobName}}
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/x-www-form-urlencoded

>> ./outputs/local/response.json
