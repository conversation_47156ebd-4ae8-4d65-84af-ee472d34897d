### EntraId IDP Token
POST https://{{msbooking_host_idp}}/{{msbooking_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id={{msbooking_oauth_m2m_api_client_id}}
    &client_secret={{msbooking_oauth_m2m_api_client_secret}}
    &grant_type=client_credentials
    &scope={{msbooking_scope}}

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response_token.json

### Bookings: List all appointments
GET https://graph.microsoft.com/v1.0/solutions/bookingBusinesses/{{bookings_id}}/appointments
Authorization: Bearer {{auth_token}}
Content-Type: application/json

>> ./outputs/response_bookings_listOfAppointments.json
