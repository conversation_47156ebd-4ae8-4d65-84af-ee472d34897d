### SAP IDP Test Token
POST https://{{host_idp}}/oauth/token
Content-Type: application/x-www-form-urlencoded

## secret can be obtained from AWS SecretManager oauth2_vs_client_secret_for_pace
client_id={{oauth_m2m_api_client_id}}
    &client_secret={{oauth_m2m_api_client_secret}}
    &grant_type=client_credentials

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response.json

### Create order number for maintenance or factoryCarPreparation (Y73X)
## Please note: It is designed from SPInE Team that we have to use a GET-Methode.
GET https://{{host_cpi}}/http/FMSInternalOrders/3541
Authorization: Bearer {{auth_token}}
accept: application/json
Content-Type: application/json

{
  "ORDER_TYPE": "Y73X",
  "ORDER_NAME": "Rep. Eigenve PV-CC 16 WP1ZZZ95ZKLB17399",
  "VIN": "WP1ZZZ95ZKLB17399",
  "CO_AREA": "0001",
  "COMP_CODE": "0001",
  "RESPCCTR": "0000005130",
  "S_ORD_ITEM": "000000",
  "PERSON_RESP": "00123456",
  "ESTIMATED_COSTS": "0.00",
  "APPLICATION_DATE": "2024-11-13",
  "DATE_WORK_BEGINS": "0000-00-00",
  "DATE_WORK_ENDS": "0000-00-00",
  "PROCESSING_GROUP": "00",
  "PLN_RELEASE": "0000-00-00",
  "PLN_COMPLETION": "0000-00-00",
  "PLN_CLOSE": "0000-00-00",
  "SETTL_TYPE": "PER",
  "SOURCE": null,
  "PERCENTAGE": "100",
  "COSTCENTER": "0000005130"
}
