### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
    client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
    client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

### Get User Info
GET /api/vs/userInfo
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

>> ./outputs/response.json

### Validate Access Token
GET /oidc/userinfo HTTP/1.1
Host: graph.microsoft.com
Authorization: Bearer {{access_token}}

>> ./outputs/response.json

### Get Usergroups
GET /api/vs/ui/role
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

### DELETE Usergroups
### Environment variables
@role-id = b78d429b-c2e4-46ea-bed4-6b1c9ca6f1c0
DELETE /api/vs/ui/role/{{role-id}}
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

### POST roles by email
POST /api/vs/ui/roles/by-employee-number
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "employeeNumber": "employeeNumber"
}
