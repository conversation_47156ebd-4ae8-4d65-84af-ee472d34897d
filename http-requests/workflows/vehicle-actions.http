### IDP Token
POST https://{{host_idp}}/auth/realms/Porsche-Central/protocol/openid-connect/token
Content-Type: application/x-www-form-urlencoded

client_id={{oauth_m2m_api_client_id}}
    &client_secret={{oauth_m2m_api_client_secret}}
    &grant_type=client_credentials

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response_rwil.json


### Get Vehicle Data Availability by vin
POST https://{{host_gateway}}/rwil/pag/retail/vdas/v141/{{KVPS}}/getDataAvailability
X-Personal-ID: {{KVPS}}
Authorization: Bearer {{auth_token}}
accept: application/json
Content-Type: application/json

{
  "languageID": "en-EN",
  "vehicleRef": {
    "vin": {
      "value": "{{VIN}}"
    }
  }
}

>> ./outputs/response_rwil_vehicle_data_html

### Get get Campaigns by vin
POST https://{{host_gateway}}/rwil/pag/retail/vrm/v110/{{KVPS}}/getCampaignsByVehicle
X-Personal-ID: {{KVPS}}
Authorization: Bearer {{auth_token}}
accept: application/json
Content-Type: application/json

{
  "languageID": "en-EN",
  "openRecallsIND": true,
  "vehicleRef": {
    "vin": {
      "value": "{{VIN}}"
    }
  }
}

>> ./outputs/response_rwil_vehicle_campaigns_html


