### Azure IDP Test Token
POST https://login.microsoftonline.com/{{azure_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id = {{oauth_m2m_api_client_id}} &
client_secret = {{oauth_m2m_api_client_secret}} &
grant_type = client_credentials &
scope = {{api_scope}}/.default

> {%
    client.global.set("auth_token", response.body.access_token);
%}

### Health check
GET /api/vs/actuator/health
Host: localhost:8080

>> ./outputs/local/response.json

### Get Vehicle Data
GET /api/vs/vehicles/vin/WP1ZZZ9YZKDA00237
Host: localhost:8080
Authorization: Bearer {{auth_token}}
accept: application/json

>> ./outputs/local/response.json

### Get Vehicle Data by Vguid
GET /api/vs/vehicles/vguid/0zxRMNrahKhX0000ZIIswG
Host: localhost:8080
Authorization: Bearer {{auth_token}}
accept: application/json

>> ./outputs/local/response.json

### Retrieve vehicle data from PVH by Vguids
POST /api/vs/vehicles/vguid
Host: localhost:8080
Authorization: Bearer {{auth_token}}
accept: application/json
Content-Type: application/json

[
  "0zxRMNrahKhX0000ZIIswG"
]

>> ./outputs/local/response.json
