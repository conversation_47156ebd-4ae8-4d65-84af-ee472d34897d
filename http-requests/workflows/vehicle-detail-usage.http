### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
    client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
    client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

>> ./outputs/local/response.json

### Environment variables
@vehicle-id = 26e1ee23-47ed-4073-b379-627203356af7
### Get Vehicle Data
GET /api/vs/ui/vehicles/{{vehicle-id}}/usage
Host: localhost:8080
Authorization: Bearer {{data_token}}
accept: application/json

>> ./outputs/local/response.json