### Azure IDP Test Token
POST https://login.microsoftonline.com/{{azure_tenant_id}}/oauth2/v2.0/token
Content-Type: application/x-www-form-urlencoded

client_id = {{oauth_m2m_legalhold_api_client_id}} &
client_secret = {{oauth_m2m_legalhold_api_client_secret}} &
grant_type = client_credentials &
scope = {{api_scope}}/.default

> {%
    client.global.set("auth_token", response.body.access_token);
%}

### Create legal-hold for vehicle and relevant entites
POST /api/vs/private/legal-hold/vehicles
Authorization: Bearer {{auth_token}}
Host: localhost:8080
Content-Type: application/json

{
  "vehicleIds": [
    "cf7c5d3e-a291-4cdd-8b8e-545d00f0e0d9"
  ],
  "lockId": "12345",
  "legalHoldId": "default"
}

>> ./outputs/local/response.json

### Release legal-hold for vehicle and relevant entites
POST /api/vs/private/legal-hold/release/vehicles
Authorization: Bearer {{auth_token}}
Host: localhost:8080
Content-Type: application/json

{
  "keys": ["default-cf7c5d3e-a291-4cdd-8b8e-545d00f0e0d9-12345"]
}

>> ./outputs/local/response.json
