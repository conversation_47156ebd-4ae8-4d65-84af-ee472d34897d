### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
    client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
    client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

>> ./outputs/local/response.json

### Get all vehicles
GET /api/vs/ui/vehicles
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/response.json

### Environment variables
@vehicle-id = fde5feee-dc55-4d93-b47b-777f5d601673

### Get vehicle details by vehicleId
GET /api/vs/ui/vehicles/{{vehicle-id}}
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/response.json

### refresh vehicle details by vehicleId from PVH
POST /api/vs/ui/vehicles/{{vehicle-id}}/refresh
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

>> ./outputs/local/response.json

### Create vehicle
POST /api/vs/vehicles
x-amzn-oidc-accesstoken: "access-token"
x-amzn-oidc-data: "oidc-data"
Host: localhost:9001
accept: application/json
Content-Type: application/json

{
  "vin": "JH4KA3160KC018608",
  "manufacturer": "Porsche",
  "modelDescription": "Macan",
  "orderType": "ABCDEF",
  "vehicleType": "TRAILER",
  "financialAssetType": "AV"
}

>> ./outputs/local/response.json

### Get vehicle for vehicle_manager page
POST /api/vs/ui/vehicles/search
Host: localhost:8080
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json
X-user-groups: b78d429b-c2e4-46ea-bed4-6b1c9ca6f1c0

{
  "startRow": 0,
  "endRow": 3,
  "rowGroupCols": [],
  "valueCols": [],
  "pivotCols": [],
  "pivotMode": false,
  "groupKeys": [],
  "filterModel": {
    "vehicleData.vin": {
      "filterType": "multi",
      "filterModels": [
        {
          "filterType": "text",
          "type": "notContains",
          "filter": "TEST"
        },
        null
      ]
    },
    "vehicleRegistration.registrationType": {
      "values": [
        "1",
        "2",
        "3",
        "4"
      ],
      "filterType": "set"
    }
  },
  "sortModel": []
}


### Export vehicles to csv
POST /api/vs/ui/vehicles/export
Host: localhost:8080
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json
X-user-groups: b78d429b-c2e4-46ea-bed4-6b1c9ca6f1c0

{
  "columnState": [
    {
      "colId": "vehicleData.vin",
      "hide": false,
      "translation": "FIN"
    },
    {
      "colId": "vehicleData.colorExterior",
      "hide": false,
      "translation": "Außenfarbe"
    },
    {
      "colId": "vehicleData.vguid",
      "hide": false,
      "translation": "VGUID"
    },
    {
      "colId": "vehicleData.modelDescription",
      "hide": false,
      "translation": "Modell"
    },
    {
      "colId": "vehicleData.orderType",
      "hide": false,
      "translation": "Bestelltyp"
    },
    {
      "colId": "vehicleData.driveType",
      "hide": false,
      "translation": "Antrieb"
    },
    {
      "colId": "vehicleData.leasingType",
      "hide": false,
      "translation": "Leasingart"
    },
    {
      "colId": "vehicleData.department",
      "hide": false,
      "translation": "Abteilung"
    },
    {
      "colId": "vehicleData.inEmbargo",
      "hide": false,
      "translation": "Sperre",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "vehicleData.productId",
      "hide": false,
      "translation": "Produkt ID"
    },
    {
      "colId": "vehicleData.productCode",
      "hide": false,
      "translation": "Produkt Code"
    },
    {
      "colId": "vehicleData.tradingPartnerNumber",
      "hide": false,
      "translation": "Handelspartnernummer"
    },
    {
      "colId": "vehicleData.purposeOrderType",
      "hide": false,
      "translation": "Bestelltyp Verwendung"
    },
    {
      "colId": "vehicleData.importerShortName",
      "hide": false,
      "translation": "Importerkürzel"
    },
    {
      "colId": "vehicleData.commissionNumber",
      "hide": false,
      "translation": "Komissionsnummer"
    },
    {
      "colId": "vehicleData.invoiceNumber",
      "hide": false,
      "translation": "Rechnungsnummer"
    },
    {
      "colId": "vehicleData.invoiceDate",
      "hide": false,
      "translation": "Rechnungsdatum"
    },
    {
      "colId": "vehicleData.purchaseOrderDate",
      "hide": false,
      "translation": "Bestelldatum"
    },
    {
      "colId": "vehicleData.requestedDeliveryDate",
      "hide": false,
      "translation": "Gewünschtes Lieferdatum"
    },
    {
      "colId": "vehicleData.deliveryType",
      "hide": false,
      "translation": "Auslieferungstyp"
    },
    {
      "colId": "vehicleData.primaryStatus",
      "hide": false,
      "translation": "Primärstatus PVH"
    },
    {
      "colId": "vehicleData.colorExteriorDescription",
      "hide": false,
      "translation": "Außenfarbe Beschreibung"
    },
    {
      "colId": "vehicleData.colorInterior",
      "hide": false,
      "translation": "Innenfarbe"
    },
    {
      "colId": "vehicleData.colorInteriorDescription",
      "hide": false,
      "translation": "Innenausstattung Beschreibung"
    },
    {
      "colId": "vehicleData.productionNumber",
      "hide": false,
      "translation": "Produktionsnummer"
    },
    {
      "colId": "vehicleData.productionEndDate",
      "hide": false,
      "translation": "Ist ZP8-Datum"
    },
    {
      "colId": "vehicleData.technicalModelYear",
      "hide": false,
      "translation": "Modelljahr"
    },
    {
      "colId": "vehicleData.factory",
      "hide": false,
      "translation": "Werk"
    },
    {
      "colId": "vehicleData.factoryVW",
      "hide": false,
      "translation": "Werk VW"
    },
    {
      "colId": "vehicleData.quoteMonth",
      "hide": false,
      "translation": "Quotenmonat"
    },
    {
      "colId": "vehicleData.quoteYear",
      "hide": false,
      "translation": "Quotenjahr"
    },
    {
      "colId": "vehicleData.plannedProductionEndDate",
      "hide": false,
      "translation": "Plan ZP8-Datum"
    },
    {
      "colId": "vehicleData.gearBoxClass",
      "hide": false,
      "translation": "Getriebeart"
    },
    {
      "colId": "vehicleData.countryInfoBNRValue",
      "hide": false,
      "translation": "Länderinfo BNR"
    },
    {
      "colId": "vehicleData.countryInfoCNRValue",
      "hide": false,
      "translation": "Länderinfo CNR"
    },
    {
      "colId": "vehicleData.countryInfoCNRValueCountryDescription",
      "hide": false,
      "translation": "Länderinfo CNR Beschreibung"
    },
    {
      "colId": "vehicleData.pmpDataOdometer",
      "hide": false,
      "translation": "Laufleistung"
    },
    {
      "colId": "vehicleData.pmpDataTimestamp",
      "hide": false,
      "translation": "PMP Data Timestamp"
    },
    {
      "colId": "vehicleData.consumptionDataTypification",
      "hide": false,
      "translation": "Abgaszertifizierung"
    },
    {
      "colId": "vehicleData.equiId",
      "hide": false,
      "translation": "Equipment-Nr."
    },
    {
      "colId": "vehicleData.equipmentNumber",
      "hide": false,
      "translation": "Interne Fzg.-Bezeichnung"
    },
    {
      "colId": "vehicleData.preproductionVehicle",
      "hide": false,
      "translation": "Vorserie",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "vehicleData.blockedForSale",
      "hide": false,
      "translation": "Für Verkauf gesperrt",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "vehicleData.scrapVehicle",
      "hide": false,
      "translation": "Schrottfahrzeug",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "vehicleData.source",
      "hide": false,
      "translation": "Quelle"
    },
    {
      "colId": "vehicleData.financialAssetType",
      "hide": false,
      "translation": "Vermögenstyp"
    },
    {
      "colId": "vehicleData.importId",
      "hide": false,
      "translation": "Import ID"
    },
    {
      "colId": "vehicleData.createdAt",
      "hide": false,
      "translation": "Erstelldatum"
    },
    {
      "colId": "vehicleRegistration.licencePlate",
      "hide": false,
      "translation": "KFZ-Kennzeichen"
    },
    {
      "colId": "vehicleRegistration.registrationDate",
      "hide": false,
      "translation": "Zulassungsdatum"
    },
    {
      "colId": "vehicleRegistration.firstRegistrationDate",
      "hide": false,
      "translation": "Erstes Zulassungsdatum"
    },
    {
      "colId": "vehicleRegistration.lastRegistrationDate",
      "hide": false,
      "translation": "Letztes Zulassungsdatum"
    },
    {
      "colId": "vehicleRegistration.lastDeRegistrationDate",
      "hide": false,
      "translation": "Letztes Abmeldedatum"
    },
    {
      "colId": "vehicleRegistration.hsn",
      "hide": false,
      "translation": "HSN"
    },
    {
      "colId": "vehicleRegistration.tsn",
      "hide": false,
      "translation": "TSN"
    },
    {
      "colId": "vehicleRegistration.briefNumber",
      "hide": false,
      "translation": "Fahrzeugbriefnummer"
    },
    {
      "colId": "vehicleRegistration.sfme",
      "hide": false,
      "translation": "Selbstfahrmieteintrag",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "vehicleRegistration.testVehicle",
      "hide": false,
      "translation": "Erprobung",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "vehicleRegistration.storageLocation",
      "hide": false,
      "translation": "Lagerort"
    },
    {
      "colId": "vehicleRegistration.registrationType",
      "hide": false,
      "translation": "Zulassungsart",
      "enumValues": {
        "1": "1 Erstzulassung",
        "2": "2 Wiederzulassung",
        "3": "3 Umkennzeichnung",
        "4": "4 Abmeldung"
      }
    },
    {
      "colId": "vehicleRegistration.registrationStatus",
      "hide": false,
      "translation": "Berichtspunkt Zulassung",
      "enumValues": {
        "CREATED": "Erstellt",
        "FAILED": "Fehler",
        "IN_PROGRESS": "In Arbeit",
        "OPEN": "Offen",
        "READY": "Bereit für Export",
        "EXPORTED": "Exportiert",
        "COMPLETED": "Abgeschlossen",
        "REGISTERED": "Zugelassen",
        "DE_REGISTERED": "Abgemeldet"
      }
    },
    {
      "colId": "vehicleRegistration.testNumber",
      "hide": false,
      "translation": "Erprobungsnummer"
    },
    {
      "colId": "vehicleLastKnownLocation.compoundName",
      "hide": false,
      "translation": "Compound"
    },
    {
      "colId": "vehicleLastKnownLocation.building",
      "hide": false,
      "translation": "Gebäude"
    },
    {
      "colId": "vehicleLastKnownLocation.level",
      "hide": false,
      "translation": "Ebene"
    },
    {
      "colId": "vehicleLastKnownLocation.parkingLot",
      "hide": false,
      "translation": "Parkplatz"
    },
    {
      "colId": "vehicleLastKnownLocation.eventType",
      "hide": false,
      "translation": "Event",
      "enumValues": {
        "IN": "Eingang",
        "OUT": "Ausgang"
      }
    },
    {
      "colId": "vehicleLastKnownLocation.occurredOn",
      "hide": false,
      "translation": "Datum, Uhrzeit"
    },
    {
      "colId": "vehicleLastKnownLocation.comment",
      "hide": false,
      "translation": "Kommentar"
    },
    {
      "colId": "preDeliveryInspection.tireType",
      "hide": false,
      "translation": "Reifentyp"
    },
    {
      "colId": "preDeliveryInspection.isRelevant",
      "hide": false,
      "translation": "Ist relevant",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "preDeliveryInspection.foiling",
      "hide": false,
      "translation": "Folierung",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "preDeliveryInspection.refuel",
      "hide": false,
      "translation": "Auftanken",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "preDeliveryInspection.charge",
      "hide": false,
      "translation": "Aufladen",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "preDeliveryInspection.digitalLogbook",
      "hide": false,
      "translation": "Digitales Fahrtenbuch",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "preDeliveryInspection.licencePlateMounting",
      "hide": false,
      "translation": "KFZ-Kennzeichen montage",
      "enumValues": {
        "false": "Nein",
        "true": "Ja"
      }
    },
    {
      "colId": "fd1.column1",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 1"
    },
    {
      "colId": "fd1.column2",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 2"
    },
    {
      "colId": "fd1.column3",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 3"
    },
    {
      "colId": "fd1.column4",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 4"
    },
    {
      "colId": "fd1.column5",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 5"
    },
    {
      "colId": "fd1.column6",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 6"
    },
    {
      "colId": "fd1.column7",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 7"
    },
    {
      "colId": "fd1.column8",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 8"
    },
    {
      "colId": "fd1.column9",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 9"
    },
    {
      "colId": "fd1.column10",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 10"
    },
    {
      "colId": "fd1.column11",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 11"
    },
    {
      "colId": "fd1.column12",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 12"
    },
    {
      "colId": "fd1.column13",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 13"
    },
    {
      "colId": "fd1.column14",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 14"
    },
    {
      "colId": "fd1.column15",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 15"
    },
    {
      "colId": "fd1.column16",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 16"
    },
    {
      "colId": "fd1.column17",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 17"
    },
    {
      "colId": "fd1.column18",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 18"
    },
    {
      "colId": "fd1.column19",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 19"
    },
    {
      "colId": "fd1.column20",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 20"
    },
    {
      "colId": "fd1.column21",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 21"
    },
    {
      "colId": "fd1.column22",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 22"
    },
    {
      "colId": "fd1.column23",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 23"
    },
    {
      "colId": "fd1.column24",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 24"
    },
    {
      "colId": "fd1.column25",
      "hide": true,
      "translation": "Fake Dimension 1 - Column 25"
    },
    {
      "colId": "fd2.column1",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 1"
    },
    {
      "colId": "fd2.column2",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 2"
    },
    {
      "colId": "fd2.column3",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 3"
    },
    {
      "colId": "fd2.column4",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 4"
    },
    {
      "colId": "fd2.column5",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 5"
    },
    {
      "colId": "fd2.column6",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 6"
    },
    {
      "colId": "fd2.column7",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 7"
    },
    {
      "colId": "fd2.column8",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 8"
    },
    {
      "colId": "fd2.column9",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 9"
    },
    {
      "colId": "fd2.column10",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 10"
    },
    {
      "colId": "fd2.column11",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 11"
    },
    {
      "colId": "fd2.column12",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 12"
    },
    {
      "colId": "fd2.column13",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 13"
    },
    {
      "colId": "fd2.column14",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 14"
    },
    {
      "colId": "fd2.column15",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 15"
    },
    {
      "colId": "fd2.column16",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 16"
    },
    {
      "colId": "fd2.column17",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 17"
    },
    {
      "colId": "fd2.column18",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 18"
    },
    {
      "colId": "fd2.column19",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 19"
    },
    {
      "colId": "fd2.column20",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 20"
    },
    {
      "colId": "fd2.column21",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 21"
    },
    {
      "colId": "fd2.column22",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 22"
    },
    {
      "colId": "fd2.column23",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 23"
    },
    {
      "colId": "fd2.column24",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 24"
    },
    {
      "colId": "fd2.column25",
      "hide": true,
      "translation": "Fake Dimension 2 - Column 25"
    },
    {
      "colId": "fd3.column1",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 1"
    },
    {
      "colId": "fd3.column2",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 2"
    },
    {
      "colId": "fd3.column3",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 3"
    },
    {
      "colId": "fd3.column4",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 4"
    },
    {
      "colId": "fd3.column5",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 5"
    },
    {
      "colId": "fd3.column6",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 6"
    },
    {
      "colId": "fd3.column7",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 7"
    },
    {
      "colId": "fd3.column8",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 8"
    },
    {
      "colId": "fd3.column9",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 9"
    },
    {
      "colId": "fd3.column10",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 10"
    },
    {
      "colId": "fd3.column11",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 11"
    },
    {
      "colId": "fd3.column12",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 12"
    },
    {
      "colId": "fd3.column13",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 13"
    },
    {
      "colId": "fd3.column14",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 14"
    },
    {
      "colId": "fd3.column15",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 15"
    },
    {
      "colId": "fd3.column16",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 16"
    },
    {
      "colId": "fd3.column17",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 17"
    },
    {
      "colId": "fd3.column18",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 18"
    },
    {
      "colId": "fd3.column19",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 19"
    },
    {
      "colId": "fd3.column20",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 20"
    },
    {
      "colId": "fd3.column21",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 21"
    },
    {
      "colId": "fd3.column22",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 22"
    },
    {
      "colId": "fd3.column23",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 23"
    },
    {
      "colId": "fd3.column24",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 24"
    },
    {
      "colId": "fd3.column25",
      "hide": true,
      "translation": "Fake Dimension 3 - Column 25"
    },
    {
      "colId": "fd4.column1",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 1"
    },
    {
      "colId": "fd4.column2",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 2"
    },
    {
      "colId": "fd4.column3",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 3"
    },
    {
      "colId": "fd4.column4",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 4"
    },
    {
      "colId": "fd4.column5",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 5"
    },
    {
      "colId": "fd4.column6",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 6"
    },
    {
      "colId": "fd4.column7",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 7"
    },
    {
      "colId": "fd4.column8",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 8"
    },
    {
      "colId": "fd4.column9",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 9"
    },
    {
      "colId": "fd4.column10",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 10"
    },
    {
      "colId": "fd4.column11",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 11"
    },
    {
      "colId": "fd4.column12",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 12"
    },
    {
      "colId": "fd4.column13",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 13"
    },
    {
      "colId": "fd4.column14",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 14"
    },
    {
      "colId": "fd4.column15",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 15"
    },
    {
      "colId": "fd4.column16",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 16"
    },
    {
      "colId": "fd4.column17",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 17"
    },
    {
      "colId": "fd4.column18",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 18"
    },
    {
      "colId": "fd4.column19",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 19"
    },
    {
      "colId": "fd4.column20",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 20"
    },
    {
      "colId": "fd4.column21",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 21"
    },
    {
      "colId": "fd4.column22",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 22"
    },
    {
      "colId": "fd4.column23",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 23"
    },
    {
      "colId": "fd4.column24",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 24"
    },
    {
      "colId": "fd4.column25",
      "hide": true,
      "translation": "Fake Dimension 4 - Column 25"
    },
    {
      "colId": "fd5.column1",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 1"
    },
    {
      "colId": "fd5.column2",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 2"
    },
    {
      "colId": "fd5.column3",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 3"
    },
    {
      "colId": "fd5.column4",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 4"
    },
    {
      "colId": "fd5.column5",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 5"
    },
    {
      "colId": "fd5.column6",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 6"
    },
    {
      "colId": "fd5.column7",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 7"
    },
    {
      "colId": "fd5.column8",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 8"
    },
    {
      "colId": "fd5.column9",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 9"
    },
    {
      "colId": "fd5.column10",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 10"
    },
    {
      "colId": "fd5.column11",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 11"
    },
    {
      "colId": "fd5.column12",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 12"
    },
    {
      "colId": "fd5.column13",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 13"
    },
    {
      "colId": "fd5.column14",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 14"
    },
    {
      "colId": "fd5.column15",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 15"
    },
    {
      "colId": "fd5.column16",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 16"
    },
    {
      "colId": "fd5.column17",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 17"
    },
    {
      "colId": "fd5.column18",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 18"
    },
    {
      "colId": "fd5.column19",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 19"
    },
    {
      "colId": "fd5.column20",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 20"
    },
    {
      "colId": "fd5.column21",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 21"
    },
    {
      "colId": "fd5.column22",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 22"
    },
    {
      "colId": "fd5.column23",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 23"
    },
    {
      "colId": "fd5.column24",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 24"
    },
    {
      "colId": "fd5.column25",
      "hide": true,
      "translation": "Fake Dimension 5 - Column 25"
    },
    {
      "colId": "fd6.column1",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 1"
    },
    {
      "colId": "fd6.column2",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 2"
    },
    {
      "colId": "fd6.column3",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 3"
    },
    {
      "colId": "fd6.column4",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 4"
    },
    {
      "colId": "fd6.column5",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 5"
    },
    {
      "colId": "fd6.column6",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 6"
    },
    {
      "colId": "fd6.column7",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 7"
    },
    {
      "colId": "fd6.column8",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 8"
    },
    {
      "colId": "fd6.column9",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 9"
    },
    {
      "colId": "fd6.column10",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 10"
    },
    {
      "colId": "fd6.column11",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 11"
    },
    {
      "colId": "fd6.column12",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 12"
    },
    {
      "colId": "fd6.column13",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 13"
    },
    {
      "colId": "fd6.column14",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 14"
    },
    {
      "colId": "fd6.column15",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 15"
    },
    {
      "colId": "fd6.column16",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 16"
    },
    {
      "colId": "fd6.column17",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 17"
    },
    {
      "colId": "fd6.column18",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 18"
    },
    {
      "colId": "fd6.column19",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 19"
    },
    {
      "colId": "fd6.column20",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 20"
    },
    {
      "colId": "fd6.column21",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 21"
    },
    {
      "colId": "fd6.column22",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 22"
    },
    {
      "colId": "fd6.column23",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 23"
    },
    {
      "colId": "fd6.column24",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 24"
    },
    {
      "colId": "fd6.column25",
      "hide": true,
      "translation": "Fake Dimension 6 - Column 25"
    },
    {
      "colId": "fd7.column1",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 1"
    },
    {
      "colId": "fd7.column2",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 2"
    },
    {
      "colId": "fd7.column3",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 3"
    },
    {
      "colId": "fd7.column4",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 4"
    },
    {
      "colId": "fd7.column5",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 5"
    },
    {
      "colId": "fd7.column6",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 6"
    },
    {
      "colId": "fd7.column7",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 7"
    },
    {
      "colId": "fd7.column8",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 8"
    },
    {
      "colId": "fd7.column9",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 9"
    },
    {
      "colId": "fd7.column10",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 10"
    },
    {
      "colId": "fd7.column11",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 11"
    },
    {
      "colId": "fd7.column12",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 12"
    },
    {
      "colId": "fd7.column13",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 13"
    },
    {
      "colId": "fd7.column14",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 14"
    },
    {
      "colId": "fd7.column15",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 15"
    },
    {
      "colId": "fd7.column16",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 16"
    },
    {
      "colId": "fd7.column17",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 17"
    },
    {
      "colId": "fd7.column18",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 18"
    },
    {
      "colId": "fd7.column19",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 19"
    },
    {
      "colId": "fd7.column20",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 20"
    },
    {
      "colId": "fd7.column21",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 21"
    },
    {
      "colId": "fd7.column22",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 22"
    },
    {
      "colId": "fd7.column23",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 23"
    },
    {
      "colId": "fd7.column24",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 24"
    },
    {
      "colId": "fd7.column25",
      "hide": true,
      "translation": "Fake Dimension 7 - Column 25"
    },
    {
      "colId": "fd8.column1",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 1"
    },
    {
      "colId": "fd8.column2",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 2"
    },
    {
      "colId": "fd8.column3",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 3"
    },
    {
      "colId": "fd8.column4",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 4"
    },
    {
      "colId": "fd8.column5",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 5"
    },
    {
      "colId": "fd8.column6",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 6"
    },
    {
      "colId": "fd8.column7",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 7"
    },
    {
      "colId": "fd8.column8",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 8"
    },
    {
      "colId": "fd8.column9",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 9"
    },
    {
      "colId": "fd8.column10",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 10"
    },
    {
      "colId": "fd8.column11",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 11"
    },
    {
      "colId": "fd8.column12",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 12"
    },
    {
      "colId": "fd8.column13",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 13"
    },
    {
      "colId": "fd8.column14",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 14"
    },
    {
      "colId": "fd8.column15",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 15"
    },
    {
      "colId": "fd8.column16",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 16"
    },
    {
      "colId": "fd8.column17",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 17"
    },
    {
      "colId": "fd8.column18",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 18"
    },
    {
      "colId": "fd8.column19",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 19"
    },
    {
      "colId": "fd8.column20",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 20"
    },
    {
      "colId": "fd8.column21",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 21"
    },
    {
      "colId": "fd8.column22",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 22"
    },
    {
      "colId": "fd8.column23",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 23"
    },
    {
      "colId": "fd8.column24",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 24"
    },
    {
      "colId": "fd8.column25",
      "hide": true,
      "translation": "Fake Dimension 8 - Column 25"
    },
    {
      "colId": "fd9.column1",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 1"
    },
    {
      "colId": "fd9.column2",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 2"
    },
    {
      "colId": "fd9.column3",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 3"
    },
    {
      "colId": "fd9.column4",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 4"
    },
    {
      "colId": "fd9.column5",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 5"
    },
    {
      "colId": "fd9.column6",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 6"
    },
    {
      "colId": "fd9.column7",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 7"
    },
    {
      "colId": "fd9.column8",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 8"
    },
    {
      "colId": "fd9.column9",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 9"
    },
    {
      "colId": "fd9.column10",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 10"
    },
    {
      "colId": "fd9.column11",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 11"
    },
    {
      "colId": "fd9.column12",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 12"
    },
    {
      "colId": "fd9.column13",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 13"
    },
    {
      "colId": "fd9.column14",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 14"
    },
    {
      "colId": "fd9.column15",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 15"
    },
    {
      "colId": "fd9.column16",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 16"
    },
    {
      "colId": "fd9.column17",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 17"
    },
    {
      "colId": "fd9.column18",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 18"
    },
    {
      "colId": "fd9.column19",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 19"
    },
    {
      "colId": "fd9.column20",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 20"
    },
    {
      "colId": "fd9.column21",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 21"
    },
    {
      "colId": "fd9.column22",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 22"
    },
    {
      "colId": "fd9.column23",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 23"
    },
    {
      "colId": "fd9.column24",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 24"
    },
    {
      "colId": "fd9.column25",
      "hide": true,
      "translation": "Fake Dimension 9 - Column 25"
    },
    {
      "colId": "fd10.column1",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 1"
    },
    {
      "colId": "fd10.column2",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 2"
    },
    {
      "colId": "fd10.column3",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 3"
    },
    {
      "colId": "fd10.column4",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 4"
    },
    {
      "colId": "fd10.column5",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 5"
    },
    {
      "colId": "fd10.column6",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 6"
    },
    {
      "colId": "fd10.column7",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 7"
    },
    {
      "colId": "fd10.column8",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 8"
    },
    {
      "colId": "fd10.column9",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 9"
    },
    {
      "colId": "fd10.column10",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 10"
    },
    {
      "colId": "fd10.column11",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 11"
    },
    {
      "colId": "fd10.column12",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 12"
    },
    {
      "colId": "fd10.column13",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 13"
    },
    {
      "colId": "fd10.column14",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 14"
    },
    {
      "colId": "fd10.column15",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 15"
    },
    {
      "colId": "fd10.column16",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 16"
    },
    {
      "colId": "fd10.column17",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 17"
    },
    {
      "colId": "fd10.column18",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 18"
    },
    {
      "colId": "fd10.column19",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 19"
    },
    {
      "colId": "fd10.column20",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 20"
    },
    {
      "colId": "fd10.column21",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 21"
    },
    {
      "colId": "fd10.column22",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 22"
    },
    {
      "colId": "fd10.column23",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 23"
    },
    {
      "colId": "fd10.column24",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 24"
    },
    {
      "colId": "fd10.column25",
      "hide": true,
      "translation": "Fake Dimension 10 - Column 25"
    }
  ],
  "filterModel": {},
  "sortModel": [],
  "language": "de",
  "selectedRows": [
    {
      "vehicleData": {
        "id": "00012eb6-c60a-4b9c-ac67-53d136555a04",
        "vin": "WP1AA29Y4NDA00006",
        "vguid": "FwB7NY8js4BX0000ZIIsKm",
        "equiId": null,
        "source": null,
        "referenceId": null,
        "equipmentNumber": null,
        "modelDescription": "Cayenne",
        "driveType": null,
        "department": "Erprobung",
        "leasingType": "AF",
        "inEmbargo": false,
        "productId": "9Y0",
        "productCode": "FE3BA",
        "orderType": "9YAAA1",
        "tradingPartnerNumber": "1004000",
        "purposeOrderType": "PV",
        "importerShortName": "PIV",
        "commissionNumber": "006409",
        "invoiceNumber": "394727",
        "invoiceDate": "2022-11-25",
        "purchaseOrderDate": "2020-05-23",
        "requestedDeliveryDate": null,
        "deliveryType": "A",
        "primaryStatus": "FD05",
        "colorExterior": "A1A1",
        "colorExteriorDescription": "schwarz/schwarz",
        "colorInterior": "AM",
        "colorInteriorDescription": "Teillederausstattung in Serienfarbe, schwarz",
        "productionNumber": "3020231",
        "productionEndDate": "2021-04-30",
        "technicalModelYear": 2022,
        "factory": "0150",
        "factoryVW": null,
        "quoteMonth": 5,
        "quoteYear": 2021,
        "plannedProductionEndDate": "2021-05-03",
        "gearBoxClass": "T",
        "countryInfoBNRValue": "B36",
        "countryInfoCNRValue": "C33",
        "countryInfoCNRValueCountryDescription": "China",
        "pmpDataOdometer": null,
        "pmpDataTimestamp": null,
        "consumptionDataTypification": null,
        "preproductionVehicle": null,
        "blockedForSale": null,
        "scrapVehicle": null,
        "createdAt": null
      },
      "vehicleRegistration": {
        "id": 0,
        "vin": "WP1AA29Y4NDA00006",
        "testNumber": null,
        "storageLocation": null,
        "tsn": null,
        "hsn": null,
        "registrationType": null,
        "registrationDate": null,
        "firstRegistrationDate": null,
        "lastRegistrationDate": null,
        "lastDeRegistrationDate": null,
        "licencePlate": null,
        "sfme": null,
        "testVehicle": null,
        "registrationStatus": null,
        "version": 0
      },
      "vehicleLastKnownLocation": {
        "vehicleId": "00012eb6-c60a-4b9c-ac67-53d136555a04",
        "compoundName": null,
        "parkingLot": null,
        "building": null,
        "eventType": null,
        "occurredOn": null,
        "level": null,
        "comment": null
      },
      "fd1": {
        "column1": null,
        "column2": null,
        "column3": null,
        "column4": null,
        "column5": null,
        "column6": null,
        "column7": null,
        "column8": null,
        "column9": null,
        "column10": null,
        "column11": null,
        "column12": null,
        "column13": null,
        "column14": null,
        "column15": null,
        "column16": null,
        "column17": null,
        "column18": null,
        "column19": null,
        "column20": null,
        "column21": null,
        "column22": null,
        "column23": null,
        "column24": null,
        "column25": null
      },
      "fd2": {
        "column1": null,
        "column2": null,
        "column3": null,
        "column4": null,
        "column5": null,
        "column6": null,
        "column7": null,
        "column8": null,
        "column9": null,
        "column10": null,
        "column11": null,
        "column12": null,
        "column13": null,
        "column14": null,
        "column15": null,
        "column16": null,
        "column17": null,
        "column18": null,
        "column19": null,
        "column20": null,
        "column21": null,
        "column22": null,
        "column23": null,
        "column24": null,
        "column25": null
      },
      "fd3": {
        "column1": null,
        "column2": null,
        "column3": null,
        "column4": null,
        "column5": null,
        "column6": null,
        "column7": null,
        "column8": null,
        "column9": null,
        "column10": null,
        "column11": null,
        "column12": null,
        "column13": null,
        "column14": null,
        "column15": null,
        "column16": null,
        "column17": null,
        "column18": null,
        "column19": null,
        "column20": null,
        "column21": null,
        "column22": null,
        "column23": null,
        "column24": null,
        "column25": null
      },
      "fd4": {
        "column1": null,
        "column2": null,
        "column3": null,
        "column4": null,
        "column5": null,
        "column6": null,
        "column7": null,
        "column8": null,
        "column9": null,
        "column10": null,
        "column11": null,
        "column12": null,
        "column13": null,
        "column14": null,
        "column15": null,
        "column16": null,
        "column17": null,
        "column18": null,
        "column19": null,
        "column20": null,
        "column21": null,
        "column22": null,
        "column23": null,
        "column24": null,
        "column25": null
      },
      "fd5": {
        "column1": null,
        "column2": null,
        "column3": null,
        "column4": null,
        "column5": null,
        "column6": null,
        "column7": null,
        "column8": null,
        "column9": null,
        "column10": null,
        "column11": null,
        "column12": null,
        "column13": null,
        "column14": null,
        "column15": null,
        "column16": null,
        "column17": null,
        "column18": null,
        "column19": null,
        "column20": null,
        "column21": null,
        "column22": null,
        "column23": null,
        "column24": null,
        "column25": null
      },
      "fd6": {
        "column1": null,
        "column2": null,
        "column3": null,
        "column4": null,
        "column5": null,
        "column6": null,
        "column7": null,
        "column8": null,
        "column9": null,
        "column10": null,
        "column11": null,
        "column12": null,
        "column13": null,
        "column14": null,
        "column15": null,
        "column16": null,
        "column17": null,
        "column18": null,
        "column19": null,
        "column20": null,
        "column21": null,
        "column22": null,
        "column23": null,
        "column24": null,
        "column25": null
      },
      "fd7": {
        "column1": null,
        "column2": null,
        "column3": null,
        "column4": null,
        "column5": null,
        "column6": null,
        "column7": null,
        "column8": null,
        "column9": null,
        "column10": null,
        "column11": null,
        "column12": null,
        "column13": null,
        "column14": null,
        "column15": null,
        "column16": null,
        "column17": null,
        "column18": null,
        "column19": null,
        "column20": null,
        "column21": null,
        "column22": null,
        "column23": null,
        "column24": null,
        "column25": null
      },
      "fd8": {
        "column1": null,
        "column2": null,
        "column3": null,
        "column4": null,
        "column5": null,
        "column6": null,
        "column7": null,
        "column8": null,
        "column9": null,
        "column10": null,
        "column11": null,
        "column12": null,
        "column13": null,
        "column14": null,
        "column15": null,
        "column16": null,
        "column17": null,
        "column18": null,
        "column19": null,
        "column20": null,
        "column21": null,
        "column22": null,
        "column23": null,
        "column24": null,
        "column25": null
      },
      "fd9": {
        "column1": null,
        "column2": null,
        "column3": null,
        "column4": null,
        "column5": null,
        "column6": null,
        "column7": null,
        "column8": null,
        "column9": null,
        "column10": null,
        "column11": null,
        "column12": null,
        "column13": null,
        "column14": null,
        "column15": null,
        "column16": null,
        "column17": null,
        "column18": null,
        "column19": null,
        "column20": null,
        "column21": null,
        "column22": null,
        "column23": null,
        "column24": null,
        "column25": null
      }
    }
  ]
}

### Get all vehicle option tags
GET /api/vs/ui/vehicles/options/tags
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/response.json


### Export access control permissions to excel
POST /api/vs/ui/accesscontrol/export/b78d429b-c2e4-46ea-bed4-6b1c9ca6f1c0
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080

{
  "translations": {
    "vehicle": {
      "model": {
        "description": "Model Description"
      },
      "vin_one": "VIN",
      "vin_other": "VINs"
    }
  }
}

### Import access control permissions
POST /api/vs/ui/access-control/import/b78d429b-c2e4-46ea-bed4-6b1c9ca6f1c0
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: multipart/form-data; boundary=WebAppBoundary

--WebAppBoundary
Content-Disposition: form-data; name="file"; filename="validData.xlsx"; comment="test"

< ../../src/test/resources/accesscontrol/import-all-permissions.xlsx

###
