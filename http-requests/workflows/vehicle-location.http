### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
    client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
    client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

>> ./outputs/local/response.json

###
@vehicle_id = fde5feee-dc55-4d93-b47b-777f5d601673

### Get Location History
GET /api/vs/ui/vehicles/{{vehicle_id}}/location-history
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/response.json

### Get Known Compounds
GET /api/vs/ui/vehicles/locations
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/response.json

### Add Location Event
POST /api/vs/ui/vehicles/locations/event
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "vehicleId": "{{vehicle_id}}",
  "compoundName": "Zentrale Werkstätten Zuffenhausen - Parkhaus",
  "parkingLot": null,
  "building": "Bau 60",
  "eventType": "OUT",
  "occurredOn": "2024-10-07T16:08:00Z",
  "level": null,
  "source": "MANUAL"
}

>> ./outputs/local/response.json
###



