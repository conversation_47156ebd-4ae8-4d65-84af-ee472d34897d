### PPN IDP Token for C@P
POST https://{{ppn_tenant}}/as/token.oauth2
Content-Type: application/x-www-form-urlencoded

client_id = {{ppn_oauth_m2m_api_client_id}} &
client_secret = {{ppn_oauth_m2m_api_client_secret}} &
grant_type = client_credentials

> {%
    client.global.set("auth_token", response.body.access_token);
%}

>> ./outputs/response_ppn.json

### Get capVehicleGuid by commissionNumber
GET https://{{host_gateway}}/porsche-group/{{environment}}/crm/vehicle-relationship/VehicleDataSet?
    $filter=commissionNumber eq '{{commissionNumber}}'
Authorization: Bearer {{auth_token}}
X-Porsche-Client-Id: {{cap_ibm_api_gateway_client_id}}
X-Porsche-Client-Secret: {{cap_ibm_api_gateway_client_secret}}
region: {{region}}
accept: application/json
Content-Type: application/json

>> ./outputs/response_cap_vguid_html


### Get businessPartnerId by vguid
GET https://{{host_gateway}}/porsche-group/{{environment}}/crm/vehicle-relationship/VehicleRelationSet?
    $filter=vehicleGuid eq '{{capVehicleGuid}}'
Authorization: Bearer {{auth_token}}
X-Porsche-Client-Id: {{cap_ibm_api_gateway_client_id}}
X-Porsche-Client-Secret: {{cap_ibm_api_gateway_client_secret}}
region: {{region}}
accept: application/json
Content-Type: application/json

>> ./outputs/response_cap_bpid_html


### Get Employee Number by bpId
GET https://{{host_gateway}}/porsche-group/{{environment}}/crm/businesspartner/BpExternalIdSet?
    $filter=BusinessPartnerId eq '{{bpId}}'
Authorization: Bearer {{auth_token}}
X-Porsche-Client-Id: {{cap_ibm_api_gateway_client_id}}
X-Porsche-Client-Secret: {{cap_ibm_api_gateway_client_secret}}
region: {{region}}
accept: application/json
Content-Type: application/json

>> ./outputs/response_cap_employeeNumber_html
