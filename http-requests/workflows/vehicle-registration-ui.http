### Get all headers
GET {{alb_host}}/api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie-0}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}

> {%
    client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
    client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

>> ./outputs/local/response.json

### Get vehicle registration details
GET /api/vs/ui/vehicleregistration/orders
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/response.json

### Put vehicle registration details
PUT /api/vs/ui/vehicleregistration/orders
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "data": [
    {
      "vin": "WP1ZZZ9Y0RDA64263",
      "briefNumber": "HA055316",
      "driveType": null,
      "leasingType": null,
      "modelType": null,
      "department": null,
      "registrationOffice": null,
      "plannedLicencePlate": null,
      "fuelType": null,
      "plannedRegistrationDate": null,
      "commenter": null,
      "orderStatus": null,
      "storageLocation": null,
      "tsn": null,
      "hsn": null,
      "registrationType": 1,
      "registrationDate": "2024-02-15T00:00:00Z",
      "licencePlate": "BB-PS 948",
      "remark": null,
      "sfme": null,
      "testVehicle": null,
      "registrationStatus": null,
      "createdAt": null,
      "updatedAt": null
    }
  ],
  "errors": null
}

### Put vehicle registration details
PUT /api/vs/ui/vehicleregistration/orders
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "data": [
    {
      "vin": "WP1ZZZ9Y0RDA64263",
      "briefNumber": "HA055316",
      "driveType": null,
      "leasingType": null,
      "modelType": null,
      "department": null,
      "registrationOffice": null,
      "plannedLicencePlate": null,
      "fuelType": null,
      "plannedRegistrationDate": null,
      "commenter": null,
      "orderStatus": null,
      "storageLocation": null,
      "tsn": null,
      "hsn": null,
      "registrationType": 1,
      "registrationDate": "2024-02-15T00:00:00Z",
      "licencePlate": "BB-PS 948",
      "remark": null,
      "sfme": null,
      "testVehicle": null,
      "registrationStatus": null,
      "createdAt": null,
      "updatedAt": null
    }
  ],
  "errors": null
}



### Create vehicle registration
POST /api/vs/ui/vehicleregistration/orders
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

[{"vin":"bbb","briefNumber":null}]

### Search orders
POST /api/vs/ui/vehicleregistration/orders/search
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "startRow": 0,
  "endRow": 5,
  "rowGroupCols": [],
  "valueCols": [],
  "pivotCols": [],
  "pivotMode": false,
  "groupKeys": [],
  "filterModel": {
    "licencePlate": {
      "filterType": "text",
      "type": "contains",
      "filter": "948"
    },
    "registrationType": {
      "filterType": "number",
      "type": "equals",
      "filter": 1
    },
    "orderType": {
      "filterType": "number",
      "type": "equals",
      "filter": 1
    }
  },
  "sortModel": [],
  "columns": []
}
