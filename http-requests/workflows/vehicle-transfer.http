### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
    client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
    client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

>> ./outputs/local/response.json

### Create planned vehicle transfer
POST /api/vs/ui/vehicle-transfers/create
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "vehicleIds": ["3f2b9c8e-1a5d-4f7d-bc5a-8e7d1f6c9b24"]
}

>> ./outputs/local/vehicle-transfer/create-planned-vehicle-transfer.json

### Create planned vehicle transfer
POST /api/vs/ui/vehicle-transfers/search
Host: localhost:8080
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json
X-user-groups: b78d429b-c2e4-46ea-bed4-6b1c9ca6f1c0

{
  "startRow": 0,
  "endRow": 3,
  "rowGroupCols": [],
  "valueCols": [],
  "pivotCols": [],
  "pivotMode": false,
  "groupKeys": [],
  "filterModel": {},
  "sortModel": [],
  "columns": []
}
