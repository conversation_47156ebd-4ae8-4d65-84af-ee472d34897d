### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
  client.global.headers.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
  client.global.headers.set("data_token", response.body["x-amzn-oidc-data"]);
%}

>> ./outputs/local/response.json

### Create Vehicle Invoice
POST /api/vs/ui/vehicles/invoices
Host: localhost:8080
Content-Type: application/json

{
  "vehicleId": "002b872f-9a1d-4cfe-911f-535548ae5482",
  "salesDiscountPercentage": 10,
  "salesNetPriceEUR": 100000.00,
  "winterTiresDiscountPercentage": 5,
  "winterTiresNetPriceEUR": 2000,
  "winterTiresId": 1234,
  "salesDiscountType": "NO_DISCOUNT",
  "customerInvoiceRecipient": false,
  "paymentType": "LEASING",
  "customerPartnerNumber": "123456",
  "invoiceRecipientNumber": "654321",
  "salesPersonNumber": "P12345678"
}

>> ./outputs/local/response.json

### Get Vehicle Invoice
GET /api/vs/ui/vehicles/invoices
Host: localhost:8080
