### Get all headers
GET /api/vs/vehicles/albheaders
cookie: AWSELBAuthSessionCookie-0={{awselbauthsessioncookie}}; AWSELBAuthSessionCookie-1={{awselbauthsessioncookie-1}}
Host: {{alb_host}}

> {%
    client.global.set("access_token", response.body["x-amzn-oidc-accesstoken"]);
    client.global.set("data_token", response.body["x-amzn-oidc-data"]);
%}

### Get views by key
GET /api/vs/ui/views?key=vehicle_registration
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

### Get view
GET /api/vs/ui/views/1
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

### Create personal view
POST /api/vs/ui/views
Host: localhost:8080
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
accept: application/json
Content-Type: application/json

{
  "key": "new_created_view_key",
  "name": "Personal View",
  "description": "My personal created view",
  "isPublic": false,
  "isDefault": false,
  "filterState": "{}",
  "columnState": "{}"
}

### Update existing view
PATCH /api/vs/ui/views/1
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{
  "key": "vehicle_registration",
  "name": "Updated View 1",
  "description": "My personal created view",
  "isPublic": true,
  "isDefault": false,
  "filterState": "{}",
  "columnState": "{}"
}

### Delete view
DELETE /api/vs/ui/views/1
X-Amzn-Oidc-Accesstoken: {{access_token}}
X-Amzn-Oidc-Data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json
