### Get all winter tires timeframe
GET /api/vs/ui/predelivery-inspection/winter-tires-timeframe
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
Content-Type: application/json

>> ./outputs/local/response.json


### Update consignee data
PUT /api/vs/ui/predelivery-inspection/winter-tires-timeframe
x-amzn-oidc-accesstoken: {{access_token}}
x-amzn-oidc-data: {{data_token}}
Host: localhost:8080
accept: application/json
Content-Type: application/json

{"data":[{
  "id": "d4b07384-d9a1-4f1b-b6a3-6ed5b870b41b",
  "fromDate": "2027-11-02",
  "toDate": "2028-03-31",
  "version": 1
}
]}

