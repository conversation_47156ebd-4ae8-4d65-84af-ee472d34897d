# Load Testing

## How to run

Current the load-testing is being run manually, we should aim to make this an automated job.

### Steps

1. Log into the FVM website with your Azure credentials, and copy the `AWSELBAuthSessionCookie-0` cookie 
2. Copy the `AWSELBAuthSessionCookie-1` cookie also 
3. Run the following command
    ```sh
    export COOKIE1=<the value of the cookie from step 1>
    export COOKIE2=<the value of the cookie from step 2>
    locust -f vehicle-manager/vehicle-manager-filtering-and-sorting.py --headless --host https://vr.mobilityservicesdev.aws.platform.porsche-preview.cloud
    ```
4. Navigate to `http://localhost:8089` in your browser
5. Start a load-test with the desired number of users and a spawn rate.

### Notes 

1. Since this request goes through the UI load-balancer, errors on the server are translated to 200 due to the way cloudfront is configured.
2. This would mean that after the load-test, we must have a look at how many requests were successful / failed from X-Ray Traces
3. The load-test scripts can also assert on the response data to make sure that an expected response came back from the server

```sh
service(id(name: "vehicle-service" , type: "AWS::ECS::Fargate" )) http.url CONTAINS "/vehicles" fault
```


