import os
import random

from locust import HttpUser, task, between
from json import JSONDecodeError

session_cookie_1 = os.getenv('COOKIE1')

data = {
    "startRow":100,
    "endRow":150,
    "rowGroupCols":[],
    "valueCols":[],
    "pivotCols":[],
    "pivotMode": "false",
    "groupKeys":[],
    "filterModel":{},
    "sortModel":[]
}


data_with_sort = {
    "startRow":0,
    "endRow":50,
    "rowGroupCols":[],
    "valueCols":[],
    "pivotCols":[],
    "pivotMode": "false",
    "groupKeys":[],
    "filterModel": {
        "peopleData.city":{
            "filterType":"text",
            "type":"contains",
            "filter":"Winterbach"
        }
    },
    "sortModel":[{"sort":"asc","colId":"peopleData.city"}]
}

cookies = {
    'AWSELBAuthSessionCookie-0': session_cookie_1,
}

class DomainUser(HttpUser):

    @task
    def read_vehicle(self):
        with self.client.post(
                "/api/vs/ui/people/search",
                cookies=cookies,
                headers={
                    "x-trace-id": "locust-load-test-1-without-sort"
                },
                json=data,
                catch_response=True
        ) as response:
            if len(response.json()["data"]) != 50:
                response.failure("Did not get expected response")

    @task
    def read_vehicle_with_filter_and_sort(self):
        with self.client.post(
                "/api/vs/ui/people/search",
                cookies=cookies,
                headers={
                    "x-trace-id": "locust-load-test-1-with-sort"
                },
                json=data_with_sort,
                catch_response=True
        ) as response:
            if len(response.json()["data"]) != 30:
                response.failure("Did not get expected response")
