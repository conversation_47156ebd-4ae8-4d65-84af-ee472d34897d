# Load Testing 

## How to run 

Current the load-testing is being run manually, we should aim to make this an automated job. 

### Steps 

1. Log into to AWS Session Manager and start a session with a bastion machine
2. Ensure python, pip and the dependencies in `requirements.txt` are installed 
3. Run the following command 

```sh
export BEARER_TOKEN=<test_token_azure_for_vehicle_service>
locust -f read-vehicle-load-testing.py --headless -u 1000 -r 100 --host https://vr.mobilityservicesdev.aws.platform.porsche-preview.cloud
```

## Past runs 

### Scenario 1

#### Test parameters 
|                                                 |  | 
| --------------------------------------------------- | ---------- |
|**Number of vehicles:** | 60| 
| **Nature of vehicles:** |All vehicles are available on PVH, and are previously loaded into EMH Vehicle Service|
| **Testing usecase:** | Retrieval of vehicle-object through the GET endpoint continously at a high rate|  
| Duration of load | 5m |

#### Results 
A total of 46K requests were sent to the vehice-service in a span of 5 minutes.

95% of those calls had a 200 OK response within 180ms.

99% of those calls had a 200 OK response within 2600ms (2 sec)


### Scenario 2

#### Test parameters 
|                                                 |  | 
| --------------------------------------------------- | ---------- |
|**Number of vehicles:** | 360| 
| **Nature of vehicles:** |75% of the vehicles are available on PVH, 25% have invalid or missing data (test-vehicles). The vehicles are not loaded into the vehicle-service, so a "load-vehicle" use-case also happens|
| **Testing usecase:** | Retrieval of vehicle-object through the GET endpoint / Loading of missing vehicles continously at a high rate |  
| Duration of load | 5m |

#### Results 

A total of 23738  requests were fired in a span of 5 minutes.

50% of users got a response faster than 3200 ms

95% of users got a response faster than 5500 ms

99% of users got a response faster than 7300 ms.


### Follow ups

**Loading of a vehicle should be de-coupled from a user-request  
(reducing runtime dependency)**  

Vehicles must be preloaded into our system. There is already a story to integrate with PVH through streamzilla to where this is possible.

**Exception handling**   
- We can run a quick spike to see if the "exception driven flow" is reducing the throughput of the system. 

- How do we prevent multiple failing requests from bogging down the system?

### AWS dashboards 

https://eu-west-1.console.aws.amazon.com/ec2/home?region=eu-west-1#LoadBalancers:v=3;$case=tags:false%5C,client:false;$regex=tags:false%5C,client:false