import os
import random

from locust import HttpUser, task, between

bearer_token = os.getenv('BEARER_TOKEN')
vin_numbers = ["WP0ZZZ979PL122558", "WP0ZZZ979PL161408", "WP0ZZZ981RK200462", "WP0ZZZ990RS235194", "WP0ZZZ991RS200695",
              "WP0ZZZ994RS235196", "WP0ZZZ995RS200652", "WP0ZZZ997RS235192", "WP0ZZZ998RS241583", "WP0ZZZ999RS213341",
              "WP0ZZZ99XRS200713", "WP0ZZZY10RSA77292", "WP0ZZZY11PSA70400", "WP0ZZZY11RSA55933", "WP0ZZZY11RSA55947",
              "WP0ZZZY12RSA30863", "WP0ZZZY13RSA55920", "WP0ZZ<PERSON>Y15PSA82145", "WP0ZZZY17PSA44576", "WP0ZZZY19RSA77291",
              "WP0ZZZY1XRSA55946", "WP1ZZZ951RLB04684", "WP1ZZZ955RLB04686", "WP1ZZZ955RLB27689", "WP1ZZZ955RLB42788",
              "WP1ZZZ958RLB04729", "WP1ZZZ9Y1RDA20689", "WP1ZZZ9Y1RDA20742", "WP1ZZZ9Y1RDA61176", "WP1ZZZ9Y1RDA61243",
              "WP1ZZZ9Y2RDA20779", "WP1ZZZ9Y2RDA58965", "WP1ZZZ9Y2RDA61087", "WP1ZZZ9Y3RDA20399", "WP1ZZZ9Y3RDA20743",
              "WP1ZZZ9Y3RDA20841", "WP1ZZZ9Y4RDA20752", "WP1ZZZ9Y4RDA60796", "WP1ZZZ9Y4RDA60846", "WP1ZZZ9Y6RDA20736",
              "WP1ZZZ9Y6RDA20770", "WP1ZZZ9Y7RDA60890", "WP1ZZZ9Y7RDA61084", "WP1ZZZ9Y9RDA60924", "WP1ZZZ9YXRDA20741",
              "WP0ZZZ990RS230674", "WP0ZZZY11RSA30837", "WP0ZZZY14RSA62715", "WP0ZZZY15RSA01549", "WP0ZZZY18RSA77279",
              "WP0ZZZY19PSA44546", "WP1ZZZ9Y6RDA61013", "WP1ZZZ9Y8RDA20687", "WP1ZZZ9Y8RDA60946", "WP0ZZZ999RS241608",
              "WP0ZZZY1XRSA55932", "WP0ZZZ999RS235193", "WP0ZZZ975PL161406", "WP0ZZZ977PL161410", "WP0ZZZ979PL139134",
              "WP0ZZZ996RS213295", "WP0ZZZY15PSA82176", "WP1ZZZ9YXRDA60978", "WP0ZZZ993RS241619", "WP0ZZZ992RS213391",
              "WP0ZZZ994RS213392", "WP0ZZZY10RSA81584"]

vin_numbers_25_pct_failure_rate = ["WP0ZZZ979PL122558", "WP0ZZZ979PL161408", "WP0ZZZ981RK200462", "WP0ZZZ990RS235194", "WP0ZZZ991RS200695",
                                   "WP0ZZZ994RS235196", "WP0ZZZ995RS200652", "WP0ZZZ997RS235192", "WP0ZZZ998RS241583", "WP0ZZZ999RS213341",
                                   "WP0ZZZ99XRS200713", "WP0ZZZY10RSA77292", "WP0ZZZY11PSA70400", "WP0ZZZY11RSA55933", "WP0ZZZY11RSA55947",
                                   "WP0ZZZY12RSA30863", "WP0ZZZY13RSA55920", "WP0ZZZY15PSA82145", "WP0ZZZY17PSA44576", "WP0ZZZY19RSA77291",
                                   "WP0ZZZY1XRSA55946", "WP1ZZZ951RLB04684", "WP1ZZZ955RLB04686", "WP1ZZZ955RLB27689", "WP1ZZZ955RLB42788",
                                   "WP1ZZZ958RLB04729", "WP1ZZZ9Y1RDA20689", "WP1ZZZ9Y1RDA20742", "WP1ZZZ9Y1RDA61176", "WP1ZZZ9Y1RDA61243",
                                   "WP1ZZZ9Y2RDA20779", "WP1ZZZ9Y2RDA58965", "WP1ZZZ9Y2RDA61087", "WP1ZZZ9Y3RDA20399", "WP1ZZZ9Y3RDA20743",
                                   "WP1ZZZ9Y3RDA20841", "WP1ZZZ9Y4RDA20752", "WP1ZZZ9Y4RDA60796", "WP1ZZZ9Y4RDA60846", "WP1ZZZ9Y6RDA20736",
                                   "WP1ZZZ9Y6RDA20770", "WP1ZZZ9Y7RDA60890", "WP1ZZZ9Y7RDA61084", "WP1ZZZ9Y9RDA60924", "WP1ZZZ9YXRDA20741",
                                   "WP0ZZZ990RS230674", "WP0ZZZY11RSA30837", "WP0ZZZY14RSA62715", "WP0ZZZY15RSA01549", "WP0ZZZY18RSA77279",
                                   "WP0ZZZY19PSA44546", "WP1ZZZ9Y6RDA61013", "WP1ZZZ9Y8RDA20687", "WP1ZZZ9Y8RDA60946", "WP0ZZZ999RS241608",
                                   "WP0ZZZY1XRSA55932", "WP0ZZZ999RS235193", "WP0ZZZ975PL161406", "WP0ZZZ977PL161410", "WP0ZZZ979PL139134",
                                   "WP0ZZZ996RS213295", "WP0ZZZY15PSA82176", "WP1ZZZ9YXRDA60978", "WP0ZZZ993RS241619", "WP0ZZZ992RS213391",
                                   "WP0ZZZ994RS213392", "WP0ZZZY10RSA81584", "WP0AF2A79PL138011", "WP0ZZZ97ZML189014", "WP0ZZZ97ZNL125049",
                                   "WP0ZZZ97ZNL125529", "WP0ZZZ97ZNL130005", "WP0ZZZ97ZNL132008", "WP0ZZZ97ZNL132009", "WP0ZZZ97ZNL135008",
                                   "WP0ZZZ97ZNL140003", "WP0ZZZ97ZNL140004", "WP0ZZZ97ZNL142004", "WP0ZZZ97ZNL164002", "WP0ZZZ97ZNL169003",
                                   "WP0ZZZ97ZNL171001", "WP0ZZZ97ZNL173002", "WP0ZZZ97ZNL175002", "WP0ZZZ97ZNL177004", "WP0ZZZ980RS232044",
                                   "WP0ZZZ982RK232031", "WP0ZZZ982RS232045", "WP0ZZZ983PS250003", "WP0ZZZ983RS232037", "WP0ZZZ984PS200002",
                                   "WP0ZZZ984RS232046", "WP0ZZZ985PS250004", "WP0ZZZ985RS232038", "WP0ZZZ986PS200003", "WP0ZZZ986RS232047",
                                   "WP0ZZZ987PS250005", "WP0ZZZ987RS232042", "WP0ZZZ987RS272007", "WP0ZZZ988PS200004", "WP0ZZZ988PS273003",
                                   "WP0ZZZ989RS272008", "WP0ZZZ98ZLK283028", "WP0ZZZ98ZLK283809", "WP0ZZZ98ZLS201901", "WP0ZZZ98ZLS220653",
                                   "WP0ZZZ98ZLS235114", "WP0ZZZ98ZLS250945", "WP0ZZZ98ZLS270260", "WP0ZZZ98ZMS200730", "WP0ZZZ98ZMS230787",
                                   "WP0ZZZ98ZMS235828", "WP0ZZZ98ZMS250550", "WP0ZZZ98ZMS279846", "WP0ZZZ98ZMS279857", "WP0ZZZ98ZNS200990",
                                   "WP0ZZZ98ZNS220120", "WP0ZZZ98ZNS223273", "WP0ZZZ98ZNS223281", "WP0ZZZ98ZNS233510", "WP0ZZZ98ZNS250708",
                                   "WP0ZZZ98ZNS262145", "WP0ZZZ98ZNS264664", "WP0ZZZ98ZNS264671", "WP0ZZZ98ZNS272316", "WP0ZZZ98ZNS277034",
                                   "WP0ZZZ98ZNS277035", "WP0ZZZ98ZNS277036", "WP0ZZZ98ZNS277037", "WP0ZZZ990PS200006", "WP0ZZZ990PS229179",
                                   "WP0ZZZ990PS240229", "WP0ZZZ991PS210009", "WP0ZZZ991PS266242", "WP0ZZZ991PS273045", "WP0ZZZ991RS264025",
                                   "WP0ZZZ992PS253774", "WP0ZZZ992PS260689", "WP0ZZZ992PS273040", "WP0ZZZ993PS210335", "WP0ZZZ993PS240287",
                                   "WP0ZZZ993PS260670", "WP0ZZZ993PS265559", "WP0ZZZ993PS273046", "WP0ZZZ994PS200008", "WP0ZZZ994PS225054",
                                   "WP0ZZZ994PS253775", "WP0ZZZ994PS273041", "WP0ZZZ994RS200061", "WP0ZZZ995PS200003", "WP0ZZZ995PS210465",
                                   "WP0ZZZ995PS273050", "WP0ZZZ995RS264030", "WP0ZZZ996PS200009", "WP0ZZZ996PS225055", "WP0ZZZ996PS273039",
                                   "WP0ZZZ996RS248001", "WP0ZZZ996RS264022", "WP0ZZZ997PS200004", "WP0ZZZ997PS200536", "WP0ZZZ997PS225050",
                                   "WP0ZZZ997PS266052", "WP0ZZZ998RS248002", "WP0ZZZ999PS200005", "WP0ZZZ999PS225048", "WP0ZZZ999PS273049",
                                   "WP0ZZZ999RS264029", "WP0ZZZ999RS264032", "WP0ZZZ99XPS210364", "WP0ZZZ99XPS210431", "WP0ZZZ99XPS225060",
                                   "WP0ZZZ99XPS253344", "WP0ZZZ99XPS266241", "WP0ZZZ99XPS273044", "WP0ZZZ99XRS264024", "WP0ZZZ99ZLS212296",
                                   "WP0ZZZ99ZLS212297", "WP0ZZZ99ZLS212301", "WP0ZZZ99ZLS212302", "WP0ZZZ99ZLS212305", "WP0ZZZ99ZLS212314",
                                   "WP0ZZZ99ZLS212325", "WP0ZZZ99ZLS212327", "WP0ZZZ99ZLS212336", "WP0ZZZ99ZLS212338", "WP0ZZZ99ZLS212347",
                                   "WP0ZZZ99ZLS212349", "WP0ZZZ99ZLS212355", "WP0ZZZ99ZLS212364", "WP0ZZZ99ZLS212368", "WP0ZZZ99ZLS212369",
                                   "WP0ZZZ99ZLS212372", "WP0ZZZ99ZLS212373", "WP0ZZZ99ZLS212375", "WP0ZZZ99ZLS212422", "WP0ZZZ99ZLS269091",
                                   "WP0ZZZ99ZLS269093", "WP0ZZZ99ZLS269095", "WP0ZZZ99ZLS269096", "WP0ZZZ99ZLS269099", "WP0ZZZ99ZLS269100",
                                   "WP0ZZZ99ZLS269104", "WP0ZZZ99ZLS269114", "WP0ZZZ99ZLS277082", "WP0ZZZ99ZLS277086", "WP0ZZZ99ZLS277089",
                                   "WP0ZZZ99ZLS277090", "WP0ZZZ99ZLS277095", "WP0ZZZ99ZLS282352", "WP0ZZZ99ZLS282356", "WP0ZZZ99ZMS213812",
                                   "WP0ZZZ99ZMS231086", "WP0ZZZ99ZMS253449", "WP0ZZZ99ZMS253454", "WP0ZZZ99ZMS253456", "WP0ZZZ99ZMS253506",
                                   "WP0ZZZ99ZMS261323", "WP0ZZZ99ZMS266089", "WP0ZZZ99ZMS266091", "WP0ZZZ99ZMS266093", "WP0ZZZ99ZMS266096",
                                   "WP0ZZZ99ZNS200074", "WP0ZZZ99ZNS201124", "WP0ZZZ99ZNS210074", "WP0ZZZ99ZNS210084", "WP0ZZZ99ZNS210099",
                                   "WP0ZZZ99ZNS210109", "WP0ZZZ99ZNS210126", "WP0ZZZ99ZNS210127", "WP0ZZZ99ZNS210134", "WP0ZZZ99ZNS210152",
                                   "WP0ZZZ99ZNS210184", "WP0ZZZ99ZNS210455", "WP0ZZZ99ZNS211461", "WP0ZZZ99ZNS211545", "WP0ZZZ99ZNS211546",
                                   "WP0ZZZ99ZNS211584", "WP0ZZZ99ZNS227075", "WP0ZZZ99ZNS227110", "WP0ZZZ99ZNS227125", "WP0ZZZ99ZNS228134",
                                   "WP0ZZZ99ZNS233069", "WP0ZZZ99ZNS238078", "WP0ZZZ99ZNS238090", "WP0ZZZ99ZNS238098", "WP0ZZZ99ZNS238107",
                                   "WP0ZZZ99ZNS238914", "WP0ZZZ99ZNS238930", "WP0ZZZ99ZNS248033", "WP0ZZZ99ZNS248034", "WP0ZZZ99ZNS250094",
                                   "WP0ZZZ99ZNS250105", "WP0ZZZ99ZNS250106", "WP0ZZZ99ZNS250117", "WP0ZZZ99ZNS251262", "WP0ZZZ99ZNS251263",
                                   "WP0ZZZ99ZNS257078", "WP0ZZZ99ZNS257097", "WP0ZZZ99ZNS257105", "WP0ZZZ99ZNS262068", "WP0ZZZ99ZNS262070",
                                   "WP0ZZZ99ZNS262071", "WP0ZZZY10PSA50011", "WP0ZZZY10PSA55032", "WP0ZZZY10PSA69013", "WP0ZZZY10PSA86006",
                                   "WP0ZZZY11PSA43018", "WP0ZZZY11PSA66007", "WP0ZZZY11PSA77010", "WP0ZZZY12PSA30018", "WP0ZZZY12PSA50012",
                                   "WP0ZZZY12PSA55033", "WP0ZZZY12PSA69014", "WP0ZZZY12PSA86007", "WP0ZZZY13PSA43019", "WP0ZZZY13PSA66008",
                                   "WP0ZZZY13PSA77011", "WP0ZZZY14PSA30019", "WP0ZZZY14PSA55034", "WP0ZZZY14PSA62016", "WP0ZZZY16PSA00035",
                                   "WP0ZZZY16PSA62017", "WP0ZZZY17PSA81014", "WP0ZZZY18PSA00036", "WP0ZZZY19PSA50010", "WP0ZZZY19PSA81015",
                                   "WP0ZZZY19PSA86005", "WP0ZZZY1XPSA66006", "WP0ZZZY1ZLSA35473", "WP0ZZZY1ZLSA37565", "WP0ZZZY1ZLSA66159",
                                   "WP0ZZZY1ZLSA67770", "WP0ZZZY1ZMSA02329", "WP0ZZZY1ZMSA29965", "WP0ZZZY1ZMSA30004", "WP0ZZZY1ZMSA57254",
                                   "WP0ZZZY1ZMSA57465", "WP0ZZZY1ZMSA57468", "WP0ZZZY1ZNSA57090", "WP0ZZZY1ZNSA82087", "WP0ZZZYA0RL000168",
                                   "WP0ZZZYA1RL000163", "WP0ZZZYA2RL000169", "WP0ZZZYA3RL000164", "WP0ZZZYA5RL000165", "WP0ZZZYA7RL000166",
                                   "WP0ZZZYA8RL000161", "WP0ZZZYA9RL000167", "WP0ZZZYA9RL000170", "WP1ZZZ951PLB43174", "WP1ZZZ955PLB28435",
                                   "WP1ZZZ95XPLB43173", "WP1ZZZ95ZLLB70080", "WP1ZZZ95ZNLB00040", "WP1ZZZ95ZNLB00043", "WP1ZZZ95ZNLB00045",
                                   "WP1ZZZ95ZNLB06266", "WP1ZZZ95ZNLB06267", "WP1ZZZ95ZNLB06268", "WP1ZZZ95ZNLB06269", "WP1ZZZ95ZNLB06270",
                                   "WP1ZZZ95ZNLB06271", "WP1ZZZ95ZNLB30038", "WP1ZZZ95ZNLB30042", "WP1ZZZ95ZNLB30045", "WP1ZZZ95ZNLB45032",
                                   "WP1ZZZ95ZNLB45046", "WP1ZZZ95ZNLB45049", "WP1ZZZ95ZNLB45053", "WP1ZZZ9Y0RDA86022", "WP1ZZZ9Y1RDA17064",
                                   "WP1ZZZ9Y1RDA43048", "WP1ZZZ9Y1RDA73067", "WP1ZZZ9Y2RDA73031", "WP1ZZZ9Y2RDA86023", "WP1ZZZ9Y3RDA17065",
                                   "WP1ZZZ9Y3RDA43049", "WP1ZZZ9Y3RDA73071", "WP1ZZZ9Y3RDA75015", "WP1ZZZ9Y4RDA86024", "WP1ZZZ9Y5RDA17066",
                                   "WP1ZZZ9Y5RDA32067", "WP1ZZZ9Y5RDA35020", "WP1ZZZ9Y5RDA75016", "WP1ZZZ9Y6RDA00082", "WP1ZZZ9Y6RDA32045",
                                   "WP1ZZZ9Y6RDA56040", "WP1ZZZ9Y7RDA17067", "WP1ZZZ9Y7RDA32040", "WP1ZZZ9Y7RDA32068", "WP1ZZZ9Y7RDA35021",
                                   "WP1ZZZ9Y7RDA45029", "WP1ZZZ9Y7RDA86020", "WP1ZZZ9Y8RDA45041", "WP1ZZZ9Y8RDA56038", "WP1ZZZ9Y8RDA56041",
                                   "WP1ZZZ9Y8RDA83031", "WP1ZZZ9Y9RDA00058", "WP1ZZZ9Y9RDA35019", "WP1ZZZ9Y9RDA86021", "WP1ZZZ9YXRDA43047",
                                   "WP1ZZZ9YXRDA56039", "WP1ZZZ9YXRDA73066", "WP1ZZZ9YXRDA83032", "WP1ZZZ9YZLDA05627", "WP1ZZZ9YZLDA25749",
                                   "WP1ZZZ9YZLDA37100", "WP1ZZZ9YZLDA48566", "WP1ZZZ9YZNDA00019", "WP1ZZZ9YZNDA13035", "WP1ZZZ9YZNDA27005",
                                   "WP1ZZZ9YZNDA32002", "WP1ZZZ9YZNDA32003", "WP1ZZZ9YZNDA36027", "WP1ZZZ9YZNDA36028", "WP1ZZZ9YZNDA38030",
                                   "WP1ZZZ9YZNDA40012", "WP1ZZZ9YZNDA45016", "WP1ZZZ9YZNDA49323", "WP1ZZZ9YZNDA52002", "WP1ZZZ9YZNDA55005",
                                   "WP1ZZZ9YZNDA55006", "WP1ZZZ9YZNDA57009", "WP1ZZZ9YZNDA58011",
                                   ]


class DomainUser(HttpUser):
    wait_time = between(1, 5)

    @task
    def read_vehicle(self):
        vin = random.choice(vin_numbers)
        self.client.get(f"/api/vs/vehicles/vin/{vin}", headers={
            "Authorization": f"Bearer {bearer_token}"})
