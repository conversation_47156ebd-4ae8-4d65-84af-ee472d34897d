# PACT Contract Testing 
> **Intended Audience**
> 1. You would like to consume the `Vehicle Object` through the `vehicle-service` synchronous API
>  
> 
> 2. You would like to learn about PACT use in the `vehicle-service`

## Why do we use PACT 
### Guarantee API behavior for consumers

Ensure consumers of the vehicle-object can safely use our API with a guarantee that the API behaves as expected

### Avoid API versioning

Allows the maintainers of the `vehicle-service` to safely rename / remove unused-fields from the API format **without having to do API versioning**

This allows for lesser code for the team maintaining the `vehicle-service`.

### For evolutionary API strategy

We would like to know how exactly are the our consumers using the `vehicle-object` (which part of the `vehicle-object` does each consumer need). 

This allows us to build optimization (eg: caching) to have better API SLAs. PACT enables us to understand our consumer's dependencies and allows us to change our strategy to serve consumers better. 

## How to use PACT in `vehicle-service`
### Required reading 
1. [Pact Concepts](https://docs.pact.io/getting_started/conceptual_overview)
2. [Pact Terminology](https://docs.pact.io/getting_started/terminology)

### Our setup 
Online documentation can be vast since there are many ways to set up PACT in a project (hence we ask to read the above two links well, and can defer other documentation for the time being)

We tried to make the setup as simple as possible at the beginning. We (the team maintaining the `vehicle-service`) would also like to gauge PACT is something that the ART can use and benefit from, and then move on to more serious PACT implementations.

1. **No use of PACT Broker**
   https://skyway.porsche.com/confluence/display/FPT3/%5BProposal%5D+Use+of+PACT+to+enforce+contracts  

2. **We use PACT V3**  
     This is an older version, but it is also the one with the most amount of online documentation - as such we decided to use this version until the documentation catches up for V4. 

With both these decisions, we narrow down the cognitive load for most teams when using PACT.

### Step by Step Guide for Consumers 

**Workshop Format**  
https://github.com/pact-foundation/pact-workshop-Maven-Springboot-JUnit5

**The Quick and Dirty Approach**  
As a consumer who would like to consume the `vehicle-object` through the API endpoint 

1. Create an API client in your consuming backend service that will call the `vehicle-service` API


2. Write a Pact (Consumer) Test for this API client in your backend service  
  https://github.com/pact-foundation/pact-workshop-Maven-Springboot-JUnit5/tree/step3#step-3---pact-to-the-rescue
  

3. Share the generated contract file when the consumer-test runs (refer to the documentation above to see where they can be found) with the `vehicle-service` team. This can be done by opening a new Merge-Request in the `vehicle-service` with you contract copied to the `pacts\provider` folder at the root level.


4. Inform a team-member of the new contract. The `vehicle-service` maintainer may suggest alternate field-names or a different structure depending on the requirement - however - this should then enable you to update your contract to how it should look like. 


5. Continue development on your consuming backend application (maybe using a feature-toggle, disable the feature requiring the new updated API, so that you can do trunk-based-development) until the `vehicle-service` is updated to satisfy the contract (as a consumer you can track this by getting notified when the merge-request you had opened have been merged) 


## What if you don't use Pact when consuming the `vehicle-object`

As maintainers of the `vehicle-object` we would not be able to provide any guarantees that a future update of the `vehicle-object` will not break your applications.


## Examples 

We have one working implementation of Pact which you can find here for reference: 

### Consumer Test 
https://cicd.skyway.porsche.com/FP20/fleet-management/vehicle-registration-server/-/blob/main/src/test/kotlin/com/fleetmanagement/vehicleregistration/server/client/VehicleServiceClientConsumerPact.kt

### Provider Test
https://cicd.skyway.porsche.com/FP20/fleet-management/myride/-/blob/main/src/test/kotlin/com/fleetmanagement/modules/vehicledata/internal/controller/VehicleReadPactVerificationTest.kt
