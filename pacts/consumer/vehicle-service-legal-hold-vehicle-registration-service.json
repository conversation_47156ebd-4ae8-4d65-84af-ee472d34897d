{"consumer": {"name": "vehicle-service-legal-hold"}, "interactions": [{"description": "Create LegalHold", "providerStates": [{"name": "LegalHold created on vehicles [888da9c6-f9af-4c59-8bd4-8ac18badfd23, a696f1ec-d0a6-4f62-9d5b-5701e2d10d88]"}], "request": {"body": {"legalHoldId": "courtReferenceId", "lockId": "test", "vehicleIds": ["888da9c6-f9af-4c59-8bd4-8ac18badfd23", "a696f1ec-d0a6-4f62-9d5b-5701e2d10d88"]}, "headers": {"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************.dw1oz77jAV2AUdIv7MAiarGl1EmVM8HGJUPxCaC5GUyD6VLp3c8K58fbgOPnslpgDf8wmgiwr2KzgPPNXCX5ebxz3b_q-09fHirXn_8fhUV2GAbcvgW9aCL8LxmUH-zLbyBYWcdc-GGFucOVNCB7uP-nWHgjim7BLiyUn1XwRuJhZTaZtMGnAgZ8oTw83yznLFdjpZBD9NUGE_m_FlGT_7559ixUk1jQVPkDjRZldQWwjSSzHVwLpQXHgCoHhWhRykmTgyg8KERtwywvBJikQABOEYw592uP2cWl023g3reZu8xl-17ojSFUplz2J4zqMhqZptP3z7kpe0C_SQQlNw", "Content-Type": "application/json"}, "matchingRules": {"body": {"$.legalHoldId": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.lockId": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.vehicleIds[0]": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.vehicleIds[1]": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer (eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+)"}]}}}, "method": "POST", "path": "/private/legal-hold/registration-orders"}, "response": {"body": {"keys": ["courtReferenceId-888da9c6-f9af-4c59-8bd4-8ac18badfd23-test", "courtReferenceId-a696f1ec-d0a6-4f62-9d5b-5701e2d10d88-test"]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.keys[0]": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.keys[1]": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}}}, "status": 200}}, {"description": "Release LegalHold", "providerStates": [{"name": "LegalHold released on vehicles [888da9c6-f9af-4c59-8bd4-8ac18badfd23, a696f1ec-d0a6-4f62-9d5b-5701e2d10d88]"}], "request": {"body": {"keys": ["courtReferenceId-888da9c6-f9af-4c59-8bd4-8ac18badfd23-test", "courtReferenceId-a696f1ec-d0a6-4f62-9d5b-5701e2d10d88-test"]}, "headers": {"Authorization": "Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.***************************.dw1oz77jAV2AUdIv7MAiarGl1EmVM8HGJUPxCaC5GUyD6VLp3c8K58fbgOPnslpgDf8wmgiwr2KzgPPNXCX5ebxz3b_q-09fHirXn_8fhUV2GAbcvgW9aCL8LxmUH-zLbyBYWcdc-GGFucOVNCB7uP-nWHgjim7BLiyUn1XwRuJhZTaZtMGnAgZ8oTw83yznLFdjpZBD9NUGE_m_FlGT_7559ixUk1jQVPkDjRZldQWwjSSzHVwLpQXHgCoHhWhRykmTgyg8KERtwywvBJikQABOEYw592uP2cWl023g3reZu8xl-17ojSFUplz2J4zqMhqZptP3z7kpe0C_SQQlNw", "Content-Type": "application/json"}, "matchingRules": {"body": {"$.keys[0]": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.keys[1]": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "regex", "regex": "Bearer (eyJ[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+\\.[a-zA-Z0-9-_]+)"}]}}}, "method": "POST", "path": "/private/legal-hold/release/registration-orders"}, "response": {"body": {"keys": ["courtReferenceId-888da9c6-f9af-4c59-8bd4-8ac18badfd23-test", "courtReferenceId-a696f1ec-d0a6-4f62-9d5b-5701e2d10d88-test"]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.keys[0]": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.keys[1]": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}}}, "status": 200}}], "metadata": {"pact-jvm": {"version": "4.6.17"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-registration-service"}}