{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "A request for listing the access control history", "providerStates": [{"name": "vehicle service provides access control history to an admin user"}], "request": {"method": "GET", "path": "/vs/ui/access-control-history"}, "response": {"body": {"data": [{"comment": "Initial import for authorized group", "description": "Authorized Group 1", "displayName": "Group 1", "groupId": "550e8400-e29b-41d4-a716-446655440000", "id": 1, "uploadedAt": "2024-01-01T10:00:00Z", "uploadedBy": "<PERSON> (FDC3)"}, {"comment": "Initial import for auditing group", "description": "Auditing Group 2", "displayName": "Auditors Externa", "groupId": "6ba7b810-9dad-11d1-80b4-00c04fd430c8", "id": 2, "uploadedAt": "2024-01-01T10:00:00Z", "uploadedBy": "<PERSON> (FDC3)"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].displayName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].groupId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}]}, "$.data[0].id": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[0].uploadedAt": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$"}]}, "$.data[0].uploadedBy": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].displayName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].groupId": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}]}, "$.data[1].id": {"combine": "AND", "matchers": [{"match": "integer"}]}, "$.data[1].uploadedAt": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$"}]}, "$.data[1].uploadedBy": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "access-control"}}