{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request for creation of consignee data", "providerStates": [{"name": "admin can create consignee data"}], "request": {"body": {"consignee": "0QRswqn12aQ", "depreciationRelevantCostCenterId": "8276a6eb-6f45-42b1-9452-56bb61be4676", "description": "0eXBqWG", "internalContactPerson": "ZTVIbnxDqeGUP", "internalOrderNumber": "ts4WaENv64dazuC9i9Te", "isBlockedForSale": true, "isPreproductionVehicle": true, "isScrapped": true, "manufacturer": "s1XeT", "maximumServiceLifeInMonths": 1, "pdiCurrentTireSet": "WR", "pdiDigitalLogbook": false, "pdiFoiling": true, "pdiLicensePlateMounting": false, "pdiRecharge": true, "pdiRefuel": false, "pdiRelevant": false, "usageGroupId": "76bb73d2-19c2-45a9-9bc1-cc6e7663a8ff", "usingCostCenter": "HwqAK", "validationOfLeasingPrivileges": "A1bAW4", "vehicleResponsiblePerson": "j1nMllCgeWp", "vehicleType": "PKW", "vehicleUsageId": "a2de1023-3b08-4e38-b287-594904adf9f1"}, "headers": {"Authorization": "Bearer .*", "Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.consignee": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.depreciationRelevantCostCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.internalContactPerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.internalOrderNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.isBlockedForSale": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.isPreproductionVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.isScrapped": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.maximumServiceLifeInMonths": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.pdiCurrentTireSet": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.pdiDigitalLogbook": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.pdiFoiling": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.pdiLicensePlateMounting": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.pdiRecharge": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.pdiRefuel": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.pdiRelevant": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.usageGroupId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.usingCostCenter": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.validationOfLeasingPrivileges": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleResponsiblePerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleUsageId": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/vs/ui/vehicle-transfer-maintenance/consignee-data"}, "response": {"body": {"data": {"consignee": "0QRswqn12aQ", "depreciationRelevantCostCenter": {"costCenterId": "0QRswqn12aQ", "description": "0eXBqWG", "id": "8276a6eb-6f45-42b1-9452-56bb61be4676", "vehicleTypes": ["PKW"], "vehicleUsageIds": ["8276a6eb-6f45-42b1-9452-56bb61be4676"], "version": 0}, "description": "0eXBqWG", "id": "70bee60b-5b8b-41d8-afc5-56e69e09d466", "internalContactPerson": "ZTVIbnxDqeGUP", "internalOrderNumber": "ts4WaENv64dazuC9i9Te", "isBlockedForSale": true, "isPreproductionVehicle": true, "isScrapped": true, "manufacturer": "s1XeT", "maximumServiceLifeInMonths": 1, "pdiCurrentTireSet": "WR", "pdiDigitalLogbook": false, "pdiFoiling": true, "pdiLicensePlateMounting": false, "pdiRecharge": true, "pdiRefuel": false, "pdiRelevant": false, "usageGroup": {"description": "0eXBqWG", "id": "76bb73d2-19c2-45a9-9bc1-cc6e7663a8ff", "usageGroupId": 5, "version": 0}, "usingCostCenter": "HwqAK", "validationOfLeasingPrivileges": "A1bAW4", "vehicleResponsiblePerson": "j1nMllCgeWp", "vehicleType": "PKW", "vehicleUsage": {"id": "a2de1023-3b08-4e38-b287-594904adf9f1", "usage": "0eXBqWG", "usageId": 4, "version": 0}, "version": 0}, "errors": null, "warnings": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.consignee": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.depreciationRelevantCostCenter.costCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.depreciationRelevantCostCenter.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.depreciationRelevantCostCenter.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.depreciationRelevantCostCenter.vehicleTypes": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data.depreciationRelevantCostCenter.vehicleUsageIds": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data.depreciationRelevantCostCenter.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.internalContactPerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.internalOrderNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.isBlockedForSale": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.isPreproductionVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.isScrapped": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.maximumServiceLifeInMonths": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.pdiCurrentTireSet": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.pdiDigitalLogbook": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.pdiFoiling": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.pdiLicensePlateMounting": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.pdiRecharge": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.pdiRefuel": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.pdiRelevant": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.usageGroup.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.usageGroup.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.usageGroup.usageGroupId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.usageGroup.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.usingCostCenter": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.validationOfLeasingPrivileges": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleResponsiblePerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleUsage.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleUsage.usage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleUsage.usageId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleUsage.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request for delete of consignee data", "providerStates": [{"name": "admin can delete consignee data"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "DELETE", "path": "/vs/ui/vehicle-transfer-maintenance/consignee-data/e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}, "response": {"status": 200}}, {"description": "a request for retrieval of consignee data", "providerStates": [{"name": "consignee data exists"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/vs/ui/vehicle-transfer-maintenance/consignee-data"}, "response": {"body": {"data": [{"consignee": "0QRswqn12aQ", "depreciationRelevantCostCenter": {"costCenterId": "0QRswqn12aQ", "description": "0eXBqWG", "id": "8276a6eb-6f45-42b1-9452-56bb61be4676", "vehicleTypes": ["PKW"], "vehicleUsageIds": ["8276a6eb-6f45-42b1-9452-56bb61be4676"], "version": 0}, "description": "0eXBqWG", "id": "70bee60b-5b8b-41d8-afc5-56e69e09d466", "internalContactPerson": "ZTVIbnxDqeGUP", "internalOrderNumber": "ts4WaENv64dazuC9i9Te", "isBlockedForSale": true, "isPreproductionVehicle": true, "isScrapped": true, "manufacturer": "s1XeT", "maximumServiceLifeInMonths": 1, "pdiCurrentTireSet": "WR", "pdiDigitalLogbook": false, "pdiFoiling": true, "pdiLicensePlateMounting": false, "pdiRecharge": true, "pdiRefuel": false, "pdiRelevant": false, "usageGroup": {"description": "0eXBqWG", "id": "76bb73d2-19c2-45a9-9bc1-cc6e7663a8ff", "usageGroupId": 5, "version": 0}, "usingCostCenter": "HwqAK", "validationOfLeasingPrivileges": "A1bAW4", "vehicleResponsiblePerson": "j1nMllCgeWp", "vehicleType": "PKW", "vehicleUsage": {"id": "a2de1023-3b08-4e38-b287-594904adf9f1", "usage": "0eXBqWG", "usageId": 4, "version": 0}, "version": 0}], "errors": null, "warnings": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0]": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].consignee": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].depreciationRelevantCostCenter.costCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].depreciationRelevantCostCenter.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].depreciationRelevantCostCenter.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].depreciationRelevantCostCenter.vehicleTypes": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[0].depreciationRelevantCostCenter.vehicleUsageIds": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[0].depreciationRelevantCostCenter.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].internalContactPerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].internalOrderNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].isBlockedForSale": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].isPreproductionVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].isScrapped": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].maximumServiceLifeInMonths": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiCurrentTireSet": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiDigitalLogbook": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiFoiling": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiLicensePlateMounting": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiRecharge": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiRefuel": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiRelevant": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usageGroup.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usageGroup.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usageGroup.usageGroupId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usageGroup.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usingCostCenter": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].validationOfLeasingPrivileges": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleResponsiblePerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleUsage.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleUsage.usage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleUsage.usageId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleUsage.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request for update of consignee data", "providerStates": [{"name": "admin can update consignee data"}], "request": {"body": [{"consignee": "0QRswqn12aQ", "depreciationRelevantCostCenterId": "8276a6eb-6f45-42b1-9452-56bb61be4676", "description": "0eXBqWG", "id": "70bee60b-5b8b-41d8-afc5-56e69e09d466", "internalContactPerson": "ZTVIbnxDqeGUP", "internalOrderNumber": "ts4WaENv64dazuC9i9Te", "isBlockedForSale": true, "isPreproductionVehicle": true, "isScrapped": true, "manufacturer": "s1XeT", "maximumServiceLifeInMonths": 1, "pdiCurrentTireSet": "WR", "pdiDigitalLogbook": false, "pdiFoiling": true, "pdiLicensePlateMounting": false, "pdiRecharge": true, "pdiRefuel": false, "pdiRelevant": false, "usageGroupId": "76bb73d2-19c2-45a9-9bc1-cc6e7663a8ff", "usingCostCenter": "HwqAK", "validationOfLeasingPrivileges": "A1bAW4", "vehicleResponsiblePerson": "j1nMllCgeWp", "vehicleType": "PKW", "vehicleUsageId": "a2de1023-3b08-4e38-b287-594904adf9f1", "version": 0}], "headers": {"Authorization": "Bearer .*", "Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].consignee": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].depreciationRelevantCostCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].internalContactPerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].internalOrderNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].isBlockedForSale": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].isPreproductionVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].isScrapped": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].maximumServiceLifeInMonths": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].pdiCurrentTireSet": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].pdiDigitalLogbook": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].pdiFoiling": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].pdiLicensePlateMounting": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].pdiRecharge": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].pdiRefuel": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].pdiRelevant": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].usageGroupId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].usingCostCenter": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].validationOfLeasingPrivileges": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].vehicleResponsiblePerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].vehicleUsageId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "PUT", "path": "/vs/ui/vehicle-transfer-maintenance/consignee-data"}, "response": {"body": {"data": [{"consignee": "0QRswqn12aQ", "depreciationRelevantCostCenter": {"costCenterId": "0QRswqn12aQ", "description": "0eXBqWG", "id": "8276a6eb-6f45-42b1-9452-56bb61be4676", "vehicleTypes": ["PKW"], "vehicleUsageIds": ["8276a6eb-6f45-42b1-9452-56bb61be4676"], "version": 0}, "description": "0eXBqWG", "id": "70bee60b-5b8b-41d8-afc5-56e69e09d466", "internalContactPerson": "ZTVIbnxDqeGUP", "internalOrderNumber": "ts4WaENv64dazuC9i9Te", "isBlockedForSale": true, "isPreproductionVehicle": true, "isScrapped": true, "manufacturer": "s1XeT", "maximumServiceLifeInMonths": 1, "pdiCurrentTireSet": "WR", "pdiDigitalLogbook": false, "pdiFoiling": true, "pdiLicensePlateMounting": false, "pdiRecharge": true, "pdiRefuel": false, "pdiRelevant": false, "usageGroup": {"description": "0eXBqWG", "id": "76bb73d2-19c2-45a9-9bc1-cc6e7663a8ff", "usageGroupId": 5, "version": 0}, "usingCostCenter": "HwqAK", "validationOfLeasingPrivileges": "A1bAW4", "vehicleResponsiblePerson": "j1nMllCgeWp", "vehicleType": "PKW", "vehicleUsage": {"id": "a2de1023-3b08-4e38-b287-594904adf9f1", "usage": "0eXBqWG", "usageId": 4, "version": 0}, "version": 0}], "errors": null, "warnings": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0]": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].consignee": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].depreciationRelevantCostCenter.costCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].depreciationRelevantCostCenter.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].depreciationRelevantCostCenter.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].depreciationRelevantCostCenter.vehicleTypes": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[0].depreciationRelevantCostCenter.vehicleUsageIds": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[0].depreciationRelevantCostCenter.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].internalContactPerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].internalOrderNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].isBlockedForSale": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].isPreproductionVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].isScrapped": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].maximumServiceLifeInMonths": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiCurrentTireSet": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiDigitalLogbook": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiFoiling": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiLicensePlateMounting": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiRecharge": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiRefuel": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].pdiRelevant": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usageGroup.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usageGroup.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usageGroup.usageGroupId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usageGroup.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usingCostCenter": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].validationOfLeasingPrivileges": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleResponsiblePerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleUsage.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleUsage.usage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleUsage.usageId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleUsage.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "consignee-data"}}