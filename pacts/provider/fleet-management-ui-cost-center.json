{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request for creation of cost center data", "providerStates": [{"name": "admin can create cost center"}], "request": {"body": {"costCenterId": "0QRswqn12aQ", "description": "0eXBqWG", "vehicleTypes": [], "vehicleUsageIds": []}, "headers": {"Authorization": "Bearer .*", "Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.costCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleTypes": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleUsageIds": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/vs/ui/vehicle-transfer-maintenance/cost-center"}, "response": {"body": {"data": {"costCenterId": "0QRswqn12aQ", "description": "0eXBqWG", "id": "70bee60b-5b8b-41d8-afc5-56e69e09d466", "vehicleTypes": ["PKW"], "vehicleUsageIds": ["4955-7d21-48b4-af2d-e825518"], "version": 4}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.costCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleTypes": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data.vehicleUsageIds": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data.version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request for delete of cost center data", "providerStates": [{"name": "admin can delete cost center data"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "DELETE", "path": "/vs/ui/vehicle-transfer-maintenance/cost-center/e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}, "response": {"status": 200}}, {"description": "a request for retrieval of cost center data", "providerStates": [{"name": "cost center data exists"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/vs/ui/vehicle-transfer-maintenance/cost-center"}, "response": {"body": {"data": [{"costCenterId": "0QRswqn12aQ", "description": "0eXBqWG", "id": "70bee60b-5b8b-41d8-afc5-56e69e09d466", "vehicleTypes": ["PKW"], "vehicleUsageIds": ["4955-7d21-48b4-af2d-e825518"], "version": 4}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0]": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].costCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTypes": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[0].vehicleUsageIds": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[0].version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request for update of cost center data", "providerStates": [{"name": "admin can update cost center data"}], "request": {"body": {"costCenterId": "0QRswqn12aQ", "description": "0eXBqWG", "vehicleTypes": [], "vehicleUsageIds": []}, "headers": {"Authorization": "Bearer .*", "Content-Type": "application/json", "version": "4"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.costCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleTypes": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleUsageIds": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}, "version": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "PUT", "path": "/vs/ui/vehicle-transfer-maintenance/cost-center/70bee60b-5b8b-41d8-afc5-56e69e09d466"}, "response": {"body": {"data": {"costCenterId": "0QRswqn12aQ", "description": "0eXBqWG", "id": "70bee60b-5b8b-41d8-afc5-56e69e09d466", "vehicleTypes": ["PKW"], "vehicleUsageIds": ["4955-7d21-48b4-af2d-e825518"], "version": 4}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.costCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleTypes": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data.vehicleUsageIds": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data.version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "cost-center"}}