{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request to delete non-customer adequate vehicle option tag", "providerStates": [{"name": "FVM has a non-customer adequate option tags - AB0"}], "request": {"method": "DELETE", "path": "/ui/non-customer-adequate-vehicle-options/AB0"}, "response": {"status": 200}}, {"description": "a request to get all non-customer adequate vehicle option tags", "providerStates": [{"name": "FVM has multiple non-customer adequate option tags"}], "request": {"method": "GET", "path": "/ui/non-customer-adequate-vehicle-options"}, "response": {"body": {"data": [{"description": "SCHALENSITZ 918 SPYDER LI.", "id": "VR0"}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[*].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].id": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request to make option tag as non-customer adequate vehicle option tag", "providerStates": [{"name": "FVM has an option tags - AB0"}], "request": {"body": {"id": "AB0"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.id": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/non-customer-adequate-vehicle-options"}, "response": {"body": {"data": {"description": "SCHALENSITZ 918 SPYDER LI.", "id": "AB0"}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.id": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 201}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "non-customer-adequate-vehicles"}}