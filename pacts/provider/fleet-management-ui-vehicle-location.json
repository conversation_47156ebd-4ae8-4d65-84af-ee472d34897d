{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request for vehicle compounds", "providerStates": [{"name": "vehicle location compounds fetched"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/ui/vehicles/locations"}, "response": {"body": {"data": {"compounds": [{"buildings": [{"levels": ["8"], "name": "Bau 60", "parkingLots": ["parkingLotA"]}], "name": "Zentrale Werkstätten Zuffenhausen - Parkhaus"}]}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.compounds[0].buildings[0].levels[0].name": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^(10|[1-9])$"}]}, "$.data.compounds[0].buildings[0].levels[0].parkingLots": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data.compounds[0].buildings[0].levels[0].parkingLots[*]": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[1-9][0-9]{0,2}?$"}]}, "$.data.compounds[0].buildings[0].name": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data.compounds[0].name": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}}, "status": {}}, "status": 200}}, {"description": "a request for vehicle location add event", "providerStates": [{"name": "vehicle location event added"}], "request": {"body": {"building": "Bau 60", "compoundName": "Werkscompound Leipzig", "eventType": "IN", "level": "10", "occurredOn": "2024-10-04T08:59:00Z", "parkingLot": "999", "source": "MANUAL", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}, "headers": {"Authorization": "Bearer .*", "Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.building": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.compoundName": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.eventType": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^(IN|OUT)$"}]}, "$.level": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[1-9]$|^10$"}]}, "$.occurredOn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.parkingLot": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[1-9][0-9]{0,2}$"}]}, "$.source": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/locations/event"}, "response": {"status": 201}}, {"description": "a request for vehicle location history tab", "providerStates": [{"name": "vehicle location history exist"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/ui/vehicles/e89a3f2f-1986-4f7a-9dbb-2d0408f12345/location-history"}, "response": {"body": {"data": [{"building": "Bau 60", "compoundName": "Werkscompound Leipzig", "eventType": "IN", "level": "1", "occurredOn": "2024-10-04T08:59:00Z", "parkingLot": "665", "source": "MANUAL", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].building": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].compoundName": {"combine": "AND", "matchers": [{"match": "regex", "regex": ".*"}]}, "$.data[0].eventType": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^(IN|OUT)$"}]}, "$.data[0].level": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^(10|[1-9])$"}]}, "$.data[0].occurredOn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].parkingLot": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[1-9][0-9]{0,2}$"}]}, "$.data[0].source": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-location"}}