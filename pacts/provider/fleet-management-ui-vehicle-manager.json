{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request to get vehicle", "providerStates": [{"name": "a vehicle exist"}], "request": {"body": {"endRow": 150, "filterModel": {}, "sortModel": [], "startRow": 0}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/search"}, "response": {"body": {"data": [{"nonCustomerAdequate": {"actualRebuildCost": 999.99, "comment": "parsley", "expectedRevenue": 838489.**********, "ncaStatus": "NCA", "plannedRebuildCost": 99.99, "profitabilityAuditDone": "2011-12-04T00:00:00Z", "rebuildDone": "2020-02-19T00:00:00Z", "rebuildStarted": "2019-12-13T00:00:00Z", "salesPrice": 315688.**********}, "preDeliveryInspection": {"charge": false, "comment": "Alles gut", "completedDate": "2026-01-07T19:01:08.449Z", "digitalLogbook": true, "foiling": false, "id": "be1dbb6a-c912-4b46-89a2-d2f51e3aaa9e", "isRelevant": true, "licencePlateMounting": false, "orderedDate": "2026-01-06T19:01:08.449Z", "plannedDate": "2026-01-01T19:01:08.449Z", "refuel": true, "tireSet": "SR"}, "vehicle": {"color": {"exterior": "licorice", "exteriorDescription": "hello world", "interior": "hello world", "interiorDescription": "hello world"}, "consumption": {"driveType": "OVC_HEV", "primaryFuelType": "E10", "secondaryFuelType": "ELECTRIC", "typification": "patriot"}, "country": {"bnrValue": "hello world", "cnrCountryDescription": "elongation", "cnrValue": "hello world"}, "createdAt": "2020-07-19T00:00:00Z", "currentTires": "SR", "delivery": {"isPreparationNecessary": true, "preparationDoneDate": "2020-07-19T00:00:00Z"}, "embargo": {"inEmbargo": true}, "equiId": "53c648a8-1dc6-41d6-8db9-aed0c3d6a713", "equipmentNumber": 6692555223728128, "evaluation": {"appraisalNetPrice": 1000, "pcComplaintCheckComment": "abc", "vehicleEvaluationComment": "abc"}, "externalLeaseEnd": "2025-03-09T09:04:49.918Z", "externalLeaseLessee": "<PERSON>", "externalLeaseRate": 3788680269398016, "externalLeaseStart": "2025-03-09T09:04:49.918Z", "financialAssetType": "AV", "fleet": {"approvedForScrappingDate": "2020-07-19T00:00:00Z", "comment": "Fleet comment", "costEstimationOrderedDate": "2020-07-19T00:00:00Z", "isResidualValueMarket": false, "profitabilityAuditDate": "2020-07-19T00:00:00Z", "scrapVehicle": true, "scrappedDate": "2020-07-19T00:00:00Z", "scrappedVehicleOfferedDate": "2020-07-19T00:00:00Z", "soldCupCarDate": "2020-07-19T00:00:00Z", "soldDate": "2020-07-19T00:00:00Z", "stolenDate": "2020-07-19T00:00:00Z", "vehicleSentToSalesDate": "2020-07-19T00:00:00Z"}, "id": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "model": {"description": "911 Carrera", "manufacturer": "grandfather", "orderType": "mind", "productCode": "valuable", "productId": "bCkIhYOTDvNa9DLr4f1HS", "range": "Taycan", "vehicleType": "ecclesia"}, "numberOfDamages": 3, "order": {"blockedForSale": false, "commissionNumber": "hello world", "deliveryType": "hello world", "department": "VAM, VAQ", "importerShortName": "hello world", "invoiceDate": "2025-03-09T09:04:49.918Z", "invoiceNumber": "hello world", "leasingType": "DLF", "preproductionVehicle": false, "primaryStatus": "hello world", "purchaseOrderDate": "2025-02-06T13:56:43.937Z", "purposeOrderType": "mobility", "requestedDeliveryDate": "2024-09-17T04:44:44.200Z", "tradingPartnerNumber": "dogsled"}, "pmp": {"odometer": ****************, "timestamp": "2024-07-23T11:23:23.873Z"}, "price": {"factoryGrossPriceEUR": 1234, "factoryNetPriceEUR": 1234, "grossPriceWithExtras": 466729, "netPriceWithExtras": 357972}, "production": {"endDate": "2025-03-04T00:17:45.145Z", "factory": "hello world", "factoryVW": 4080693292302336, "gearBoxClass": "hello world", "number": "counsel", "plannedEndDate": "2025-11-13T22:10:53.021Z", "quoteMonth": 7, "quoteYear": 2001, "technicalModelYear": 2014}, "referenceId": "21f5dd3f-7cbf-4205-b9e8-ab91571e5b57", "repairfixCarId": "d4r6yIhYOTDvNa9DLr34gte", "returnInfo": {"factoryCarPreparationOrderNumber": "abcs", "isUsedCar": true, "keyReturned": "2020-07-19T00:00:00Z", "nextProcess": "SALES"}, "source": "PVH", "status": "SX100", "technical": {"amountSeats": 8, "engineCapacity": 3689}, "tireSetChange": {"comment": "something", "completedDate": "2016-08-30T00:00:00Z", "orderedDate": "2016-08-30T00:00:00Z"}, "version": 1, "vguid": "fBPfUuBi9zuhZQ4XSg-9q", "vin": "WF72R3RJXNFT33348"}, "vehicleCampaigns": {"campaignIds": "ABC,DEF"}, "vehicleLastKnownLocation": {"building": "rotten", "comment": "uh-huh", "compoundName": "viewer", "eventType": "OUT", "level": "9", "occurredOn": "2015-12-21T00:00:00Z", "parkingLot": "141", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}, "vehicleRegistration": {"firstRegistrationDate": "2012-07-23T00:00:00Z", "hsn": "6319", "lastDeRegistrationDate": "2014-12-29T00:00:00Z", "lastRegistrationDate": "2019-05-22T00:00:00Z", "licencePlate": "WI 2685", "registrationDate": "2012-01-16T00:00:00Z", "registrationStatus": "REGISTERED", "registrationType": 3, "sfme": true, "storageLocation": "ZMU", "testNumber": 12345, "testVehicle": false, "tsn": "EVCLZDGI"}, "vehicleSales": {"comment": "test comment", "contractSigned": false, "customerDeliveryDate": "2026-04-30T10:00:00Z", "customerPartnerNumber": "0000123456", "fvmTransactionId": "FVMTRX5678", "invoiceDate": "2025-02-06T00:00:00Z", "invoiceNumber": "INV123456", "invoiceRecipientNumber": "0000567890", "plannedDeliveryDate": "2026-04-30T10:00:00Z", "receiptNumber": "RCPT123456", "reservedForB2C": true, "salesNetPriceAfterDiscountEUR": 112300.67, "salesNetPriceEUR": 112345.67, "salesPersonNumber": "001234", "winterTiresId": 987654321, "winterTiresNetPriceEUR": 234.56}, "vehicleTransfer": {"latestReturnDate": "2026-05-10T10:00:00Z", "mileageAtDelivery": 15000, "mileageAtReturn": 20000, "plannedReturnDate": "2026-05-01T10:00:00Z", "returnDate": "2026-05-08T10:00:00Z", "status": "ACTIVE", "vehicleTransferKey": 98765, "vehicleUsageId": "ABC123"}}], "errors": null, "rowCount": 1}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.actualRebuildCost": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.expectedRevenue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.ncaStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.plannedRebuildCost": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.profitabilityAuditDone": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.rebuildDone": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.rebuildStarted": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.salesPrice": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.charge": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.completedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.digitalLogbook": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.foiling": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.isRelevant": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.licencePlateMounting": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.orderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.plannedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.refuel": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.tireSet": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.exterior": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.exteriorDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.interior": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.interiorDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.driveType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.primaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.secondaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.typification": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.country.bnrValue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.country.cnrCountryDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.country.cnrValue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.createdAt": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.currentTires": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.delivery.isPreparationNecessary": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.delivery.preparationDoneDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.embargo.inEmbargo": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.equiId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.equipmentNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseEnd": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseLessee": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseRate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseStart": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.financialAssetType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.approvedForScrappingDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.costEstimationOrderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.isResidualValueMarket": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.profitabilityAuditDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.scrapVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.scrappedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.scrappedVehicleOfferedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.soldCupCarDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.soldDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.stolenDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.vehicleSentToSalesDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.orderType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.productCode": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.productId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.range": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.numberOfDamages": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.blockedForSale": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.commissionNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.deliveryType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.department": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.importerShortName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.invoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.leasingType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.preproductionVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.primaryStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.purchaseOrderDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.purposeOrderType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.requestedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.tradingPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.pmp.odometer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.pmp.timestamp": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.price.factoryGrossPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.price.factoryNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.price.grossPriceWithExtras": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.price.netPriceWithExtras": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.endDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.factory": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.factoryVW": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.gearBoxClass": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.number": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.plannedEndDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.quoteMonth": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.quoteYear": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.technicalModelYear": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.referenceId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.repairfixCarId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.source": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.technical.amountSeats": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.technical.engineCapacity": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.tireSetChange.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.tireSetChange.completedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.tireSetChange.orderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.vguid": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.vin": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleCampaigns.campaignIds": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.building": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.compoundName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.eventType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.level": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.occurredOn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.parkingLot": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.firstRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.hsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.lastDeRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.lastRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.licencePlate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.registrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.registrationStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.registrationType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.storageLocation": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.testNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.testVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.tsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.contractSigned": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.customerDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.customerPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.fvmTransactionId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.invoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.invoiceRecipientNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.plannedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.receiptNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.reservedForB2C": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.salesNetPriceAfterDiscountEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.salesNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.salesPersonNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.winterTiresId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.winterTiresNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.latestReturnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.mileageAtDelivery": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.mileageAtReturn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.plannedReturnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.returnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.vehicleTransferKey": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.vehicleUsageId": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request to get vehicle", "providerStates": [{"name": "a vehicle exist only with mandatory fields"}], "request": {"body": {"endRow": 150, "filterModel": {}, "sortModel": [], "startRow": 0}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/search"}, "response": {"body": {"data": [{"vehicle": {"id": "e89a3f2f-1986-4f7a-9dbb-2d0408f67890", "version": 1}}], "errors": null, "rowCount": 1}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "an update request with all domains to update vehicle", "providerStates": [{"name": "a vehicle exist for update"}], "request": {"body": [{"nonCustomerAdequate": {"actualRebuildCost": 999.99, "comment": "parsley", "expectedRevenue": 838489.**********, "plannedRebuildCost": 99.99, "profitabilityAuditDone": "2011-12-04T00:00:00Z", "rebuildDone": "2020-02-19T00:00:00Z", "rebuildStarted": "2019-12-13T00:00:00Z", "salesPrice": 315688.**********}, "preDeliveryInspection": {"charge": false, "comment": "test comment", "completedDate": "2026-01-07T19:01:08.449Z", "digitalLogbook": true, "foiling": false, "id": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "isRelevant": true, "licencePlateMounting": false, "orderedDate": "2026-01-06T19:01:08.449Z", "plannedDate": "2026-01-01T19:01:08.449Z", "refuel": true, "tireSet": "SR", "version": 1}, "vehicle": {"consumption": {"primaryFuelType": "ELECTRIC", "secondaryFuelType": "ELECTRIC"}, "currentTires": "WR", "delivery": {"isPreparationNecessary": true, "preparationDoneDate": "2020-07-19T00:00:00Z"}, "evaluation": {"appraisalNetPrice": 1000, "pcComplaintCheckComment": "abc", "vehicleEvaluationComment": "abc"}, "externalLeaseEnd": "2025-03-09T09:04:49.918Z", "externalLeaseLessee": "<PERSON>", "externalLeaseRate": 3788680269398016, "externalLeaseStart": "2025-03-09T09:04:49.918Z", "financialAssetType": "AV", "fleet": {"approvedForScrappingDate": "2020-07-19T00:00:00Z", "comment": "Fleet comment", "costEstimationOrderedDate": "2020-07-19T00:00:00Z", "isResidualValueMarket": false, "profitabilityAuditDate": "2020-07-19T00:00:00Z", "scrapVehicle": false, "scrappedDate": "2020-07-19T00:00:00Z", "scrappedVehicleOfferedDate": "2020-07-19T00:00:00Z", "soldCupCarDate": "2020-07-19T00:00:00Z", "soldDate": "2020-07-19T00:00:00Z", "stolenDate": "2020-07-19T00:00:00Z", "vehicleSentToSalesDate": "2020-07-19T00:00:00Z"}, "id": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "order": {"blockedForSale": false, "deliveryType": "Standard", "preproductionVehicle": false, "primaryStatus": "Pending", "purchaseOrderDate": "2025-02-06T13:56:43.937Z", "requestedDeliveryDate": "2024-09-17T04:44:44.200Z"}, "price": {"netPriceWithExtras": 357972}, "referenceId": "21f5dd3f-7cbf-4205-b9e8-ab91571e5b57", "returnInfo": {"nextProcess": "SALES"}, "technical": {"amountSeats": 8, "engineCapacity": 3689}, "tireSetChange": {"comment": "something", "completedDate": "2016-08-30T00:00:00Z", "orderedDate": "2016-08-30T00:00:00Z"}, "version": 1}, "vehicleSales": {"comment": "test comment", "contractSigned": true, "customerDeliveryDate": "2026-04-30T10:00:00Z", "plannedDeliveryDate": "2026-04-30T10:00:00Z", "reservedForB2C": true}}], "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$[*].nonCustomerAdequate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].preDeliveryInspection": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].vehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].vehicleSales": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "PUT", "path": "/ui/vehicles/update"}, "response": {"body": {"data": [{"nonCustomerAdequate": {"actualRebuildCost": 999.99, "comment": "parsley", "expectedRevenue": 838489.**********, "ncaStatus": "NCA", "plannedRebuildCost": 99.99, "profitabilityAuditDone": "2011-12-04T00:00:00Z", "rebuildDone": "2020-02-19T00:00:00Z", "rebuildStarted": "2019-12-13T00:00:00Z", "salesPrice": 315688.**********}, "preDeliveryInspection": {"charge": false, "comment": "Alles gut", "completedDate": "2026-01-07T19:01:08.449Z", "digitalLogbook": true, "foiling": false, "id": "be1dbb6a-c912-4b46-89a2-d2f51e3aaa9e", "isRelevant": true, "licencePlateMounting": false, "orderedDate": "2026-01-06T19:01:08.449Z", "plannedDate": "2026-01-01T19:01:08.449Z", "refuel": true, "tireSet": "SR"}, "vehicle": {"color": {"exterior": "licorice", "exteriorDescription": "hello world", "interior": "hello world", "interiorDescription": "hello world"}, "consumption": {"driveType": "OVC_HEV", "primaryFuelType": "ELECTRIC", "secondaryFuelType": "ELECTRIC", "typification": "patriot"}, "country": {"bnrValue": "hello world", "cnrCountryDescription": "elongation", "cnrValue": "hello world"}, "createdAt": "2020-07-19T00:00:00Z", "currentTires": "WR", "delivery": {"isPreparationNecessary": true, "preparationDoneDate": "2020-07-19T00:00:00Z"}, "embargo": {"inEmbargo": true}, "equiId": "53c648a8-1dc6-41d6-8db9-aed0c3d6a713", "equipmentNumber": 6692555223728128, "evaluation": {"appraisalNetPrice": 1000, "pcComplaintCheckComment": "abc", "vehicleEvaluationComment": "abc"}, "externalLeaseEnd": "2025-03-09T09:04:49.918Z", "externalLeaseLessee": "<PERSON>", "externalLeaseRate": 3788680269398016, "externalLeaseStart": "2025-03-09T09:04:49.918Z", "financialAssetType": "AV", "fleet": {"approvedForScrappingDate": "2020-07-19T00:00:00Z", "comment": "Fleet comment", "costEstimationOrderedDate": "2020-07-19T00:00:00Z", "isResidualValueMarket": false, "profitabilityAuditDate": "2020-07-19T00:00:00Z", "scrapVehicle": false, "scrappedDate": "2020-07-19T00:00:00Z", "scrappedVehicleOfferedDate": "2020-07-19T00:00:00Z", "soldCupCarDate": "2020-07-19T00:00:00Z", "soldDate": "2020-07-19T00:00:00Z", "stolenDate": "2020-07-19T00:00:00Z", "vehicleSentToSalesDate": "2020-07-19T00:00:00Z"}, "id": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "model": {"description": "911 Carrera", "manufacturer": "grandfather", "orderType": "mind", "productCode": "valuable", "productId": "bCkIhYOTDvNa9DLr4f1HS", "range": "Taycan", "vehicleType": "ecclesia"}, "numberOfDamages": 3, "order": {"blockedForSale": false, "commissionNumber": "hello world", "deliveryType": "Standard", "department": "VAM, VAQ", "importerShortName": "hello world", "invoiceDate": "2025-03-09T09:04:49.918Z", "invoiceNumber": "hello world", "leasingType": "DLF", "preproductionVehicle": false, "primaryStatus": "Pending", "purchaseOrderDate": "2025-02-06T13:56:43.937Z", "purposeOrderType": "mobility", "requestedDeliveryDate": "2024-09-17T04:44:44.200Z", "tradingPartnerNumber": "dogsled"}, "pmp": {"odometer": ****************, "timestamp": "2024-07-23T11:23:23.873Z"}, "price": {"grossPriceWithExtras": 466729, "netPriceWithExtras": 357972}, "production": {"endDate": "2025-03-04T00:17:45.145Z", "factory": "hello world", "factoryVW": 4080693292302336, "gearBoxClass": "hello world", "number": "counsel", "plannedEndDate": "2025-11-13T22:10:53.021Z", "quoteMonth": 7, "quoteYear": 2001, "technicalModelYear": 2014}, "referenceId": "21f5dd3f-7cbf-4205-b9e8-ab91571e5b57", "repairfixCarId": "d4r6yIhYOTDvNa9DLr34gte", "returnInfo": {"factoryCarPreparationOrderNumber": "abcs", "isUsedCar": true, "keyReturned": "2020-07-19T00:00:00Z", "nextProcess": "SALES"}, "source": "PVH", "status": "SX100", "technical": {"amountSeats": 8, "engineCapacity": 3689}, "tireSetChange": {"comment": "something", "completedDate": "2016-08-30T00:00:00Z", "orderedDate": "2016-08-30T00:00:00Z"}, "version": 1, "vguid": "fBPfUuBi9zuhZQ4XSg-9q", "vin": "WF72R3RJXNFT33348"}, "vehicleCampaigns": {"campaignIds": "ABC,DEF"}, "vehicleLastKnownLocation": {"building": "rotten", "comment": "uh-huh", "compoundName": "viewer", "eventType": "OUT", "level": "9", "occurredOn": "2015-12-21T00:00:00Z", "parkingLot": "141", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}, "vehicleRegistration": {"firstRegistrationDate": "2012-07-23T00:00:00Z", "hsn": "6319", "lastDeRegistrationDate": "2014-12-29T00:00:00Z", "lastRegistrationDate": "2019-05-22T00:00:00Z", "licencePlate": "WI 2685", "registrationDate": "2012-01-16T00:00:00Z", "registrationStatus": "REGISTERED", "registrationType": 3, "sfme": true, "storageLocation": "ZMU", "testNumber": 12345, "testVehicle": false, "tsn": "EVCLZDGI"}, "vehicleSales": {"comment": "test comment", "contractSigned": true, "customerDeliveryDate": "2026-04-30T10:00:00Z", "customerPartnerNumber": "0000123456", "fvmTransactionId": "FVMTRX5678", "invoiceDate": "2025-02-06T00:00:00Z", "invoiceNumber": "INV123456", "invoiceRecipientNumber": "0000567890", "plannedDeliveryDate": "2026-04-30T10:00:00Z", "receiptNumber": "RCPT123456", "reservedForB2C": true, "salesNetPriceAfterDiscountEUR": 112300.67, "salesNetPriceEUR": 112345.67, "salesPersonNumber": "001234", "winterTiresId": 987654321, "winterTiresNetPriceEUR": 234.56}, "vehicleTransfer": {"latestReturnDate": "2026-05-10T10:00:00Z", "mileageAtDelivery": 15000, "mileageAtReturn": 20000, "plannedReturnDate": "2026-05-01T10:00:00Z", "returnDate": "2026-05-08T10:00:00Z", "status": "ACTIVE", "vehicleTransferKey": 98765, "vehicleUsageId": "ABC123"}}], "errors": null, "rowCount": 1}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.actualRebuildCost": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.expectedRevenue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.ncaStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.plannedRebuildCost": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.profitabilityAuditDone": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.rebuildDone": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.rebuildStarted": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.salesPrice": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.charge": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.completedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.digitalLogbook": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.foiling": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.isRelevant": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.licencePlateMounting": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.orderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.plannedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.refuel": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.tireSet": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.exterior": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.exteriorDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.interior": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.interiorDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.driveType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.primaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.secondaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.typification": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.country.bnrValue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.country.cnrCountryDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.country.cnrValue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.createdAt": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.currentTires": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.delivery.isPreparationNecessary": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.delivery.preparationDoneDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.embargo.inEmbargo": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.equiId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.equipmentNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseEnd": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseLessee": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseRate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseStart": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.financialAssetType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.approvedForScrappingDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.costEstimationOrderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.isResidualValueMarket": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.profitabilityAuditDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.scrapVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.scrappedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.scrappedVehicleOfferedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.soldCupCarDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.soldDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.stolenDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.vehicleSentToSalesDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.orderType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.productCode": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.productId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.range": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.numberOfDamages": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.blockedForSale": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.commissionNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.deliveryType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.department": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.importerShortName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.invoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.leasingType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.preproductionVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.primaryStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.purchaseOrderDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.purposeOrderType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.requestedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.tradingPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.pmp.odometer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.pmp.timestamp": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.price.grossPriceWithExtras": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.price.netPriceWithExtras": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.endDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.factory": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.factoryVW": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.gearBoxClass": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.number": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.plannedEndDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.quoteMonth": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.quoteYear": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.technicalModelYear": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.referenceId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.repairfixCarId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.source": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.technical.amountSeats": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.technical.engineCapacity": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.tireSetChange.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.tireSetChange.completedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.tireSetChange.orderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.vguid": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.vin": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleCampaigns.campaignIds": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.building": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.compoundName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.eventType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.level": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.occurredOn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.parkingLot": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.firstRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.hsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.lastDeRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.lastRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.licencePlate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.registrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.registrationStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.registrationType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.storageLocation": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.testNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.testVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.tsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.contractSigned": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.customerDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.customerPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.fvmTransactionId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.invoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.invoiceRecipientNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.plannedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.receiptNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.reservedForB2C": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.salesNetPriceAfterDiscountEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.salesNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.salesPersonNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.winterTiresId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.winterTiresNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.latestReturnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.mileageAtDelivery": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.mileageAtReturn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.plannedReturnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.returnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.vehicleTransferKey": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.vehicleUsageId": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "an update request with basic domains to update vehicle", "providerStates": [{"name": "a vehicle exist for update"}], "request": {"body": [{"vehicle": {"currentTires": "WR", "id": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "version": 1}}], "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$[*].vehicle.currentTires": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].vehicle.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$[*].vehicle.version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "PUT", "path": "/ui/vehicles/update"}, "response": {"body": {"data": [{"nonCustomerAdequate": {"actualRebuildCost": 999.99, "comment": "parsley", "expectedRevenue": 838489.**********, "ncaStatus": "NCA", "plannedRebuildCost": 99.99, "profitabilityAuditDone": "2011-12-04T00:00:00Z", "rebuildDone": "2020-02-19T00:00:00Z", "rebuildStarted": "2019-12-13T00:00:00Z", "salesPrice": 315688.**********}, "preDeliveryInspection": {"charge": false, "comment": "Alles gut", "completedDate": "2026-01-07T19:01:08.449Z", "digitalLogbook": true, "foiling": false, "id": "be1dbb6a-c912-4b46-89a2-d2f51e3aaa9e", "isRelevant": true, "licencePlateMounting": false, "orderedDate": "2026-01-06T19:01:08.449Z", "plannedDate": "2026-01-01T19:01:08.449Z", "refuel": true, "tireSet": "SR"}, "vehicle": {"color": {"exterior": "licorice", "exteriorDescription": "hello world", "interior": "hello world", "interiorDescription": "hello world"}, "consumption": {"driveType": "OVC_HEV", "primaryFuelType": "ELECTRIC", "secondaryFuelType": "ELECTRIC", "typification": "patriot"}, "country": {"bnrValue": "hello world", "cnrCountryDescription": "elongation", "cnrValue": "hello world"}, "createdAt": "2020-07-19T00:00:00Z", "currentTires": "WR", "delivery": {"isPreparationNecessary": true, "preparationDoneDate": "2020-07-19T00:00:00Z"}, "embargo": {"inEmbargo": true}, "equiId": "53c648a8-1dc6-41d6-8db9-aed0c3d6a713", "equipmentNumber": 6692555223728128, "evaluation": {"appraisalNetPrice": 1000, "pcComplaintCheckComment": "abc", "vehicleEvaluationComment": "abc"}, "externalLeaseEnd": "2025-03-09T09:04:49.918Z", "externalLeaseLessee": "<PERSON>", "externalLeaseRate": 3788680269398016, "externalLeaseStart": "2025-03-09T09:04:49.918Z", "financialAssetType": "AV", "fleet": {"approvedForScrappingDate": "2020-07-19T00:00:00Z", "comment": "Fleet comment", "costEstimationOrderedDate": "2020-07-19T00:00:00Z", "isResidualValueMarket": false, "profitabilityAuditDate": "2020-07-19T00:00:00Z", "scrapVehicle": false, "scrappedDate": "2020-07-19T00:00:00Z", "scrappedVehicleOfferedDate": "2020-07-19T00:00:00Z", "soldCupCarDate": "2020-07-19T00:00:00Z", "soldDate": "2020-07-19T00:00:00Z", "stolenDate": "2020-07-19T00:00:00Z", "vehicleSentToSalesDate": "2020-07-19T00:00:00Z"}, "id": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "model": {"description": "911 Carrera", "manufacturer": "grandfather", "orderType": "mind", "productCode": "valuable", "productId": "bCkIhYOTDvNa9DLr4f1HS", "range": "Taycan", "vehicleType": "ecclesia"}, "numberOfDamages": 3, "order": {"blockedForSale": false, "commissionNumber": "hello world", "deliveryType": "Standard", "department": "VAM, VAQ", "importerShortName": "hello world", "invoiceDate": "2025-03-09T09:04:49.918Z", "invoiceNumber": "hello world", "leasingType": "DLF", "preproductionVehicle": false, "primaryStatus": "Pending", "purchaseOrderDate": "2025-02-06T13:56:43.937Z", "purposeOrderType": "mobility", "requestedDeliveryDate": "2024-09-17T04:44:44.200Z", "tradingPartnerNumber": "dogsled"}, "pmp": {"odometer": ****************, "timestamp": "2024-07-23T11:23:23.873Z"}, "price": {"grossPriceWithExtras": 466729, "netPriceWithExtras": 357972}, "production": {"endDate": "2025-03-04T00:17:45.145Z", "factory": "hello world", "factoryVW": 4080693292302336, "gearBoxClass": "hello world", "number": "counsel", "plannedEndDate": "2025-11-13T22:10:53.021Z", "quoteMonth": 7, "quoteYear": 2001, "technicalModelYear": 2014}, "referenceId": "21f5dd3f-7cbf-4205-b9e8-ab91571e5b57", "repairfixCarId": "d4r6yIhYOTDvNa9DLr34gte", "returnInfo": {"factoryCarPreparationOrderNumber": "abcs", "isUsedCar": true, "keyReturned": "2020-07-19T00:00:00Z", "nextProcess": "SALES"}, "source": "PVH", "status": "SX100", "technical": {"amountSeats": 8, "engineCapacity": 3689}, "tireSetChange": {"comment": "something", "completedDate": "2016-08-30T00:00:00Z", "orderedDate": "2016-08-30T00:00:00Z"}, "version": 1, "vguid": "fBPfUuBi9zuhZQ4XSg-9q", "vin": "WF72R3RJXNFT33348"}, "vehicleCampaigns": {"campaignIds": "ABC,DEF"}, "vehicleLastKnownLocation": {"building": "rotten", "comment": "uh-huh", "compoundName": "viewer", "eventType": "OUT", "level": "9", "occurredOn": "2015-12-21T00:00:00Z", "parkingLot": "141", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}, "vehicleRegistration": {"firstRegistrationDate": "2012-07-23T00:00:00Z", "hsn": "6319", "lastDeRegistrationDate": "2014-12-29T00:00:00Z", "lastRegistrationDate": "2019-05-22T00:00:00Z", "licencePlate": "WI 2685", "registrationDate": "2012-01-16T00:00:00Z", "registrationStatus": "REGISTERED", "registrationType": 3, "sfme": true, "storageLocation": "ZMU", "testNumber": 12345, "testVehicle": false, "tsn": "EVCLZDGI"}, "vehicleSales": {"comment": "test comment", "contractSigned": true, "customerDeliveryDate": "2026-04-30T10:00:00Z", "customerPartnerNumber": "0000123456", "fvmTransactionId": "FVMTRX5678", "invoiceDate": "2025-02-06T00:00:00Z", "invoiceNumber": "INV123456", "invoiceRecipientNumber": "0000567890", "plannedDeliveryDate": "2026-04-30T10:00:00Z", "receiptNumber": "RCPT123456", "reservedForB2C": true, "salesNetPriceAfterDiscountEUR": 112300.67, "salesNetPriceEUR": 112345.67, "salesPersonNumber": "001234", "winterTiresId": 987654321, "winterTiresNetPriceEUR": 234.56}, "vehicleTransfer": {"latestReturnDate": "2026-05-10T10:00:00Z", "mileageAtDelivery": 15000, "mileageAtReturn": 20000, "plannedReturnDate": "2026-05-01T10:00:00Z", "returnDate": "2026-05-08T10:00:00Z", "status": "ACTIVE", "vehicleTransferKey": 98765, "vehicleUsageId": "ABC123"}}], "errors": null, "rowCount": 1}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.actualRebuildCost": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.expectedRevenue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.ncaStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.plannedRebuildCost": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.profitabilityAuditDone": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.rebuildDone": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.rebuildStarted": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].nonCustomerAdequate.salesPrice": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.charge": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.completedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.digitalLogbook": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.foiling": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.isRelevant": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.licencePlateMounting": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.orderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.plannedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.refuel": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].preDeliveryInspection.tireSet": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.exterior": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.exteriorDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.interior": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.color.interiorDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.driveType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.primaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.secondaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.consumption.typification": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.country.bnrValue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.country.cnrCountryDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.country.cnrValue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.createdAt": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.currentTires": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.delivery.isPreparationNecessary": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.delivery.preparationDoneDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.embargo.inEmbargo": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.equiId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.equipmentNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseEnd": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseLessee": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseRate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.externalLeaseStart": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.financialAssetType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.approvedForScrappingDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.costEstimationOrderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.isResidualValueMarket": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.profitabilityAuditDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.scrapVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.scrappedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.scrappedVehicleOfferedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.soldCupCarDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.soldDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.stolenDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.fleet.vehicleSentToSalesDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.orderType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.productCode": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.productId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.range": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.model.vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.numberOfDamages": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.blockedForSale": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.commissionNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.deliveryType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.department": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.importerShortName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.invoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.leasingType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.preproductionVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.primaryStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.purchaseOrderDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.purposeOrderType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.requestedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.order.tradingPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.pmp.odometer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.pmp.timestamp": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.price.grossPriceWithExtras": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.price.netPriceWithExtras": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.endDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.factory": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.factoryVW": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.gearBoxClass": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.number": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.plannedEndDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.quoteMonth": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.quoteYear": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.production.technicalModelYear": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.referenceId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.repairfixCarId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.source": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.technical.amountSeats": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.technical.engineCapacity": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.tireSetChange.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.tireSetChange.completedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.tireSetChange.orderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.vguid": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicle.vin": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleCampaigns.campaignIds": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.building": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.compoundName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.eventType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.level": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.occurredOn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.parkingLot": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleLastKnownLocation.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.firstRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.hsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.lastDeRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.lastRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.licencePlate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.registrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.registrationStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.registrationType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.storageLocation": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.testNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.testVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleRegistration.tsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.contractSigned": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.customerDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.customerPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.fvmTransactionId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.invoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.invoiceRecipientNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.plannedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.receiptNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.reservedForB2C": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.salesNetPriceAfterDiscountEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.salesNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.salesPersonNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.winterTiresId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleSales.winterTiresNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.latestReturnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.mileageAtDelivery": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.mileageAtReturn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.plannedReturnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.returnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.vehicleTransferKey": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].vehicleTransfer.vehicleUsageId": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-manager"}}