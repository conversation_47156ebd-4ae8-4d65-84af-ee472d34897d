{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "A request to look up certain vehicle transfers based on a given licence plate and date.", "providerStates": [{"name": "At least one vehicle transfer exists for the given input."}], "request": {"body": {"date": "2024-10-04T08:59:00Z", "licencePlate": "A-BC-123"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.date": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.licencePlate": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/enquiries/vehicle-responsibility-details"}, "response": {"body": {"data": [{"city": "Some city", "companyEmail": "<EMAIL>", "country": "Some country", "department": "PPV", "employeeNumber": "31KCwYxwTvTTYH26VZmYj", "firstName": "Max", "internalContactPerson": "<PERSON><PERSON>", "lastName": "<PERSON><PERSON><PERSON>", "licencePlate": "A-BC-123", "licencePlateValidFrom": "2012-07-23T00:00:00Z", "licencePlateValidUntil": "2012-07-23T00:00:00Z", "postalCode": "12345", "privateEmail": "<EMAIL>", "registrationDate": "2012-07-23T00:00:00Z", "status": "PLANNED", "street": "Some street", "usageGroup": "A usage group description", "vehicleTransferKey": 1234, "vin": "1FMZU34E1XUA27757"}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].city": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].companyEmail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].country": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].department": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].employeeNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].firstName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].internalContactPerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].lastName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].licencePlate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].licencePlateValidFrom": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].licencePlateValidUntil": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].postalCode": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].privateEmail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].registrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].street": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].usageGroup": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleTransferKey": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vin": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-responsibility-details"}}