{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request to approve vehicle invoice", "providerStates": [{"name": "a vehicle invoice exists with CANCELED status"}], "request": {"method": "PATCH", "path": "/ui/vehicles/invoices/a2de1023-3b08-4e38-b287-594904adf9f1/approve"}, "response": {"body": {"data": null, "errors": [{"type": "error.fvm.vehicleSales.invoiceUpdateError"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.errors[*].type": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 400}}, {"description": "a request to approve vehicle invoice", "providerStates": [{"name": "a vehicle invoice exists with WAITING_FOR_APPROVAL status"}], "request": {"method": "PATCH", "path": "/ui/vehicles/invoices/a2de1023-3b08-4e38-b287-594904adf9f1/approve"}, "response": {"body": {"data": {}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request to cancel vehicle invoice", "providerStates": [{"name": "a vehicle invoice exists with APPROVED status"}], "request": {"method": "POST", "path": "/ui/vehicles/invoices/a2de1023-3b08-4e38-b287-594904adf9f1/cancel"}, "response": {"body": {"data": {}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request to create vehicle sales invoice with all fields populated", "providerStates": [{"name": "a vehicle containing all sales fields exist"}], "request": {"body": {"customerInvoiceRecipient": false, "customerPartnerNumber": "0123", "invoiceRecipientNumber": "0123", "paymentType": "LEASING", "salesDiscountPercentage": 20.5, "salesDiscountType": "VIP", "salesNetPriceEUR": 999.99, "salesPersonNumber": "S0123", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "winterTiresDiscountPercentage": 8, "winterTiresId": 1234, "winterTiresNetPriceEUR": 99.99}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.customerInvoiceRecipient": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.customerPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.invoiceRecipientNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.paymentType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.salesDiscountPercentage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.salesDiscountType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.salesNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.salesPersonNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.winterTiresDiscountPercentage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.winterTiresId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.winterTiresNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/invoices"}, "response": {"status": 201}}, {"description": "a request to create vehicle sales invoice with mandatory fields", "providerStates": [{"name": "a vehicle containing only mandatory sales fields exist"}], "request": {"body": {"customerInvoiceRecipient": false, "customerPartnerNumber": "0123", "invoiceRecipientNumber": "0123", "paymentType": "LEASING", "salesNetPriceEUR": 999.99, "salesPersonNumber": "S0123", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.customerInvoiceRecipient": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.customerPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.invoiceRecipientNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.paymentType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.salesNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.salesPersonNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/invoices"}, "response": {"status": 201}}, {"description": "a request to create vehicle sales invoice with winter tires discount percentage greater than 100", "providerStates": [{"name": "a vehicle exists but input is invalid due to invalid discount percentage"}], "request": {"body": {"customerInvoiceRecipient": false, "customerPartnerNumber": "0123", "invoiceRecipientNumber": "0123", "paymentType": "LEASING", "salesDiscountPercentage": 20.5, "salesDiscountType": "VIP", "salesNetPriceEUR": 999.99, "salesPersonNumber": "S0123", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "winterTiresDiscountPercentage": 180, "winterTiresId": 1234, "winterTiresNetPriceEUR": 99.99}, "headers": {"Authorization": "Bearer testToken", "Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/invoices"}, "response": {"body": {"data": null, "errors": [{"detail": "winterTiresDiscountPercentage: Discount percentage must be between 0 and 100", "status": 400, "title": "Bad Request", "type": "error.system"}]}, "headers": {"Content-Type": "application/problem+json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.errors[*].detail": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 400}}, {"description": "a request to get vehicle sales invoices", "providerStates": [{"name": "get vehicle invoices failed with an internal error"}], "request": {"method": "GET", "path": "/ui/vehicles/invoices"}, "response": {"body": {"data": null, "errors": [{"type": "error.fvm.vehicleSales.invoiceReadError"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.errors[*].type": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 404}}, {"description": "a request to get vehicle sales invoices", "providerStates": [{"name": "no invoice data available for last one year"}], "request": {"method": "GET", "path": "/ui/vehicles/invoices"}, "response": {"body": {"data": [], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request to get vehicle sales invoices with all fields", "providerStates": [{"name": "a vehicle exist with sales data"}], "request": {"method": "GET", "path": "/ui/vehicles/invoices"}, "response": {"body": {"data": [{"peopleData": {"accountingArea": "DEPT1", "companyEmail": "<EMAIL>", "department": "DEPT1", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>"}, "vehicle": {"id": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "model": {"description": "911 Carrera"}, "status": "SX100", "version": 1, "vin": "*****************"}, "vehicleSales": {"comment": "reserved for <PERSON>", "customerDeliveryDate": null, "customerInvoiceRecipient": true, "customerPartnerNumber": "0123", "errorMessage": null, "finalInvoiceNumber": null, "fvmTransactionId": "T0111", "invoiceDate": null, "invoiceId": "a2de1023-3b08-4e38-b287-594904adf9f1", "invoiceNumber": null, "invoiceRecipientNumber": "0222", "invoiceStatus": "APPROVED", "isCurrent": true, "paymentReceived": false, "paymentType": "CASH", "plannedDeliveryDate": "2012-07-23T00:00:00Z", "receiptNumber": null, "referenceTransactionId": "T0222", "salesDiscountPercentage": 7.5, "salesDiscountType": "VIP", "salesNetPriceAfterDiscountEUR": 899.99, "salesNetPriceEUR": 999.99, "salesPersonNumber": "SP123", "transactionType": "INVOICE", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "winterTiresDiscountPercentage": 10.5, "winterTiresId": 1234, "winterTiresNetPriceAfterDiscountEUR": 99.99, "winterTiresNetPriceEUR": 99.99}}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[*].peopleData.accountingArea": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].peopleData.companyEmail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].peopleData.department": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].peopleData.firstName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].peopleData.lastName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicle.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicle.model.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicle.status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicle.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicle.vin": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.customerDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.customerInvoiceRecipient": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.customerPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.errorMessage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.finalInvoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.fvmTransactionId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.invoiceId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.invoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.invoiceRecipientNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.invoiceStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.isCurrent": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.paymentReceived": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.paymentType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.plannedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.receiptNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.referenceTransactionId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.salesDiscountPercentage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.salesDiscountType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.salesNetPriceAfterDiscountEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.salesNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.salesPersonNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.transactionType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.winterTiresDiscountPercentage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.winterTiresId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.winterTiresNetPriceAfterDiscountEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].vehicleSales.winterTiresNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request to reject vehicle invoice", "providerStates": [{"name": "a vehicle invoice exists with WAITING_FOR_APPROVAL status"}], "request": {"method": "DELETE", "path": "/ui/vehicles/invoices/a2de1023-3b08-4e38-b287-594904adf9f1"}, "response": {"body": {"data": "1506a0f9-623b-40c4-a37c-c2a294090198", "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request to update receipt and invoice data for an invoice", "providerStates": [{"name": "a request to update an existing invoice"}], "request": {"body": {"customerDeliveryDate": "2012-07-23T00:00:00Z", "finalInvoiceNumber": "IN123", "invoiceDate": "2012-07-23T00:00:00Z", "invoiceNumber": "INV-123456", "paymentReceived": true, "receiptNumber": "REC-123456"}, "headers": {"Authorization": "Bearer testToken", "Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.customerDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.finalInvoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.paymentReceived": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "PUT", "path": "/ui/vehicles/invoices/a2de1023-3b08-4e38-b287-594904adf9f1"}, "response": {"status": 200}}, {"description": "a request to update vehicle invoice with invalid status", "providerStates": [{"name": "an invoice exists with a status that cannot be updated"}], "request": {"body": {"invoiceNumber": "INV-789012", "receiptNumber": "REC-789012"}, "headers": {"Authorization": "Bearer testToken", "Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "PUT", "path": "/ui/vehicles/invoices/a2de1023-3b08-4e38-b287-594904adf9f1"}, "response": {"body": {"data": null, "errors": [{"detail": "Vehicle with invoice status: REFUND_COMPLETED cannot be updated", "title": "Vehicle invoice update failed", "type": "error.fvm.vehicleSales.invoiceUpdateError"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors[0].detail": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 400}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-sales"}}