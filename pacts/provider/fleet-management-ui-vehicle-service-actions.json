{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request for all campaigns", "providerStates": [{"name": "FVM has a multiple campaigns"}], "request": {"method": "GET", "path": "/ui/campaigns"}, "response": {"body": {"data": [{"createdAt": "2025-03-10T20:00:00Z", "description": "test-campaign-description", "id": "test-campaign-id", "isProcessable": false}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[*].createdAt": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].isProcessable": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request for campaign details for vehicle - error condition", "providerStates": [{"name": "get all campaigns API has an internal error"}], "request": {"method": "GET", "path": "/ui/campaigns"}, "response": {"body": {"data": null, "errors": [{"type": "error.fvm.campaigns.readError"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.errors[*].type": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 404}}, {"description": "a request for campaign details for vehicle - min condition", "providerStates": [{"name": "vehicle with id e89a3f2f-1986-4f7a-9dbb-2d0408f12345 has a single campaign with only required fields"}], "request": {"method": "GET", "path": "/ui/vehicles/e89a3f2f-1986-4f7a-9dbb-2d0408f12345/campaigns"}, "response": {"body": {"data": [{"description": null, "id": "test-campaign-id", "isProcessable": null}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[*].description": {"combine": "AND", "matchers": [{"match": "null"}]}, "$.data[*].id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].isProcessable": {"combine": "AND", "matchers": [{"match": "null"}]}}, "status": {}}, "status": 200}}, {"description": "a request for vehicle-campaign details - max condition", "providerStates": [{"name": "vehicle with id e89a3f2f-1986-4f7a-9dbb-2d0408f12345 has a single campaign with all fields"}], "request": {"method": "GET", "path": "/ui/vehicles/e89a3f2f-1986-4f7a-9dbb-2d0408f12345/campaigns"}, "response": {"body": {"data": [{"description": "test-campaign-description", "id": "test-campaign-id", "isProcessable": false}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data[*].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[*].isProcessable": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request to create a campaign with (existing) campaign-id OF22", "providerStates": [{"name": "FVM has campaign with campaign-id OF22"}], "request": {"body": {"description": "test-campaign-description for OF22", "id": "OF22", "isProcessable": false}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.isProcessable": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/campaigns"}, "response": {"body": {"type": "error.fvm.campaigns.alreadyExists"}, "headers": {"Content-Type": "application/problem+json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.type": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 400}}, {"description": "a request to create a new campaign with campaign-id OF22", "providerStates": [{"name": "FVM does not have a campaign with campaign-id OF22"}], "request": {"body": {"description": "test-campaign-description for OF22", "id": "OF22", "isProcessable": false}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.isProcessable": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/campaigns"}, "response": {"body": {"data": {"createdAt": "2025-03-10T20:00:00Z", "description": "test-campaign-description for OF22", "id": "OF22", "isProcessable": false}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.createdAt": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.isProcessable": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 201}}, {"description": "a request to update a campaign (does not exist in FVM) campaign-id OF22", "providerStates": [{"name": "FVM does not have campaign with campaign-id OF22"}], "request": {"body": {"id": "OF22", "isProcessable": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.isProcessable": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "PUT", "path": "/ui/campaigns/OF22"}, "response": {"body": {"data": null, "errors": [{"type": "error.fvm.campaigns.notFoundError"}]}, "headers": {"Content-Type": "application/problem+json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.errors[*].type": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 404}}, {"description": "a request to update a campaign with campaign-id OF22", "providerStates": [{"name": "FVM have a campaign with campaign-id OF22"}], "request": {"body": {"id": "OF22", "isProcessable": true}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.isProcessable": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "PUT", "path": "/ui/campaigns/OF22"}, "response": {"body": {"data": {"createdAt": "2025-03-10T20:00:00Z", "description": "test-campaign-description for OF22", "id": "OF22", "isProcessable": true}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.createdAt": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.isProcessable": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-service-actions"}}