{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "A request for creating a vehicle using a dataset with fields with a failed data.", "providerStates": [{"name": "Creation Service Returns validation errors when data is not matching"}], "request": {"body": {"amountSeats": 7, "currentTires": "SR", "depreciationRelevantCostCenterId": "44a7bfd2-ee52-42f7-bd3a-877a7fee4df0", "engineCapacity": 1234, "enginePower": 150, "externalLeaseEnd": null, "externalLeaseLessee": null, "externalLeaseRate": null, "externalLeaseStart": null, "financialAssetType": null, "internalContactPerson": "<PERSON><PERSON>", "internalOrderNumber": null, "licencePlate": null, "manufacturer": "Volkswagen", "modelDescription": "Lorem ipsum", "netPriceWithExtras": null, "orderType": "", "pmpDataOdometer": 12345, "primaryFuelType": "AIR", "registrationDate": null, "secondaryFuelType": "ELECTRIC", "technicalModelYear": 2025, "usageGroupId": "e7da3c85-175f-483a-98b9-310635c6dce8", "vehicleResponsiblePerson": "<PERSON><PERSON> sit", "vehicleType": "DEF", "vehicleUsageId": "def84b54-318a-46c0-b02c-8a65989bbb29", "vin": "ABCDEFGHJKLMNOPQR"}, "headers": {"Content-Type": "application/json"}, "method": "POST", "path": "/ui/vehicles"}, "response": {"body": {"data": null, "errors": [{"field": "primaryFuelType", "type": "error.validation.invalid-fuel-type"}, {"field": "orderType", "type": "error.validation.not-blank"}, {"field": "vin", "type": "error.validation.pattern"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.errors": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors[*]": {"combine": "AND", "matchers": [{"match": "type"}, {"match": "type"}, {"match": "type"}]}}, "header": {}, "status": {}}, "status": 422}}, {"description": "A request for creating a vehicle using a dataset with fields with a wrong data type.", "providerStates": [{"name": "Creation Service Returns a structure error when data type is not matching"}], "request": {"body": {"amountSeats": 7, "currentTires": "SR", "depreciationRelevantCostCenterId": "44a7bfd2-ee52-42f7-bd3a-877a7fee4df0", "engineCapacity": 1234, "enginePower": 150, "externalLeaseEnd": null, "externalLeaseLessee": null, "externalLeaseRate": null, "externalLeaseStart": null, "financialAssetType": null, "internalContactPerson": "<PERSON><PERSON>", "internalOrderNumber": null, "licencePlate": null, "manufacturer": "Volkswagen", "modelDescription": "Lorem ipsum", "netPriceWithExtras": null, "orderType": "", "pmpDataOdometer": 12345, "primaryFuelType": "SUPER", "registrationDate": null, "secondaryFuelType": "ELECTRIC", "technicalModelYear": 2025, "usageGroupId": "e7da3c85-175f-483a-98b9-310635c6dce8", "vehicleResponsiblePerson": "<PERSON><PERSON> sit", "vehicleType": "DEF", "vehicleUsageId": "just-a-astring-instead-of-uuid", "vin": "ABCDEFGHJKLMNOPQR"}, "headers": {"Content-Type": "application/json"}, "method": "POST", "path": "/ui/vehicles"}, "response": {"body": {"data": null, "errors": [{"type": "error.validation.structure"}]}, "headers": {"Content-Type": "application/json"}, "status": 422}}, {"description": "A request for creating a vehicle using a dataset with required fields.", "providerStates": [{"name": "Creation Service Returns the created vehicle data"}], "request": {"body": {"amountSeats": 7, "currentTires": "SR", "depreciationRelevantCostCenterId": "44a7bfd2-ee52-42f7-bd3a-877a7fee4df0", "engineCapacity": 1234, "enginePower": 150, "externalLeaseEnd": null, "externalLeaseLessee": null, "externalLeaseRate": null, "externalLeaseStart": null, "financialAssetType": null, "internalContactPerson": "<PERSON><PERSON>", "internalOrderNumber": null, "licencePlate": null, "manufacturer": "Volkswagen", "modelDescription": "Lorem ipsum", "netPriceWithExtras": null, "orderType": "ABC", "pmpDataOdometer": 12345, "primaryFuelType": "SUPER", "registrationDate": null, "secondaryFuelType": "ELECTRIC", "technicalModelYear": 2025, "usageGroupId": "e7da3c85-175f-483a-98b9-310635c6dce8", "vehicleResponsiblePerson": "<PERSON><PERSON> sit", "vehicleType": "PKW", "vehicleUsageId": "e03a2177-54b4-4300-859a-db617c29cb84", "vin": "ABCDEFG12345HJKLM"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.amountSeats": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.currentTires": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.depreciationRelevantCostCenterId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.engineCapacity": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.enginePower": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.internalContactPerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.modelDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.orderType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.pmpDataOdometer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.primaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.secondaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.technicalModelYear": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.usageGroupId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleResponsiblePerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleUsageId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vin": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicles"}, "response": {"body": {"data": {"vehicle": {"id": "fb88bce8-1e4c-454e-a64f-b21e10e39103"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.data.vehicle.id": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 201}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-service-create-vehicle"}}