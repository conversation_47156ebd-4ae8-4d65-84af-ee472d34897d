{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "A request for key flag with vehicleIds some of which have errors", "providerStates": [{"name": "Data Service Returns errors for some vehicleIds for key flag"}], "request": {"body": {"vehicleIds": ["b34f718d-a658-404c-af70-4027e2330111", "013bfe5a-eb5e-43de-a7ac-7972d16f4222"]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.vehicleIds[0]": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleIds[1]": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicle-transfers/key-flag"}, "response": {"body": {"data": null, "errors": [{"detail": "12345678-1234-5678-1234-000000000111 : vehicle is missing", "status": 500, "title": "Problem generating PDF", "type": "error.fvm.dataMissing"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.errors": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.errors[*].detail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors[*].status": {"combine": "AND", "matchers": [{"match": "number"}]}, "$.errors[*].title": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors[*].type": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 500}}, {"description": "A request for power of attorney with vehicleIds some of which have errors", "providerStates": [{"name": "Data Service Returns errors for some vehicleIds for power of attorney"}], "request": {"body": {"vehicleIds": ["12345678-1234-5678-1234-000000000333", "12345678-1234-5678-1234-000000000444"]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.vehicleIds[0]": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleIds[1]": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicles/power-of-attorney"}, "response": {"body": {"data": null, "errors": [{"detail": "12345678-1234-5678-1234-000000000111 : LicensePlate is missing", "status": 500, "title": "Problem generating PDF", "type": "error.fvm.dataMissing"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.errors": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.errors[*].detail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors[*].status": {"combine": "AND", "matchers": [{"match": "number"}]}, "$.errors[*].title": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors[*].type": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 500}}, {"description": "A request to download a PDF for vehicle insurances", "providerStates": [{"name": "PDF download service is available"}], "request": {"body": {"vehicleIds": ["ce118b6e-d8e1-11e7-9296-cec278b6b50a"]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.vehicleIds": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.vehicleIds[*]": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicles/insurance-card"}, "response": {"headers": {"Content-Disposition": "attachment; filename=document.pdf", "Content-Type": "application/pdf"}, "status": 200}}, {"description": "A request to download a power of attorney PDF", "providerStates": [{"name": "Power of attorney service is available"}], "request": {"body": {"vehicleIds": ["ce118b6e-d8e1-11e7-9296-cec278b6b50a"]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.vehicleIds": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.vehicleIds[*]": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicles/power-of-attorney"}, "response": {"headers": {"Content-Disposition": "attachment; filename=document.pdf", "Content-Type": "application/pdf"}, "status": 200}}, {"description": "A request with vehicleIds some of which have errors", "providerStates": [{"name": "Data Service Returns data for some vehicleIds and errors for some vehicleIds"}], "request": {"body": {"vehicleIds": ["12345678-1234-5678-1234-000000000333", "12345678-1234-5678-1234-000000000444"]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.vehicleIds[0]": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleIds[1]": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicles/insurance-card"}, "response": {"body": {"data": null, "errors": [{"detail": "12345678-1234-5678-1234-000000000111 : no registration date found!", "status": 500, "title": "Problem generating PDF", "type": "error.fvm.dataMissing"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.errors": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.errors[*].detail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors[*].status": {"combine": "AND", "matchers": [{"match": "number"}]}, "$.errors[*].title": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors[*].type": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 500}}, {"description": "a request to create a key flag", "providerStates": [{"name": "can create the key flag successfully"}], "request": {"body": {"vehicleIds": ["ce118b6e-d8e1-11e7-9296-cec278b6b50a"]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.vehicleIds": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.vehicleIds[*]": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}(-[0-9a-f]{4}){3}-[0-9a-f]{12}$"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicle-transfers/key-flag"}, "response": {"headers": {"Content-Disposition": "attachment; filename=document.pdf", "Content-Type": "application/pdf"}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-service-document"}}