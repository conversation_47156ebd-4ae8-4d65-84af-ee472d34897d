{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request to export all vehicles from vehicle-manager to csv", "providerStates": [{"name": "Export service exports all vehicles as csv"}], "request": {"body": {"columnState": [{"colId": "vehicle.vin", "hide": true, "translation": "VIN"}, {"colId": "vehicle.id", "hide": false, "translation": "ID"}, {"colId": "vehicle.model.description", "hide": false, "translation": "Model Description"}], "filterModel": {"vehicle.model.description": {"filter": "<PERSON><PERSON>", "filterType": "text", "type": "contains"}}, "language": "de", "selectedRows": [], "sortModel": [{"colId": "vehicle.model.description", "sort": "asc"}]}, "headers": {"Content-Type": "application/json", "x-amzn-oidc-accesstoken": ".*", "x-amzn-oidc-data": ".*"}, "matchingRules": {"body": {}, "header": {"x-amzn-oidc-accesstoken": {"combine": "AND", "matchers": [{"match": "type"}]}, "x-amzn-oidc-data": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/export"}, "response": {"body": "ID;Model Description\nbe1dbb6a-c912-4b46-89a2-d2f51e3aaa9e;<PERSON><PERSON> A\na83bc3e6-f316-44fa-8c89-b5f66a5dbcc8;<PERSON><PERSON> B\n", "headers": {"Content-Type": "text/csv"}, "status": 200}}, {"description": "a request to export selected client side data to csv", "providerStates": [{"name": "Export service exports selected client side data as csv"}], "request": {"body": {"columnState": [{"colId": "label", "hide": false, "translation": "Label"}, {"colId": "oldValue", "hide": false, "translation": "Old Value"}, {"colId": "newValue", "hide": false, "translation": "New Value"}], "filterModel": {}, "language": "de", "selectedRows": [{"field": "vehicleRegistrationOrder.commenter", "label": "<PERSON><PERSON><PERSON>", "modifiedAt": "2024-09-30T18:53:57.221824Z", "modifiedBy": "<PERSON>", "newValue": "Testing222", "oldValue": "Testing111", "vehicleId": "16092335-4bee-4093-a611-d8d85e09f19f"}, {"field": "vehicleRegistrationOrder.briefNumber", "label": "Brief Number", "modifiedAt": "2024-09-30T18:53:57.221824Z", "modifiedBy": "<PERSON>", "newValue": "2", "oldValue": "1", "vehicleId": "ca113760-729f-4a86-97fb-2604e11f4b75"}], "sortModel": []}, "headers": {"Content-Type": "application/json", "x-amzn-oidc-accesstoken": ".*", "x-amzn-oidc-data": ".*"}, "matchingRules": {"body": {}, "header": {"x-amzn-oidc-accesstoken": {"combine": "AND", "matchers": [{"match": "type"}]}, "x-amzn-oidc-data": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/export"}, "response": {"body": "Label;Old Value;New Value\nCommenter;Testing222;Testing111\nBrief Number;1;2\n", "headers": {"Content-Type": "text/csv"}, "status": 200}}, {"description": "a request to export user-selected vehicles from vehicle-manager to csv", "providerStates": [{"name": "Export service exports selected vehicles as csv"}], "request": {"body": {"columnState": [{"colId": "vehicle.id", "hide": false, "translation": "VIN"}, {"colId": "vehicle.model.description", "hide": false, "translation": "Model Description"}, {"colId": "preDeliveryInspection.orderedDate", "hide": false, "translation": "Ordered"}, {"colId": "preDeliveryInspection.plannedDate", "hide": false, "translation": "Planned"}, {"colId": "preDeliveryInspection.completedDate", "hide": false, "translation": "Completed"}, {"colId": "preDeliveryInspection.comment", "hide": false, "translation": "Comment"}], "filterModel": {}, "language": "de", "selectedRows": [{"nonCustomerAdequate": {"actualRebuildCost": null, "comment": "parsley", "expectedRevenue": 838489.**********, "ncaStatus": "NCA", "plannedRebuildCost": null, "profitabilityAuditDone": "2011-12-04T00:00:00Z", "rebuildDone": "2020-02-19T00:00:00Z", "rebuildStarted": "2019-12-13T00:00:00Z", "salesPrice": 315688.0594557952}, "preDeliveryInspection": {"charge": false, "comment": "Alles gut", "completedDate": "2026-01-07T19:01:08.449Z", "digitalLogbook": true, "foiling": false, "id": "be1dbb6a-c912-4b46-89a2-d2f51e3aaa9e", "isRelevant": true, "licencePlateMounting": false, "orderedDate": "2026-01-06T19:01:08.449Z", "plannedDate": "2026-01-01T19:01:08.449Z", "refuel": true, "tireSet": "SR"}, "vehicle": {"color": {"exterior": "licorice", "exteriorDescription": null, "interior": null, "interiorDescription": null}, "consumption": {"driveType": "OVC_HEV", "primaryFuelType": "ELECTRIC", "secondaryFuelType": "REGULAR", "typification": "patriot"}, "country": {"bnrValue": null, "cnrCountryDescription": "elongation", "cnrValue": null}, "createdAt": "Fri May 10 2024 02:45:40 GMT+0200 (Central European Summer Time)", "currentTires": "SR", "delivery": {"isPreparationNecessary": true, "preparationDoneDate": "2020-07-19T00:00:00Z"}, "embargo": {"inEmbargo": true}, "equiId": "53c648a8-1dc6-41d6-8db9-aed0c3d6a713", "equipmentNumber": 6692555223728128, "evaluation": {"appraisalNetPrice": 1000, "pcComplaintCheckComment": "abc", "vehicleEvaluationComment": "abc"}, "externalLeaseEnd": "Fri Sep 13 2024 23:14:48 GMT+0200 (Central European Summer Time)", "externalLeaseLessee": "<PERSON>", "externalLeaseRate": 3788680269398016, "externalLeaseStart": "Wed Aug 28 2024 18:33:19 GMT+0200 (Central European Summer Time)", "financialAssetType": "UV", "fleet": {"approvedForScrappingDate": "2020-07-19T00:00:00Z", "costEstimationOrderedDate": "2020-07-19T00:00:00Z", "isResidualValueMarket": false, "profitabilityAuditDate": "2020-07-19T00:00:00Z", "scrapVehicle": true, "scrappedDate": "2020-07-19T00:00:00Z", "scrappedVehicleOfferedDate": "2020-07-19T00:00:00Z", "soldCupCarDate": "2020-07-19T00:00:00Z", "soldDate": "2020-07-19T00:00:00Z", "stolenDate": "2020-07-19T00:00:00Z", "vehicleSentToSalesDate": "2020-07-19T00:00:00Z"}, "id": "7f1718e2-c7ed-45a8-b9c1-77ff6be44fcf", "model": {"description": "911 Carrera", "manufacturer": "grandfather", "orderType": "mind", "productCode": "valuable", "productId": "bCkIhYOTDvNa9DLr4f1HS", "vehicleType": "PKW"}, "numberOfDamages": 3, "order": {"blockedForSale": null, "commissionNumber": null, "deliveryType": null, "department": "VAM, VAQ", "importerShortName": null, "invoiceDate": "2025-03-09T09:04:49.918Z", "invoiceNumber": null, "leasingType": "DLF", "preproductionVehicle": null, "primaryStatus": null, "purchaseOrderDate": "2025-02-06T13:56:43.937Z", "purposeOrderType": "mobility", "requestedDeliveryDate": "2024-09-17T04:44:44.200Z", "tradingPartnerNumber": "dogsled"}, "pmp": {"odometer": ****************, "timestamp": "2024-07-23T11:23:23.873Z"}, "price": {"factoryGrossPriceEUR": 1234, "factoryNetPriceEUR": 1234, "grossPrice": 466729, "netPriceWithExtras": 357972}, "production": {"endDate": "2025-03-04T00:17:45.145Z", "factory": null, "factoryVW": 4080, "gearBoxClass": null, "number": "counsel", "plannedEndDate": "2025-11-13T22:10:53.021Z", "quoteMonth": 7, "quoteYear": 2001, "technicalModelYear": 2014}, "referenceId": "21f5dd3f-7cbf-4205-b9e8-ab91571e5b57", "repairfixCarId": "d4r6yIhYOTDvNa9DLr34gte", "returnInfo": {"factoryCarPreparationOrderNumber": "123", "isUsedCar": false, "keyReturned": "2020-07-19T00:00:00Z", "nextProcess": "CHECK_IF_REUSAGE_IS_POSSIBLE"}, "source": "PVH", "status": "SX100", "technical": {"amountSeats": 8, "engineCapacity": 3689}, "tireSetChange": {"comment": "something", "completedDate": "2016-08-30T00:00:00Z", "orderedDate": "2016-08-30T00:00:00Z"}, "version": 0, "vguid": "fBPfUuBi9zuhZQ4XSg-9q", "vin": "WF72R3RJXNFT33348"}, "vehicleCampaigns": null, "vehicleLastKnownLocation": {"building": "abaft", "comment": "but", "compoundName": "astride", "eventType": "IN", "level": "0", "occurredOn": "2020-07-19T00:00:00Z", "parkingLot": "365", "vehicleId": "0a3f74a9-9c0f-42a9-a88b-2d24dd5fc9a3"}, "vehicleRegistration": {"briefNumber": "1234", "commenter": "test", "firstRegistrationDate": "2014-11-19T00:00:00Z", "hsn": "2034", "lastDeRegistrationDate": "2020-02-06T00:00:00Z", "lastRegistrationDate": "2010-12-30T00:00:00Z", "licencePlate": "GI 6505", "registrationDate": null, "registrationStatus": "REGISTERED", "registrationType": 4, "sfme": true, "storageLocation": "LOG", "testNumber": null, "testVehicle": false, "tsn": "XWBOTXCO"}, "vehicleSales": {"comment": "adult", "contractSigned": false, "customerDeliveryDate": "2012-03-24T00:00:00Z", "customerPartnerNumber": "0000123456", "finalInvoiceNumber": "INV123456", "fvmTransactionId": "FVMTRX5678", "invoiceDate": "2025-02-06T00:00:00Z", "invoiceNumber": "INV123456", "invoiceRecipientNumber": "0000567890", "plannedDeliveryDate": "2019-02-06T00:00:00Z", "receiptNumber": "RCPT123456", "reservedForB2C": false, "salesNetPriceAfterDiscountEUR": 112300.67, "salesNetPriceEUR": 112345.67, "salesPersonNumber": "001234", "winterTiresId": 987654321, "winterTiresNetPriceEUR": 234.56}, "vehicleTransfer": {"deliveryComment": "Deliver to the main office.", "deliveryDate": "2026-04-08T10:00:00Z", "deliveryLeipzig": true, "depreciationRelevantCostCenterId": null, "desiredDeliveryDate": "2026-04-05T10:00:00Z", "desiredTireSet": "SR", "desiredTireSetChangedManually": false, "id": "7e6edb8f-cfbc-44c8-b97a-6cc9c5497b9d", "internalContactPerson": "<PERSON>", "internalOrderNumber": "67890", "latestReturnDate": "2026-05-10T10:00:00Z", "leasingPrivilege": "Standard", "leasingPrivilegeValidationSuccessful": true, "licensePlate": "XYZ-1234", "maintenanceOrderNumber": "MNT-123456", "maximumServiceLifeInMonths": 36, "mileageAtDelivery": 15000, "mileageAtReturn": 20000, "plannedDeliveryDate": null, "plannedReturnDate": "2026-05-01T10:00:00Z", "predecessorLatestReturnDate": "2026-04-30T10:00:00Z", "provisionForDeliveryComment": "Handle with care.", "registrationNeeded": false, "remark": "This vehicle is in good condition.", "returnComment": "Returned in excellent condition.", "returnDate": "2026-05-08T10:00:00Z", "serviceCard": "CHARGING_CARD", "status": "ACTIVE", "successorOrderDate": null, "usageGroupId": null, "usingCostCenter": "12345", "utilizationArea": "Urban", "vehicleId": "7f1718e2-c7ed-45a8-b9c1-77ff6be44fcf", "vehicleResponsiblePerson": "54321", "vehicleTransferKey": 98765, "vehicleUsageId": null, "version": 1}}, {"nonCustomerAdequate": {"actualRebuildCost": null, "comment": "loosely", "expectedRevenue": 795415.**********, "ncaStatus": null, "plannedRebuildCost": 819146.**********, "profitabilityAuditDone": "2022-12-22T00:00:00Z", "rebuildDone": "2016-10-12T00:00:00Z", "rebuildStarted": "2010-12-01T00:00:00Z", "salesPrice": 913979.9066944646}, "preDeliveryInspection": {"charge": true, "comment": "Alles gut", "completedDate": "2026-01-07T19:01:08.449Z", "digitalLogbook": true, "foiling": true, "id": "a83bc3e6-f316-44fa-8c89-b5f66a5dbcc8", "isRelevant": true, "licencePlateMounting": false, "orderedDate": "2026-01-06T19:01:08.449Z", "plannedDate": "2026-01-01T19:01:08.449Z", "refuel": false, "tireSet": "WR"}, "vehicle": {"color": {"exterior": "licorice", "exteriorDescription": null, "interior": null, "interiorDescription": null}, "consumption": {"driveType": "OVC_HEV", "primaryFuelType": "ELECTRIC", "secondaryFuelType": "REGULAR", "typification": "patriot"}, "country": {"bnrValue": null, "cnrCountryDescription": "elongation", "cnrValue": null}, "createdAt": "Fri May 10 2024 02:45:40 GMT+0200 (Central European Summer Time)", "currentTires": "SR", "delivery": {"isPreparationNecessary": true, "preparationDoneDate": "2020-07-19T00:00:00Z"}, "embargo": {"inEmbargo": true}, "equiId": "53c648a8-1dc6-41d6-8db9-aed0c3d6a713", "equipmentNumber": 6692555223728128, "evaluation": {"appraisalNetPrice": 1000, "pcComplaintCheckComment": "abc", "vehicleEvaluationComment": "abc"}, "externalLeaseEnd": "Fri Sep 13 2024 23:14:48 GMT+0200 (Central European Summer Time)", "externalLeaseLessee": "<PERSON>", "externalLeaseRate": 3788680269398016, "externalLeaseStart": "Wed Aug 28 2024 18:33:19 GMT+0200 (Central European Summer Time)", "financialAssetType": "UV", "fleet": {"approvedForScrappingDate": "2020-07-19T00:00:00Z", "costEstimationOrderedDate": "2020-07-19T00:00:00Z", "isResidualValueMarket": false, "profitabilityAuditDate": "2020-07-19T00:00:00Z", "scrapVehicle": true, "scrappedDate": "2020-07-19T00:00:00Z", "scrappedVehicleOfferedDate": "2020-07-19T00:00:00Z", "soldCupCarDate": "2020-07-19T00:00:00Z", "soldDate": "2020-07-19T00:00:00Z", "stolenDate": "2020-07-19T00:00:00Z", "vehicleSentToSalesDate": "2020-07-19T00:00:00Z"}, "id": "be1dbb6a-c912-4b46-89a2-d2f51e3aaa9e", "model": {"description": "911 Carrera", "manufacturer": "grandfather", "orderType": "mind", "productCode": "valuable", "productId": "bCkIhYOTDvNa9DLr4f1HS", "vehicleType": "PKW"}, "numberOfDamages": 3, "order": {"blockedForSale": null, "commissionNumber": null, "deliveryType": null, "department": "VAM, VAQ", "importerShortName": null, "invoiceDate": "2025-03-09T09:04:49.918Z", "invoiceNumber": null, "leasingType": "DLF", "preproductionVehicle": null, "primaryStatus": null, "purchaseOrderDate": "2025-02-06T13:56:43.937Z", "purposeOrderType": "mobility", "requestedDeliveryDate": "2024-09-17T04:44:44.200Z", "tradingPartnerNumber": "dogsled"}, "pmp": {"odometer": ****************, "timestamp": "2024-07-23T11:23:23.873Z"}, "price": {"factoryGrossPriceEUR": 1234, "factoryNetPriceEUR": 1234, "grossPrice": 466729, "netPriceWithExtras": 357972}, "production": {"endDate": "2025-03-04T00:17:45.145Z", "factory": null, "factoryVW": 4080, "gearBoxClass": null, "number": "counsel", "plannedEndDate": "2025-11-13T22:10:53.021Z", "quoteMonth": 7, "quoteYear": 2001, "technicalModelYear": 2014}, "referenceId": "21f5dd3f-7cbf-4205-b9e8-ab91571e5b57", "repairfixCarId": "d4r6yIhYOTDvNa9DLr34gte", "returnInfo": {"factoryCarPreparationOrderNumber": "123", "isUsedCar": false, "keyReturned": "2020-07-19T00:00:00Z", "nextProcess": "CHECK_IF_REUSAGE_IS_POSSIBLE"}, "source": "PVH", "status": "SX100", "technical": {"amountSeats": 8, "engineCapacity": 3689}, "tireSetChange": {"comment": "something", "completedDate": "2016-08-30T00:00:00Z", "orderedDate": "2016-08-30T00:00:00Z"}, "version": 0, "vguid": "fBPfUuBi9zuhZQ4XSg-9q", "vin": "WF72R3RJXNFT33348"}, "vehicleCampaigns": null, "vehicleLastKnownLocation": null, "vehicleRegistration": {"briefNumber": "1234", "commenter": "test", "firstRegistrationDate": "2017-03-04T00:00:00Z", "hsn": "5824", "lastDeRegistrationDate": null, "lastRegistrationDate": "2019-08-11T00:00:00Z", "licencePlate": "PS 3407", "registrationDate": null, "registrationStatus": null, "registrationType": 1, "sfme": false, "storageLocation": "BCG", "testNumber": null, "testVehicle": true, "tsn": "CREKYJKG"}, "vehicleSales": {"comment": "adult", "contractSigned": false, "customerDeliveryDate": "2012-03-24T00:00:00Z", "customerPartnerNumber": "0000123456", "finalInvoiceNumber": "INV123456", "fvmTransactionId": "FVMTRX5678", "invoiceDate": "2025-02-06T00:00:00Z", "invoiceNumber": "INV123456", "invoiceRecipientNumber": "0000567890", "plannedDeliveryDate": "2019-02-06T00:00:00Z", "receiptNumber": "RCPT123456", "reservedForB2C": false, "salesNetPriceAfterDiscountEUR": 112300.67, "salesNetPriceEUR": 112345.67, "salesPersonNumber": "001234", "winterTiresId": 987654321, "winterTiresNetPriceEUR": 234.56}, "vehicleTransfer": {"deliveryComment": "Deliver to the main office.", "deliveryDate": "2026-04-08T10:00:00Z", "deliveryLeipzig": true, "depreciationRelevantCostCenterId": null, "desiredDeliveryDate": "2026-04-05T10:00:00Z", "desiredTireSet": "SR", "desiredTireSetChangedManually": false, "id": "7e6edb8f-cfbc-44c8-b97a-6cc9c5497b9d", "internalContactPerson": "<PERSON>", "internalOrderNumber": "67890", "latestReturnDate": "2026-05-10T10:00:00Z", "leasingPrivilege": "Standard", "leasingPrivilegeValidationSuccessful": true, "licensePlate": "XYZ-1234", "maintenanceOrderNumber": "MNT-123456", "maximumServiceLifeInMonths": 36, "mileageAtDelivery": 15000, "mileageAtReturn": 20000, "plannedDeliveryDate": null, "plannedReturnDate": "2026-05-01T10:00:00Z", "predecessorLatestReturnDate": "2026-04-30T10:00:00Z", "provisionForDeliveryComment": "Handle with care.", "registrationNeeded": false, "remark": "This vehicle is in good condition.", "returnComment": "Returned in excellent condition.", "returnDate": "2026-05-08T10:00:00Z", "serviceCard": "CHARGING_CARD", "status": "ACTIVE", "successorOrderDate": null, "usageGroupId": null, "usingCostCenter": "12345", "utilizationArea": "Urban", "vehicleId": "7f1718e2-c7ed-45a8-b9c1-77ff6be44fcf", "vehicleResponsiblePerson": "54321", "vehicleTransferKey": 98765, "vehicleUsageId": null, "version": 1}}], "sortModel": []}, "headers": {"Content-Type": "application/json", "x-amzn-oidc-accesstoken": ".*", "x-amzn-oidc-data": ".*"}, "matchingRules": {"body": {}, "header": {"x-amzn-oidc-accesstoken": {"combine": "AND", "matchers": [{"match": "type"}]}, "x-amzn-oidc-data": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/export"}, "response": {"body": "VIN;Model Description;Ordered;Planned;Completed;Comment\nBVNZFVAT3XK875647;Cayman;2026-01-06T19:01:08.449Z;2026-01-01T19:01:08.449Z;2026-01-07T19:01:08.449Z,<PERSON><PERSON> gut\nSN5VBMTX0VJY49668;Cayenne;2026-01-06T19:01:08.449Z;2026-01-01T19:01:08.449Z;2026-01-07T19:01:08.449Z,<PERSON><PERSON> gut\n", "headers": {"Content-Type": "text/csv"}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-service-export"}}