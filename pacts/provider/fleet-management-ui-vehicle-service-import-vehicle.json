{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "A request for importing a vehicle using a vin when not found.", "providerStates": [{"name": "Vehicle with identifier (1FMZU34E1XUA27757) is not available in PVH"}], "request": {"body": {"identifier": "1FMZU34E1XUA27757", "reference": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.identifier": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicles/import"}, "response": {"body": {"data": null, "errors": [{"detail": "any string", "instance": "/ui/vehicles/import", "status": 500, "title": "Vehicle not found on PVH", "type": "error.fvm.pvhVehicleNotFound"}]}, "headers": {"Content-Type": "application/problem+json"}, "matchingRules": {"body": {"$.errors[0].detail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors[0].title": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 500}}, {"description": "A request for importing a vehicle using a vin.", "providerStates": [{"name": "Import Service Returns the imported vehicle data"}], "request": {"body": {"identifier": "1FMZU34E1XUA27757", "reference": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.identifier": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicles/import"}, "response": {"body": {"data": {"vehicle": {"vin": "1FMZU34E1XUA27757"}}}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.data.vehicle.vin": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 201}}, {"description": "A request for importing a vehicle using a vin.", "providerStates": [{"name": "Vehicle with identifier 1FMZU34E1XUA27757 already exists"}], "request": {"body": {"identifier": "1FMZU34E1XUA27757", "reference": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.identifier": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicles/import"}, "response": {"body": {"data": null, "errors": [{"detail": "Vehicle with identifier (1FMZU34E1XUA27757) already exists", "instance": "/ui/vehicles/import", "properties": {"vin": "1FMZU34E1XUA27757"}, "status": 409, "title": "Conflict", "type": "error.fvm.vehicleAlreadyExists"}]}, "headers": {"Content-Type": "application/problem+json"}, "matchingRules": {"body": {"$.errors[0].detail": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}, "status": {}}, "status": 409}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-service-import-vehicle"}}