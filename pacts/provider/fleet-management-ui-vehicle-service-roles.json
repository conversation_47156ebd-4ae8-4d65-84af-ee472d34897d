{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "A request for fetching the roles assigned to a user by employee number", "providerStates": [{"name": "vehicle service provides list of associated roles to an admin user"}], "request": {"body": {"employeeNumber": "Personalnummer"}, "headers": {"Content-Type": "application/json"}, "method": "POST", "path": "/ui/roles/by-employee-number"}, "response": {"body": {"data": [{"active": true, "createdAt": "2024-01-01T10:00:00Z", "description": "Authorized Group 1", "displayName": "Group 1", "id": "550e8400-e29b-41d4-a716-446655440000"}, {"active": false, "createdAt": "2024-01-01T10:00:00Z", "description": "Auditing Group 2", "displayName": "Auditors Externa", "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].active": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].createdAt": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$"}]}, "$.data[0].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].displayName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].id": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}]}, "$.data[1].active": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].createdAt": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$"}]}, "$.data[1].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].displayName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].id": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}]}}, "header": {}, "status": {}}, "status": 200}}, {"description": "A request for listing the roles", "providerStates": [{"name": "vehicle service provides roles list to an admin user"}], "request": {"method": "GET", "path": "/ui/roles"}, "response": {"body": {"data": [{"active": true, "createdAt": "2024-01-01T10:00:00Z", "description": "Authorized Group 1", "displayName": "Group 1", "id": "550e8400-e29b-41d4-a716-446655440000"}, {"active": false, "createdAt": "2024-01-01T10:00:00Z", "description": "Auditing Group 2", "displayName": "Auditors Externa", "id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8"}, {"active": true, "createdAt": "2024-01-01T10:00:00Z", "description": "Human Resources", "displayName": "Human Resources", "id": "7f8d8c1d-5a9b-4f6e-9227-cd1a4a3cb50b"}]}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].active": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].createdAt": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$"}]}, "$.data[0].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].displayName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].id": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}]}, "$.data[1].active": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].createdAt": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$"}]}, "$.data[1].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].displayName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[1].id": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}]}, "$.data[2].active": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[2].createdAt": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}(\\.\\d+)?Z$"}]}, "$.data[2].description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[2].displayName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[2].id": {"combine": "AND", "matchers": [{"match": "regex", "regex": "^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$"}]}}, "header": {}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-service-roles"}}