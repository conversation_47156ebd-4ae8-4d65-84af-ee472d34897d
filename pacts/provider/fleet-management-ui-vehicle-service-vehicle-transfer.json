{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request for vehicle transfer usage data", "providerStates": [{"name": "Vehicle transfer usage data exists"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/ui/vehicles/e89a3f2f-1986-4f7a-9dbb-2d0408f12345/usage"}, "response": {"body": {"data": {"currentTransfer": {"accountingArea": "a123", "companyEmail": "<EMAIL>", "companyMobileNumber": "(*************", "deliveryDate": "2025-09-27T03:38:15.700Z", "department": "Shoes", "employeeNumber": "31KCwYxwTvTTYH26VZmYj", "firstName": "<PERSON>", "internalOrderNumber": "978-1-9743-1028-9", "key": 1, "lastName": "<PERSON><PERSON><PERSON>", "leasingPrivilegeLeasing": "glass", "licensePlate": "YN 2499", "mileageAtDelivery": 45695, "mileageAtReturn": 3876, "plannedDeliveryDate": "2024-04-11T20:34:44.565Z", "plannedReturnedDate": "2025-11-06T05:55:12.176Z", "privateEmail": "<EMAIL>", "usingCostCenter": "alternative", "returnedDate": "2025-08-30T21:03:50.947Z", "status": "PLANNED", "usageGroup": "baggie", "vehicleUsage": "Books"}, "transfers": [{"accountingArea": "a123", "companyEmail": "<EMAIL>", "companyMobileNumber": "(*************", "deliveryDate": "2025-09-27T03:38:15.700Z", "department": "Shoes", "employeeNumber": "31KCwYxwTvTTYH26VZmYj", "firstName": "<PERSON>", "internalOrderNumber": "978-1-9743-1028-9", "key": 1, "lastName": "<PERSON><PERSON><PERSON>", "leasingPrivilegeLeasing": "glass", "licensePlate": "YN 2499", "mileageAtDelivery": 45695, "mileageAtReturn": 3876, "plannedDeliveryDate": "2024-04-11T20:34:44.565Z", "plannedReturnedDate": "2025-11-06T05:55:12.176Z", "privateEmail": "<EMAIL>", "usingCostCenter": "alternative", "returnedDate": "2025-08-30T21:03:50.947Z", "status": "PLANNED", "usageGroup": "baggie", "vehicleUsage": "Books"}]}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.data.currentTransfer.accountingArea": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.companyEmail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.companyMobileNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.deliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.department": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.employeeNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.firstName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.internalOrderNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.key": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.lastName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.leasingPrivilegeLeasing": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.licensePlate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.mileageAtDelivery": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.mileageAtReturn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.plannedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.plannedReturnedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.privateEmail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.usingCostCenter": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.returnedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.usageGroup": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.currentTransfer.vehicleUsage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers": {"combine": "AND", "matchers": [{"match": "type", "min": 1}]}, "$.data.transfers[*].accountingArea": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].companyEmail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].companyMobileNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].deliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].department": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].employeeNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].firstName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].internalOrderNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].key": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].lastName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].leasingPrivilegeLeasing": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].licensePlate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].mileageAtDelivery": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].mileageAtReturn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].plannedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].plannedReturnedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].privateEmail": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].usingCostCenter": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].returnedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].status": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].usageGroup": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.transfers[*].vehicleUsage": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-service-vehicle-transfer"}}