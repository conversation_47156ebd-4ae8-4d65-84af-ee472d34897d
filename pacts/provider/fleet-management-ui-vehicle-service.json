{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request for mileage reading", "providerStates": [{"name": "vehicle mileage readings exist"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/ui/vehicles/e89a3f2f-1986-4f7a-9dbb-2d0408f12345/mileage-readings"}, "response": {"body": {"data": [{"createdBy": "P1234", "mileage": 100, "readDate": "2025-01-01T00:00:00Z", "source": "CAR_SYNC"}], "errors": null, "warnings": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request for vehicle details", "providerStates": [{"name": "vehicle details exist"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/ui/vehicles/e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}, "response": {"body": {"data": {"vehicle": {"color": {"exterior": "licorice", "exteriorDescription": "hello world", "interior": "hello world", "interiorDescription": "hello world"}, "consumption": {"driveType": "HEV", "primaryFuelType": "E10", "secondaryFuelType": "ELECTRIC", "typification": "patriot"}, "country": {"bnrValue": "hello world", "cnrCountryDescription": "elongation", "cnrValue": "hello world"}, "createdAt": "2025-03-09T09:04:49.918Z", "currentMileage": {"mileage": 123213, "readDate": "2012-01-16T00:00:00Z"}, "currentTires": "SR", "delivery": {"isPreparationNecessary": true, "preparationDoneDate": "2012-01-16T00:00:00Z"}, "embargo": {"inEmbargo": true}, "equiId": "53c648a8-1dc6-41d6-8db9-aed0c3d6a713", "equipmentNumber": 6692555223728128, "evaluation": {"appraisalNetPrice": 1000, "pcComplaintCheckComment": "abc", "vehicleEvaluationComment": "abc"}, "externalLeaseEnd": "2025-03-09T09:04:49.918Z", "externalLeaseLessee": "<PERSON>", "externalLeaseRate": 3788680269398016, "externalLeaseStart": "2025-03-09T09:04:49.918Z", "financialAssetType": "AV", "fleet": {"soldDate": "2012-01-16T00:00:00Z", "stolenDate": "2012-01-16T00:00:00Z"}, "id": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "model": {"description": "911 Carrera", "manufacturer": "grandfather", "orderType": "mind", "productCode": "valuable", "productId": "bCkIhYOTDvNa9DLr4f1HS", "range": "Taycan", "vehicleType": "ecclesia"}, "options": null, "order": {"blockedForSale": false, "commissionNumber": "hello world", "deliveryType": "hello world", "department": "VAM, VAQ", "importerShortName": "hello world", "invoiceDate": "2025-03-09T09:04:49.918Z", "invoiceNumber": "hello world", "leasingType": "DLF", "preproductionVehicle": false, "primaryStatus": "hello world", "purchaseOrderDate": "2025-02-06T13:56:43.937Z", "purposeOrderType": "mobility", "requestedDeliveryDate": "2024-09-17T04:44:44.200Z", "tradingPartnerNumber": "dogsled"}, "pmp": {"odometer": 4023353465634816, "timestamp": "2024-07-23T11:23:23.873Z"}, "price": {"factoryGrossPriceEUR": 1234, "factoryNetPriceEUR": 1234, "grossPrice": 466729, "netPriceWithExtras": 357972, "piaEvent": null}, "production": {"endDate": "2025-03-04T00:17:45.145Z", "factory": "hello world", "factoryVW": 4080693292302336, "gearBoxClass": "hello world", "number": "counsel", "plannedEndDate": "2025-11-13T22:10:53.021Z", "quoteMonth": 7, "quoteYear": 2001, "technicalModelYear": 2014}, "referenceId": "21f5dd3f-7cbf-4205-b9e8-ab91571e5b57", "returnInfo": {"factoryCarPreparationOrderNumber": "abcs", "isUsedCar": true, "keyReturned": "2020-07-19T00:00:00Z", "nextProcess": "SALES"}, "source": "PVH", "status": "SX100", "technical": {"amountSeats": 8, "engineCapacity": 3689}, "tireSetChange": {"comment": "something", "completedDate": "2016-08-30T00:00:00Z", "orderedDate": "2016-08-30T00:00:00Z"}, "version": 0, "vguid": "fBPfUuBi9zuhZQ4XSg-9q", "vin": "WF72R3RJXNFT33348"}, "vehicleLastKnownLocation": {"building": "rotten", "comment": "uh-huh", "compoundName": "viewer", "eventType": "OUT", "level": "9", "occurredOn": "2015-12-21T00:00:00Z", "parkingLot": "141", "vehicleId": "e89a3f2f-1986-4f7a-9dbb-2d0408f12345"}, "vehicleRegistration": {"briefNumber": "TS37EGQ", "commenter": "test", "firstRegistrationDate": "2012-07-23T00:00:00Z", "hsn": "6319", "lastDeRegistrationDate": "2014-12-29T00:00:00Z", "lastRegistrationDate": "2019-05-22T00:00:00Z", "licencePlate": "WI 2685", "registrationDate": "2012-01-16T00:00:00Z", "registrationStatus": "REGISTERED", "registrationType": 3, "sfme": true, "storageLocation": "ZMU", "testNumber": 2498307439984640, "testVehicle": false, "tsn": "EVCLZDGI"}, "vehicleSales": {"auctionId": "texts", "comment": "texts", "contractSigned": true, "customerDeliveryDate": "2024-10-04T08:59:00Z", "customerPartnerNumber": "12345", "fvmTransactionId": "texts", "invoiceDate": "texts", "invoiceNumber": "texts", "invoiceRecipientNumber": "12345", "salesDiscountPercentage": 123, "salesDiscountType": "NO_DISCOUNT", "salesNetPriceAfterDiscountEUR": 123, "salesNetPriceEUR": 123, "salesPersonNumber": "56789", "winterTiresDiscountPercentage": 123, "winterTiresId": 123, "winterTiresNetPriceAfterDiscountEUR": 123, "winterTiresNetPriceEUR": 123}}, "errors": null, "warnings": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.color.exterior": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.color.exteriorDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.color.interior": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.color.interiorDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.consumption.driveType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.consumption.primaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.consumption.secondaryFuelType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.consumption.typification": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.country.bnrValue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.country.cnrCountryDescription": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.country.cnrValue": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.createdAt": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.currentTires": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.delivery.isPreparationNecessary": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.delivery.preparationDoneDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.embargo.inEmbargo": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.equiId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.equipmentNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.externalLeaseEnd": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.externalLeaseLessee": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.externalLeaseRate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.externalLeaseStart": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.financialAssetType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.fleet.soldDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.fleet.stolenDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.model.description": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.model.manufacturer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.model.orderType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.model.productCode": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.model.productId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.model.range": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.model.vehicleType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.options": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.blockedForSale": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.commissionNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.deliveryType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.department": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.importerShortName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.invoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.leasingType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.preproductionVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.primaryStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.purchaseOrderDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.purposeOrderType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.requestedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.order.tradingPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.pmp.odometer": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.pmp.timestamp": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.price.factoryGrossPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.price.factoryNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.price.grossPrice": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.price.netPriceWithExtras": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.price.piaEvent": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.production.endDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.production.factory": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.production.factoryVW": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.production.gearBoxClass": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.production.number": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.production.plannedEndDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.production.quoteMonth": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.production.quoteYear": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.production.technicalModelYear": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.referenceId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.source": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.technical.amountSeats": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.technical.engineCapacity": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.tireSetChange.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.tireSetChange.completedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.tireSetChange.orderedDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.version": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.vguid": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicle.vin": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleLastKnownLocation.building": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleLastKnownLocation.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleLastKnownLocation.compoundName": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleLastKnownLocation.eventType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleLastKnownLocation.level": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleLastKnownLocation.occurredOn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleLastKnownLocation.parkingLot": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleLastKnownLocation.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.briefNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.commenter": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.firstRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.hsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.lastDeRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.lastRegistrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.licencePlate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.registrationDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.registrationStatus": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.registrationType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.sfme": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.storageLocation": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.testNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.testVehicle": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleRegistration.tsn": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.auctionId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.comment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.contractSigned": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.customerDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.customerPartnerNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.fvmTransactionId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.invoiceDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.invoiceNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.invoiceRecipientNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.salesDiscountPercentage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.salesDiscountType": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.salesNetPriceAfterDiscountEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.salesNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.salesPersonNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.winterTiresDiscountPercentage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.winterTiresId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.winterTiresNetPriceAfterDiscountEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.vehicleSales.winterTiresNetPriceEUR": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.errors": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.warnings": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}, {"description": "a request for vehicles evaluation", "providerStates": [{"name": "Vehicles can be evaluated"}], "request": {"body": {"vehicleIds": ["e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "fb6470f1-c73c-45a3-ab26-640421d3da30"]}, "headers": {"Authorization": "Bearer .*", "Content-Type": "application/json"}, "matchingRules": {"body": {}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/evaluation"}, "response": {"body": {"data": [], "errors": null}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request for vehicles evaluation", "providerStates": [{"name": "Vehicles can not be evaluated"}], "request": {"body": {"vehicleIds": ["e89a3f2f-1986-4f7a-9dbb-2d0408f12345", "fb6470f1-c73c-45a3-ab26-640421d3da30"]}, "headers": {"Authorization": "Bearer .*", "Content-Type": "application/json"}, "matchingRules": {"body": {}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicles/evaluation"}, "response": {"body": {"data": [], "errors": [{"properties": {"vins": ["fb6470f1-c73c-45a3-ab26-640421d3da30"]}, "type": "error.fvm.vehicle.evaluation.appraisalServiceEmailError"}]}, "headers": {"Content-Type": "application/json"}, "status": 409}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-service"}}