{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request for pdi slots", "providerStates": [{"name": "PreDeliveryInspections exists"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/ui/vehicle-transfers/pdi-slots/2025-01-01"}, "response": {"body": {"data": [{"date": "2025-01-01", "pdiSlotsReserved": 5}, {"date": "2025-02-01", "pdiSlotsReserved": 1}, {"date": "2025-03-01", "pdiSlotsReserved": 20}, {"date": "2025-04-02", "pdiSlotsReserved": 10}]}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to cancel vehicle transfer", "providerStates": [{"name": "can send request to cancel a vehicle transfer successfully"}], "request": {"body": {"version": 1, "vin": "ABC123"}, "headers": {"Content-Type": "application/json", "x-amzn-oidc-accesstoken": ".*", "x-amzn-oidc-data": ".*"}, "matchingRules": {"body": {}, "header": {"x-amzn-oidc-accesstoken": {"combine": "AND", "matchers": [{"match": "type"}]}, "x-amzn-oidc-data": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicle-transfers/cancel"}, "response": {"status": 200}}, {"description": "a request to create a vehicle transfer", "providerStates": [{"name": "can send request to create a vehicle transfer successfully"}], "request": {"body": {"deliveryLeipzig": false, "desiredDeliveryDate": "2025-04-07T00:00:00Z", "desiredTireSet": "WR", "internalContactPerson": "00002548", "internalOrderNumber": "123432", "maximumServiceLifeInMonths": 24, "plannedDeliveryDate": "2025-04-07T00:00:00Z", "plannedReturnDate": "2025-04-07T00:00:00Z", "provisionForDeliveryComment": "it is the best", "registrationNeeded": true, "remark": "you are the best", "serviceCards": ["FUELING_CARD", "WASHING_CARD"], "usingCostCenter": "123432", "vehicleId": "0003d2cf-241a-44ad-82fa-1c6c03eefbd5", "vehicleResponsiblePerson": "00000296", "vehicleUsageId": "4bce288f-b04f-4c83-b390-0b605a99b2ae"}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.deliveryLeipzig": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.desiredDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.desiredTireSet": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.internalContactPerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.internalOrderNumber": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.maximumServiceLifeInMonths": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.plannedDeliveryDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.plannedReturnDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.provisionForDeliveryComment": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.registrationNeeded": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.remark": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.serviceCards": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.usingCostCenter": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleResponsiblePerson": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.vehicleUsageId": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {}}, "method": "POST", "path": "/ui/vehicle-transfers"}, "response": {"body": {"errors": null}, "headers": {"Content-Type": "application/json"}, "status": 200}}, {"description": "a request to delete vehicle transfer", "providerStates": [{"name": "can send request to delete a vehicle transfer successfully"}], "request": {"body": {"version": 1, "vin": "ABC123"}, "headers": {"Content-Type": "application/json", "x-amzn-oidc-accesstoken": ".*", "x-amzn-oidc-data": ".*"}, "matchingRules": {"body": {}, "header": {"x-amzn-oidc-accesstoken": {"combine": "AND", "matchers": [{"match": "type"}]}, "x-amzn-oidc-data": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "POST", "path": "/ui/vehicle-transfers/delete"}, "response": {"status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-transfer-service"}}