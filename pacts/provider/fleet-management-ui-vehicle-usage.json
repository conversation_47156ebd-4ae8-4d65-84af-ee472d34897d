{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request for retrieval of vehicle usage data", "providerStates": [{"name": "vehicle usage data exists"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/ui/vehicle-transfer-maintenance/vehicle-usage"}, "response": {"body": {"data": [{"id": "5ccb1d12-352c-49b0-8d4a-61ef6b5bdccb", "usage": "0eXBqWG", "usageId": 7, "version": 4}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0]": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usage": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].usageId": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data[0].version": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "vehicle-usage"}}