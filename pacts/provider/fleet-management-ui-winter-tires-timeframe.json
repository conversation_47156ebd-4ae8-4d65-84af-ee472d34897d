{"consumer": {"name": "fleet-management-ui"}, "interactions": [{"description": "a request to read winter tires timeframes", "providerStates": [{"name": "read winter tires time frames"}], "request": {"headers": {"Authorization": "Bearer .*"}, "matchingRules": {"header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "GET", "path": "/ui/predelivery-inspection/winter-tires-timeframe"}, "response": {"body": {"data": [{"id": "b3e610cb-e0a5-433e-9e9a-f22aa87f6a7b", "fromDate": "2026-11-01", "toDate": "2027-03-31", "version": 1}]}, "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.data.winterTiresSchedule.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.winterTiresSchedule.fromDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.winterTiresSchedule.toDate": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}, {"description": "a request to update winter tires timeframes", "providerStates": [{"name": "update winter tires time frames"}], "request": {"body": {"data": [{"id": "b3e610cb-e0a5-433e-9e9a-f22aa87f6a7b", "fromDate": "2026-11-01", "toDate": "2027-03-31", "version": 1}]}, "headers": {"Authorization": "Bearer .*", "Version": "1"}, "matchingRules": {"body": {"$": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.fromDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.toDate": {"combine": "AND", "matchers": [{"match": "type"}]}}, "header": {"Authorization": {"combine": "AND", "matchers": [{"match": "type"}]}}}, "method": "PUT", "path": "/ui/predelivery-inspection/winter-tires-timeframe"}, "response": {"body": {"data": [{"id": "b3e610cb-e0a5-433e-9e9a-f22aa87f6a7b", "fromDate": "2026-11-01", "toDate": "2027-03-31"}], "errors": null}, "headers": {"Content-Type": "application/json"}, "matchingRules": {"body": {"$.data.winterTiresSchedule.id": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.winterTiresSchedule.fromDate": {"combine": "AND", "matchers": [{"match": "type"}]}, "$.data.winterTiresSchedule.toDate": {"combine": "AND", "matchers": [{"match": "type"}]}}, "status": {}}, "status": 200}}], "metadata": {"pact-js": {"version": "13.1.3"}, "pactRust": {"ffi": "0.4.22", "models": "1.2.3"}, "pactSpecification": {"version": "3.0.0"}}, "provider": {"name": "winter-tires-timeframe"}}