# RELA Client Module Implementation Plan

## Overview
Create a new RELA client module following the project's hexagonal architecture pattern to integrate with the RELA tire service appointment system.

## Current State Analysis
- ✅ RELA API specification exists (`api/rela.yaml`)
- ✅ OpenAPI code generation configured in `build.gradle` 
- ✅ Basic module directory structure exists (`src/main/kotlin/com/fleetmanagement/modules/rela/`)
- ❓ Generated code location needs verification
- ❌ Application layer implementation missing
- ❌ Configuration and client setup missing

## RELA API Understanding
The RELA API manages tire service appointments with:
- **Create Appointment**: POST `/rela-rest-ws/luckyneutron/appointments`
- **Cancel Appointment**: POST `/rela-rest-ws/luckyneutron/appointments/{orderNumber}/cancelation`
- German field names (WerkstattterminDatum, KFZKennzeichen, etc.)
- Vehicle data requirements (VIN, license plate, customer info)

## Architecture Pattern (Based on msbooking module)
Following hexagonal architecture with clear separation of concerns:

```
rela/
├── adapter/
│   └── out/
│       └── rest/           # Generated client code + configuration
├── application/
│   ├── port/              # Interfaces (ports)
│   └── [services]         # Business logic
└── api/                   # Module API (if exposing to other modules)
```

## Implementation Tasks

### Phase 1: Verify Generated Code
- [ ] 1.1 Run build to generate RELA client code
- [ ] 1.2 Verify generated `AppointmentsApi` interface
- [ ] 1.3 Check generated DTOs location
- [ ] 1.4 Document generated code structure

### Phase 2: Create Application Ports
- [ ] 2.1 Create `CreateRelaAppointment` port interface
- [ ] 2.2 Create `CancelRelaAppointment` port interface  
- [ ] 2.3 Create internal DTOs for business logic
- [ ] 2.4 Create domain exceptions

### Phase 3: Implement Adapter Layer
- [ ] 3.1 Create `RelaWebClient` interface (wrapper for generated API)
- [ ] 3.2 Create `RelaClient` implementation (implements ports)
- [ ] 3.3 Create `RelaClientConfiguration` with WebClient setup
- [ ] 3.4 Create `RelaClientExceptionHandler`
- [ ] 3.5 Create custom exceptions

### Phase 4: Configuration & Properties
- [ ] 4.1 Add RELA configuration properties to `application.yml`
- [ ] 4.2 Create `@ConfigurationProperties` class
- [ ] 4.3 Add feature flag support (`@ConditionalOnProperty`)
- [ ] 4.4 Configure authentication if needed

### Phase 5: Testing & Documentation
- [ ] 5.1 Create unit tests for client
- [ ] 5.2 Create integration tests
- [ ] 5.3 Add module README.md
- [ ] 5.4 Document configuration requirements

## ELI5: How to Create External Client Modules in the Future

### The Pattern (Like Building with LEGO blocks)
1. **Generated Code** = The raw API client (like LEGO instructions)
2. **Adapter Layer** = Wrapper that makes it fit our system (like LEGO adapter pieces)
3. **Application Ports** = What our module promises to do (like LEGO interface)
4. **Configuration** = Settings to make it work (like LEGO power supply)

### Step-by-Step Recipe:
1. **Add API spec** to `api/` folder
2. **Configure code generation** in `build.gradle`
3. **Create ports** (interfaces) in `application/port/`
4. **Wrap generated client** in `adapter/out/rest/`
5. **Add configuration** with feature flags
6. **Test everything** works together

### Key Files You Always Need:
- `[Module]WebClient.kt` - Interface wrapping generated API
- `[Module]Client.kt` - Implementation of your business ports
- `[Module]ClientConfiguration.kt` - Spring configuration
- `[Module]ClientExceptionHandler.kt` - Error handling
- `application.yml` - Configuration properties

## Next Steps
1. Verify current state and generated code
2. Get user approval for plan
3. Begin implementation phase by phase
4. Test each phase before moving to next

## Dependencies & Considerations
- Spring WebClient for HTTP calls
- OAuth2/Authentication setup (TBD based on RELA requirements)
- Error handling strategy
- Retry/circuit breaker patterns (future enhancement)
- Monitoring/logging integration
