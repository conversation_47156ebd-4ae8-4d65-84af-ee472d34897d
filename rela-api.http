### RELA: GET /appointments
GET /rela-rest-ws/luckyneutron/appointments HTTP/1.1
Host: {{host}}
Authorization: Rela-Token {{rela_auth_token}}
Content-Type: application/json

>> ./outputs/response_rela_listOfAppointments.json


### RELA: GET /appointments by date
GET /rela-rest-ws/luckyneutron/appointments?date={{date}} HTTP/1.1
Host: {{host}}
Authorization: Rela-Token {{rela_auth_token}}
Content-Type: application/json

>> ./outputs/response_rela_listOfAppointments.json


### RELA: GET appointments by orderNumber
GET /rela-rest-ws/luckyneutron/appointments/{{orderNumber}} HTTP/1.1
Host: {{host}}
Authorization: Rela-Token {{rela_auth_token}}
Content-Type: application/json

>> ./outputs/response_rela_appointments_by_orderNumber.json


### RELA: GET data of demounted tieres by orderNumber
GET /rela-rest-ws/luckyneutron/appointments/{{orderNumber}}/demounting HTTP/1.1
Host: {{host}}
Authorization: Rela-Token {{rela_auth_token}}
Content-Type: application/json

>> ./outputs/response_rela_dataOfDemountedTieres_by_orderNumber.json

### RELA: GET data of mounted tieres by orderNumber
GET /rela-rest-ws/luckyneutron/appointments/{{orderNumber}}/mounting HTTP/1.1
Host: {{host}}
Authorization: Rela-Token {{rela_auth_token}}
Content-Type: application/json

>> ./outputs/response_rela_dataOfmountedTieres_by_orderNumber.json


### RELA: GET vehicle of mounted tieres by vin
GET /rela-rest-ws/luckyneutron/vehicles/{{vin}}/mounting HTTP/1.1
Host: {{host}}
Authorization: Rela-Token {{rela_auth_token}}
Content-Type: application/json

>> ./outputs/response_rela_vehicleOfMountedTieres_by_vin.json

### RELA: GET vehicle of demounted tieres by orderNumber
GET https://rela.sdnord.de/rela-rest-ws/luckyneutron/vehicles/{{vin}}/demounting HTTP/1.1
Host: {{host}}
Authorization: Rela-Token {{rela_auth_token}}
Content-Type: application/json

>> ./outputs/response_rela_vehicleOfDemountedTieres_by_vin.json


### RELA: POST cancel appointments by orderNumber
POST https://rela.sdnord.de/rela-rest-ws/luckyneutron/appointments/{{orderNumber}}/cancelation HTTP/1.1
Host: {{host}}
Authorization: Rela-Token {{rela_auth_token}}
Content-Type: application/json


### RELA: POST create appointment
POST /rela-rest-ws/luckyneutron/appointments HTTP/1.1
Host: {{host}}
Authorization: Rela-Token {{rela_auth_token}}
Content-Type: application/json

{
  "WerkstattterminDatum": "2025-07-31T00:00:00",
  "WerkstattterminUhrzeit": "2025-07-31T10:00:00",
  "Buehne": 1,
  "Dienstleistung": 2,
  "KFZKennzeichen": "S-PL 1234",
  "FIN": "WP0ZZZ999SS123456",
  "Name": "Piehl",
  "Vorname": "Silke",
  "TypCode": "992642",
  "TypBeschreibung": "911 Carrera 4 GTS Cabriolet",
  "PCCBCode": "",
  "WheelCode": "58Y",
  "RDKCode": "1PJ",
  "RASCode": null,
  "PCCBBeschreibung": "",
  "ReifenbeschreibungDemontage": "CARRERA GTS SCHMIEDERAD",
  "VentilbeschreibungDemontage": "ZENTRALVERSCHLUSS",
  "ReifenbeschreibungMontage": "",
  "VentilbeschreibungMontage": "",
  "bestelltVon": "<EMAIL>",
  "bestelltAmDatum": "2025-07-18T00:00:00",
  "bestelltAmUhrzeit": "2025-07-18T16:00:00"
}

>> ./outputs/response_rela_createAppointment.json