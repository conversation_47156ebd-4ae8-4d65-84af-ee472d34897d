@file:Suppress("ktlint:standard:if-else-wrapping")
/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.emhshared

import com.fleetmanagement.emhshared.domain.VehicleType

/**
 * Outdated concept that merges types of leasing based on vehicleUsage, vehicleType and manufacturer. We maintain this
 * information and respective mappings to support some downstream systems, like PVCC, that are unable to adapt to
 * vehicleUsage in time.
 */
enum class LeasingArt(
    val id: String,
    val description: String,
    val isPool: Boolean,
) {
    /**
     *The list of supported leasingArts is mirrored in DMS.
     * If you are updating, adding or deleting any leasingArts here, be sure to also update DMS to avoid any issues.
     */

    // leasing types
    @Suppress("ktlint:standard:no-consecutive-comments")
    L1_1("L1.1", "ÜT2 Leasing", false),
    L1_2("L1.2", "Leasing ab P11", false),
    L1_3("L1.3", "Treueleasing 25 Jahre", false),
    L1_4("L1.4", "Rentner", false),
    L2("L2", "Zweitleasing", false),
    L9_1("L9.1", "Dienstwagen", false),

    // pool leasing types
    L9_0("L9.0", "Entwicklung", true),
    L9_2("L9.2", "Abteilungsfahrzeuge", true),
    L9_3("L9.3", "Fremd Fahrzeuge", true),
    L9_5("L9.5", "LKW", true),
    L9_6("L9.6", "Anhänger/Sonderfahrzeuge", true),
    ;

    companion object {
        val poolLeasingArts = LeasingArt.entries.filter { it.isPool }
        val leasingArts = LeasingArt.entries.filterNot { it.isPool }

        fun LeasingArt.isPool(): Boolean = poolLeasingArts.any { this.id == it.id }

        fun of(id: String): LeasingArt =
            try {
                entries.single { id.equals(it.id, true) }
            } catch (exception: NoSuchElementException) {
                throw UnknownLeasingArtException("LeasingArt with id [$id] is not supported.", exception)
            }

        fun of(
            vehicleUsage: String,
            vehicleType: VehicleType,
            manufacturer: String,
        ): LeasingArt {
            // Leasing Tarif
            return if (VEHICLE_USAGE_LEASING_TARIF.equals(vehicleUsage, ignoreCase = true)) {
                L1_2
            }
            // Leasing ÜT2
            else if (VEHICLE_USAGE_LEASING_MK.equals(vehicleUsage, ignoreCase = true)) {
                L1_1
            }
            // Leasing Dienstwagen
            else if (VEHICLE_USAGE_DIENSTWAGEN.equals(vehicleUsage, ignoreCase = true)) {
                L9_1
            }
            // Leasing Renter
            else if (VEHICLE_USAGE_RENTNER.equals(vehicleUsage, ignoreCase = true)) {
                L1_4
            }
            // Leasing Treueleasing
            else if (VEHICLE_USAGE_TREUELEASING.equals(vehicleUsage, ignoreCase = true)) {
                L1_3
            }
            // Leasing Zweitleasing
            else if (VEHICLE_USAGE_ZWEITLEASING.equals(vehicleUsage, ignoreCase = true)) {
                L2
            }

            // Pool Entwicklung
            else if (VEHICLE_USAGE_ENTWICKLUNG.equals(vehicleUsage, ignoreCase = true)) {
                L9_0
            }

            // all other vehicleUsages
            // Pool Abteilungsfahrzeuge
            else if (
                VehicleType.PKW == vehicleType && MANUFACTURER_PORSCHE.equals(manufacturer, ignoreCase = true)
            ) {
                L9_2
            }
            // Pool Fremdfahrzeuge
            else if (
                VehicleType.PKW == vehicleType && !MANUFACTURER_PORSCHE.equals(manufacturer, ignoreCase = true)
            ) {
                L9_3
            }

            // LKW
            else if (VehicleType.TRUCK == vehicleType) {
                L9_5
            }
            // Anhänger/Sonderfahrzeuge
            else {
                L9_6
            }
        }

        private const val VEHICLE_USAGE_LEASING_TARIF = "Leasing Tarif"
        private const val VEHICLE_USAGE_LEASING_MK = "Leasing MK"
        private const val VEHICLE_USAGE_DIENSTWAGEN = "Dienstwagen"
        private const val VEHICLE_USAGE_RENTNER = "Rentner"
        private const val VEHICLE_USAGE_TREUELEASING = "Treueleasing"
        private const val VEHICLE_USAGE_ZWEITLEASING = "Zweitleasing"
        private const val VEHICLE_USAGE_ENTWICKLUNG = "Entwicklung"
        private const val MANUFACTURER_PORSCHE = "Porsche"
    }
}

// NOSONAR
class UnknownLeasingArtException(
    override val message: String,
    override val cause: Throwable?,
) : RuntimeException()
