package com.fleetmanagement.emhshared

import java.time.DayOfWeek
import java.time.OffsetDateTime
import java.time.format.DateTimeFormatter

fun formatAsDotFormattedString(dateTime: OffsetDateTime): String = dateTime.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))

val WORKING_DAYS =
    setOf(DayOfWeek.MONDAY, DayOfWeek.TUESDAY, DayOfWeek.WEDNESDAY, DayOfWeek.THURSDAY, DayOfWeek.FRIDAY)

/**
 * @throws InvalidTimeRangeException if [end] is before [start]
 */
fun workingDaysBetween(
    start: OffsetDateTime,
    end: OffsetDateTime,
): Long {
    if (end.isBefore(start)) {
        throw InvalidTimeRangeException(start, end)
    }

    return start
        .toLocalDate()
        .datesUntil(end.toLocalDate())
        .filter { WORKING_DAYS.contains(it.dayOfWeek) }
        .count()
}

fun OffsetDateTime.addWorkingDays(daysToAdd: Long): OffsetDateTime {
    var dateTime = this
    var daysAdded = 0

    while (daysAdded < daysToAdd) {
        dateTime = dateTime.plusDays(1)
        if (WORKING_DAYS.contains(dateTime.dayOfWeek)) {
            daysAdded++
        }
    }
    return dateTime
}

class InvalidTimeRangeException(
    start: OffsetDateTime,
    end: OffsetDateTime,
) : IllegalArgumentException(
        "Invalid time range: end time ($end) is before start time ($start)",
    )
