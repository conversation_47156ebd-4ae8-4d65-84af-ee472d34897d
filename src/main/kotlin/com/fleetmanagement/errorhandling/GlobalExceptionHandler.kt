/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.errorhandling

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.readValue
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import feign.FeignException
import org.slf4j.LoggerFactory
import org.slf4j.event.Level
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.validation.BindException
import org.springframework.web.bind.MissingRequestHeaderException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.context.request.async.AsyncRequestTimeoutException
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException
import org.springframework.web.server.ResponseStatusException
import java.net.URI
import java.nio.charset.StandardCharsets

@ControllerAdvice
@Order(Ordered.LOWEST_PRECEDENCE)
class GlobalExceptionHandler(
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(GlobalExceptionHandler::class.java)

    @ExceptionHandler(AsyncRequestTimeoutException::class)
    fun handleAsyncRequestTimeoutException(ex: AsyncRequestTimeoutException): ProblemDetail {
        logger.error("AsyncRequestTimeoutException occurred", ex)
        return ex.body.also {
            it.type = URI.create(ErrorType.SYSTEM_ERROR_ASYNC_REQUEST_TIMEOUT.value)
        }
    }

    @ExceptionHandler(MissingRequestHeaderException::class)
    fun handleMissingRequestHeaderInRequest(ex: MissingRequestHeaderException): ProblemDetail =
        ProblemDetail
            .forStatusAndDetail(
                HttpStatus.BAD_REQUEST,
                "Request Header \'" + ex.headerName + "\' is missing",
            ).also { it.type = URI.create(ErrorType.SYSTEM_ERROR_MISSING_REQUEST_HEADER.value) }

    @ExceptionHandler(ResponseStatusException::class)
    fun handleResponseStatusException(ex: ResponseStatusException): ProblemDetail =
        ProblemDetail
            .forStatusAndDetail(
                ex.statusCode,
                ex.reason ?: "Error occurred while processing the request.",
            ).also { it.type = URI.create(ErrorType.SYSTEM_ERROR.value) }

    @ExceptionHandler(RuntimeException::class)
    fun handleUncaughtExceptions(ex: Exception): ProblemDetail {
        logger.error("An unhandled exception occurred", ex)
        return ProblemDetail
            .forStatusAndDetail(
                HttpStatus.INTERNAL_SERVER_ERROR,
                "Something went wrong. The development team has been notified.",
            ).also { it.type = URI.create(ErrorType.SYSTEM_ERROR_INTERNAL_SERVER_ERROR.value) }
    }

    @ExceptionHandler(DataIntegrityViolationException::class)
    fun handleDataIntegrityExceptions(ex: Exception): ProblemDetail {
        logger.error("An data integrity violation exception occurred", ex)

        val (errorMessage, type) =
            when {
                ex.message?.contains("unique constraint") == true ->
                    "An entry with this data already exists." to URI.create(ErrorType.FVM_ALREADY_EXISTS.value)

                else ->
                    "A data integrity violation exception occurred." to URI.create(ErrorType.FVM_DATA_INTEGRITY_VIOLATION.value)
            }

        return ProblemDetail
            .forStatusAndDetail(
                HttpStatus.CONFLICT,
                errorMessage,
            ).apply { this.type = type }
    }

    @ExceptionHandler(org.springframework.security.access.AccessDeniedException::class)
    fun handleAccessDeniedException(ex: org.springframework.security.access.AccessDeniedException): ProblemDetail {
        logger.error("An Access Denied Exception occurred", ex)
        return ProblemDetail
            .forStatusAndDetail(
                HttpStatus.FORBIDDEN,
                ex.message ?: "Access denied.",
            ).also { it.type = URI.create(ErrorType.FVM_ACCESS_DENIED.value) }
    }

    @ExceptionHandler(BindException::class)
    fun onError(cause: BindException): ProblemDetail {
        val detail = cause.fieldErrors.joinToString(",") { "${it.field}: ${it.defaultMessage}" }
        return ProblemDetail
            .forStatusAndDetail(HttpStatus.BAD_REQUEST, detail)
            .also { it.type = URI.create(ErrorType.SYSTEM_ERROR.value) }
    }

    @ExceptionHandler(FeignException::class)
    fun onFeignException(ex: FeignException): ProblemDetail {
        val logLevel = if (ex.status() in (400..499)) Level.ERROR else Level.WARN
        logger.atLevel(logLevel).setCause(ex).log("Unexpected response from remote system")

        val problemDetail = ex.getProblemDetail(objectMapper)
        if (problemDetail != null) {
            return problemDetail
        }

        return ProblemDetail.forStatus(502).apply {
            title = "Unexpected response from remote system"
            detail = "The current operation could not be completed. Please retry again after sometime"
            type = URI.create(ErrorType.SYSTEM_ERROR_REMOTE_SYSTEM_PROBLEM.value)
        }
    }

    @ExceptionHandler(MethodArgumentTypeMismatchException::class)
    fun handleMethodArgumentTypeMismatch(ex: MethodArgumentTypeMismatchException): ProblemDetail {
        val problemDetail = ProblemDetail.forStatus(HttpStatus.BAD_REQUEST)
        problemDetail.title = "Bad Request"
        problemDetail.detail = "Invalid input parameter"
        problemDetail.setProperty("invalidType", mapOf(ex.name to ex.value))
        problemDetail.type = URI.create(ErrorType.SYSTEM_ERROR_BAD_REQUEST.value)
        return problemDetail
    }

    private fun FeignException.isProblemDetail(): Boolean = responseHeaders()["Content-Type"]?.contains("application/problem+json") == true

    private fun FeignException.getProblemDetail(objectMapper: ObjectMapper): ProblemDetail? {
        if (!this.isProblemDetail()) return null
        return try {
            this
                .responseBody()
                .map {
                    val body = String(it.array(), StandardCharsets.UTF_8)
                    objectMapper.readValue<ProblemDetail>(body)
                }.orElse(null)
        } catch (ex: Exception) {
            null
        }
    }
}
