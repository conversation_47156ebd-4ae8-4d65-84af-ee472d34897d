# Error Handling

## Overview

We do a combination of both exceptions and result-objects to handle errors in our applications.

**Result Monads using Kotlin Sealed Classes**
https://adambennett.dev/2020/05/the-result-monad/

## How to choose what approach to take? 

```mermaid
graph TD
A[Business Process] -->|An error has occurred| B(Handle Error)
B --> C{Is there a follow-up action after the error?}
C -->|Yes| D[Use a Sealed-Class/Result object and explicitly handle the error]
C -->|No| E{"Can the error be represented 
with a generic Spring/Kotlin exception?"}
E -->|Yes| F["Throw and Handle generic Spring/Java exception"]
E -->|No| G["Throw Spring ResponseStatusException with required details" ]
```

### Global Exception Handler  
1. It only references Spring/Kotlin exceptions 
2. It should not reference any custom exception created outside the `errorhandling` package
3. In the case you would like to handle a custom exception, make it a subclass of the `ResponseStatusException`

## Drivers for the error-handling strategy
1. [Do not use exceptions for flow-control][3]
2. [Principle of Least Astonishment][1]
3. [Fail fast][2]

[1]: https://en.wikipedia.org/wiki/Principle_of_least_astonishment
[2]: https://www.martinfowler.com/ieeeSoftware/failFast.pdf
[3]: https://wiki.c2.com/?DontUseExceptionsForFlowControl