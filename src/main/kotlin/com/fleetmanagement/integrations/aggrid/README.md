## AG Grid Filters

### Filters For JSONB columns

#### equals filter : DataManagerViewRowFilterSpecifications.equalsJsonPath()

**Purpose:**

This function allows you to filter records based on whether a specific field in a JSON object, accessed via a JSON path, has an exact match to a given value.

**How it works:**

It uses the jsonb_path_exists function in PostgreSQL to check if the JSON path exists and if its value matches the given fieldValue.
The filter returns records where the specified jsonPath matches the given fieldValue exactly.

Example:
```kotlin
val specification = equalsJsonPath("vehicle.options", "03B", "$.current.individualOptions[*].id")
```

Filters vehicles where the 'options' field contains the id '03B' in 'individualOptions.id'

#### notEqual filter : DataManagerViewRowFilterSpecifications.notEqualJsonPath()

**Purpose:**

This function allows you to filter records based on whether a specific field in a JSON object, accessed via a JSON path, does not match a given value.

**How it works:**

It checks if the JSON path exists and if its value matches the given fieldValue.
If the field is null or the value at the specified jsonPath does not match the fieldValue, it returns true (i.e., the vehicle matches).
The filter is useful for cases where you want to exclude records based on a specific condition in a jsonb field.
Example:
```kotlin
val specification = notEqualJsonPath("options", "4QW", "$.current.individualOptions[*].id")
```
Filters vehicles where 'options' does not contain the id '4QW' in 'individualOptions', or where 'options' is null

### Why Not jsonb_path_query_first:
**jsonb_path_exists:** Used in the functions as it efficiently checks if a JSON path exists and returns a boolean. Ideal for checking the presence of a value at a specified path without iterating over array elements.

**jsonb_path_query_first:** Retrieves the first matching element from a JSON array, which requires iteration over array elements. This is unnecessary for equality or inequality checks, as we only need to check for the presence of a value, not extract it.


### Example Request
```json
{
  "startRow": 0,
  "endRow": 50,
  "rowGroupCols": [],
  "valueCols": [],
  "pivotCols": [],
  "pivotMode": false,
  "groupKeys": [],
  "filterModel": {
    "vehicle.options": {
      "filterType": "jsonb",
      "operator": "AND",
      "conditions": [
        {
          "filterType": "jsonb",
          "operator": "OR",
          "conditions": [
            {
              "filterType": "jsonb",
              "type": "equals",
              "filter": "03B",
              "jsonPath": "$.current.individualOptions[*].id"
            },
            {
              "filterType": "jsonb",
              "type": "equals",
              "filter": "4QT",
              "jsonPath": "$.current.individualOptions[*].id"
            },
            {
              "filterType": "jsonb",
              "type": "equals",
              "filter": "PT5",
              "jsonPath": "$.current.individualOptions[*].id"
            },
            {
              "filterType": "jsonb",
              "type": "equals",
              "filter": "9QA",
              "jsonPath": "$.current.individualOptions[*].id"
            }
          ]
        },
        {
          "filterType": "jsonb",
          "operator": "OR",
          "conditions": [
            {
              "filterType": "jsonb",
              "type": "equals",
              "filter": "1WQ",
              "jsonPath": "$.current.individualOptions[*].id"
            },
            {
              "filterType": "jsonb",
              "type": "equals",
              "filter": "2AB",
              "jsonPath": "$.current.individualOptions[*].id"
            }
          ]
        },
        {
          "filterType": "jsonb",
          "operator": "OR",
          "conditions": [
            {
              "filterType": "jsonb",
              "type": "notEquals",
              "filter": "QWE",
              "jsonPath": "$.current.individualOptions[*].id"
            },
            {
              "filterType": "jsonb",
              "type": "notEquals",
              "filter": "TUV",
              "jsonPath": "$.current.individualOptions[*].id"
            }
          ]
        }
      ]
    }
  },
  "sortModel": []
}
```

The provided contract follows the default ag-Grid filter model. The filter structure works as follows:

**Top-level Operator (AND):** The conditions under the vehicle.options field are AND-ed together, meaning all conditions must be satisfied.

**Sub-level Operator (OR):** Within each group of conditions, they are OR-ed, allowing any condition in that group to be valid.

**Conditions:**

**Equality (equals):** Filters for specific option IDs, checking if a path (such as $.current.individualOptions[*].id) contains one of the listed values.

For example: 03B, 4QT, PT5, 9QA, 1WQ, 2AB.

**Inequality (notEqual):** Filters out specific option IDs, ensuring they do not match.

For example: QWE, TUV.

**jsonPath:** Each condition includes a jsonPath that targets the location of the option ID within the JSON structure, specifically inside the $.current.individualOptions[*].id path.

This filter contract sticks to the default ag-Grid filter model, requiring no additional customisation other than specifying the jsonPath field for each condition. 
It allows flexible filtering of vehicles based on the presence or absence of specific options, utilising logical AND and OR operators to combine conditions for precise matching.