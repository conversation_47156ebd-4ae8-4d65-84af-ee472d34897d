/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.integrations.aggrid.api

import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.integrations.aggrid.repository.DataManagerViewRepository
import com.fleetmanagement.integrations.aggrid.repository.DataManagerViewRowFilterSpecifications
import com.fleetmanagement.integrations.aggrid.repository.PagedQueryResult
import org.springframework.stereotype.Service

@Service
class DataManagerService(
    private val viewRepository: DataManagerViewRepository,
) {
    fun <T> search(
        request: SearchRequest,
        entityClass: Class<T>,
    ): PagedQueryResult<T> {
        require(request.startRow >= 0 && request.endRow >= 0) { "start/end row must be positive" }
        require(request.endRow >= request.startRow) { "start-row must be lower than end-row" }

        val specification = request.toSpecification<T>(DataManagerViewRowFilterSpecifications())
        val sort = request.toSort()

        return if (request.filterModel.isNotEmpty()) {
            viewRepository.findAllWithCount(
                specification = specification,
                offset = request.startRow,
                limit = request.endRow - request.startRow,
                sort = sort,
                entityClass = entityClass,
            )
        } else {
            val rows =
                viewRepository.findAll(
                    specification = specification,
                    offset = request.startRow,
                    limit = request.endRow - request.startRow,
                    sort = sort,
                    entityClass = entityClass,
                )
            val rowCount = countAll(entityClass)
            PagedQueryResult(rows, rowCount)
        }
    }

    private fun <T> countAll(entityClass: Class<T>): Long = viewRepository.countAll(entityClass = entityClass)
}
