/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.integrations.aggrid.api.dto

import com.fasterxml.jackson.annotation.JsonFormat
import com.fleetmanagement.integrations.aggrid.repository.DataManagerViewRowFilterSpecifications
import org.springframework.data.jpa.domain.Specification
import java.time.OffsetDateTime

private const val PREDICATES_BATCH_SIZE = 500
private const val BULK_SEARCH_INPUT_LIMIT = 65000

data class AgGridFilterModel(
    val filterType: String,
    val type: String? = null,
    val filter: String? = null,
    val operator: String? = null,
    val conditions: List<AgGridFilterModel>? = null,
    // multi-filters (maybe we can have another look at this model)
    val filterModels: List<AgGridFilterModel?>? = null,
    val filters: List<String>? = null,
    // set filters
    val values: List<String?>? = null,
    // date filters
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val dateFrom: OffsetDateTime? = null,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss[.SSS]XXX")
    val dateTo: OffsetDateTime? = null,
    val filterTo: Double? = null,
    val jsonPath: String? = null,
) {
    fun <T> toSpecification(
        field: String,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
    ): Specification<T> =
        when {
            operator != null && conditions != null -> specificationWithConditions(field, filterSpecifications, conditions)
            filterType == "text" -> specificationWithTextFilter(field, filterSpecifications)
            filterType == "number" -> specificationWithNumberFilter(field, filterSpecifications)
            filterType == "date" -> specificationWithDateFilter<T>(field, filterSpecifications)
            filterType == "multi" -> specificationWithMultipleFilters(field, filterSpecifications)
            filterType == "multiTexts" && filters != null && type != null ->
                specificationWithAMultiTextsFilter(
                    field,
                    filters,
                    type,
                    filterSpecifications,
                )

            filterType == "set" -> specificationWithSetFilter(field, filterSpecifications)
            filterType == "jsonb" -> specificationWithJsonbFilter(field, filterSpecifications)
            else -> throw IllegalArgumentException("Unsupported filterType: $filterType")
        }

    private fun <T> specificationWithJsonbFilter(
        field: String,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
    ): Specification<T> =
        when {
            jsonPath == null -> throw IllegalArgumentException("jsonPath can not be empty for jsonb filter")
            type == "equals" -> filterSpecifications.equalsJsonPath(field, filter ?: "", jsonPath)
            type == "notEqual" -> filterSpecifications.notEqualJsonPath(field, filter ?: "", jsonPath)
            else -> throw IllegalArgumentException("Unsupported jsonb filter type: $type")
        }

    private fun <T> specificationWithConditions(
        field: String,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
        conditions: List<AgGridFilterModel>,
    ): Specification<T> {
        val specs = conditions.map { it.toSpecification<T>(field, filterSpecifications) }
        return when (operator) {
            "AND" -> specs.reduce(Specification<T>::and)
            "OR" -> specs.reduce(Specification<T>::or)
            else -> throw IllegalArgumentException("Unsupported operator: $operator")
        }
    }

    private fun <T> specificationWithTextFilter(
        field: String,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
    ): Specification<T> =
        when (type) {
            "contains" -> filterSpecifications.like(field, "%$filter%")
            "notContains" -> filterSpecifications.notLike(field, "%$filter%")
            "startsWith" -> filterSpecifications.like(field, "$filter%")
            "endsWith" -> filterSpecifications.like(field, "%$filter")
            "equals" -> filterSpecifications.equals(field, filter ?: "")
            "notEqual" -> filterSpecifications.notEquals(field, filter ?: "")
            "blank" -> filterSpecifications.isNull<T>(field).or(filterSpecifications.equals(field, ""))
            "notBlank" -> filterSpecifications.isNotNull<T>(field).and(filterSpecifications.notEquals(field, ""))
            else -> throw IllegalArgumentException("Unsupported text filter type: $type")
        }

    private fun <T> specificationWithNumberFilter(
        field: String,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
    ): Specification<T> =
        when {
            type == "equals" -> filterSpecifications.equals(field, filter?.toDouble())
            type == "notEqual" -> filterSpecifications.notEquals(field, filter?.toDouble())
            type == "greaterThan" && filter != null -> filterSpecifications.greaterThan(field, filter.toDouble())
            type == "greaterThanOrEqual" && filter != null ->
                filterSpecifications.greaterThanOrEqual(
                    field,
                    filter.toDouble(),
                )

            type == "lessThan" && filter != null -> filterSpecifications.lessThan(field, filter.toDouble())
            type == "lessThanOrEqual" && filter != null ->
                filterSpecifications.lessThanOrEqual(
                    field,
                    filter.toDouble(),
                )

            type == "inRange" && filter != null && filterTo != null ->
                filterSpecifications.between(
                    field,
                    filter.toDouble(),
                    filterTo,
                )

            type == "blank" -> filterSpecifications.isNull(field)
            type == "notBlank" -> filterSpecifications.isNotNull(field)
            else -> throw IllegalArgumentException("Unable to handle filter type: $type")
        }

    private fun <T> specificationWithDateFilter(
        field: String,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
    ): Specification<T> =
        when {
            type == "equals" -> filterSpecifications.equals<T, OffsetDateTime?>(field, dateFrom)
            type == "notEqual" -> filterSpecifications.notEquals(field, dateFrom)
            type == "blank" -> filterSpecifications.isNull(field)
            type == "notBlank" -> filterSpecifications.isNotNull(field)
            type == "greaterThan" && dateFrom != null -> filterSpecifications.dateFrom(field, dateFrom)
            type == "lessThan" && dateFrom != null -> filterSpecifications.dateTo(field, dateFrom)
            type == "inRange" && dateTo != null && dateFrom != null ->
                filterSpecifications.dateBetween(
                    field,
                    dateFrom,
                    dateTo,
                )
            type == "notInRange" && dateTo != null && dateFrom != null ->
                filterSpecifications.dateNotBetween(
                    field,
                    dateFrom,
                    dateTo,
                )

            else -> throw IllegalArgumentException("Unsupported date filter type: $type")
        }

    private fun <T> specificationWithMultipleFilters(
        field: String,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
    ): Specification<T> {
        val jpaSpecifications = filterModels?.mapNotNull { it?.toSpecification<T>(field, filterSpecifications) }
        return when {
            jpaSpecifications.isNullOrEmpty() -> Specification.where(null)
            jpaSpecifications.size == 1 -> jpaSpecifications.first()
            else -> jpaSpecifications.reduce { acc, spec -> acc.and(spec) }
        }
    }

    private fun <T> specificationWithAMultiTextsFilter(
        field: String,
        filters: List<String>,
        type: String,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
    ): Specification<T> {
        if (filters.size > BULK_SEARCH_INPUT_LIMIT) {
            throw InputLimitExceededException(
                "Provided ${filters.size} input values but only upto $BULK_SEARCH_INPUT_LIMIT inputs are supported",
                BULK_SEARCH_INPUT_LIMIT,
            )
        }

        return when (type) {
            "equals" -> filterSpecifications.contains(field, filters)
            "notEqual" -> Specification.not(filterSpecifications.contains(field, filters))
            else -> chunkedSpecifications(field, type, filters, filterSpecifications)
        }
    }

    private fun <T> chunkedSpecifications(
        field: String,
        type: String,
        filters: List<String>,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
    ): Specification<T> {
        val specifications =
            filters.chunked(PREDICATES_BATCH_SIZE).map { chunk ->
                when (type) {
                    "startsWith" -> Specification.anyOf<T>(chunk.map { value -> filterSpecifications.like(field, "$value%") })
                    "endsWith" -> Specification.anyOf(chunk.map { value -> filterSpecifications.like(field, "%$value") })
                    "contains" -> Specification.anyOf(chunk.map { value -> filterSpecifications.like(field, "%$value%") })
                    "notContains" -> Specification.allOf(chunk.map { value -> filterSpecifications.notLike(field, "%$value%") })
                    else -> throw IllegalArgumentException("Unsupported multiTexts filter type: $type")
                }
            }
        return if (type == "notContains") {
            specifications.reduce { spec1, spec2 -> Specification.where(spec1).and(spec2) }
        } else {
            specifications.reduce { spec1, spec2 -> Specification.where(spec1).or(spec2) }
        }
    }

    private fun <T> specificationWithSetFilter(
        field: String,
        filterSpecifications: DataManagerViewRowFilterSpecifications,
    ): Specification<T> =
        when {
            // Select nothing when SET filter has deselected all options and sent empty values
            values.isNullOrEmpty() -> filterSpecifications.selectNone()
            // Select rows having null values when values contain null, however, contains clause does not support it, so we have to use isNull
            values.any { it == null } ->
                filterSpecifications
                    .contains<T>(field, values)
                    .or(filterSpecifications.isNull(field))

            else -> filterSpecifications.contains(field, values)
        }
}

data class InputLimitExceededException(
    override val message: String,
    val limit: Int,
) : Exception()
