/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.integrations.aggrid.repository

import jakarta.persistence.EntityManager
import jakarta.persistence.PersistenceContext
import jakarta.persistence.criteria.CriteriaBuilder
import jakarta.persistence.criteria.Order
import jakarta.persistence.criteria.Path
import jakarta.persistence.criteria.Root
import org.springframework.data.domain.Sort
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Repository

data class PagedQueryResult<T>(
    val rows: List<T>,
    val totalRowsAvailable: Long,
)

/**
 * This repository was refactored to improve performance in paginated queries with JPA Specifications.
 * The default JPA findAll with pagination triggers an unnecessary and slow count query. To avoid this,
 * we implemented custom methods: findAll for paginated queries without a count and findAllWithCount when
 * the count is needed. This approach improves performance by using a more efficient count method and
 * avoiding redundant queries.
 *
 * We currently can not skip the additional count query with JPA paginated findAll
 * Refer to the open issue here: https://github.com/spring-projects/spring-data-jpa/issues/2762
 */
@Repository
class DataManagerViewRepository(
    @PersistenceContext private val entityManager: EntityManager,
) {
    fun <T> findAll(
        specification: Specification<T>,
        offset: Int,
        limit: Int,
        sort: Sort,
        entityClass: Class<T>,
    ): List<T> {
        val criteriaBuilder = entityManager.criteriaBuilder
        val criteriaQuery = criteriaBuilder.createQuery(entityClass)
        val root = criteriaQuery.from(entityClass)
        val predicate = specification.toPredicate(root, criteriaQuery, criteriaBuilder)

        criteriaQuery.select(root)
        if (predicate != null) {
            criteriaQuery.where(specification.toPredicate(root, criteriaQuery, criteriaBuilder))
        }
        criteriaQuery.orderBy(sort.toJpaOrder(criteriaBuilder, root))

        val query = entityManager.createQuery(criteriaQuery)
        query.firstResult = offset
        query.maxResults = limit

        return query.resultList
    }

    fun <T> countAll(entityClass: Class<T>): Long {
        val criteriaBuilder = entityManager.criteriaBuilder

        val countQuery = criteriaBuilder.createQuery(Long::class.java)
        val countRoot = countQuery.from(entityClass)
        countQuery.select(criteriaBuilder.count(countRoot))

        return entityManager
            .createQuery(
                countQuery,
            ).singleResult
    }

    private fun <T> countAllWithFilter(
        specification: Specification<T>,
        entityClass: Class<T>,
    ): Long {
        val criteriaBuilder = entityManager.criteriaBuilder

        val countQuery = criteriaBuilder.createQuery(Long::class.java)
        val countRoot = countQuery.from(entityClass)
        val countPredicate = specification.toPredicate(countRoot, countQuery, criteriaBuilder)
        countQuery.select(criteriaBuilder.count(countRoot))
        if (countPredicate != null) {
            countQuery.where(countPredicate)
        }

        return entityManager.createQuery(countQuery).singleResult
    }

    fun <T> findAllWithCount(
        specification: Specification<T>,
        offset: Int,
        limit: Int,
        sort: Sort,
        entityClass: Class<T>,
    ): PagedQueryResult<T> {
        val count = countAllWithFilter(specification, entityClass)
        val resultList = findAll(specification, offset, limit, sort, entityClass)

        return PagedQueryResult(resultList, count)
    }

    private fun <T> Sort.toJpaOrder(
        criteriaBuilder: CriteriaBuilder,
        root: Root<T>,
    ): List<Order> =
        this
            .map { order ->

                val propertyPath =
                    order.property.split(".").fold(root as Path<*>) { path, segment ->
                        path.get<Any>(segment)
                    }

                if (order.isAscending) {
                    criteriaBuilder.asc(propertyPath)
                } else {
                    criteriaBuilder.desc(propertyPath)
                }
            }.toList()
}
