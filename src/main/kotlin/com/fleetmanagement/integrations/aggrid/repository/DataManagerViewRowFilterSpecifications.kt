/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.integrations.aggrid.repository

import jakarta.persistence.criteria.*
import org.hibernate.query.sqm.tree.domain.SqmBasicValuedSimplePath
import org.springframework.data.jpa.domain.Specification
import java.time.OffsetDateTime
import java.util.UUID

class DataManagerViewRowFilterSpecifications {
    fun <T> isNotNull(fieldName: String): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate = criteriaBuilder.isNotNull(root.fromFieldName<T, String>(fieldName))
            criteriaBuilder.and(predicate)
        }

    fun <T> isNull(fieldName: String): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate = criteriaBuilder.isNull(root.fromFieldName<T, String>(fieldName))
            criteriaBuilder.and(predicate)
        }

    fun <T, U> equals(
        fieldName: String,
        fieldValue: U,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                when (fieldValue) {
                    is OffsetDateTime -> createDateEqualsPredicate(root, criteriaBuilder, fieldName, fieldValue)
                    else ->
                        criteriaBuilder.equal(
                            root.fromFieldName<T, U>(fieldName),
                            fieldValue,
                        )
                }
            criteriaBuilder.and(predicate)
        }

    fun <T> equals(
        fieldName: String,
        fieldValue: String,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.equal(
                    criteriaBuilder.lower(root.fromFieldName(fieldName)),
                    fieldValue.lowercase(),
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> equalsJsonPath(
        fieldName: String,
        fieldValue: String,
        jsonPath: String,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val jsonExistsFunction =
                criteriaBuilder.function(
                    "jsonb_path_exists",
                    Boolean::class.java,
                    root.fromFieldName<T, String>(fieldName),
                    criteriaBuilder.literal("$jsonPath ? (@ == \"$fieldValue\")"),
                )

            val trueValue = true
            criteriaBuilder.equal(jsonExistsFunction, trueValue)
        }

    fun <T> notEqualJsonPath(
        fieldName: String,
        fieldValue: String,
        jsonPath: String,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val jsonField = root.fromFieldName<T, String>(fieldName)

            val jsonExistsFunction =
                criteriaBuilder.function(
                    "jsonb_path_exists",
                    Boolean::class.java,
                    jsonField,
                    criteriaBuilder.literal("$jsonPath ? (@ == \"$fieldValue\")"),
                )

            val falseValue = false
            criteriaBuilder.or(
                criteriaBuilder.isNull(jsonField),
                criteriaBuilder.equal(jsonExistsFunction, falseValue),
            )
        }

    fun <T, U> notEquals(
        fieldName: String,
        fieldValue: U,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            when (fieldValue) {
                is OffsetDateTime -> createDateNotEqualsPredicate(root, criteriaBuilder, fieldName, fieldValue)
                else -> createStandardNotEqualsPredicate(root, criteriaBuilder, fieldName, fieldValue)
            }
        }

    private fun <T> createDateEqualsPredicate(
        root: Root<T>,
        criteriaBuilder: CriteriaBuilder,
        fieldName: String,
        fieldValue: OffsetDateTime,
    ): Predicate {
        val dateFunction =
            criteriaBuilder.function("DATE", OffsetDateTime::class.java, root.fromFieldName<T, String>(fieldName))
        return criteriaBuilder.equal(dateFunction, fieldValue)
    }

    private fun <T> createDateNotEqualsPredicate(
        root: Root<T>,
        criteriaBuilder: CriteriaBuilder,
        fieldName: String,
        fieldValue: OffsetDateTime,
    ): Predicate {
        val dateFunction =
            criteriaBuilder.function("DATE", OffsetDateTime::class.java, root.fromFieldName<T, String>(fieldName))
        return criteriaBuilder.notEqual(dateFunction, fieldValue)
    }

    private fun <T, U> createStandardNotEqualsPredicate(
        root: Root<T>,
        criteriaBuilder: CriteriaBuilder,
        fieldName: String,
        fieldValue: U,
    ) = criteriaBuilder.notEqual(root.get<U>(fieldName), fieldValue)

    fun <T> notEquals(
        fieldName: String,
        fieldValue: String,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.notEqual(
                    criteriaBuilder.lower(root.fromFieldName<T, String>(fieldName)),
                    fieldValue.lowercase(),
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> like(
        fieldName: String,
        fieldValue: String,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.like(
                    criteriaBuilder.lower(root.fromFieldName<T, String?>(fieldName)),
                    fieldValue.lowercase(),
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> notLike(
        fieldName: String,
        fieldValue: String,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.notLike(
                    criteriaBuilder.lower(root.fromFieldName<T, String?>(fieldName)),
                    fieldValue.lowercase(),
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> greaterThan(
        fieldName: String,
        fieldValue: Double,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.greaterThan(
                    root.fromFieldName<T, Double>(fieldName),
                    fieldValue,
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> greaterThanOrEqual(
        fieldName: String,
        fieldValue: Double,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.greaterThanOrEqualTo(
                    root.fromFieldName<T, Double>(fieldName),
                    fieldValue,
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> lessThan(
        fieldName: String,
        fieldValue: Double,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.lessThan(
                    root.fromFieldName<T, Double>(fieldName),
                    fieldValue,
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> lessThanOrEqual(
        fieldName: String,
        fieldValue: Double,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.lessThanOrEqualTo(
                    root.fromFieldName<T, Double>(fieldName),
                    fieldValue,
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> between(
        fieldName: String,
        fromValue: Double,
        toValue: Double,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.between(
                    root.fromFieldName<T, Double>(fieldName),
                    fromValue,
                    toValue,
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> contains(
        fieldName: String,
        values: List<String?>,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val fieldType = (root.fromFieldName<T, String>(fieldName) as SqmBasicValuedSimplePath).javaType
            val predicate =
                when (fieldType) {
                    Boolean::class.java, Boolean::class.javaObjectType ->
                        root.fromFieldName<T, Boolean>(fieldName).`in`(values.map { it?.toBoolean() })

                    Int::class.java, Int::class.javaObjectType ->
                        root.fromFieldName<T, Int>(fieldName).`in`(values.map { it?.toIntOrNull() })

                    Long::class.java, Long::class.javaObjectType ->
                        root.fromFieldName<T, Long>(fieldName).`in`(values.map { it?.toLongOrNull() })

                    Double::class.java, Double::class.javaObjectType ->
                        root.fromFieldName<T, Double>(fieldName).`in`(values.map { it?.toDoubleOrNull() })

                    Float::class.java, Float::class.javaObjectType ->
                        root.fromFieldName<T, Float>(fieldName).`in`(values.map { it?.toFloatOrNull() })

                    UUID::class.java, UUID::class.javaObjectType ->
                        root.fromFieldName<T, UUID>(fieldName).`in`(
                            values.map { it?.let { UUID.fromString(it) } },
                        )

                    else ->
                        root.fromFieldName<T, String>(fieldName).`in`(values)
                }
            criteriaBuilder.and(predicate)
        }

    fun <T> dateBetween(
        fieldName: String,
        dateFrom: OffsetDateTime,
        dateTo: OffsetDateTime,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.between(
                    root.fromFieldName<T, OffsetDateTime?>(fieldName),
                    dateFrom,
                    dateTo,
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> dateNotBetween(
        fieldName: String,
        dateFrom: OffsetDateTime,
        dateTo: OffsetDateTime,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.not(
                    criteriaBuilder.between(
                        root.fromFieldName<T, OffsetDateTime?>(fieldName),
                        dateFrom,
                        dateTo,
                    ),
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> dateFrom(
        fieldName: String,
        dateFrom: OffsetDateTime,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.greaterThan(
                    root.fromFieldName<T, OffsetDateTime?>(fieldName),
                    dateFrom,
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> dateTo(
        fieldName: String,
        dateTo: OffsetDateTime,
    ): Specification<T> =
        Specification<T> { root, _, criteriaBuilder ->
            val predicate =
                criteriaBuilder.lessThan(
                    root.fromFieldName<T, OffsetDateTime?>(fieldName),
                    dateTo,
                )
            criteriaBuilder.and(predicate)
        }

    fun <T> selectNone(): Specification<T> =
        Specification { _, _, criteriaBuilder ->
            criteriaBuilder.disjunction()
        }
}

private fun <T, R> Root<T>.fromFieldName(field: String): Expression<R> {
    val parts = field.split(".")
    check(parts.size >= 2) { "fieldName must have at least 2 parts" }

    var join: From<*, *> = this // Start from the root entity

    for (i in 0 until parts.lastIndex) {
        join = join.join<Any, Any>(parts[i])
    }
    return join.get<R>(parts.last()) // Get the final field
}
