/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.integrations.kafka

import io.micrometer.core.instrument.MeterRegistry
import org.springframework.boot.autoconfigure.kafka.KafkaProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.core.ConsumerFactory
import org.springframework.kafka.core.DefaultKafkaConsumerFactory
import org.springframework.kafka.core.MicrometerConsumerListener

@Configuration
class KafkaConsumerFactoryConfiguration(
    private val meterRegistry: MeterRegistry,
) {
    @Bean
    fun consumerFactory(config: KafkaProperties): ConsumerFactory<Any, Any> {
        val configs = config.buildConsumerProperties(null)

        val consumerFactory = DefaultKafkaConsumerFactory<Any, Any>(configs)
        consumerFactory.addListener(
            MicrometerConsumerListener(meterRegistry),
        )
        return consumerFactory
    }
}
