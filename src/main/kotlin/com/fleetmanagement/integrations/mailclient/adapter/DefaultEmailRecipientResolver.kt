package com.fleetmanagement.integrations.mailclient.adapter

import com.aspose.email.MailAddress
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(prefix = "mailclient", name = ["override-email-recipients"], havingValue = "false")
class DefaultEmailRecipientResolver : EmailRecipientResolver {
    override fun resolveToRecipients(emailDto: EmailDto): List<MailAddress> = emailDto.recipientsMailAddressInTo

    override fun resolveCcRecipients(emailDto: EmailDto): List<MailAddress> = emailDto.recipientsMailAddressInCC
}
