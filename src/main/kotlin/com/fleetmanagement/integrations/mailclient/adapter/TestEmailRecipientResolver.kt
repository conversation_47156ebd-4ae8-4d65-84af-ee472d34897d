package com.fleetmanagement.integrations.mailclient.adapter

import com.aspose.email.MailAddress
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(prefix = "mailclient", name = ["override-email-recipients"], havingValue = "true")
class TestEmailRecipientResolver(
    @Value("\${mailclient.override-recipients-list}") private val recipients: String,
) : EmailRecipientResolver {
    override fun resolveToRecipients(emailDto: EmailDto): List<MailAddress> = recipients.split(",").map { MailAddress(it.trim()) }

    override fun resolveCcRecipients(emailDto: EmailDto): List<MailAddress> = emptyList()
}
