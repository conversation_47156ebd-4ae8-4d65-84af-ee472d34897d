package com.fleetmanagement.jobmanagement.configuration

import org.quartz.JobKey
import org.quartz.Scheduler
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.ObjectProvider
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener

class QuartzJobManager(
    private val schedulerProvider: ObjectProvider<Scheduler>,
    private val jobKey: JobK<PERSON>,
    private val isJobEnabled: <PERSON><PERSON><PERSON>,
) {
    private val logger = LoggerFactory.getLogger(QuartzJobManager::class.java)

    @EventListener(ApplicationReadyEvent::class)
    fun manageQuartzJob() {
        val scheduler = schedulerProvider.ifAvailable
        if (scheduler == null) {
            logger.warn("No scheduler available, skipping job-management for ${jobKey.name}")
            return
        }
        if (scheduler.checkExists(jobKey)) {
            if (isJobEnabled) {
                logger.info("Job ${jobKey.name} is enabled, resuming Quartz job")
                scheduler.resumeJob(jobKey)
            } else {
                logger.info("Job ${jobKey.name} is disabled, pausing Quartz job")
                scheduler.pauseJob(jobKey)
            }
        } else {
            logger.info("Job not scheduled, skipping job-management for ${jobKey.name}")
        }
    }
}
