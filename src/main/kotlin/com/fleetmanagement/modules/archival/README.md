# Vehicle Archival Module

This module is responsible for archiving vehicles on a monthly basis using a Quartz job. 

### Purpose
The purpose of this job is to archive vehicles by storing the archive information in S3 buckets and then removing the vehicles from operational database.

### Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Configuration](#configuration)
- [Endpoints](#endpoints)
- [Error Handling](#error-handling)
- [Logging](#logging)
- [Security](#security)
- [Troubleshooting](#troubleshooting)

### Overview

The Vehicle Archival Module automates the process of archiving vehicles on a scheduled monthly basis. It leverages Quartz to schedule and run a job that interacts with other services via REST endpoints (`/archivable`,`/archive`) to retrieve and archive vehicles.

** Conditions to fulfill for archiving a vehicle:
1. Vehicle should contain a scrapped or sold date
2. Vehicle and the related entities are not updated in the past 3 years

### Architecture

The module interacts with external services via the following endpoints:

1. **/archivable**: This endpoint returns a list of vehicles that are eligible for archiving
2. **/archive**: This endpoint archives the vehicles provided to it

The Quartz job orchestrates these calls to ensure that the archiving process is completed efficiently.

```mermaid
graph LR;
    A[Quartz Job Scheduler] --> |1. Schedule monthly job| B[Archival Module];
    
    B --> |2. Call /archivable| C1[Vehicle Registration Service];
    B --> |2. Call /archivable| C2[Location Service];
    B --> |2. Call /archivable| C3[Other Services];
    
    C1 -.-> |3. List of eligible vehicles| B;
    C2 -.-> |3. List of eligible vehicles| B;
    C3 -.-> |3. List of eligible vehicles| B;
    
    B --> |4. identify common set| B;
    
    B --> |5. Call /archive with vehicles| C1[Vehicle Registration Service];
    B --> |5. Call /archive with vehicles| C2[Location Service];
    B --> |5. Call /archive with vehicles| C3[Other Services];
```

### Configuration

The Quartz job configuration for the Archival service is designed to periodically trigger the archival of vehicle data. This ensures that data will be efficiently moved to s3 buckets and removed from operational database fulfilling the compliance and GoBD regulations

To enable this locally, simply set the following flag to true and add the M2M client credentials to connect to vehicle-registration service

```sh
export ENABLE_ARCHIVAL_SCHEDULER=true
export VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_ID=d594f124-a777-41ca-b09b-9af3def7379a
export VEHICLE_REGISTRATION_ARCHIVE_API_CLIENT_SECRET=`/vehicle-service/secret/azure-idp/vehicle_registration_archive_m2m_client_secret`
export VEHICLE_REGISTRATION_ARCHIVE_API_SCOPE=app://cfb99f74-19b0-4747-b96f-81989c59bb4a/.default
```

#### Environment Variables

Ensure that the following environment variables are configured for the module:

- `OAUTH2_TOKEN_URI`: The token endpoint of the Azure Idp to fetch M2M token
- `VR_BASE_URI`: Base URI of the vehicle registration service
- `VEHICLE_ARCHIVE_CRON_SCHEDULE`: Cron expression for scheduling the job (default: `0 0 0 1 * ?` for midnight on the first of every month)

### Endpoints

#### 1. `/archivable`

- **Method**: `GET`
- **Description**: Retrieves a list of vehicles eligible for archiving
- **Response**: Returns ArchivalResponse that contains a list of vehicles to archive

#### 2. `/archive`

- **Method**: `POST`
- **Description**: Archives the vehicles provided in the request body
- **Request Body**: A DTO that takes the list of vehicles to archive along with the job execution id
- **Response**: Returns if the request is accepted or not

### Error Handling

- Incase the downstream system is not available the job fails with a server exception and will throw an error in MS Teams. It needs to be handled explicitly.

### Logging

- There will be a unique ID created on every execution and will be provided to the downstream system to have a distributed tracing in case of failures. Log messages are streamlined with this identifier when multiple log groups are selected in AWS

### Security

- **API Authentication**: The provided REST endpoints are authenticated using OAuth2 tokens as per Porsche service’s security model
- **Secure Storage**: The client secret is stored in AWS Parameter store as documented in the configuration section

### Demo data setup
The following criteria needs to be fulfilled before the vehicles fulfil the eligibility criteria
```sql
update vehicle.vehicle
set last_updated_at = now() at time zone 'utc' - '4 year'::interval
where id = '<vehicle_id>';

update vehicleregistration.order
set updated_at = now() at time zone 'utc' - '4 year'::interval
where vehicle_id = '<vehicle_id>';

update vehicletransfer.vehicle_transfer
set last_modified_date = now() at time zone 'utc' - '4 year'::interval
where vehicle_id = '<vehicle_id>';

update vehiclesales.vehicle_sale
set updated_at = now() at time zone 'utc' - '4 year'::interval
where vehicle_id = '<vehicle_id>';

update vehiclesales.vehicle_invoice
set updated_at = now() at time zone 'utc' - '4 year'::interval
where vehicle_id = '<vehicle_id>';
```

### Troubleshooting

- **Job Not Running**: Ensure that the Quartz scheduler is properly configured and the cron expression is correct
- **API Errors**: Check the logs for detailed error messages and verify that the external services are reachable and functioning as expected
- **Tracing**: The job execution ID references all the logs from all services
