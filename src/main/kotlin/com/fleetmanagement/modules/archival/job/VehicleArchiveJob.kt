/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.archival.job

import com.fleetmanagement.modules.archival.job.service.ArchivalResult
import com.fleetmanagement.modules.archival.job.service.JobStatus
import com.fleetmanagement.modules.archival.job.service.VehicleArchivalService
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import java.util.*

class VehicleArchiveJob(
    private val vehicleArchivalService: VehicleArchivalService,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(VehicleArchiveJob::class.java)
    }

    override fun execute(context: JobExecutionContext) {
        val jobExecutionId = UUID.randomUUID().toString()
        val result = vehicleArchivalService.archive(jobExecutionId)

        when (result.status) {
            JobStatus.SUCCESS -> handleSuccess(result)
            else -> handleFailure(result)
        }
    }

    private fun handleSuccess(result: ArchivalResult) {
        log.info("Archive job successful for job execution: ${result.jobExecutionId}")
    }

    private fun handleFailure(result: ArchivalResult) {
        log.error("Archive job failed for job execution: ${result.jobExecutionId}", result.errors)
    }
}
