/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.archival.job.client

import com.fleetmanagement.modules.archival.job.client.configuration.vehicleregistration.VehicleRegistrationArchivalClientConfig
import com.fleetmanagement.modules.archival.job.service.ArchiveOrderDto
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam

@FeignClient(
    name = "vehicle-registration-archive",
    url = "\${vehicle-archive.clients.vehicle-registration.base-url}",
    configuration = [VehicleRegistrationArchivalClientConfig::class],
)
interface VehicleRegistrationArchivalClient {
    @PostMapping("/private/archive/orders", consumes = [MediaType.APPLICATION_JSON_VALUE])
    fun archiveVehicleRegistrationData(
        @RequestBody archiveOrderDto: ArchiveOrderDto,
    ): ResponseEntity<Void>

    @GetMapping(value = ["/private/archivable/orders"])
    fun registrationOrdersToArchive(
        @RequestParam("jobExecutionId") jobExecutionId: String,
    ): ResponseEntity<ArchivalResponse>
}
