/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.archival.job.client.configuration.vehicleregistration

import com.fleetmanagement.oidc.client.OIDCConfigurationProperties
import com.fleetmanagement.oidc.client.OIDCTokenClient
import com.fleetmanagement.oidc.service.OIDCTokenService
import feign.RequestInterceptor
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class VehicleRegistrationArchivalClientConfigProperties {
    @Bean("vrArchivalClientConfiguration")
    @ConfigurationProperties("vehicle-archive.clients.vehicle-registration")
    fun oidcConfigurationProperties(): OIDCConfigurationProperties = OIDCConfigurationProperties()
}

class VehicleRegistrationArchivalClientConfig {
    @Bean("vrArchivalTokenService")
    fun oidcTokenService(
        vrArchivalTokenClient: OIDCTokenClient,
        @Qualifier("vrArchivalClientConfiguration") vrArchivalClientConfiguration: OIDCConfigurationProperties,
    ): OIDCTokenService = OIDCTokenService(vrArchivalTokenClient, vrArchivalClientConfiguration)

    @Bean
    fun requestInterceptor(
        @Qualifier("vrArchivalTokenService") vrArchivalTokenService: OIDCTokenService,
    ): RequestInterceptor =
        RequestInterceptor { template ->
            val token = vrArchivalTokenService.getAccessToken()
            template.header("Authorization", "Bearer $token")
        }
}
