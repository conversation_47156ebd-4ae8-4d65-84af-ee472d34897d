/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.archival.job.configurations

import com.fleetmanagement.modules.archival.job.VehicleArchiveJob
import org.quartz.*
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.*

@Configuration
@ConditionalOnProperty(name = ["vehicle-archive.enabled"], havingValue = "true", matchIfMissing = false)
class VehicleArchiveJobConfig {
    @Bean("vehicleArchiveJobDetail")
    fun vehicleArchiveJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(VehicleArchiveJob::class.java)
            .withIdentity("VehicleArchiveJob")
            .withDescription("vehicle archive job")
            .storeDurably()
            .build()

    @Bean("vehicleArchiveTrigger")
    fun vehicleArchiveTrigger(
        vehicleArchiveJobDetail: JobDetail,
        @Value("\${vehicle-archive.scheduler.cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(vehicleArchiveJobDetail)
            .withIdentity("VehicleArchiveJobTrigger")
            .withDescription("vehicle archive job trigger")
            .withSchedule(
                CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
