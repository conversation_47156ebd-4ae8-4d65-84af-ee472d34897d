/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.archival.job.configurations

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.emh.shared.s3.client.adapter.config.StorageProperties
import io.awspring.cloud.s3.S3Template
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import software.amazon.awssdk.services.s3.S3Client

@Configuration
class VehicleArchiveStorageConfiguration {
    @Bean("vehicleArchiveStorageProperties")
    @ConfigurationProperties("vehicle-archive.vehicle-data.storage")
    fun vehicleArchiveStorageProperties(): VehicleArchiveStorageProperties = VehicleArchiveStorageProperties()

    @Bean("vehicleTransferArchiveStorageProperties")
    @ConfigurationProperties("vehicle-archive.vehicle-transfer.storage")
    fun vehicleTransferArchiveStorageProperties(): VehicleArchiveStorageProperties = VehicleArchiveStorageProperties()

    @Bean("vehicleHistoryArchiveStorageProperties")
    @ConfigurationProperties("vehicle-archive.vehicle-history.storage")
    fun vehicleHistoryArchiveStorageProperties(): VehicleArchiveStorageProperties = VehicleArchiveStorageProperties()

    @Bean("vehicleSalesArchiveStorageProperties")
    @ConfigurationProperties("vehicle-archive.vehicle-sales.storage")
    fun vehicleSalesArchiveStorageProperties(): VehicleArchiveStorageProperties = VehicleArchiveStorageProperties()

    @Bean
    @Qualifier("vehicleArchiveStorageClient")
    fun vehicleArchiveStorageClient(
        s3Client: S3Client,
        s3Template: S3Template,
        @Qualifier("vehicleArchiveStorageProperties") vehicleArchiveStorageProperties: VehicleArchiveStorageProperties,
    ): S3StorageClient = createS3StorageClient(s3Client, s3Template, vehicleArchiveStorageProperties)

    @Bean
    @Qualifier("vehicleTransferArchiveStorageClient")
    fun vehicleTransferArchiveStorageClient(
        s3Client: S3Client,
        s3Template: S3Template,
        @Qualifier("vehicleTransferArchiveStorageProperties") vehicleArchiveStorageProperties: VehicleArchiveStorageProperties,
    ): S3StorageClient = createS3StorageClient(s3Client, s3Template, vehicleArchiveStorageProperties)

    @Bean
    @Qualifier("vehicleHistoryArchiveStorageClient")
    fun vehicleHistoryArchiveStorageClient(
        s3Client: S3Client,
        s3Template: S3Template,
        @Qualifier("vehicleHistoryArchiveStorageProperties") vehicleArchiveStorageProperties: VehicleArchiveStorageProperties,
    ): S3StorageClient = createS3StorageClient(s3Client, s3Template, vehicleArchiveStorageProperties)

    @Bean
    @Qualifier("vehicleSalesArchiveStorageClient")
    fun vehicleSalesArchiveStorageClient(
        s3Client: S3Client,
        s3Template: S3Template,
        @Qualifier("vehicleSalesArchiveStorageProperties") vehicleArchiveStorageProperties: VehicleArchiveStorageProperties,
    ): S3StorageClient = createS3StorageClient(s3Client, s3Template, vehicleArchiveStorageProperties)

    private fun createS3StorageClient(
        s3Client: S3Client,
        s3Template: S3Template,
        storageProperties: VehicleArchiveStorageProperties,
    ): S3StorageClient {
        val storageProps = StorageProperties(storageProperties.sse, storageProperties.sseKmsKey)
        return S3StorageClient(s3Client, s3Template, storageProps)
    }
}
