/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.archival.job.service

import com.fleetmanagement.modules.archival.api.ArchivalService
import org.slf4j.LoggerFactory
import org.springframework.aop.framework.AopProxyUtils
import org.springframework.stereotype.Service
import java.util.*

@Service
class VehicleArchivalService(
    private val archivalServices: List<ArchivalService>,
) {
    companion object {
        private val log = LoggerFactory.getLogger(VehicleArchivalService::class.java)
    }

    fun archive(jobExecutionId: String): ArchivalResult {
        log.info("Starting scheduled vehicle archive job")
        val errors = mutableListOf<String>()
        try {
            val vehicleIds =
                archivalServices
                    .map {
                        val service = AopProxyUtils.ultimateTargetClass(it).simpleName
                        log.info("Fetching vehicles to archive from $service")

                        val vehicles = it.vehiclesToArchive(jobExecutionId)

                        if (vehicles.isNotEmpty()) {
                            log.info("Vehicles $vehicles fetched to archive")
                        }
                        vehicles
                    }.findCommonElements()

            if (vehicleIds.isNotEmpty()) {
                log.info("Vehicles to archive from all services: $vehicleIds")
                return archiveVehicles(jobExecutionId, vehicleIds, errors)
            }

            log.info("No vehicles archived")
            return ArchivalResult(jobExecutionId, JobStatus.SUCCESS)
        } catch (e: Exception) {
            errors.add("Failed to fetch vehicles to archive: ${e.message}")
            return ArchivalResult(jobExecutionId, JobStatus.FAILED, errors)
        }
    }

    private fun archiveVehicles(
        jobExecutionId: String,
        vehicleIds: List<UUID>,
        errors: MutableList<String>,
    ): ArchivalResult {
        var jobStatus = JobStatus.SUCCESS
        archivalServices.forEach {
            val service = AopProxyUtils.ultimateTargetClass(it).simpleName
            try {
                log.info("Archiving vehicle from service: $service")
                it.archiveVehicleData(jobExecutionId, vehicleIds)
                log.info("vehicles with ids $vehicleIds archived from service: $service")
            } catch (e: Exception) {
                val message = "Archiving vehicles failed for service: $service - ${e.message}"
                errors.add(message)
            }
        }
        if (errors.isNotEmpty()) {
            jobStatus = JobStatus.FAILED
        } else {
            log.info("vehicles with $vehicleIds archived successfully")
        }
        return ArchivalResult(jobExecutionId, jobStatus, errors)
    }
}

private fun <T> List<List<T>>.findCommonElements(): List<T> {
    if (this.isEmpty()) return emptyList()
    return this.map { it.toSet() }.reduce { acc, set -> acc.intersect(set) }.toList()
}

data class ArchivalResult(
    val jobExecutionId: String,
    val status: JobStatus,
    val errors: List<String> = emptyList(),
)

enum class JobStatus {
    SUCCESS,
    FAILED,
}
