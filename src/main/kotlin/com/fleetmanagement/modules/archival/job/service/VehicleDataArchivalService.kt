/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.archival.job.service

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.emh.shared.s3.client.adapter.S3StorageClientException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.archival.api.ArchivalService
import com.fleetmanagement.modules.archival.job.configurations.VehicleArchiveStorageProperties
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehiclehistory.api.VehicleHistoryArchival
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.temporal.ChronoUnit
import java.util.UUID

@Service
@Transactional
class VehicleDataArchivalService(
    private val vehicleRepository: JPAVehicleRepository,
    @Qualifier("vehicleArchiveStorageProperties") private val storageProperties: VehicleArchiveStorageProperties,
    @Qualifier("archivalObjectMapper") private val objectMapper: ObjectMapper,
    @Qualifier("vehicleArchiveStorageClient") private val s3StorageClient: S3StorageClient,
    private val vehicleHistoryArchival: VehicleHistoryArchival,
) : ArchivalService {
    companion object {
        private val logger = LoggerFactory.getLogger(VehicleDataArchivalService::class.java)
    }

    private fun validateBucketConfig() {
        val bucketExists = s3StorageClient.bucketExists(storageProperties.bucket)
        if (!bucketExists) {
            throw StorageException("Bucket [${storageProperties.bucket}] does not exist.")
        }
    }

    fun deleteVehicleBy(vehicleId: UUID) {
        logger.info("Deleting vehicle with id: $vehicleId")
        return vehicleRepository.deleteById(vehicleId)
    }

    override fun vehiclesToArchive(jobExecutionId: String): List<UUID> {
        val threeYearsAgo = ZonedDateTime.now(ZoneOffset.UTC).minus(3, ChronoUnit.YEARS)
        return vehicleRepository
            .findByLastUpdatedAtBefore(threeYearsAgo)
            .filter { hasArchiveRelevantDate(it) || isCancelledOrder(it) }
            .map { checkNotNull(it.id) }
    }

    override fun archiveVehicleData(
        jobExecutionId: String,
        vehicleIds: List<UUID>,
    ) {
        validateBucketConfig()
        vehicleIds.forEach { vehicleId ->
            try {
                processArchival(vehicleId)
                processArchivalForVehicleHistory(vehicleId)
            } catch (exception: S3StorageClientException) {
                logger.error("Upload to S3 failed for vehicleId $vehicleId with error", exception)
            } catch (exception: Exception) {
                logger.error("An unexpected error encountered while archiving vehicle: $vehicleId", exception)
            }
        }
    }

    private fun processArchivalForVehicleHistory(vehicleId: UUID) {
        vehicleHistoryArchival.archiveVehicleHistory(vehicleId)
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun processArchival(vehicleId: UUID) {
        val vehicle =
            vehicleRepository.findById(vehicleId).orElseThrow {
                throw IllegalArgumentException("Vehicle with ID $vehicleId not found")
            }
        logger.info("Uploading vehicle data of [${vehicle.id}] to S3")
        val resource = ByteArrayResource(objectMapper.writeValueAsBytes(vehicle))
        s3StorageClient.uploadResource("vehicleData-$vehicleId", resource, storageProperties.bucket)
        logger.info("Upload vehicledata successful")
        deleteVehicleBy(vehicleId)
    }

    private fun hasArchiveRelevantDate(jpaVehicleEntity: JPAVehicleEntity): Boolean {
        val fleetInfo = jpaVehicleEntity.fleetInfo
        return listOfNotNull(
            fleetInfo?.soldDate,
            fleetInfo?.scrappedDate,
            fleetInfo?.soldCupCarDate,
            fleetInfo?.stolenDate,
        ).any()
    }

    private fun isCancelledOrder(jpaVehicleEntity: JPAVehicleEntity): Boolean = jpaVehicleEntity.orderInfo?.isOrderCancelled() ?: false
}

class StorageException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()
