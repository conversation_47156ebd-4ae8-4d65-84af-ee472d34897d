/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.archival.job.service

import com.fleetmanagement.modules.archival.api.ArchivalService
import com.fleetmanagement.modules.archival.job.client.VehicleRegistrationArchivalClient
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.NotEmpty
import org.springframework.stereotype.Service
import java.util.*

@Service
class VehicleRegistrationArchivalService(
    private val client: VehicleRegistrationArchivalClient,
) : ArchivalService {
    override fun vehiclesToArchive(jobExecutionId: String): List<UUID> {
        val response = client.registrationOrdersToArchive(jobExecutionId)
        return response.body?.data ?: emptyList()
    }

    override fun archiveVehicleData(
        jobExecutionId: String,
        vehicleIds: List<UUID>,
    ) {
        val archiveOrderDto =
            ArchiveOrderDto(
                orders = vehicleIds,
                jobExecutionId = jobExecutionId,
            )
        client.archiveVehicleRegistrationData(archiveOrderDto)
    }
}

data class ArchiveOrderDto(
    @field:NotEmpty
    val orders: List<UUID>,
    @field:NotBlank
    val jobExecutionId: String,
)
