/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.archival.job.service

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.emh.shared.s3.client.adapter.S3StorageClientException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.archival.api.ArchivalService
import com.fleetmanagement.modules.archival.job.configurations.VehicleArchiveStorageProperties
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferArchiveDto
import com.fleetmanagement.modules.vehicletransfer.application.port.ArchiveVehicleTransferUseCase
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.*

@Component
@Transactional
class VehicleTransferArchivalService(
    @Qualifier("vehicleTransferArchiveStorageProperties") private val storageProperties: VehicleArchiveStorageProperties,
    @Qualifier("vehicleTransferArchiveStorageClient") private val s3StorageClient: S3StorageClient,
    private val archiveVehicleTransferUseCase: ArchiveVehicleTransferUseCase,
    @Qualifier("archivalObjectMapper") private val objectMapper: ObjectMapper,
) : ArchivalService {
    companion object {
        private val logger = LoggerFactory.getLogger(VehicleTransferArchivalService::class.java)
    }

    private fun validateBucketConfig() {
        val bucketExists = s3StorageClient.bucketExists(storageProperties.bucket)
        if (!bucketExists) {
            throw StorageException("Bucket [${storageProperties.bucket}] does not exist.")
        }
    }

    private fun deleteVehicleTransfer(key: Long) {
        logger.info("Deleting vehicle Transfer with key: $key")
        return archiveVehicleTransferUseCase.deleteVehicleTransferByKey(key)
    }

    /**
     * get all planned and not planed vehicle transfers and extract the id
     */
    override fun vehiclesToArchive(jobExecutionId: String): List<UUID> {
        val threeYearsAgo = OffsetDateTime.now().minusYears(3)
        return archiveVehicleTransferUseCase
            .findVehicleTransfersByLastUpdatedAtBefore(threeYearsAgo)
            .toList()
    }

    /**
     * get a list of vehicle id's
     * will process each vehicle id and find the correspondences vehicle transfers
     * process each transfer
     */
    override fun archiveVehicleData(
        jobExecutionId: String,
        vehicleIds: List<UUID>,
    ) {
        validateBucketConfig()
        vehicleIds.forEach { vehicleId ->
            val vehicleTransfers =
                archiveVehicleTransferUseCase.findPlannedAndVehicleTransfersByVehicleId(vehicleId)
            vehicleTransfers.forEach {
                try {
                    processArchival(it)
                } catch (exception: S3StorageClientException) {
                    logger.error("Upload to S3 failed for vehicleId $vehicleId with error", exception)
                } catch (exception: Exception) {
                    logger.error("An unexpected error encountered while archiving vehicle: $vehicleId", exception)
                }
            }
        }
    }

    /**
     *
     * for each record it tried to upload to s3, if successful will delete the record from the db
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun processArchival(toArchiveVehicleTransfer: VehicleTransferArchiveDto) {
        logger.info("Uploading vehicle transfer data of [${toArchiveVehicleTransfer.key}] to S3")
        val resource = ByteArrayResource(objectMapper.writeValueAsBytes(toArchiveVehicleTransfer))
        s3StorageClient.uploadResource(toArchiveVehicleTransfer.key.toString(), resource, storageProperties.bucket)
        logger.info("Upload vehicle transfer successful")
        deleteVehicleTransfer(toArchiveVehicleTransfer.key)
    }
}
