/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.balancesheet

import liquibase.integration.spring.SpringLiquibase
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import javax.sql.DataSource

@Configuration
class BalanceSheetDBConfiguration {
    @Bean
    fun balancesheetLiquibase(datasource: DataSource): SpringLiquibase {
        val liquibase = SpringLiquibase()
        liquibase.changeLog = "classpath:/db/changelog/db.changelog-balancesheet.yml"
        liquibase.liquibaseSchema = "balancesheet"
        liquibase.dataSource = datasource
        return liquibase
    }
}
