/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.adapter.out.pace

import com.fleetmanagement.modules.balancesheet.application.port.CostCenterUpdateException
import com.fleetmanagement.modules.balancesheet.application.port.CostCenterUpdateUseCase
import com.fleetmanagement.modules.balancesheet.application.port.SalesLockUpdateException
import com.fleetmanagement.modules.balancesheet.application.port.SalesLockUpdateUseCase
import com.fleetmanagement.modules.balancesheet.application.port.ScrappingInitiationException
import com.fleetmanagement.modules.balancesheet.application.port.ScrappingInitiationUseCase
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.CostCenterUpdateCostCenterUpdateDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.CostCenterUpdateDataDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.CostCenterUpdateDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.SalesLockUpdateDataDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.SalesLockUpdateDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.SalesLockUpdateSalesLockUpdateDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.ScrappingInitiationDataDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.ScrappingInitiationDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.ScrappingInitiationScrappingDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.time.format.DateTimeFormatter

@Component
class BalanceSheetPaceAdapter(
    private val balanceSheetPaceClient: BalanceSheetPaceClient,
) : CostCenterUpdateUseCase,
    ScrappingInitiationUseCase,
    SalesLockUpdateUseCase {
    override fun updateCostCenter(
        vin: String,
        depreciationRelevantCostCenter: String?,
        usingCostCenter: String?,
    ) {
        val paceUsingCostCenter = sanitizeCostCenter(usingCostCenter)
        val paceDepreciationRelevantCostCenter = sanitizeCostCenter(depreciationRelevantCostCenter)
        val requestPayload =
            CostCenterUpdateDto(
                costCenterUpdate =
                    CostCenterUpdateCostCenterUpdateDto(
                        data =
                            listOf(
                                CostCenterUpdateDataDto(
                                    companyCode = PACE_BALANCE_SHEET_COMPANY_CODE,
                                    vehicleNumber = vin,
                                    activityDate = LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_FORMAT)),
                                    activityCode = COST_CENTER_UPDATE_ACTIVITY_CODE,
                                    costCenter = paceUsingCostCenter,
                                    afaCostCenter = paceDepreciationRelevantCostCenter,
                                ),
                            ),
                    ),
            )

        // TODO remove payload after testing
        log.info("Sending cost center update to PACE for vin [$vin]. $requestPayload")

        try {
            balanceSheetPaceClient.costCenterUpdate(requestPayload)
        } catch (exception: PaceException) {
            throw CostCenterUpdateException(
                message = "Error while sending cost center update.",
                cause = exception,
            )
        }
    }

    override fun initiateScrapping(vin: String) {
        val requestPayload =
            ScrappingInitiationDto(
                scrapping =
                    ScrappingInitiationScrappingDto(
                        data =
                            listOf(
                                ScrappingInitiationDataDto(
                                    companyCode = PACE_BALANCE_SHEET_COMPANY_CODE,
                                    vehicleNumber = vin,
                                    activityDate = LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_FORMAT)),
                                    activityCode = SCRAPPING_INITIATION_UPDATE_ACTIVITY_CODE,
                                    activityType = SCRAPPING_INITIATION_UPDATE_ACTIVITY_TYPE,
                                    activityKey = SCRAPPING_INITIATION_UPDATE_ACTIVITY_KEY,
                                    /**
                                     * As per Elena, Normally it should not be possible to change a vehicle from SX99 back to something else.
                                     * In case of a user error, the correction will be done manually in both systems.
                                     * Since we are only syncing when vehicle status is SX99, cancellation is set as TRUE
                                     */
                                    cancellation = TRUE,
                                ),
                            ),
                    ),
            )

        // TODO remove payload after testing
        log.info("Sending initiate scrapping to PACE for vin [$vin]. $requestPayload")

        try {
            balanceSheetPaceClient.scrappingInitiation(requestPayload)
        } catch (exception: PaceException) {
            throw ScrappingInitiationException(
                message = "Error while sending initiate scrapping.",
                cause = exception,
            )
        }
    }

    override fun updateSalesLock(
        vin: String,
        blockedForSale: Boolean,
    ) {
        val requestPayload =
            SalesLockUpdateDto(
                salesLockUpdate =
                    SalesLockUpdateSalesLockUpdateDto(
                        data =
                            listOf(
                                SalesLockUpdateDataDto(
                                    companyCode = PACE_BALANCE_SHEET_COMPANY_CODE,
                                    vehicleNumber = vin,
                                    activityDate = LocalDate.now().format(DateTimeFormatter.ofPattern(DATE_FORMAT)),
                                    activityCode = SALES_LOCK_UPDATE_ACTIVITY_CODE,
                                    salesLock = blockedForSale,
                                ),
                            ),
                    ),
            )

        // TODO remove payload after testing
        log.info("Sending sales lock update to PACE for vin [$vin]. $requestPayload")

        try {
            balanceSheetPaceClient.salesLockUpdate(requestPayload)
        } catch (exception: PaceException) {
            throw SalesLockUpdateException(
                message = "Error while sending sales lock update.",
                cause = exception,
            )
        }
    }

    private fun sanitizeCostCenter(costCenter: String?): String {
        /**
         * As per Elena, since PACE is able to accept empty strings, we pass empty strings if value is null
         */
        if (costCenter.isNullOrEmpty()) return ""
        val index = costCenter.indexOf(PACE_BALANCE_SHEET_COMPANY_CODE)
        return if (index >= 0 && costCenter.length >= index + 8) {
            costCenter.substring(index, index + 8)
        } else {
            log.warn("Could not sanitize costCenter: $costCenter, returning non-sanitized version")
            costCenter
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(BalanceSheetPaceAdapter::class.java)
        private const val PACE_BALANCE_SHEET_COMPANY_CODE = "H001"
        private const val COST_CENTER_UPDATE_ACTIVITY_CODE = "K"
        private const val SALES_LOCK_UPDATE_ACTIVITY_CODE = "L"
        private const val SCRAPPING_INITIATION_UPDATE_ACTIVITY_CODE = "T"
        private const val SCRAPPING_INITIATION_UPDATE_ACTIVITY_TYPE = "DUMMY"
        private const val SCRAPPING_INITIATION_UPDATE_ACTIVITY_KEY = "DUMMY"
        private const val TRUE = true
        private const val DATE_FORMAT = "yyyyMMdd"
    }
}
