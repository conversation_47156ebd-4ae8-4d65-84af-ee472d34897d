/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.balancesheet.adapter.out.pace

import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.CostCenterUpdateDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.SalesLockUpdateDto
import com.fleetmanagement.modules.pacebalancesheet.generated.adapter.out.rest.model.ScrappingInitiationDto
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.service.annotation.PostExchange

interface BalanceSheetPaceClient {
    @PostExchange(
        url = "/http/APP40749_CompanyVehicles/CostCenterUpdate",
        accept = ["application/json"],
    )
    fun costCenterUpdate(
        @RequestBody body: CostCenterUpdateDto,
    )

    @PostExchange(
        url = "/http/APP40749_CompanyVehicles/SalesLockUpdate",
        accept = ["application/json"],
    )
    fun salesLockUpdate(
        @RequestBody body: SalesLockUpdateDto,
    )

    @PostExchange(
        url = "/http/APP40749_CompanyVehicles/ScrappingInitiation",
        accept = ["application/json"],
    )
    fun scrappingInitiation(
        @RequestBody body: ScrappingInitiationDto,
    )
}
