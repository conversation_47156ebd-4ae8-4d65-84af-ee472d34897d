/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.balancesheet.adapter.out.pace

open class PaceException(
    message: String? = null,
    cause: Throwable? = null,
) : RuntimeException(message, cause)

class PaceUnreachableException(
    message: String? = null,
    cause: Throwable? = null,
) : PaceException(message = message, cause = cause)
