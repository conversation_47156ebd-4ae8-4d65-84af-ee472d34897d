/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.balancesheet.adapter.out.pace.config

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.core.JsonGenerator
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.MapperFeature
import com.fasterxml.jackson.databind.SerializationFeature
import com.fleetmanagement.modules.balancesheet.adapter.out.pace.BalanceSheetPaceClient
import com.fleetmanagement.modules.balancesheet.adapter.out.pace.BalanceSheetPaceWebClientExceptionHandler
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.MediaType
import org.springframework.http.codec.ClientCodecConfigurer
import org.springframework.http.codec.json.Jackson2JsonDecoder
import org.springframework.http.codec.json.Jackson2JsonEncoder
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.security.oauth2.client.AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.ClientCredentialsReactiveOAuth2AuthorizedClientProvider
import org.springframework.security.oauth2.client.InMemoryReactiveOAuth2AuthorizedClientService
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientManager
import org.springframework.security.oauth2.client.ReactiveOAuth2AuthorizedClientService
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.InMemoryReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.web.reactive.function.client.ServerOAuth2AuthorizedClientExchangeFilterFunction
import org.springframework.security.oauth2.client.web.server.AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository
import org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.ExchangeStrategies
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.support.WebClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory
import java.time.ZoneId
import java.util.*

@Configuration
class BalanceSheetPaceWebClientConfig {
    // moved here, as setting it up as a mean, messes with default jackson config of this service
    private val paceJackson2ObjectMapperBuilder =
        Jackson2ObjectMapperBuilder()
            .featuresToEnable(
                DeserializationFeature.ACCEPT_SINGLE_VALUE_AS_ARRAY,
                DeserializationFeature.USE_BIG_DECIMAL_FOR_FLOATS,
                JsonParser.Feature.ALLOW_COMMENTS,
                MapperFeature.ACCEPT_CASE_INSENSITIVE_ENUMS,
                JsonGenerator.Feature.WRITE_BIGDECIMAL_AS_PLAIN,
            ).featuresToDisable(
                DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,
                SerializationFeature.WRITE_DATES_AS_TIMESTAMPS,
            )
            // null values need to be included or PACE will crash
            .serializationInclusion(JsonInclude.Include.ALWAYS)
            .timeZone(TimeZone.getTimeZone(ZoneId.of("UTC")))
            .createXmlMapper(false)

    @Bean
    @Qualifier("pace-balance-sheet")
    fun balanceSheetPaceExchangeStrategies(): ExchangeStrategies =
        ExchangeStrategies
            .builder()
            .codecs { clientDefaultCodecsConfigurer: ClientCodecConfigurer ->
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonEncoder(
                        Jackson2JsonEncoder(paceJackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON),
                    )
                clientDefaultCodecsConfigurer
                    .defaultCodecs()
                    .jackson2JsonDecoder(
                        Jackson2JsonDecoder(paceJackson2ObjectMapperBuilder.build(), MediaType.APPLICATION_JSON),
                    )
                clientDefaultCodecsConfigurer.defaultCodecs().enableLoggingRequestDetails(true)
            }.build()

    @Bean
    @Qualifier("pace-balance-sheet")
    fun balanceSheetPaceReactiveClientRegistrationRepository(
        clientRegistrationRepository: ClientRegistrationRepository,
    ): ReactiveClientRegistrationRepository =
        InMemoryReactiveClientRegistrationRepository(
            clientRegistrationRepository.findByRegistrationId(CLIENT_REGISTRATION_ID),
        )

    @Bean
    @Qualifier("pace-balance-sheet")
    fun balanceSheetPaceReactiveOAuth2AuthorizedClientService(
        @Qualifier("pace-balance-sheet") clientRegistrationRepository: ReactiveClientRegistrationRepository,
    ): ReactiveOAuth2AuthorizedClientService = InMemoryReactiveOAuth2AuthorizedClientService(clientRegistrationRepository)

    @Bean
    @Qualifier("pace-balance-sheet")
    fun balanceSheetPaceReactiveAuthorizedClientRepository(
        @Qualifier("pace-balance-sheet") authorizedClientService: ReactiveOAuth2AuthorizedClientService,
    ): ServerOAuth2AuthorizedClientRepository = AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository(authorizedClientService)

    @Bean
    @Qualifier("pace-balance-sheet")
    fun balanceSheetPaceReactiveOAuth2AuthorizedClientManager(
        @Qualifier("pace-balance-sheet") clientRegistrationRepository: ReactiveClientRegistrationRepository,
        @Qualifier("pace-balance-sheet") authorizedClientService: ReactiveOAuth2AuthorizedClientService,
    ): ReactiveOAuth2AuthorizedClientManager =
        AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager(
            clientRegistrationRepository,
            authorizedClientService,
        ).apply {
            setAuthorizedClientProvider(
                ClientCredentialsReactiveOAuth2AuthorizedClientProvider(),
            )
        }

    @Bean
    @Qualifier("pace-balance-sheet")
    fun balanceSheetPaceServiceLocationHttpServiceProxyFactory(
        @Value("\${balance-sheet.pace.base-url}") baseUrl: String,
        @Qualifier("pace-balance-sheet") authorizedClientManager: ReactiveOAuth2AuthorizedClientManager,
        balanceSheetPaceWebClientExceptionHandler: BalanceSheetPaceWebClientExceptionHandler,
        @Qualifier("pace-balance-sheet") exchangeStrategies: ExchangeStrategies,
    ): HttpServiceProxyFactory {
        val oauth2Filter = ServerOAuth2AuthorizedClientExchangeFilterFunction(authorizedClientManager)
        oauth2Filter.setDefaultClientRegistrationId(CLIENT_REGISTRATION_ID)

        val webClient =
            WebClient
                .builder()
                .baseUrl(baseUrl)
                .exchangeStrategies(exchangeStrategies)
                .filter(ExchangeFilterFunction.ofResponseProcessor(balanceSheetPaceWebClientExceptionHandler::clientErrorResponseProcessor))
                .filter(balanceSheetPaceWebClientExceptionHandler::clientErrorRequestProcessor)
                .filter(oauth2Filter)
                .build()

        val adapter = WebClientAdapter.create(webClient)

        return HttpServiceProxyFactory.builderFor(adapter).build()
    }

    @Bean
    @Qualifier("pace-balance-sheet")
    fun balanceSheetPaceClient(
        @Qualifier("pace-balance-sheet") paceHttpServiceProxyFactory: HttpServiceProxyFactory,
    ): BalanceSheetPaceClient = paceHttpServiceProxyFactory.createClient(BalanceSheetPaceClient::class.java)

    companion object {
        const val CLIENT_REGISTRATION_ID = "pace-balance-sheet"
    }
}
