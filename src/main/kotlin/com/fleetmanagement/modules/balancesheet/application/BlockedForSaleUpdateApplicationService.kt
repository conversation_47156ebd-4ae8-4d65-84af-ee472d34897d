/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.application

import com.fleetmanagement.modules.balancesheet.application.port.SalesLockUpdateException
import com.fleetmanagement.modules.balancesheet.application.port.SalesLockUpdateUseCase
import com.fleetmanagement.modules.balancesheet.domain.service.blockedforsaleupdate.CreateBlockedForSaleUpdateService
import com.fleetmanagement.modules.balancesheet.domain.service.blockedforsaleupdate.UpdateBlockedForSaleUpdateService
import com.fleetmanagement.modules.vehicledata.api.ReadVehiclesActivatedForAccounting
import org.slf4j.LoggerFactory
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class BlockedForSaleUpdateApplicationService(
    private val blockedForSaleUpdateFinder: BlockedForSaleUpdateFinder,
    private val updateBlockedForSaleUpdateService: UpdateBlockedForSaleUpdateService,
    private val salesLockUpdateUseCase: SalesLockUpdateUseCase,
    private val readVehiclesActivatedForAccounting: ReadVehiclesActivatedForAccounting,
    private val createBlockedForSaleUpdateService: CreateBlockedForSaleUpdateService,
) {
    @Transactional
    fun syncBlockedForSaleUpdateToP40() {
        val unSynced = blockedForSaleUpdateFinder.findUnSyncedBlockedForSaleUpdate()
        unSynced.forEach {
            try {
                salesLockUpdateUseCase.updateSalesLock(it.vin, it.blockedForSale)
                updateBlockedForSaleUpdateService.syncBlockedForSaleUpdate(it)
            } catch (exception: SalesLockUpdateException) {
                log.error("Error syncing BlockedForSaleUpdate vin: ${it.vin}, ${exception.message}")
            }
        }
    }

    @Transactional
    fun prepareBlockedForSaleUpdate() {
        var pageNumber = INITIAL_PAGE_NUMBER
        var hasNext: Boolean
        do {
            val pageable =
                PageRequest.of(
                    pageNumber,
                    PAGE_SIZE,
                    Sort.by(SORT_BY).ascending(),
                )
            val vehiclesPage = readVehiclesActivatedForAccounting.readVehiclesActivatedForAccounting(pageable)
            val vehicles = vehiclesPage.content
            vehicles.forEach { vehicle ->
                val blockedForSaleUpdate = blockedForSaleUpdateFinder.findBlockedForSaleUpdateByVehicleId(vehicle.id)
                val vehicleBlockedForSale = vehicle.order?.blockedForSale
                if (vehicleBlockedForSale == null) {
                    log.warn("Cannot prepare blockedForSaleUpdate, Vehicle's blockedForSale is null id: ${vehicle.id} ")
                    return@forEach
                }

                val vin = vehicle.vin
                if (vin == null) {
                    log.warn("Cannot prepare blockedForSaleUpdate, Vehicle's vin is null id: ${vehicle.id} ")
                    return@forEach
                }
                if (blockedForSaleUpdate == null && vehicleBlockedForSale) {
                    createBlockedForSaleUpdateService.createBlockedForSaleUpdate(
                        vehicleId = vehicle.id,
                        vin = vin,
                        blockedForSale = vehicleBlockedForSale,
                    )
                } else if (blockedForSaleUpdate != null && blockedForSaleUpdate.blockedForSale != vehicleBlockedForSale) {
                    updateBlockedForSaleUpdateService.updateBlockedForSale(vehicleBlockedForSale, blockedForSaleUpdate)
                }
            }
            hasNext = vehiclesPage.hasNext()
            pageNumber++
        } while (hasNext)
    }

    companion object {
        private val log = LoggerFactory.getLogger(BlockedForSaleUpdateApplicationService::class.java)
        private const val PAGE_SIZE = 1000
        private const val SORT_BY = "createdAt"
        private const val INITIAL_PAGE_NUMBER = 0
    }
}
