/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.application

import com.fleetmanagement.modules.balancesheet.domain.BlockedForSaleUpdate
import com.fleetmanagement.modules.balancesheet.domain.BlockedForSaleUpdateRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
@Transactional(readOnly = true)
class BlockedForSaleUpdateFinder(
    private val blockedForSaleUpdateRepository: BlockedForSaleUpdateRepository,
) {
    fun findUnSyncedBlockedForSaleUpdate(): List<BlockedForSaleUpdate> = blockedForSaleUpdateRepository.findBySyncedIsFalse()

    fun findBlockedForSaleUpdateByVehicleId(vehicleId: UUID): BlockedForSaleUpdate? =
        blockedForSaleUpdateRepository.findByVehicleId(vehicleId)
}
