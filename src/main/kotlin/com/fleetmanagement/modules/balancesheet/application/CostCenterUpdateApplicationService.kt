package com.fleetmanagement.modules.balancesheet.application

import com.fleetmanagement.emhshared.USAGE_GROUP_DESCRIPTION_PERSON
import com.fleetmanagement.modules.balancesheet.application.port.CostCenterUpdateException
import com.fleetmanagement.modules.balancesheet.application.port.CostCenterUpdatePreparationUseCase
import com.fleetmanagement.modules.balancesheet.application.port.CostCenterUpdateUseCase
import com.fleetmanagement.modules.balancesheet.application.port.SyncAllCostCenterUpdatesUseCase
import com.fleetmanagement.modules.balancesheet.domain.CostCenterUpdate
import com.fleetmanagement.modules.balancesheet.domain.service.CostCenterUpdateCreateService
import com.fleetmanagement.modules.balancesheet.domain.service.CostCenterUpdateSyncService
import com.fleetmanagement.modules.balancesheet.domain.service.CostCenterUpdateUpdate
import com.fleetmanagement.modules.balancesheet.domain.service.CostCenterUpdateUpdateService
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterFinder
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupFinder
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupId
import com.fleetmanagement.modules.vehicledata.api.ReadVehiclesActivatedForAccounting
import com.fleetmanagement.modules.vehicletransfer.application.CurrentVehicleTransferService
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import org.slf4j.LoggerFactory
import org.springframework.dao.DataAccessException
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Sort
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.*

@Component
@Transactional
class CostCenterUpdateApplicationService(
    private val costCenterUpdateUseCase: CostCenterUpdateUseCase,
    private val costCenterUpdateSyncService: CostCenterUpdateSyncService,
    private val costCenterUpdateCreateService: CostCenterUpdateCreateService,
    private val costCenterUpdateUpdateService: CostCenterUpdateUpdateService,
    private val costCenterUpdateFinder: CostCenterUpdateFinder,
    private val readVehiclesActivatedForAccounting: ReadVehiclesActivatedForAccounting,
    private val vehicleTransferFinder: VehicleTransferFinder,
    private val plannedVehicleTransferFinder: PlannedVehicleTransferFinder,
    private val currentVehicleTransferService: CurrentVehicleTransferService,
    private val costCenterFinder: CostCenterFinder,
    private val usageGroupFinder: UsageGroupFinder,
) : SyncAllCostCenterUpdatesUseCase,
    CostCenterUpdatePreparationUseCase {
    override fun syncAll() {
        val allUnsynced = costCenterUpdateFinder.findAllUnsynced()
        allUnsynced.forEach { costCenterUpdate ->
            try {
                costCenterUpdateUseCase.updateCostCenter(
                    vin = costCenterUpdate.vin,
                    depreciationRelevantCostCenter = costCenterUpdate.depreciationRelevantCostCenter?.value,
                    usingCostCenter = costCenterUpdate.usingCostCenter?.value,
                )
                log.info("Updated cost center on p40 for {}", costCenterUpdate)
                costCenterUpdateSyncService.syncCostCenterUpdate(costCenterUpdate, OffsetDateTime.now())
                log.info("Updated cost center on p40 for {}", costCenterUpdate)
            } catch (e: CostCenterUpdateException) {
                log.warn("Could not update cost center for vin ${costCenterUpdate.vin} in PACE", e.message)
            } catch (e: DataIntegrityViolationException) {
                log.error("Error while syncing sync date for cost center update for vin ${costCenterUpdate.vin}", e)
            }
        }
    }

    override fun prepareCostCenterUpdates() {
        var currentPage = 0
        do {
            val activeVehicles =
                readVehiclesActivatedForAccounting.readVehiclesActivatedForAccounting(
                    PageRequest.of(currentPage, PAGE_SIZE, Sort.by(Sort.Direction.DESC, "createdAt")),
                )

            activeVehicles.forEach {
                try {
                    prepareCostCenterUpdateForVehicle(it.id, it.vin)
                } catch (e: DataAccessException) {
                    log.error("Error while preparing costCenterUpate for sync for vin: ${it.vin}", e)
                }
            }

            currentPage += 1
        } while (currentPage < activeVehicles.totalPages)
    }

    private fun prepareCostCenterUpdateForVehicle(
        vehicleId: UUID?,
        vin: String?,
    ) {
        // Do all the null checks
        if (vehicleId == null) {
            log.warn("Skipping vehicle with vin=$vin: vehicleId is null")
            return
        }

        if (vin == null) {
            log.warn("Skipping vehicle with id=$vehicleId, vin=$vin: vin is null")
            return
        }

        val currentVehicleTransferKey = currentVehicleTransferService.retrieveCurrentVehicleTransferKey(vehicleId)
        if (currentVehicleTransferKey == null) {
            log.warn("Skipping vehicle with id=$vehicleId, vin=$vin: could not retrieve current vehicle transfer key")
            return
        }

        val currentVehicleTransfer =
            vehicleTransferFinder.findVehicleTransferByKey(currentVehicleTransferKey)
                ?: plannedVehicleTransferFinder.findPlannedVehicleTransferByKey(currentVehicleTransferKey)
        if (currentVehicleTransfer == null) {
            log.warn("Skipping vehicle with id=$vehicleId, vin=$vin: could not find current vehicle transfer")
            return
        }

        val depreciationRelevantCostCenter =
            currentVehicleTransfer.depreciationRelevantCostCenterId
                ?.let { CostCenterId(it.value) }
                ?.let { costCenterFinder.findCostCenter(it) }

        // FPT1-1288 is usageGroup is PERSON use fixed costCenter instead of 'null'-value provided by transfer
        val usageGroup =
            currentVehicleTransfer.usageGroup?.let { usageGroupFinder.getUsageGroup(UsageGroupId(it.value)) }
        val usingCostCenter =
            if (USAGE_GROUP_DESCRIPTION_PERSON.equals(usageGroup?.description, true)) {
                USING_COST_CENTER_PERSON
            } else {
                currentVehicleTransfer.usingCostCenter
            }

        // Create or update
        val existingCostCenterUpdate = costCenterUpdateFinder.findByVehicleId(vehicleId)
        if (existingCostCenterUpdate == null) {
            costCenterUpdateCreateService.createCostCenterUpdate(
                CostCenterUpdate(
                    vin = vin,
                    vehicleId = vehicleId,
                    depreciationRelevantCostCenter = depreciationRelevantCostCenter?.description,
                    usingCostCenter = usingCostCenter,
                    internalOrderNumber = currentVehicleTransfer.internalOrderNumber,
                ),
            )
            log.info("Created costCenterUpdate for vehicle with id=$vehicleId and vin $vin")
        } else {
            costCenterUpdateUpdateService.updateCostCenterUpdate(
                existingCostCenterUpdate,
                CostCenterUpdateUpdate(
                    depreciationRelevantCostCenter = depreciationRelevantCostCenter?.description,
                    usingCostCenter = usingCostCenter,
                ),
            )
            log.info("Called update on costCenterUpdate for vehicle with id=$vehicleId and vin $vin")
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(CostCenterUpdateApplicationService::class.java)
        const val PAGE_SIZE = 100
        private val USING_COST_CENTER_PERSON = CostCenterDescription("00H0015129")
    }
}
