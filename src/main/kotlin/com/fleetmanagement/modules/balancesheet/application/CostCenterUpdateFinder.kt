/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.balancesheet.application

import com.fleetmanagement.modules.balancesheet.domain.CostCenterUpdate
import com.fleetmanagement.modules.balancesheet.domain.CostCenterUpdateId
import com.fleetmanagement.modules.balancesheet.domain.CostCenterUpdateRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
@Transactional(readOnly = true)
class CostCenterUpdateFinder(
    private val costCenterUpdateRepository: CostCenterUpdateRepository,
) {
    fun getCostCenterUpdate(costCenterUpdateId: CostCenterUpdateId): CostCenterUpdate =
        costCenterUpdateRepository.findById(id = costCenterUpdateId)
            ?: throw CostCenterUpdateNotFoundException(costCenterUpdateId = costCenterUpdateId)

    fun findByVehicleId(vehicleId: UUID): CostCenterUpdate? = costCenterUpdateRepository.findByVehicleId(vehicleId = vehicleId)

    fun findByVin(vin: String): CostCenterUpdate? = costCenterUpdateRepository.findByVin(vin)

    fun findAllUnsynced() = costCenterUpdateRepository.findBySyncDateIsNull()
}
