/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.application

import com.fleetmanagement.modules.balancesheet.application.port.ScrappingInitiationException
import com.fleetmanagement.modules.balancesheet.application.port.ScrappingInitiationUseCase
import com.fleetmanagement.modules.balancesheet.domain.service.scrappingstatusupdate.CreateScrappingStatusUpdateService
import com.fleetmanagement.modules.balancesheet.domain.service.scrappingstatusupdate.DeleteScrappingStatusUpdateService
import com.fleetmanagement.modules.balancesheet.domain.service.scrappingstatusupdate.UpdateScrappingStatusUpdateService
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.events.VehicleStatusChangedEvent
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener

@Component
class ScrappingStatusUpdateApplicationService(
    private val scrappingStatusUpdateFinder: ScrappingStatusUpdateFinder,
    private val updateScrappingStatusUpdateService: UpdateScrappingStatusUpdateService,
    private val scrappingInitiationUseCase: ScrappingInitiationUseCase,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val deleteScrappingStatusUpdateService: DeleteScrappingStatusUpdateService,
    private val createScrappingStatusUpdateService: CreateScrappingStatusUpdateService,
) {
    @Transactional
    fun syncScrappingStatusUpdateToP40() {
        val unSynced = scrappingStatusUpdateFinder.findUnSyncedScrappingStatusUpdate()
        unSynced.forEach {
            try {
                scrappingInitiationUseCase.initiateScrapping(it.vin)
                updateScrappingStatusUpdateService.syncScrappingStatusUpdate(it)
            } catch (exception: ScrappingInitiationException) {
                log.error("Error syncing ScrappingStatusUpdate vin: ${it.vin}, ${exception.message}")
            }
        }
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handleVehicleStatusChange(event: VehicleStatusChangedEvent) {
        when (event.status) {
            VehicleStatus.SX99 -> {
                val vehicle = readVehicleByVehicleId.readVehicleById(event.vehicleId)
                if (vehicle?.vin == null) {
                    log.error("Could not create ScrappingStatusUpdate vehicleId: ${event.vehicleId}, vehicle or vin doesnt not exist")
                    return
                }
                createScrappingStatusUpdateService.createScrappingStatusUpdate(
                    vehicle.id,
                    vehicle.vin,
                )
            }

            else -> {
                val scrappingStatusUpdate = scrappingStatusUpdateFinder.findScrappingStatusUpdateByVehicleId(event.vehicleId)
                if (scrappingStatusUpdate != null) {
                    deleteScrappingStatusUpdateService.deleteScrappingStatusUpdate(scrappingStatusUpdate)
                }
            }
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(ScrappingStatusUpdateApplicationService::class.java)
    }
}
