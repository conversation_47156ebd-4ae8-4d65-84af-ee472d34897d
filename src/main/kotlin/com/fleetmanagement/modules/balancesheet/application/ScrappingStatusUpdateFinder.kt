/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.application

import com.fleetmanagement.modules.balancesheet.domain.ScrappingStatusUpdate
import com.fleetmanagement.modules.balancesheet.domain.ScrappingStatusUpdateRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
@Transactional(readOnly = true)
class ScrappingStatusUpdateFinder(
    private val scrappingStatusUpdateRepository: ScrappingStatusUpdateRepository,
) {
    fun findUnSyncedScrappingStatusUpdate(): List<ScrappingStatusUpdate> = scrappingStatusUpdateRepository.findBySyncedIsFalse()

    fun findScrappingStatusUpdateByVehicleId(id: UUID): ScrappingStatusUpdate? = scrappingStatusUpdateRepository.findByVehicleId(id)
}
