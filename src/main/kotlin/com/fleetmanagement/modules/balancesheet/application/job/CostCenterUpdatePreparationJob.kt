package com.fleetmanagement.modules.balancesheet.application.job

import com.fleetmanagement.modules.balancesheet.application.port.CostCenterUpdatePreparationUseCase
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class CostCenterUpdatePreparationJob(
    private val costCenterUpdatePreparationUseCase: CostCenterUpdatePreparationUseCase,
) : Job {
    override fun execute(context: JobExecutionContext?) {
        log.info("Starting preparation job for updates on p40")
        costCenterUpdatePreparationUseCase.prepareCostCenterUpdates()
        log.info("Finished preparation job for updates on p40")
    }

    companion object {
        private val log = LoggerFactory.getLogger(CostCenterUpdatePreparationJob::class.java)
    }
}
