package com.fleetmanagement.modules.balancesheet.application.job

import com.fleetmanagement.modules.balancesheet.application.port.SyncAllCostCenterUpdatesUseCase
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class CostCenterUpdateSyncJob(
    private val syncAllCostCenterUpdatesUseCase: SyncAllCostCenterUpdatesUseCase,
) : Job {
    override fun execute(context: JobExecutionContext?) {
        log.info("Starting sync all cost center updates to p40")
        syncAllCostCenterUpdatesUseCase.syncAll()
        log.info("Finished sync all cost center updates to p40")
    }

    companion object {
        private val log = LoggerFactory.getLogger(CostCenterUpdateSyncJob::class.java)
    }
}
