/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.application.job

import com.fleetmanagement.modules.balancesheet.application.BlockedForSaleUpdateApplicationService
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.TimeZone

@Component
@DisallowConcurrentExecution
class PrepareBlockedForSaleUpdateJob(
    private val blockedForSaleUpdateApplicationService: BlockedForSaleUpdateApplicationService,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(PrepareBlockedForSaleUpdateJob::class.java)
    }

    override fun execute(p0: JobExecutionContext?) {
        log.info("PrepareBlockedForSaleUpdateJob started")
        blockedForSaleUpdateApplicationService.prepareBlockedForSaleUpdate()
        log.info("PrepareBlockedForSaleUpdateJob finished")
    }
}

@Configuration
class PrepareBlockedForSaleUpdateJobConfig {
    @Bean("prepareBlockedForSaleUpdateJobDetail")
    fun prepareBlockedForSaleUpdateJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(PrepareBlockedForSaleUpdateJob::class.java)
            .withIdentity("PrepareBlockedForSaleUpdateJob")
            .withDescription("PrepareBlockedForSaleUpdate Job")
            .storeDurably()
            .build()

    @Bean("prepareBlockedForSaleUpdateJobTrigger")
    fun prepareBlockedForSaleUpdateJobTrigger(
        @Qualifier("prepareBlockedForSaleUpdateJobDetail") prepareBlockedForSaleUpdateJobDetail: JobDetail,
        @Value("\${balance-sheet.scheduler.prepare-blocked-for-sale-update-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(prepareBlockedForSaleUpdateJobDetail)
            .withIdentity("PrepareBlockedForSaleUpdateJobTrigger")
            .withDescription("PrepareBlockedForSaleUpdateJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
