/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.application.job

import com.fleetmanagement.modules.balancesheet.application.BlockedForSaleUpdateApplicationService
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.TimeZone

@Component
@DisallowConcurrentExecution
class SynchronizeBlockedForSaleUpdateJob(
    private val blockedForSaleUpdateApplicationService: BlockedForSaleUpdateApplicationService,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(SynchronizeBlockedForSaleUpdateJob::class.java)
    }

    override fun execute(p0: JobExecutionContext?) {
        log.info("SynchronizeBlockedForSaleUpdateJob started")
        blockedForSaleUpdateApplicationService.syncBlockedForSaleUpdateToP40()
        log.info("SynchronizeBlockedForSaleUpdateJob finished")
    }
}

@Configuration
class SynchronizeBlockedForSaleUpdateJobConfig {
    @Bean("synchronizeBlockedForSaleUpdateJobDetail")
    fun synchronizeBlockedForSaleUpdateJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(SynchronizeBlockedForSaleUpdateJob::class.java)
            .withIdentity("SynchronizeBlockedForSaleUpdateJob")
            .withDescription("SynchronizeBlockedForSaleUpdate Job")
            .storeDurably()
            .build()

    @Bean("synchronizeBlockedForSaleUpdateJobTrigger")
    fun synchronizeBlockedForSaleUpdateJobTrigger(
        @Qualifier("synchronizeBlockedForSaleUpdateJobDetail") synchronizeBlockedForSaleUpdateJobDetail: JobDetail,
        @Value("\${balance-sheet.scheduler.synchronize-blocked-for-sale-update-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(synchronizeBlockedForSaleUpdateJobDetail)
            .withIdentity("SynchronizeBlockedForSaleUpdateJobTrigger")
            .withDescription("SynchronizeBlockedForSaleUpdateJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
