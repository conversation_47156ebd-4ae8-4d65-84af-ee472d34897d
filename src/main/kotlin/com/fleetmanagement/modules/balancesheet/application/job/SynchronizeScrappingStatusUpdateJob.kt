/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.application.job

import com.fleetmanagement.modules.balancesheet.application.ScrappingStatusUpdateApplicationService
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.TimeZone

@Component
@DisallowConcurrentExecution
class SynchronizeScrappingStatusUpdateJob(
    private val scrappingStatusUpdateApplicationService: ScrappingStatusUpdateApplicationService,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(SynchronizeScrappingStatusUpdateJob::class.java)
    }

    override fun execute(p0: JobExecutionContext?) {
        log.info("SynchronizeScrappingStatusUpdateJob started")
        scrappingStatusUpdateApplicationService.syncScrappingStatusUpdateToP40()
        log.info("SynchronizeScrappingStatusUpdateJob finished")
    }
}

@Configuration
class SynchronizeScrappingStatusUpdateJobConfig {
    @Bean("synchronizeScrappingStatusUpdateJobDetail")
    fun synchronizeScrappingStatusUpdateJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(SynchronizeScrappingStatusUpdateJob::class.java)
            .withIdentity("SynchronizeScrappingStatusUpdateJob")
            .withDescription("SynchronizeScrappingStatusUpdate Job")
            .storeDurably()
            .build()

    @Bean("synchronizeScrappingStatusUpdateJobTrigger")
    fun synchronizeScrappingStatusUpdateJobTrigger(
        @Qualifier("synchronizeScrappingStatusUpdateJobDetail") synchronizeScrappingStatusUpdateJobDetail: JobDetail,
        @Value("\${balance-sheet.scheduler.synchronize-scrapping-status-update-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(synchronizeScrappingStatusUpdateJobDetail)
            .withIdentity("SynchronizeScrappingStatusUpdateJobTrigger")
            .withDescription("SynchronizeScrappingStatusUpdateJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
