package com.fleetmanagement.modules.balancesheet.application.job.configuration

import com.fleetmanagement.modules.balancesheet.application.job.CostCenterUpdatePreparationJob
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.TimeZone

@Configuration
class CostCenterUpdatePreparationJobConfiguration {
    @Bean("costCenterUpdatePreparationJobDetail")
    fun costCenterUpdatePreparationJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(CostCenterUpdatePreparationJob::class.java)
            .withIdentity("CostCenterUpdatePreparationJob")
            .withDescription("create or update costCenterUpdate as preparation for p40 sync job")
            .storeDurably()
            .build()

    @Bean("costCenterUpdatePreparationJobTrigger")
    fun costCenterUpdatePreparationJobTrigger(
        costCenterUpdatePreparationJobDetail: JobDetail,
        @Value("\${balance-sheet.scheduler.cost-center-update-preparation-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(costCenterUpdatePreparationJobDetail)
            .withIdentity("CostCenterUpdatePreparationJobTrigger")
            .withDescription("create or update costCenterUpdate as preparation for p40 sync job trigger")
            .withSchedule(
                CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
