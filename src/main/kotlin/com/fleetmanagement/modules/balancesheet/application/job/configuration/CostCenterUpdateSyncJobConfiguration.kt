package com.fleetmanagement.modules.balancesheet.application.job.configuration

import com.fleetmanagement.modules.balancesheet.application.job.CostCenterUpdateSyncJob
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.TimeZone

@Configuration
class CostCenterUpdateSyncJobConfiguration {
    @Bean("costCenterUpdateSyncJobDetail")
    fun costCenterUpdateSyncJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(CostCenterUpdateSyncJob::class.java)
            .withIdentity("CostCenterUpdateSyncJob")
            .withDescription("cost center update for p40 balance sheet job")
            .storeDurably()
            .build()

    @Bean("costCenterUpdateSyncJobTrigger")
    fun costCenterUpdateSyncJobTrigger(
        costCenterUpdateSyncJobDetail: JobDetail,
        @Value("\${balance-sheet.scheduler.cost-center-update-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(costCenterUpdateSyncJobDetail)
            .withIdentity("CostCenterUpdateSyncJobTrigger")
            .withDescription("cost center update for p40 balance sheet job trigger")
            .withSchedule(
                CronScheduleBuilder.cronSchedule(cron).inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
