/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.balancesheet.application.port

fun interface ScrappingInitiationUseCase {
    fun initiateScrapping(vin: String)
}

class ScrappingInitiationException(
    message: String?,
    cause: Throwable?,
) : RuntimeException(message, cause)
