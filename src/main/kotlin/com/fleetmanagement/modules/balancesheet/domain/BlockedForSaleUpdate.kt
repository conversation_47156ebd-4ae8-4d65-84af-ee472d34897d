/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.balancesheet.domain

import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.LastModifiedBy
import java.io.Serializable
import java.time.OffsetDateTime
import java.util.UUID

@Entity
@Table(name = "blocked_for_sale_update", schema = "balancesheet")
class BlockedForSaleUpdate(
    @Column(name = "vehicle_id") val vehicleId: UUID,
    @Column(name = "vin") val vin: String,
    blockedForSale: Boolean,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id = BlockedForSaleUpdateId()

    @Column(name = "sync_date")
    var syncDate: OffsetDateTime? = null
        private set

    @Column(name = "synced")
    var synced: Boolean = false
        private set

    @Column(name = "blocked_for_sale")
    var blockedForSale = blockedForSale
        private set

    @CreatedBy
    @Column(name = "created_by")
    var createdBy: String = ""

    @CreationTimestamp
    @Column(name = "created_date")
    var created: OffsetDateTime = OffsetDateTime.now()

    @LastModifiedBy
    @Column(name = "last_modified_by")
    var lastModifiedBy: String = ""

    @UpdateTimestamp
    @Column(name = "last_modified_date")
    var lastModified: OffsetDateTime = OffsetDateTime.now()

    fun setSynced(syncDate: OffsetDateTime = OffsetDateTime.now()) {
        this.synced = true
        this.syncDate = syncDate
    }

    fun updateBlockedForSale(blockedForSale: Boolean) {
        this.blockedForSale = blockedForSale
        this.synced = false
        this.syncDate = null
    }
}

@Embeddable
data class BlockedForSaleUpdateId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable
