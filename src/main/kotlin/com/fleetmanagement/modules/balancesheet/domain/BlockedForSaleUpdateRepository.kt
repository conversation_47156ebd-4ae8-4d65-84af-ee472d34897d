package com.fleetmanagement.modules.balancesheet.domain

import org.springframework.data.repository.Repository
import java.util.UUID

interface BlockedForSaleUpdateRepository : Repository<BlockedForSaleUpdate, BlockedForSaleUpdateId> {
    fun save(blockedForSaleUpdate: BlockedForSaleUpdate): BlockedForSaleUpdate

    fun findById(id: BlockedForSaleUpdateId): BlockedForSaleUpdate?

    fun findBySyncDateIsNull(): List<BlockedForSaleUpdate>

    fun findBySyncedIsFalse(): List<BlockedForSaleUpdate>

    fun findByVehicleId(vehicleId: UUID): BlockedForSaleUpdate?
}
