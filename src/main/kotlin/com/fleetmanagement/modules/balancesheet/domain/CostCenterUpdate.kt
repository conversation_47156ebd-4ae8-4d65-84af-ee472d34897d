/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.balancesheet.domain

import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.LastModifiedBy
import java.io.Serializable
import java.time.OffsetDateTime
import java.util.UUID

@Entity
@Table(name = "cost_center_update", schema = "balancesheet")
class CostCenterUpdate(
    @Column(name = "vehicle_id") val vehicleId: UUID,
    @Column(name = "vin") val vin: String,
    @Column(name = "internal_order_number") val internalOrderNumber: String?,
    depreciationRelevantCostCenter: CostCenterDescription?,
    usingCostCenter: CostCenterDescription?,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: CostCenterUpdateId = CostCenterUpdateId()

    @Column(name = "sync_date")
    var syncDate: OffsetDateTime? = null
        private set

    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "depreciation_relevant_cost_center"))
    var depreciationRelevantCostCenter: CostCenterDescription? = depreciationRelevantCostCenter
        private set

    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "using_cost_center"))
    var usingCostCenter: CostCenterDescription? = usingCostCenter
        private set

    @CreatedBy
    @Column(name = "created_by")
    var createdBy: String = ""

    @CreationTimestamp
    @Column(name = "created_date")
    var created: OffsetDateTime = OffsetDateTime.now()

    @LastModifiedBy
    @Column(name = "last_modified_by")
    var lastModifiedBy: String = ""

    @UpdateTimestamp
    @Column(name = "last_modified_date")
    var lastModified: OffsetDateTime = OffsetDateTime.now()

    fun setSynced(syncDate: OffsetDateTime = OffsetDateTime.now()) {
        this.syncDate = syncDate
    }

    fun resetSynced() {
        this.syncDate = null
    }

    fun update(
        depreciationRelevantCostCenter: CostCenterDescription?,
        usingCostCenter: CostCenterDescription?,
    ) {
        var needsSync = false
        if (this.depreciationRelevantCostCenter?.value != depreciationRelevantCostCenter?.value) {
            this.depreciationRelevantCostCenter = depreciationRelevantCostCenter
            needsSync = true
        }
        if (this.usingCostCenter?.value != usingCostCenter?.value) {
            this.usingCostCenter = usingCostCenter
            needsSync = true
        }
        if (needsSync) {
            syncDate = null
        }
    }
}

@Embeddable
data class CostCenterUpdateId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable
