/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.balancesheet.domain

import org.springframework.data.repository.Repository
import java.util.UUID

interface CostCenterUpdateRepository : Repository<CostCenterUpdate, CostCenterUpdateId> {
    fun save(vehicleTransfer: CostCenterUpdate): CostCenterUpdate

    fun findById(id: CostCenterUpdateId): CostCenterUpdate?

    fun findByVin(vin: String): CostCenterUpdate?

    fun findByVehicleId(vehicleId: UUID): CostCenterUpdate?

    fun findBySyncDateIsNull(): List<CostCenterUpdate>
}
