package com.fleetmanagement.modules.balancesheet.domain

import org.springframework.data.repository.Repository
import java.util.UUID

interface ScrappingStatusUpdateRepository : Repository<ScrappingStatusUpdate, ScrappingStatusUpdateId> {
    fun save(scrappingStatusUpdate: ScrappingStatusUpdate): ScrappingStatusUpdate

    fun findById(id: ScrappingStatusUpdateId): ScrappingStatusUpdate?

    fun findBySyncDateIsNull(): List<ScrappingStatusUpdate>

    fun findBySyncedIsFalse(): List<ScrappingStatusUpdate>

    fun findByVehicleId(id: UUID): ScrappingStatusUpdate?

    fun deleteById(id: ScrappingStatusUpdateId)
}
