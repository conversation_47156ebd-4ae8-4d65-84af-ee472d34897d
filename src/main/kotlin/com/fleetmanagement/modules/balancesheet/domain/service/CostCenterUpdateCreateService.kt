package com.fleetmanagement.modules.balancesheet.domain.service

import com.fleetmanagement.modules.balancesheet.domain.CostCenterUpdate
import com.fleetmanagement.modules.balancesheet.domain.CostCenterUpdateRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class CostCenterUpdateCreateService(
    private val costCenterUpdateRepository: CostCenterUpdateRepository,
) {
    fun createCostCenterUpdate(costCenterUpdate: CostCenterUpdate): CostCenterUpdate = costCenterUpdateRepository.save(costCenterUpdate)
}
