package com.fleetmanagement.modules.balancesheet.domain.service

import com.fleetmanagement.modules.balancesheet.domain.CostCenterUpdate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime

@Service
@Transactional(propagation = Propagation.MANDATORY)
class CostCenterUpdateSyncService {
    fun syncCostCenterUpdate(
        costCenterUpdate: CostCenterUpdate,
        syncDate: OffsetDateTime,
    ) {
        costCenterUpdate.setSynced(syncDate)
    }
}
