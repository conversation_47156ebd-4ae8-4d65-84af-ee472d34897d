package com.fleetmanagement.modules.balancesheet.domain.service

import com.fleetmanagement.modules.balancesheet.domain.CostCenterUpdate
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class CostCenterUpdateUpdateService {
    fun updateCostCenterUpdate(
        costCenterUpdate: CostCenterUpdate,
        costCenterUpdateUpdate: CostCenterUpdateUpdate,
    ) {
        costCenterUpdate.update(
            costCenterUpdateUpdate.depreciationRelevantCostCenter,
            costCenterUpdateUpdate.usingCostCenter,
        )
    }
}

data class CostCenterUpdateUpdate(
    val depreciationRelevantCostCenter: CostCenterDescription?,
    val usingCostCenter: CostCenterDescription?,
)
