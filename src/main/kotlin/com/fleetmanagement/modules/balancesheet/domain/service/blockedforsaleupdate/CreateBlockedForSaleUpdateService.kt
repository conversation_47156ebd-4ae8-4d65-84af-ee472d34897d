/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.domain.service.blockedforsaleupdate

import com.fleetmanagement.modules.balancesheet.domain.BlockedForSaleUpdate
import com.fleetmanagement.modules.balancesheet.domain.BlockedForSaleUpdateRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(propagation = Propagation.MANDATORY)
class CreateBlockedForSaleUpdateService(
    private val blockedForSaleUpdateRepository: BlockedForSaleUpdateRepository,
) {
    fun createBlockedForSaleUpdate(
        vehicleId: UUID,
        vin: String,
        blockedForSale: Boolean,
    ) {
        blockedForSaleUpdateRepository.save(
            BlockedForSaleUpdate(
                vehicleId = vehicleId,
                vin = vin,
                blockedForSale = blockedForSale,
            ),
        )
    }
}
