/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.domain.service.blockedforsaleupdate

import com.fleetmanagement.modules.balancesheet.domain.BlockedForSaleUpdate
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class UpdateBlockedForSaleUpdateService {
    fun syncBlockedForSaleUpdate(blockedForSaleUpdate: BlockedForSaleUpdate) {
        blockedForSaleUpdate.setSynced()
    }

    fun updateBlockedForSale(
        blockedForSale: <PERSON>olean,
        blockedForSaleUpdate: BlockedForSaleUpdate,
    ) {
        blockedForSaleUpdate.updateBlockedForSale(blockedForSale = blockedForSale)
    }
}
