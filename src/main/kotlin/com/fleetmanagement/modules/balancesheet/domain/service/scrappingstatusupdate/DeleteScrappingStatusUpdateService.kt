/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.balancesheet.domain.service.scrappingstatusupdate

import com.fleetmanagement.modules.balancesheet.domain.ScrappingStatusUpdate
import com.fleetmanagement.modules.balancesheet.domain.ScrappingStatusUpdateRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class DeleteScrappingStatusUpdateService(
    private val scrappingStatusUpdateRepository: ScrappingStatusUpdateRepository,
) {
    fun deleteScrappingStatusUpdate(scrappingStatusUpdate: ScrappingStatusUpdate) {
        scrappingStatusUpdateRepository.deleteById(scrappingStatusUpdate.id)
    }
}
