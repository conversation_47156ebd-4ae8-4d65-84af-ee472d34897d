<h1>CarSync Integration - Vehicle Service</h1>

<p>This document describes how to configure and authenticate with the CarSync API to request vehicle-related data, such as trip invoices.</p>

<hr>

<h2>🔐 Certificate Setup</h2>

<p>CarSync provides a <code>.pfx</code> certificate file which is required for authenticating API requests.</p>

<h3>Steps:</h3>

<ol>
  <li>
    <strong>Extract Key and Certificate from <code>.pfx</code></strong>
    <pre><code>openssl pkcs12 -in carsync.pfx -nocerts -out carsync_key.key -nodes
openssl pkcs12 -in carsync.pfx -clcerts -nokeys -out carsync_cert.cert</code></pre>
  </li>

  <li>
    <strong>Convert to Base64</strong>
    <pre><code>base64 carsync_cert.cert > carsync_cert.b64
base64 carsync_key.key > carsync_key.b64</code></pre>
  </li>

  <li>
    <strong>Store in AWS Parameter Store</strong>
    <pre><code>aws ssm put-parameter \
  --name "/vehicle-service/external/carsync/carsync_cert" \
  --type "String" \
  --value "$(cat carsync_cert.b64)" \
  --overwrite

aws ssm put-parameter \
--name "/vehicle-service/external/carsync/carsync_private_key" \
--type "String" \
--value "$(cat carsync_key.b64)" \
--overwrite</code></pre>
  </li>
</ol>

<hr>

<h2>🔄 Authentication Flow</h2>

<ol>
  <li><strong>Request a Trip Invoice</strong><br>
      A request is made to retrieve a trip invoice.
  </li>

  <li><strong>Check for Session Cookie</strong><br>
      If no valid session cookie (<code>PHPSESSID</code>) is present:
    <ul>
      <li>A request is made to the CarSync auth endpoint.</li>
      <li>The <code>PHPSESSID</code> is extracted from the <code>Set-Cookie</code> header in the response.</li>
      <li>This cookie is stored and attached to all future requests in the <code>Cookie</code> header.</li>
    </ul>
  </li>

  <li><strong>Attach Certificate to Requests</strong><br>
      Every HTTPS request must include the client certificate and private key (mTLS) retrieved from AWS Parameter Store.
  </li>
</ol>

<hr>

<h2>🛠 Notes</h2>

<ul>
  <li>Ensure the application has IAM permissions to read from AWS Parameter Store.</li>
  <li>Never hardcode or commit certificate or key values to version control.</li>
  <li>The <code>PHPSESSID</code> cookie should be refreshed automatically when expired.</li>
    <li>The certificate is protected by password, ask your PO</li>
    <li>According to carsync the IP should be whitelisted, although we sent our service (NAT) ip I could also send request(prod, stg) from my local machine</li>

</ul>

<hr>

<h2>📎 Required Parameters in AWS SSM</h2>

<table>
  <thead>
    <tr>
      <th>Key Name</th>
      <th>Description</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td><code>/vehicle-service/external/carsync/carsync_cert</code></td>
      <td>Base64-encoded certificate</td>
    </tr>
    <tr>
      <td><code>/vehicle-service/external/carsync/carsync_private_key</code></td>
      <td>Base64-encoded private key</td>
    </tr>
  </tbody>
</table>
