package com.fleetmanagement.modules.carsync.adapter.out

import com.fleetmanagement.modules.carsync.application.TripDto
import com.fleetmanagement.modules.carsync.application.port.ReadTripsInvocieException
import com.fleetmanagement.modules.carsync.application.port.ReadTripsInvoiceUseCase
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@Configuration
@ConditionalOnProperty(name = ["carsync.enabled"], havingValue = "true")
class Carsync

@Component
@ConditionalOnBean(Carsync::class)
class CarsyncTripInvoiceAdapter(
    private val carsyncWebClient: CarsyncWebClient,
) : ReadTripsInvoiceUseCase {
    override fun readTripInvoice(
        vin: String,
        startTime: OffsetDateTime,
        endTime: OffsetDateTime,
    ): TripDto? {
        try {
            val trip =
                carsyncWebClient
                    .getTripInvoiceForVinPerTime(
                        vin = vin,
                        startTime = startTime.toLocalDate().toString(),
                        endTime = endTime.toLocalDate().toString(),
                    ).firstOrNull()
            return trip?.let {
                TripDto(
                    endKm = it.endKm,
                    vin = it.vin,
                )
            }
        } catch (e: CarsyncException) {
            throw ReadTripsInvocieException(
                message = "error while trying to fetch trip invoice for $vin",
                cause = e,
            )
        }
    }

    override fun readTripsInvoice(
        vins: List<String>,
        startTime: OffsetDateTime,
        endTime: OffsetDateTime,
    ): List<TripDto> {
        try {
            val trips =
                carsyncWebClient.getTripsInvoice(
                    vin = vins,
                    startTime = startTime.toLocalDate().toString(),
                    endTime = endTime.toLocalDate().toString(),
                )
            return trips.map {
                TripDto(
                    endKm = it.endKm,
                    vin = it.vin,
                )
            }
        } catch (e: CarsyncException) {
            throw ReadTripsInvocieException(
                message = "error while trying to fetch trip invoice for $vins",
                cause = e,
            )
        }
    }
}
