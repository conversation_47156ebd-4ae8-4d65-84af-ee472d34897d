package com.fleetmanagement.modules.carsync.adapter.out

import com.fleetmanagement.modules.carsync.generated.adapter.out.rest.model.TripInvoiceDto
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange

interface CarsyncWebClient {
    @GetExchange(
        url = "/tripinvoice",
        accept = ["application/json"],
    )
    fun getTripInvoiceForVinPerTime(
        @RequestParam("vin") vin: String,
        @RequestParam("startTime") startTime: String,
        @RequestParam("endTime") endTime: String,
    ): List<TripInvoiceDto>

    @GetExchange(
        url = "/tripinvoice",
        accept = ["application/json"],
    )
    fun getTripsInvoice(
        @RequestParam("vin") vin: List<String>,
        @RequestParam("startTime") startTime: String,
        @RequestParam("endTime") endTime: String,
    ): List<TripInvoiceDto>
}
