package com.fleetmanagement.modules.carsync.adapter.out

import org.slf4j.LoggerFactory
import org.springframework.security.oauth2.client.ClientAuthorizationException
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.ExchangeFunction
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono
import java.net.ConnectException

@Component
class CarsyncWebClientExceptionHandler {
    fun clientErrorRequestProcessor(
        request: ClientRequest,
        next: ExchangeFunction,
    ): Mono<ClientResponse> =
        next.exchange(request).onErrorMap {
            when (it.cause) {
                is ConnectException -> {
                    log.error("Error during request. Target not reachable. ${it.message}", it.cause)
                    CarsyncUnreachableException("Error during request. Target not reachable. ${it.message}", it.cause)
                }
                is ClientAuthorizationException -> {
                    log.error("Error during authorization.${it.message}", it.cause)
                    CarsyncException("Error during authorization.${it.message}", it.cause)
                }
                is WebClientResponseException -> {
                    log.error("Error during Carsync request. ${it.message}", it.cause)
                    CarsyncException("Error during Carsync request. ${it.message}", it.cause)
                }
                else -> it
            }
        }

    fun clientErrorResponseProcessor(clientResponse: ClientResponse): Mono<ClientResponse> {
        val statusCode = clientResponse.statusCode()
        return when {
            statusCode.isError -> {
                clientResponse.bodyToMono(String::class.java).flatMap {
                    log.error("Error during Carsync request. Status Code: $statusCode, message: $it")
                    Mono.error(CarsyncException(it))
                }
            }
            else -> Mono.just(clientResponse)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(CarsyncWebClientExceptionHandler::class.java)
    }
}
