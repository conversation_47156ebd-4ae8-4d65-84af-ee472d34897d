package com.fleetmanagement.modules.carsync.adapter.out.config

import com.fleetmanagement.modules.carsync.adapter.out.Carsync
import com.fleetmanagement.modules.carsync.adapter.out.CarsyncWebClient
import com.fleetmanagement.modules.carsync.adapter.out.CarsyncWebClientExceptionHandler
import com.fleetmanagement.modules.carsync.generated.adapter.out.rest.model.ApiAuthLoginPostRequestDto
import io.netty.handler.ssl.SslContext
import io.netty.handler.ssl.SslContextBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.http.client.reactive.ReactorClientHttpConnector
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.support.WebClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory
import reactor.core.publisher.Mono
import reactor.netty.http.client.HttpClient
import java.io.ByteArrayInputStream
import java.util.*
import java.util.concurrent.atomic.AtomicReference

@ConfigurationProperties("carsync")
class CarsyncClientProperties(
    val baseUrl: String,
    val certificate: CarsyncCertificateProperties,
    val username: String,
    val password: String,
)

class CarsyncCertificateProperties(
    val keystore: String,
    val truststore: String,
)

@Configuration
@ConditionalOnBean(Carsync::class)
class CarsyncWebClientConfiguration(
    private val properties: CarsyncClientProperties,
    private val carsyncClientExceptionHandler: CarsyncWebClientExceptionHandler,
) {
    private val sessionCookieRef = AtomicReference<String?>()

    @Bean
    @Qualifier("carsync")
    fun buildSSLContext(): SslContext =
        SslContextBuilder
            .forClient()
            .keyManager(
                ByteArrayInputStream(
                    Base64.getDecoder().decode(
                        properties.certificate.truststore,
                    ),
                ),
                ByteArrayInputStream(
                    Base64.getDecoder().decode(
                        properties.certificate.keystore,
                    ),
                ),
            ).build()

    @Bean
    @Qualifier("carsync-base")
    fun carsyncBaseWebClient(
        @Qualifier("carsync") sslContext: SslContext,
    ): WebClient =
        WebClient
            .builder()
            .baseUrl(properties.baseUrl)
            .clientConnector(ReactorClientHttpConnector(HttpClient.create().secure { it.sslContext(sslContext) }))
            .filter(ExchangeFilterFunction.ofResponseProcessor(carsyncClientExceptionHandler::clientErrorResponseProcessor))
            .filter(carsyncClientExceptionHandler::clientErrorRequestProcessor)
            .build()

    private fun fetchSessionCookie(baseClient: WebClient): String =
        baseClient
            .post()
            .uri("auth/login")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(ApiAuthLoginPostRequestDto(username = properties.username, password = properties.password))
            .exchangeToMono { response ->
                val setCookieHeader = response.headers().asHttpHeaders().getFirst("Set-Cookie")
                if (setCookieHeader == null) {
                    Mono.error(IllegalStateException("No Set-Cookie header in auth response"))
                } else {
                    val cookie = setCookieHeader.split(";")[0].trim()
                    Mono.just(cookie)
                }
            }.block() ?: throw IllegalStateException("Failed to retrieve auth cookie")

    private fun authorizationAndRetryFilter(
        @Qualifier("carsync-base") baseClient: WebClient,
    ): ExchangeFilterFunction =
        ExchangeFilterFunction
            .ofRequestProcessor { originalRequest ->
                val cookie = sessionCookieRef.get() ?: fetchSessionCookie(baseClient).also { sessionCookieRef.set(it) }

                val modifiedRequest =
                    ClientRequest
                        .from(originalRequest)
                        .header("Cookie", cookie)
                        .build()

                Mono.just(modifiedRequest)
            }.andThen { request, next ->
                next
                    .exchange(request)
                    .flatMap { response ->
                        if (response.statusCode().value() == 401) {
                            log.info("carsync sent 401 trying to re-fetch the session id (cookie")
                            val refreshedCookie = fetchSessionCookie(baseClient)
                            sessionCookieRef.set(refreshedCookie)
                        }
                        Mono.just(response)
                    }.retry(1)
            }

    @Bean
    @Qualifier("carsync")
    fun carsyncWebClient(
        @Qualifier("carsync-base") baseClient: WebClient,
    ): WebClient {
        val credentials = "${properties.username}:${properties.password}"
        val encodedCredentials = Base64.getEncoder().encodeToString(credentials.toByteArray())

        return baseClient
            .mutate()
            .defaultHeader("Authorization", "Basic $encodedCredentials")
            .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
            .filter(authorizationAndRetryFilter(baseClient)) // add the cookie filter
            .build()
    }

    @Bean
    @Qualifier("carsync")
    fun carsyncProxyFactory(
        @Qualifier("carsync") webClient: WebClient,
    ): HttpServiceProxyFactory {
        val adapter = WebClientAdapter.create(webClient)
        return HttpServiceProxyFactory.builderFor(adapter).build()
    }

    @Bean
    @Qualifier("carsync")
    fun getCarSyncWebClient(
        @Qualifier("carsync") carsyncServiceProxyFactory: HttpServiceProxyFactory,
    ): CarsyncWebClient = carsyncServiceProxyFactory.createClient(CarsyncWebClient::class.java)

    companion object {
        private val log = LoggerFactory.getLogger(CarsyncWebClientConfiguration::class.java)
    }
}
