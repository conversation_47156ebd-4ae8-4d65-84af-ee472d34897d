package com.fleetmanagement.modules.carsync.application.port

import com.fleetmanagement.modules.carsync.application.TripDto
import java.time.OffsetDateTime

interface ReadTripsInvoiceUseCase {
    // read invoice for one vin for a given time frame
    fun readTripInvoice(
        vin: String,
        startTime: OffsetDateTime,
        endTime: OffsetDateTime,
    ): TripDto?

    // read invoices for many vins for a given time frame
    fun readTripsInvoice(
        vins: List<String>,
        startTime: OffsetDateTime,
        endTime: OffsetDateTime,
    ): List<TripDto>
}

class ReadTripsInvocieException(
    message: String?,
    cause: Throwable?,
) : RuntimeException(message, cause)
