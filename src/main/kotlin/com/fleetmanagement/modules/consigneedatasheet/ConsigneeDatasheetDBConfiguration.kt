/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet

import liquibase.integration.spring.SpringLiquibase
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import javax.sql.DataSource

@Configuration
class ConsigneeDatasheetDBConfiguration {
    @Bean
    fun consigneeDatasheetLiquibase(datasource: DataSource): SpringLiquibase {
        val liquibase = SpringLiquibase()
        liquibase.changeLog = "classpath:/db/changelog/db.changelog-consigneedatasheet.yml"
        liquibase.liquibaseSchema = "consigneedatasheet"
        liquibase.dataSource = datasource
        return liquibase
    }
}
