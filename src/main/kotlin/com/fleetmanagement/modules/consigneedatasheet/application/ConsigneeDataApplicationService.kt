/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.CreateConsigneeDataUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.DeleteConsigneeDataUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.UpdateConsigneeDataUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeDataId
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupId
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import com.fleetmanagement.modules.consigneedatasheet.domain.service.consigneedata.ConsigneeDataCreateService
import com.fleetmanagement.modules.consigneedatasheet.domain.service.consigneedata.ConsigneeDataDeleteService
import com.fleetmanagement.modules.consigneedatasheet.domain.service.consigneedata.ConsigneeDataUpdateService
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
@Transactional
class ConsigneeDataApplicationService(
    private val consigneeDataCreateService: ConsigneeDataCreateService,
    private val consigneeDataDeleteService: ConsigneeDataDeleteService,
    private val consigneeDataUpdateService: ConsigneeDataUpdateService,
    private val consigneeDataFinder: ConsigneeDataFinder,
    private val costCenterFinder: CostCenterFinder,
    private val usageGroupFinder: UsageGroupFinder,
    private val vehicleUsageFinder: VehicleUsageFinder,
) : CreateConsigneeDataUseCase,
    DeleteConsigneeDataUseCase,
    UpdateConsigneeDataUseCase {
    override fun createConsigneeData(consigneeDataNew: ConsigneeDataNewOrUpdate): ConsigneeDataDto {
        val vehicleUsage =
            try {
                consigneeDataNew.vehicleUsageId?.let {
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(it))
                }
            } catch (exception: VehicleUsageNotFoundException) {
                throw CreateConsigneeDataException(
                    message = "Error while trying to create ConsigneeData ${exception.message}",
                    exception,
                )
            }
        val depreciationRelevantCostCenter =
            try {
                consigneeDataNew.depreciationRelevantCostCenterId?.let {
                    costCenterFinder.getCostCenter(CostCenterId(it))
                }
            } catch (exception: CostCenterNotFoundException) {
                throw CreateConsigneeDataException(
                    message = "Error while trying to create ConsigneeData ${exception.message}",
                    exception,
                )
            }
        val usageGroup =
            try {
                consigneeDataNew.usageGroupId?.let {
                    usageGroupFinder.getUsageGroup(UsageGroupId(it))
                }
            } catch (exception: UsageGroupNotFoundException) {
                throw CreateConsigneeDataException(
                    message = "Error while trying to create ConsigneeData ${exception.message}",
                    exception,
                )
            }

        val savedConsigneeData =
            try {
                consigneeDataCreateService.createConsigneeData(consigneeDataNew.toConsigneeData())
            } catch (exception: IllegalArgumentException) {
                throw CreateConsigneeDataException(
                    message = "Error while trying to create ConsigneeData ${exception.message}",
                    exception,
                )
            }

        return savedConsigneeData.toConsigneeDataDto(
            usageGroup = usageGroup,
            vehicleUsage = vehicleUsage,
            depreciationRelevantCostCenter = depreciationRelevantCostCenter,
        )
    }

    override fun deleteConsigneeData(id: UUID) {
        try {
            consigneeDataDeleteService.deleteConsigneeData(id)
        } catch (exception: EmptyResultDataAccessException) {
            throw ConsigneeDataNotFoundException(
                id = id,
                message = "Could not find ConsigneeData with id [$id].",
            )
        }
    }

    override fun updateConsigneeData(
        id: UUID,
        consigneeDataUpdate: ConsigneeDataNewOrUpdate,
        version: Int,
    ): ConsigneeDataDto {
        val consigneeData = consigneeDataFinder.getConsigneeData(ConsigneeDataId(id))

        // this has to be generalized once proper optimistic locking procedures are in place
        if (version != consigneeData.version) {
            throw DataIntegrityViolationException(
                "Updating of consigneeData with id [$id] failed. Invalid version supplied.",
            )
        }
        val vehicleUsage =
            try {
                consigneeDataUpdate.vehicleUsageId?.let {
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(it))
                }
            } catch (exception: VehicleUsageNotFoundException) {
                throw UpdateConsigneeDataException(
                    message = "Error while trying to update ConsigneeData. ${exception.message}",
                    exception,
                )
            }
        val depreciationRelevantCostCenter =
            try {
                consigneeDataUpdate.depreciationRelevantCostCenterId?.let {
                    costCenterFinder.getCostCenter(CostCenterId(it))
                }
            } catch (exception: CostCenterNotFoundException) {
                throw UpdateConsigneeDataException(
                    message = "Error while trying to update ConsigneeData. ${exception.message}",
                    exception,
                )
            }
        val usageGroup =
            try {
                consigneeDataUpdate.usageGroupId?.let {
                    usageGroupFinder.getUsageGroup(UsageGroupId(it))
                }
            } catch (exception: UsageGroupNotFoundException) {
                throw UpdateConsigneeDataException(
                    message = "Error while trying to update ConsigneeData. ${exception.message}",
                    exception,
                )
            }

        val updatedConsigneeData =
            try {
                consigneeDataUpdateService.updateConsigneeData(
                    consigneeData,
                    consigneeDataUpdate.toConsigneeDataUpdate(),
                )
            } catch (exception: IllegalArgumentException) {
                throw UpdateConsigneeDataException(
                    message = "Error while trying to update ConsigneeData. ${exception.message}",
                    exception,
                )
            }

        return updatedConsigneeData.toConsigneeDataDto(
            vehicleUsage = vehicleUsage,
            depreciationRelevantCostCenter = depreciationRelevantCostCenter,
            usageGroup = usageGroup,
        )
    }
}

class CreateConsigneeDataException(
    override val message: String?,
    override val cause: Throwable?,
) : RuntimeException()

class UpdateConsigneeDataException(
    override val message: String?,
    override val cause: Throwable?,
) : RuntimeException()
