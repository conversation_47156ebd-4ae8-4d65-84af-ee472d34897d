/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeData
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenter
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.consigneedatasheet.domain.TireSet
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroup
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupId
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsage
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import com.fleetmanagement.modules.consigneedatasheet.domain.service.consigneedata.ConsigneeDataUpdate
import java.util.UUID

data class ConsigneeDataDto(
    val id: UUID,
    val version: Int,
    val consignee: String,
    val description: String,
    val maximumServiceLifeInMonths: Int?,
    val vehicleUsageDto: VehicleUsageDto?,
    val vehicleResponsiblePerson: String?,
    val internalContactPerson: String?,
    val isPreproductionVehicle: Boolean,
    val isBlockedForSale: Boolean,
    val isScrapped: Boolean,
    val depreciationRelevantCostCenter: CostCenterDto?,
    val usingCostCenter: String?,
    val internalOrderNumber: String?,
    val validationOfLeasingPrivileges: String?,
    val usageGroupDto: UsageGroupDto?,
    val vehicleType: VehicleType?,
    val manufacturer: String?,
    val pdiCurrentTireSet: TireSet?,
    val pdiRelevant: Boolean?,
    val pdiFoiling: Boolean?,
    val pdiRefuel: Boolean?,
    val pdiRecharge: Boolean?,
    val pdiDigitalLogbook: Boolean?,
    val pdiLicensePlateMounting: Boolean?,
)

data class ConsigneeDataNewOrUpdate(
    val consignee: String,
    val description: String,
    val maximumServiceLifeInMonths: Int?,
    /** [VehicleUsage.id] not to be confused with [VehicleUsage.usageId] */
    val vehicleUsageId: UUID?,
    val vehicleResponsiblePerson: String?,
    val internalContactPerson: String?,
    val isPreproductionVehicle: Boolean,
    val isBlockedForSale: Boolean,
    val isScrapped: Boolean,
    /** [CostCenter.id] not to be confused with [CostCenter.costCenterId] */
    val depreciationRelevantCostCenterId: UUID?,
    val usingCostCenter: String?,
    val internalOrderNumber: String?,
    val validationOfLeasingPrivileges: String?,
    /** [UsageGroup.id] not to be confused with [UsageGroup.usageGroupId] */
    val usageGroupId: UUID?,
    val vehicleType: VehicleType?,
    val manufacturer: String?,
    val pdiCurrentTireSet: TireSet?,
    val pdiRelevant: Boolean?,
    val pdiFoiling: Boolean?,
    val pdiRefuel: Boolean?,
    val pdiRecharge: Boolean?,
    val pdiDigitalLogbook: Boolean?,
    val pdiLicensePlateMounting: Boolean?,
)

class ConsigneeDataNotFoundException(
    val id: UUID,
    override val message: String,
    override val cause: Throwable? = null,
) : NoSuchElementException()

fun ConsigneeDataNewOrUpdate.toConsigneeData(): ConsigneeData =
    ConsigneeData(
        consignee = this.consignee,
        description = this.description,
        maximumServiceLifeInMonths = this.maximumServiceLifeInMonths,
        vehicleUsageId = this.vehicleUsageId?.let { VehicleUsageId(it) },
        vehicleResponsiblePerson = this.vehicleResponsiblePerson?.let { EmployeeNumber(it) },
        internalContactPerson = this.internalContactPerson?.let { EmployeeNumber(it) },
        isPreproductionVehicle = this.isPreproductionVehicle,
        isBlockedForSale = this.isBlockedForSale,
        isScrapped = this.isScrapped,
        depreciationRelevantCostCenterId = this.depreciationRelevantCostCenterId?.let { CostCenterId(it) },
        usingCostCenter = this.usingCostCenter?.let { CostCenterDescription(it) },
        internalOrderNumber = this.internalOrderNumber,
        validationOfLeasingPrivileges = validationOfLeasingPrivileges,
        usageGroupId = this.usageGroupId?.let { UsageGroupId(it) },
        vehicleType = this.vehicleType,
        manufacturer = this.manufacturer,
        pdiCurrentTireSet = this.pdiCurrentTireSet,
        pdiRelevant = this.pdiRelevant,
        pdiRefuel = this.pdiRefuel,
        pdiRecharge = this.pdiRecharge,
        pdiFoiling = this.pdiFoiling,
        pdiDigitalLogbook = this.pdiDigitalLogbook,
        pdiLicensePlateMounting = this.pdiLicensePlateMounting,
    )

fun ConsigneeDataNewOrUpdate.toConsigneeDataUpdate(): ConsigneeDataUpdate =
    ConsigneeDataUpdate(
        consignee = consignee,
        description = description,
        maximumServiceLifeInMonths = maximumServiceLifeInMonths,
        vehicleUsageId = vehicleUsageId?.let { VehicleUsageId(it) },
        vehicleResponsiblePerson = vehicleResponsiblePerson?.let { EmployeeNumber(it) },
        internalContactPerson = internalContactPerson?.let { EmployeeNumber(it) },
        isPreproductionVehicle = isPreproductionVehicle,
        isBlockedForSale = isBlockedForSale,
        isScrapped = isScrapped,
        depreciationRelevantCostCenterId = depreciationRelevantCostCenterId?.let { CostCenterId(it) },
        usingCostCenter = usingCostCenter?.let { CostCenterDescription(it) },
        internalOrderNumber = internalOrderNumber,
        validationOfLeasingPrivileges = validationOfLeasingPrivileges,
        usageGroupId = usageGroupId?.let { UsageGroupId(it) },
        vehicleType = vehicleType,
        manufacturer = manufacturer,
        pdiCurrentTireSet = this.pdiCurrentTireSet,
        pdiRelevant = this.pdiRelevant,
        pdiFoiling = this.pdiFoiling,
        pdiRefuel = this.pdiRefuel,
        pdiRecharge = this.pdiRecharge,
        pdiDigitalLogbook = this.pdiDigitalLogbook,
        pdiLicensePlateMounting = this.pdiLicensePlateMounting,
    )

fun ConsigneeData.toConsigneeDataDto(
    depreciationRelevantCostCenter: CostCenter?,
    usageGroup: UsageGroup?,
    vehicleUsage: VehicleUsage?,
): ConsigneeDataDto =
    ConsigneeDataDto(
        id = this.id.value,
        version = this.version,
        consignee = this.consignee,
        description = this.description,
        maximumServiceLifeInMonths = this.maximumServiceLifeInMonths,
        vehicleUsageDto = vehicleUsage?.toVehicleUsageDto(),
        vehicleResponsiblePerson = this.vehicleResponsiblePerson?.value,
        internalContactPerson = this.internalContactPerson?.value,
        isPreproductionVehicle = this.isPreproductionVehicle,
        isBlockedForSale = this.isBlockedForSale,
        isScrapped = this.isScrapped,
        depreciationRelevantCostCenter = depreciationRelevantCostCenter?.toCostCenterDto(),
        usingCostCenter = this.usingCostCenter?.value,
        internalOrderNumber = this.internalOrderNumber,
        validationOfLeasingPrivileges = this.validationOfLeasingPrivileges,
        usageGroupDto = usageGroup?.toUsageGroupDto(),
        vehicleType = vehicleType,
        manufacturer = this.manufacturer,
        pdiCurrentTireSet = this.preDeliveryInspection.currentTireSet,
        pdiRelevant = this.preDeliveryInspection.isRelevant,
        pdiRefuel = this.preDeliveryInspection.refuel,
        pdiRecharge = this.preDeliveryInspection.recharge,
        pdiFoiling = this.preDeliveryInspection.foiling,
        pdiDigitalLogbook = this.preDeliveryInspection.digitalLogbook,
        pdiLicensePlateMounting = this.preDeliveryInspection.licensePlateMounting,
    )
