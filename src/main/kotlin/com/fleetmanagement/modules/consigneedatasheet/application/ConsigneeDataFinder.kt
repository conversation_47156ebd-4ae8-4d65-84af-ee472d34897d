/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.CalculateUsageGroupUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.ReadConsigneeDataUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.ReadLeasingPrivilegeUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeData
import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeDataId
import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeDataRepository
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupId
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional(readOnly = true)
class ConsigneeDataFinder(
    private val consigneeDataRepository: ConsigneeDataRepository,
    private val costCenterFinder: CostCenterFinder,
    private val usageGroupFinder: UsageGroupFinder,
    private val vehicleUsageFinder: VehicleUsageFinder,
) : ReadConsigneeDataUseCase,
    ReadLeasingPrivilegeUseCase,
    CalculateUsageGroupUseCase {
    fun getConsigneeData(id: ConsigneeDataId): ConsigneeData =
        consigneeDataRepository.findById(id) ?: throw ConsigneeDataNotFoundException(
            id = id.value,
            message = "Could not find ConsigneeData with id [${id.value}].",
        )

    fun findConsigneeDataByConsignee(consignee: String): List<ConsigneeData> = consigneeDataRepository.findByConsignee(consignee)

    fun getAllConsigneeData(): List<ConsigneeData> = consigneeDataRepository.findAllByOrderByConsigneeAscDescriptionAsc()

    override fun readAllConsigneeData(): List<ConsigneeDataDto> {
        val costCenters = costCenterFinder.getAllCostCenter()
        val usageGroups = usageGroupFinder.getAllUsageGroups()
        val vehicleUsages = vehicleUsageFinder.getAllVehicleUsages()

        return getAllConsigneeData().map { consigneeData ->
            consigneeData.toConsigneeDataDto(
                depreciationRelevantCostCenter =
                    consigneeData.depreciationRelevantCostCenterId?.let { depreciationRelevantCostCenterId ->
                        costCenters.single {
                            depreciationRelevantCostCenterId ==
                                it.id
                        }
                    },
                usageGroup = consigneeData.usageGroupId?.let { usageGroupId -> usageGroups.single { usageGroupId == it.id } },
                vehicleUsage = consigneeData.vehicleUsageId?.let { vehicleUsageId -> vehicleUsages.single { vehicleUsageId == it.id } },
            )
        }
    }

    override fun readConsigneeDataByConsignee(consignee: String): List<ConsigneeDataDto> =
        findConsigneeDataByConsignee(consignee).map { consigneeData ->
            consigneeData.toConsigneeDataDto(
                depreciationRelevantCostCenter =
                    consigneeData.depreciationRelevantCostCenterId?.let {
                        costCenterFinder.getCostCenter(
                            it,
                        )
                    },
                usageGroup = consigneeData.usageGroupId?.let { usageGroupFinder.getUsageGroup(it) },
                vehicleUsage = consigneeData.vehicleUsageId?.let { vehicleUsageFinder.getVehicleUsage(it) },
            )
        }

    override fun readLeasingPrivilegeForVehicleUsage(vehicleUsage: VehicleUsageId): List<String> =
        consigneeDataRepository.findByVehicleUsageId(vehicleUsage).mapNotNull {
            it.validationOfLeasingPrivileges
        }

    override fun calculateUsageGroup(vehicleUsageId: VehicleUsageId): UsageGroupId? =
        consigneeDataRepository
            .findByVehicleUsageId(vehicleUsageId = vehicleUsageId)
            .firstOrNull { null != it.usageGroupId }
            ?.usageGroupId
}
