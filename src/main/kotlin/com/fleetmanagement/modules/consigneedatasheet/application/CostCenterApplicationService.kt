/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.CreateCostCenterUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.DeleteCostCenterUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.UpdateCostCenterUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.service.costcenter.CostCenterCreateService
import com.fleetmanagement.modules.consigneedatasheet.domain.service.costcenter.CostCenterDeleteService
import com.fleetmanagement.modules.consigneedatasheet.domain.service.costcenter.CostCenterUpdateService
import jakarta.persistence.EntityManager
import org.hibernate.exception.ConstraintViolationException
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
@Transactional
class CostCenterApplicationService(
    private val costCenterCreateService: CostCenterCreateService,
    private val costCenterUpdateService: CostCenterUpdateService,
    private val costCenterDeleteService: CostCenterDeleteService,
    private val entityManager: EntityManager,
    private val costCenterFinder: CostCenterFinder,
) : CreateCostCenterUseCase,
    UpdateCostCenterUseCase,
    DeleteCostCenterUseCase {
    override fun createCostCenter(costCenterNew: CostCenterNewOrUpdate): CostCenterDto {
        try {
            val createdCostCenter = costCenterCreateService.createCostCenter(costCenterNew.toCostCenter())
            // to enforce constraint check
            entityManager.flush()
            return createdCostCenter.toCostCenterDto()
        } catch (exception: ConstraintViolationException) {
            throw CreateCostCenterException(
                message = "Error while trying to create Cost Center ${exception.message}",
                exception,
            )
        } catch (exception: IllegalArgumentException) {
            throw CreateCostCenterException(
                message = "Error while trying to create Cost Center ${exception.message}",
                exception,
            )
        }
    }

    override fun updateCostCenter(
        id: UUID,
        update: CostCenterNewOrUpdate,
        version: Int,
    ): CostCenterDto {
        val costCenter = costCenterFinder.getCostCenter(CostCenterId(id))

        // this has to be generalized once proper optimistic locking procedures are in place
        if (version != costCenter.version) {
            throw DataIntegrityViolationException("Updating of costCenter with id [$id] failed. Invalid version supplied.")
        }

        try {
            val updatedCostCenter = costCenterUpdateService.updateCostCenter(costCenter, update.toCostCenterUpdate())
            return updatedCostCenter.toCostCenterDto()
        } catch (exception: DataIntegrityViolationException) {
            throw UpdateCostCenterException(
                message = "Error while trying to update Cost Center ${exception.message}",
                exception,
            )
        } catch (exception: IllegalArgumentException) {
            throw UpdateCostCenterException(
                message = "Error while trying to update Cost Center ${exception.message}",
                exception,
            )
        }
    }

    override fun deleteCostCenter(id: UUID) {
        try {
            costCenterDeleteService.deleteCostCenter(id)
            // flush to force constraint check
            // TODO either handle constraint violation globally or move flush to repository fragment
            entityManager.flush()
        } catch (exception: EmptyResultDataAccessException) {
            throw CostCenterNotFoundException(
                id = id,
                message = "Could not find CostCenter with id [$id].",
            )
        } catch (exception: ConstraintViolationException) {
            throw DataIntegrityViolationException(
                "CostCenter with id [$id] failed. CostCenter is currently referenced by another entity.",
                exception,
            )
        }
    }
}
