/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenter
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import com.fleetmanagement.modules.consigneedatasheet.domain.service.costcenter.CostCenterUpdate
import java.util.*

data class CostCenterDto(
    val id: UUID,
    val version: Int,
    val costCenterId: String,
    val description: String,
    val vehicleUsageIds: List<UUID>,
    val vehicleTypes: List<VehicleType>,
)

data class CostCenterNewOrUpdate(
    val costCenterId: String,
    val description: String,
    val vehicleUsageIds: List<UUID>,
    val vehicleTypes: List<VehicleType>,
)

fun CostCenterNewOrUpdate.toCostCenter(): CostCenter =
    CostCenter(
        costCenterId = costCenterId,
        description = CostCenterDescription(description),
        vehicleTypes = vehicleTypes,
        vehicleUsageIds = vehicleUsageIds.map { VehicleUsageId(it) },
    )

fun CostCenter.toCostCenterDto(): CostCenterDto =
    CostCenterDto(
        id = this.id.value,
        version = this.version,
        costCenterId = this.costCenterId,
        description = this.description.value,
        vehicleUsageIds = this.vehicleUsageIds.map { it.value },
        vehicleTypes = this.vehicleTypes,
    )

fun CostCenterNewOrUpdate.toCostCenterUpdate(): CostCenterUpdate =
    CostCenterUpdate(
        costCenterId = costCenterId,
        description = CostCenterDescription(description),
        vehicleUsageIds = vehicleUsageIds.map { VehicleUsageId(it) },
        vehicleTypes = vehicleTypes,
    )

class CostCenterNotFoundException(
    val id: UUID,
    override val message: String,
    override val cause: Throwable? = null,
) : NoSuchElementException()

class CreateCostCenterException(
    override val message: String?,
    override val cause: Throwable?,
) : RuntimeException()

class UpdateCostCenterException(
    override val message: String?,
    override val cause: Throwable?,
) : RuntimeException()
