/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.CalculateDepreciationRelevantCostCenterUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.ReadCostCenterUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenter
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterRepository
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional(readOnly = true)
class CostCenterFinder(
    private val costCenterRepository: CostCenterRepository,
) : ReadCostCenterUseCase,
    CalculateDepreciationRelevantCostCenterUseCase {
    fun getCostCenter(id: CostCenterId): CostCenter =
        costCenterRepository.findById(id) ?: throw CostCenterNotFoundException(
            id = id.value,
            message = "Could not find CostCenter with id [${id.value}].",
        )

    override fun findCostCenter(id: CostCenterId): CostCenter? = costCenterRepository.findById(id)

    fun getAllCostCenter(): List<CostCenter> = costCenterRepository.findAllByOrderByCostCenterIdAsc()

    override fun readAllCostCenter(): List<CostCenterDto> =
        getAllCostCenter().map { costCenter ->
            costCenter.toCostCenterDto()
        }

    override fun calculateDepreciationRelevantCostCenter(
        vehicleUsageId: VehicleUsageId,
        vehicleType: VehicleType,
    ): CostCenterId? =
        costCenterRepository
            .findAllByOrderByCostCenterIdAsc()
            .firstOrNull { it ->
                it.vehicleTypes.any {
                    it == vehicleType
                } &&
                    vehicleUsageId in it.vehicleUsageIds
            }?.id
}
