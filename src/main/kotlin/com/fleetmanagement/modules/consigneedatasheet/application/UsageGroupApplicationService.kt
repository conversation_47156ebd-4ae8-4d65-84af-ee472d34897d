/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup.CreateUsageGroupUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup.DeleteUsageGroupUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup.UpdateUsageGroupUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupId
import com.fleetmanagement.modules.consigneedatasheet.domain.service.usagegroup.UsageGroupCreateService
import com.fleetmanagement.modules.consigneedatasheet.domain.service.usagegroup.UsageGroupDeleteService
import com.fleetmanagement.modules.consigneedatasheet.domain.service.usagegroup.UsageGroupUpdateService
import jakarta.persistence.EntityManager
import org.hibernate.exception.ConstraintViolationException
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
@Transactional
class UsageGroupApplicationService(
    private val usageGroupCreateService: UsageGroupCreateService,
    private val usageGroupUpdateService: UsageGroupUpdateService,
    private val usageGroupDeleteService: UsageGroupDeleteService,
    private val entityManager: EntityManager,
    private val usageGroupFinder: UsageGroupFinder,
) : CreateUsageGroupUseCase,
    UpdateUsageGroupUseCase,
    DeleteUsageGroupUseCase {
    override fun createUsageGroup(usageGroupNew: UsageGroupNewOrUpdate): UsageGroupDto {
        val usageGroup = usageGroupNew.toUsageGroup()
        val createdUsageGroup = usageGroupCreateService.createUsageGroup(usageGroup)
        return createdUsageGroup.toUsageGroupDto()
    }

    override fun updateUsageGroup(
        id: UUID,
        update: UsageGroupNewOrUpdate,
        version: Int,
    ): UsageGroupDto {
        val usageGroup = usageGroupFinder.getUsageGroup(UsageGroupId(id))

        // this has to be generalized once proper optimistic locking procedures are in place
        if (version != usageGroup.version) {
            throw DataIntegrityViolationException("Updating of usageGroup with id [$id] failed. Invalid version supplied.")
        }

        val updatedUsageGroup = usageGroupUpdateService.updateUsageGroup(usageGroup, update.toUsageGroupUpdate())
        return updatedUsageGroup.toUsageGroupDto()
    }

    override fun deleteUsageGroup(id: UUID) {
        try {
            usageGroupDeleteService.deleteUsageGroup(id)
            // flush to force constraint check
            // TODO either handle constraint violation globally or move flush to repository fragment
            entityManager.flush()
        } catch (exception: EmptyResultDataAccessException) {
            throw UsageGroupNotFoundException(
                id = id,
                message = "Could not find UsageGroup with id [$id].",
            )
        } catch (exception: ConstraintViolationException) {
            throw DataIntegrityViolationException(
                "UsageGroup with id [$id] failed. UsageGroup is currently referenced by another entity.",
                exception,
            )
        }
    }
}
