/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroup
import com.fleetmanagement.modules.consigneedatasheet.domain.service.usagegroup.UsageGroupUpdate
import java.util.UUID

data class UsageGroupDto(
    val id: UUID,
    val version: Int,
    val usageGroupId: Long,
    val description: String,
)

data class UsageGroupNewOrUpdate(
    val usageGroupId: Long,
    val description: String,
)

class UsageGroupNotFoundException(
    val id: UUID,
    override val message: String,
    override val cause: Throwable? = null,
) : NoSuchElementException()

fun UsageGroupNewOrUpdate.toUsageGroup(): UsageGroup =
    UsageGroup(
        usageGroupId = this.usageGroupId,
        description = this.description,
    )

fun UsageGroupNewOrUpdate.toUsageGroupUpdate(): UsageGroupUpdate =
    UsageGroupUpdate(
        usageGroupId = this.usageGroupId,
        description = this.description,
    )

fun UsageGroup.toUsageGroupDto(): UsageGroupDto =
    UsageGroupDto(
        id = this.id.value,
        version = this.version,
        usageGroupId = this.usageGroupId,
        description = this.description,
    )
