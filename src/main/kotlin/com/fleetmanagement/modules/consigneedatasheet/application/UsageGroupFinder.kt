/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup.ReadUsageGroupUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroup
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupId
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
@Transactional(readOnly = true)
class UsageGroupFinder(
    private val usageGroupRepository: UsageGroupRepository,
) : ReadUsageGroupUseCase {
    fun getUsageGroup(id: UsageGroupId): UsageGroup =
        usageGroupRepository.findById(id) ?: throw UsageGroupNotFoundException(
            id = id.value,
            message = "Could not find UsageGroup with id [${id.value}].",
        )

    override fun findUsageGroupById(id: UUID): UsageGroup? = usageGroupRepository.findById(UsageGroupId(id))

    fun getAllUsageGroups(): List<UsageGroup> = usageGroupRepository.findAllByOrderByUsageGroupIdAsc()

    override fun readAllUsageGroups(): List<UsageGroupDto> = getAllUsageGroups().map { it.toUsageGroupDto() }
}
