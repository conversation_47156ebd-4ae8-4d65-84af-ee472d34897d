/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage.CreateVehicleUsageUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage.DeleteVehicleUsageUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage.UpdateVehicleUsageUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import com.fleetmanagement.modules.consigneedatasheet.domain.service.vehicleusage.VehicleUsageCreateService
import com.fleetmanagement.modules.consigneedatasheet.domain.service.vehicleusage.VehicleUsageDeleteService
import com.fleetmanagement.modules.consigneedatasheet.domain.service.vehicleusage.VehicleUsageUpdateService
import jakarta.persistence.EntityManager
import org.hibernate.exception.ConstraintViolationException
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.dao.EmptyResultDataAccessException
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
@Transactional
class VehicleUsageApplicationService(
    private val vehicleUsageCreateService: VehicleUsageCreateService,
    private val vehicleUsageUpdateService: VehicleUsageUpdateService,
    private val vehicleUsageDeleteService: VehicleUsageDeleteService,
    private val entityManager: EntityManager,
    private val vehicleUsageFinder: VehicleUsageFinder,
) : CreateVehicleUsageUseCase,
    DeleteVehicleUsageUseCase,
    UpdateVehicleUsageUseCase {
    override fun createVehicleUsage(vehicleUsageNew: VehicleUsageNewOrUpdate): VehicleUsageDto {
        val vehicleUsage = vehicleUsageNew.toVehicleUsage()
        val createdVehicleUsage = vehicleUsageCreateService.createVehicleUsage(vehicleUsage)
        return createdVehicleUsage.toVehicleUsageDto()
    }

    override fun updateVehicleUsage(
        id: UUID,
        update: VehicleUsageNewOrUpdate,
        version: Int,
    ): VehicleUsageDto {
        val vehicleUsage = vehicleUsageFinder.getVehicleUsage(VehicleUsageId(id))

        // this has to be generalized once proper optimistic locking procedures are in place
        if (version != vehicleUsage.version) {
            throw DataIntegrityViolationException("Updating of vehicleUsage with id [$id] failed. Invalid version supplied.")
        }
        val updatedVehicleUsage = vehicleUsageUpdateService.updateVehicleUsage(vehicleUsage, update.toVehicleUsageUpdate())
        return updatedVehicleUsage.toVehicleUsageDto()
    }

    override fun deleteVehicleUsage(id: UUID) {
        try {
            vehicleUsageDeleteService.deleteVehicleUsage(id)
            // flush to force constraint check
            // TODO either handle constraint violation globally or move flush to repository fragment
            entityManager.flush()
        } catch (exception: EmptyResultDataAccessException) {
            throw VehicleUsageNotFoundException(
                id = id,
                message = "Could not find VehicleUsage with id [$id].",
            )
        } catch (exception: ConstraintViolationException) {
            throw DataIntegrityViolationException(
                "VehicleUsage with id [$id] failed. VehicleUsage is currently referenced by another entity.",
                exception,
            )
        }
    }
}
