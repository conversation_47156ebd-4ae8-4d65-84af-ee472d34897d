/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsage
import com.fleetmanagement.modules.consigneedatasheet.domain.service.vehicleusage.VehicleUsageUpdate
import java.util.UUID

data class VehicleUsageDto(
    val id: UUID,
    val version: Int,
    val usageId: Long,
    val usage: String,
)

data class VehicleUsageNewOrUpdate(
    val usageId: Long,
    val usage: String,
)

class VehicleUsageNotFoundException(
    val id: UUID,
    override val message: String,
    override val cause: Throwable? = null,
) : NoSuchElementException()

fun VehicleUsageNewOrUpdate.toVehicleUsage(): VehicleUsage =
    VehicleUsage(
        usageId = this.usageId,
        usage = this.usage,
    )

fun VehicleUsageNewOrUpdate.toVehicleUsageUpdate(): VehicleUsageUpdate =
    VehicleUsageUpdate(
        usageId = this.usageId,
        usage = this.usage,
    )

fun VehicleUsage.toVehicleUsageDto(): VehicleUsageDto =
    VehicleUsageDto(
        id = this.id.value,
        version = this.version,
        usageId = this.usageId,
        usage = this.usage,
    )
