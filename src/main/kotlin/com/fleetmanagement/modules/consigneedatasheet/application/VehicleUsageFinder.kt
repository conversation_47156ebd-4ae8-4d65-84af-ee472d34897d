/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application

import com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage.ReadVehicleUsageUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsage
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
@Transactional(readOnly = true)
class VehicleUsageFinder(
    private val vehicleUsageRepository: VehicleUsageRepository,
) : ReadVehicleUsageUseCase {
    fun getVehicleUsage(id: VehicleUsageId): VehicleUsage =
        vehicleUsageRepository.findById(id) ?: throw VehicleUsageNotFoundException(
            id = id.value,
            message = "Could not find VehicleUsage with id [${id.value}].",
        )

    override fun findVehicleUsageById(id: UUID): VehicleUsage? = vehicleUsageRepository.findById(VehicleUsageId(id))

    fun getAllVehicleUsages(): List<VehicleUsage> = vehicleUsageRepository.findAllByOrderByUsageIdAsc()

    override fun readAllVehicleUsage(): List<VehicleUsageDto> = getAllVehicleUsages().map { it.toVehicleUsageDto() }
}
