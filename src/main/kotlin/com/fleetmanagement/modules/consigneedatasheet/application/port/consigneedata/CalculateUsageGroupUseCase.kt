/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata

import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupId
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId

fun interface CalculateUsageGroupUseCase {
    /**
     * Will return matching [UsageGroupId] for given [VehicleUsageId].
     * Match is calculated based on all existing consigneeData entries and the relation of vehicleUsage and UsageGroup therein (yes, that seems quite dangerous).
     * If more than one match for given vehicleUsageId can be found, the result will be any one of the matches.
     */
    fun calculateUsageGroup(vehicleUsageId: VehicleUsageId): UsageGroupId?
}
