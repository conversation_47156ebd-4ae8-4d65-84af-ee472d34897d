/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata

import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataDto
import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataNewOrUpdate
import com.fleetmanagement.modules.consigneedatasheet.application.CreateConsigneeDataException

fun interface CreateConsigneeDataUseCase {
    /**
     * Will create new consignee data
     *
     * @throws CreateConsigneeDataException in case there are issues creating
     */
    fun createConsigneeData(consigneeDataNew: ConsigneeDataNewOrUpdate): ConsigneeDataDto
}
