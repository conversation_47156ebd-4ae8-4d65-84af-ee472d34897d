/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata

import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataDto
import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataNotFoundException
import java.util.UUID

fun interface DeleteConsigneeDataUseCase {
    /**
     * Will delete [ConsigneeDataDto] by given id.
     *
     * @throws ConsigneeDataNotFoundException in case consignee data doesn't exist
     */
    fun deleteConsigneeData(id: UUID)
}
