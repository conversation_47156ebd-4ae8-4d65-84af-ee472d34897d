/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata

import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataDto

interface ReadConsigneeDataUseCase {
    /**
     * Will return all [ConsigneeDataDto]s ordered by 'consignee'.
     */
    fun readAllConsigneeData(): List<ConsigneeDataDto>

    /**
     * Will return all [ConsigneeDataDto]s matching given consignee.
     */
    fun readConsigneeDataByConsignee(consignee: String): List<ConsigneeDataDto>
}
