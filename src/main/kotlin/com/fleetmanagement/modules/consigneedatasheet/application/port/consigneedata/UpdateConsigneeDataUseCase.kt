/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata

import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataDto
import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataNewOrUpdate
import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataNotFoundException
import org.springframework.dao.DataIntegrityViolationException
import java.util.UUID

fun interface UpdateConsigneeDataUseCase {
    /**
     * Will update given [ConsigneeDataDto] identified by id.
     * Optimistic locking check will be performed against given version.
     *
     * @throws DataIntegrityViolationException in case of optimistic locking issue
     * @throws ConsigneeDataNotFoundException when [ConsigneeDataDto] to be updated could not be found
     *
     * @return updated [ConsigneeDataDto]
     */
    fun updateConsigneeData(
        id: UUID,
        consigneeDataUpdate: ConsigneeDataNewOrUpdate,
        version: Int,
    ): ConsigneeDataDto
}
