/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId

fun interface CalculateDepreciationRelevantCostCenterUseCase {
    /**
     * Will return matching [CostCenterId] for given [VehicleUsageId] and [vehicleType].
     * Match is calculated based on all existing consigneeData entries and the relation.
     * If more than one match for given vehicleUsageId and type can be found, the result will be any one of the matches.
     */
    fun calculateDepreciationRelevantCostCenter(
        vehicleUsageId: VehicleUsageId,
        vehicleType: VehicleType,
    ): CostCenterId?
}
