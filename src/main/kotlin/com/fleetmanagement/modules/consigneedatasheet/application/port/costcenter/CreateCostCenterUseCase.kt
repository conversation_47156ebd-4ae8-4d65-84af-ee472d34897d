/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter

import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterDto
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterNewOrUpdate

fun interface CreateCostCenterUseCase {
    fun createCostCenter(costCenterNew: CostCenterNewOrUpdate): CostCenterDto
}
