/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter

import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterDto
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterNotFoundException
import org.springframework.dao.DataIntegrityViolationException
import java.util.UUID

fun interface DeleteCostCenterUseCase {
    /**
     * Will delete [CostCenterDto] by given id.
     *
     * @throws DataIntegrityViolationException if costCenter is currently referenced
     * @throws CostCenterNotFoundException in case cost center doesn't exist
     */
    fun deleteCostCenter(id: UUID)
}
