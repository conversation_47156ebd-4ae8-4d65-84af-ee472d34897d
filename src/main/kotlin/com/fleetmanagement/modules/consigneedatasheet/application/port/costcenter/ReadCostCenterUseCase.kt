/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter

import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterDto
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenter
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId

interface ReadCostCenterUseCase {
    /**
     * Will return all [CostCenterDto]s ordered by 'depreciationRelevantCostCenterId'.
     */
    fun readAllCostCenter(): List<CostCenterDto>

    fun findCostCenter(id: CostCenterId): CostCenter?
}
