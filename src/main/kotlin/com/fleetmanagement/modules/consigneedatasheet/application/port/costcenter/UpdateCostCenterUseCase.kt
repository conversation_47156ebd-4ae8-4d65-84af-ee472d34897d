/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter

import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterDto
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterNewOrUpdate
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterNotFoundException
import org.springframework.dao.DataIntegrityViolationException
import java.util.UUID

fun interface UpdateCostCenterUseCase {
    /**
     * Will update given [CostCenterDto] identified by id.
     * Optimistic locking check will be performed against given version.
     *
     * @throws DataIntegrityViolationException in case of optimistic locking issue
     * @throws CostCenterNotFoundException when [CostCenterDto] to be updated could not be found
     *
     * @return updated [CostCenterDto]
     */
    fun updateCostCenter(
        id: UUID,
        update: CostCenterNewOrUpdate,
        version: Int,
    ): CostCenterDto
}
