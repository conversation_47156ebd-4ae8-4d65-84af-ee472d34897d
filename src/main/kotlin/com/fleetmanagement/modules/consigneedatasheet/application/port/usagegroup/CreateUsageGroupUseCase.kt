/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup

import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupDto
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupNewOrUpdate

fun interface CreateUsageGroupUseCase {
    fun createUsageGroup(usageGroupNew: UsageGroupNewOrUpdate): UsageGroupDto
}
