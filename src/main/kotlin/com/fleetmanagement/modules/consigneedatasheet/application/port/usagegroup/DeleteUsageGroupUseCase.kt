/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup

import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupDto
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupNotFoundException
import org.springframework.dao.DataIntegrityViolationException
import java.util.UUID

fun interface DeleteUsageGroupUseCase {
    /**
     * Will delete [UsageGroupDto] by given id.
     *
     * @throws DataIntegrityViolationException if usageGroup is currently referenced
     * @throws UsageGroupNotFoundException if usageGroup doesn't exist
     */
    fun deleteUsageGroup(id: UUID)
}
