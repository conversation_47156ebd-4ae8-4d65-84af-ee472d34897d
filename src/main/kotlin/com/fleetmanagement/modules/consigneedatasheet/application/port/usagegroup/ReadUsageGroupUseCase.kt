/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup

import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupDto
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroup
import java.util.UUID

interface ReadUsageGroupUseCase {
    /**
     * Will return all [UsageGroupDto]s ordered by 'usageGroupId'.
     */
    fun readAllUsageGroups(): List<UsageGroupDto>

    fun findUsageGroupById(id: UUID): UsageGroup?
}
