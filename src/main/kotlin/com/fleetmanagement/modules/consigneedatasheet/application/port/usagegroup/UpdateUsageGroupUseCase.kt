/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup

import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupDto
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupNewOrUpdate
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupNotFoundException
import org.springframework.dao.DataIntegrityViolationException
import java.util.UUID

fun interface UpdateUsageGroupUseCase {
    /**
     * Will update given [UsageGroupDto] identified by id.
     * Optimistic locking check will be performed against given version.
     *
     * @throws DataIntegrityViolationException in case of optimistic locking issue
     * @throws UsageGroupNotFoundException when [UsageGroupDto] to be updated could not be found
     *
     * @return updated [UsageGroupDto]
     */
    fun updateUsageGroup(
        id: UUID,
        update: UsageGroupNewOrUpdate,
        version: Int,
    ): UsageGroupDto
}
