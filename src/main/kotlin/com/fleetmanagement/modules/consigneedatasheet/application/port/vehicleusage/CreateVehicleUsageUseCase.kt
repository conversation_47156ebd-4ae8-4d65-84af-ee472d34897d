/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage

import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageDto
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageNewOrUpdate

fun interface CreateVehicleUsageUseCase {
    fun createVehicleUsage(vehicleUsageNew: VehicleUsageNewOrUpdate): VehicleUsageDto
}
