/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage

import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageDto
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageNotFoundException
import org.springframework.dao.DataIntegrityViolationException
import java.util.UUID

fun interface DeleteVehicleUsageUseCase {
    /**
     * Will delete [VehicleUsageDto] by given id.
     *
     * @throws DataIntegrityViolationException if vehicleUsage is currently referenced
     * @throws VehicleUsageNotFoundException if vehicleUsage doesn't exist
     */
    fun deleteVehicleUsage(id: UUID)
}
