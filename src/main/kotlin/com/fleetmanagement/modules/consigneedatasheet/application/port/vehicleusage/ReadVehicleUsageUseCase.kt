/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage

import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageDto
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsage
import java.util.UUID

interface ReadVehicleUsageUseCase {
    /**
     * Will return all [VehicleUsageDto]s ordered by 'usageId'.
     */
    fun readAllVehicleUsage(): List<VehicleUsageDto>

    fun findVehicleUsageById(id: UUID): VehicleUsage?
}
