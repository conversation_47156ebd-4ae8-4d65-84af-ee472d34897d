/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage

import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageDto
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageNewOrUpdate
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageNotFoundException
import org.springframework.dao.DataIntegrityViolationException
import java.util.UUID

fun interface UpdateVehicleUsageUseCase {
    /**
     * Will update given [VehicleUsageDto] identified by id.
     * Optimistic locking check will be performed against given version.
     *
     * @throws DataIntegrityViolationException in case of optimistic locking issue
     * @throws VehicleUsageNotFoundException when [VehicleUsageDto] to be updated could not be found
     *
     * @return updated [VehicleUsageDto]
     */
    fun updateVehicleUsage(
        id: UUID,
        update: VehicleUsageNewOrUpdate,
        version: Int,
    ): VehicleUsageDto
}
