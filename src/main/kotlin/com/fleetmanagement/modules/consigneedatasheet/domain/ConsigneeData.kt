/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.domain

import com.fleetmanagement.emhshared.domain.VehicleType
import jakarta.persistence.AttributeOverride
import jakarta.persistence.AttributeOverrides
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import jakarta.persistence.Transient
import jakarta.persistence.Version
import java.io.Serializable
import java.util.UUID

/**
 * UsageGroup Aggregate.
 *
 * This is a manually maintained mapping table, originating from SAP 'Kst Kostenträger'.
 * Data from these entities will be used to add descriptions to costCenter mappings within [ConsigneeData]
 */
@Entity
@Table(name = "consignee_data", schema = "consigneedatasheet")
class ConsigneeData(
    consignee: String,
    description: String,
    maximumServiceLifeInMonths: Int?,
    vehicleUsageId: VehicleUsageId?,
    vehicleResponsiblePerson: EmployeeNumber?,
    internalContactPerson: EmployeeNumber?,
    isPreproductionVehicle: Boolean,
    isBlockedForSale: Boolean,
    isScrapped: Boolean,
    depreciationRelevantCostCenterId: CostCenterId?,
    usingCostCenter: CostCenterDescription?,
    internalOrderNumber: String?,
    validationOfLeasingPrivileges: String?,
    usageGroupId: UsageGroupId?,
    vehicleType: VehicleType?,
    manufacturer: String?,
    pdiCurrentTireSet: TireSet?,
    pdiRelevant: Boolean?,
    pdiFoiling: Boolean?,
    pdiRefuel: Boolean?,
    pdiRecharge: Boolean?,
    pdiDigitalLogbook: Boolean?,
    pdiLicensePlateMounting: Boolean?,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: ConsigneeDataId = ConsigneeDataId()

    /** Warenempfänger */
    @Column(name = "consignee")
    var consignee: String = consignee
        private set

    /** Bezeichnung */
    @Column(name = "description")
    var description: String = description
        private set

    /** Planlaufzeit in Monaten */
    @Column(name = "maximum_service_life_months")
    var maximumServiceLifeInMonths: Int? = maximumServiceLifeInMonths
        private set

    /** Fzg Verwendung*/
    @Embedded
    @AttributeOverride(
        name = "value",
        column = Column(name = "vehicle_usage_id"),
    )
    var vehicleUsageId: VehicleUsageId? = vehicleUsageId
        private set

    /** Fzg. Verantwortlicher */
    @AttributeOverride(name = "value", column = Column(name = "vehicle_responsible_person"))
    var vehicleResponsiblePerson: EmployeeNumber? = vehicleResponsiblePerson
        private set

    /** Interner Ansprechpartner */
    @AttributeOverride(name = "value", column = Column(name = "internal_contact_person"))
    var internalContactPerson: EmployeeNumber? = internalContactPerson
        private set

    /** Vorserie */
    @Column(name = "preproduction_vehicle")
    var isPreproductionVehicle: Boolean = isPreproductionVehicle
        private set

    /** Für Verkauf gesperrt */
    @Column(name = "blocked_for_sale")
    var isBlockedForSale: Boolean = isBlockedForSale
        private set

    /** Schrottfahrzeug */
    @Column(name = "scrapped")
    var isScrapped: Boolean = isScrapped
        private set

    /** Kst Kostenträger (Übergeordnet) */
    @Embedded
    @AttributeOverride(
        name = "value",
        column = Column(name = "depreciation_relevant_cost_center_id"),
    )
    var depreciationRelevantCostCenterId: CostCenterId? = depreciationRelevantCostCenterId
        private set

    /** Verantwortliche Kostenstelle (Abteilungsbezogen) */
    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "using_cost_center"))
    var usingCostCenter: CostCenterDescription? = usingCostCenter
        private set

    /** Prüfung auf Innenauftragsnummer */
    @Column(name = "internal_order_number")
    var internalOrderNumber: String? = internalOrderNumber
        private set

    /** Prüfung auf Leasingberechtigung */
    @Column(name = "validation_of_leasing_privileges")
    var validationOfLeasingPrivileges: String? = validationOfLeasingPrivileges
        private set

    /** Nutzungsgruppe */
    @Embedded
    @AttributeOverride(
        name = "value",
        column = Column(name = "usage_group_id"),
    )
    var usageGroupId: UsageGroupId? = usageGroupId
        private set

    /** Fzg ART */
    @Enumerated(EnumType.STRING)
    @Column(name = "vehicle_type")
    var vehicleType: VehicleType? = vehicleType
        private set

    /** Hersteller */
    @Column(name = "manufacturer")
    var manufacturer: String? = manufacturer
        private set

    @Embedded
    @AttributeOverrides(
        AttributeOverride(name = "currentTireSet", column = Column(name = "pdi_current_tire_set")),
        AttributeOverride(name = "isRelevant", column = Column(name = "pdi_relevant")),
        AttributeOverride(name = "foiling", column = Column(name = "pdi_foiling")),
        AttributeOverride(name = "refuel", column = Column(name = "pdi_refuel")),
        AttributeOverride(name = "recharge", column = Column(name = "pdi_recharge")),
        AttributeOverride(name = "digitalLogbook", column = Column(name = "pdi_digital_logbook")),
        AttributeOverride(name = "licensePlateMounting", column = Column(name = "pdi_license_plate_mounting")),
    )
    private var _preDeliveryInspection: PreDeliveryInspection?

    /**
     * Workaround for uninitialized composites, as 'create_empty_composites' is not yet production ready
     * see https://hibernate.atlassian.net/browse/HHH-11936
     */
    @get:Transient
    val preDeliveryInspection: PreDeliveryInspection
        get() = this._preDeliveryInspection ?: EMPTY_PRE_DELIVERY_INSPECTION

    @Version
    val version: Int = 0

    init {
        require(consignee.isNotBlank()) { "consignee must not be blank" }

        _preDeliveryInspection =
            PreDeliveryInspection(
                currentTireSet = pdiCurrentTireSet,
                isRelevant = pdiRelevant,
                foiling = pdiFoiling,
                refuel = pdiRefuel,
                recharge = pdiRecharge,
                digitalLogbook = pdiDigitalLogbook,
                licensePlateMounting = pdiLicensePlateMounting,
            )
    }

    internal fun update(
        consignee: String,
        description: String,
        maximumServiceLifeInMonths: Int?,
        vehicleUsageId: VehicleUsageId?,
        vehicleResponsiblePerson: EmployeeNumber?,
        internalContactPerson: EmployeeNumber?,
        isPreproductionVehicle: Boolean,
        isBlockedForSale: Boolean,
        isScrapped: Boolean,
        depreciationRelevantCostCenterId: CostCenterId?,
        usingCostCenter: CostCenterDescription?,
        internalOrderNumber: String?,
        usageGroupId: UsageGroupId?,
        validationOfLeasingPrivileges: String?,
        vehicleType: VehicleType?,
        manufacturer: String?,
        pdiCurrentTireSet: TireSet?,
        pdiRelevant: Boolean?,
        pdiFoiling: Boolean?,
        pdiRefuel: Boolean?,
        pdiRecharge: Boolean?,
        pdiDigitalLogbook: Boolean?,
        pdiLicensePlateMounting: Boolean?,
    ) {
        require(consignee.isNotBlank()) { "consignee must not be blank" }

        this.consignee = consignee
        this.description = description
        this.maximumServiceLifeInMonths = maximumServiceLifeInMonths
        this.vehicleUsageId = vehicleUsageId
        this.vehicleResponsiblePerson = vehicleResponsiblePerson
        this.internalContactPerson = internalContactPerson
        this.isPreproductionVehicle = isPreproductionVehicle
        this.isBlockedForSale = isBlockedForSale
        this.isScrapped = isScrapped
        this.depreciationRelevantCostCenterId = depreciationRelevantCostCenterId
        this.usingCostCenter = usingCostCenter
        this.internalOrderNumber = internalOrderNumber
        this.usageGroupId = usageGroupId
        this.validationOfLeasingPrivileges = validationOfLeasingPrivileges
        this.vehicleType = vehicleType
        this.manufacturer = manufacturer

        if (null == _preDeliveryInspection) _preDeliveryInspection = EMPTY_PRE_DELIVERY_INSPECTION
        this._preDeliveryInspection?.update(
            currentTireSet = pdiCurrentTireSet,
            isRelevant = pdiRelevant,
            foiling = pdiFoiling,
            refuel = pdiRefuel,
            recharge = pdiRecharge,
            digitalLogbook = pdiDigitalLogbook,
            licensePlateMounting = pdiLicensePlateMounting,
        )
    }

    companion object {
        private val EMPTY_PRE_DELIVERY_INSPECTION =
            PreDeliveryInspection(
                refuel = null,
                isRelevant = null,
                recharge = null,
                digitalLogbook = null,
                licensePlateMounting = null,
                foiling = null,
                currentTireSet = null,
            )
    }
}

@Embeddable
data class ConsigneeDataId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable
