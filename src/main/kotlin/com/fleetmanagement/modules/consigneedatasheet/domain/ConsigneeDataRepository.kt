/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain

import org.springframework.data.repository.Repository

@org.springframework.stereotype.Repository
interface ConsigneeDataRepository : Repository<ConsigneeData, ConsigneeDataId> {
    fun save(consigneeData: ConsigneeData): ConsigneeData

    fun saveAndFlush(consigneeData: ConsigneeData): ConsigneeData

    fun findById(id: ConsigneeDataId): ConsigneeData?

    fun findByConsignee(consignee: String): List<ConsigneeData>

    fun findAllByOrderByConsigneeAscDescriptionAsc(): List<ConsigneeData>

    fun deleteById(id: ConsigneeDataId)

    fun findByVehicleUsageId(vehicleUsageId: VehicleUsageId): List<ConsigneeData>

    fun findByVehicleUsageIdAndVehicleType(
        vehicleUsageId: VehicleUsageId,
        vehicleType: String,
    ): List<ConsigneeData>
}
