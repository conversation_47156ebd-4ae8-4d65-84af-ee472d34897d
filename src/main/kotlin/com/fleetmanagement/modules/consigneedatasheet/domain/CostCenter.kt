/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.domain

import com.fleetmanagement.emhshared.domain.VehicleType
import jakarta.persistence.AttributeConverter
import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.CollectionTable
import jakarta.persistence.Column
import jakarta.persistence.Convert
import jakarta.persistence.Converter
import jakarta.persistence.ElementCollection
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.JoinColumn
import jakarta.persistence.Table
import jakarta.persistence.Transient
import jakarta.persistence.Version
import java.io.Serializable
import java.util.UUID

/**
 * CostCenter Aggregate.
 *
 * This is a manually maintained mapping table, originating from SAP 'Kst Kostenträger'.
 * Data from these entities will be used to add descriptions to costCenter mappings within [ConsigneeData]
 */
@Entity
@Table(name = "cost_center", schema = "consigneedatasheet")
class CostCenter(
    costCenterId: String,
    description: CostCenterDescription,
    vehicleTypes: List<VehicleType>,
    vehicleUsageIds: List<VehicleUsageId>,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: CostCenterId = CostCenterId()

    // SAP id
    @Column(name = "cost_center_id")
    var costCenterId: String = costCenterId
        private set

    @Embedded
    @AttributeOverride(name = "value", column = Column(name = "description"))
    var description: CostCenterDescription = description
        private set

    @Version
    val version: Int = 0

    @Column(name = "vehicle_types")
    @Convert(converter = VehicleTypeListConverter::class)
    private val _vehicleTypes: MutableList<VehicleType> = vehicleTypes.toMutableList()

    @get:Transient
    val vehicleTypes: List<VehicleType>
        get() = _vehicleTypes.toList()

    @ElementCollection
    @CollectionTable(
        name = "cost_center_vehicle_usage",
        schema = "consigneedatasheet",
        joinColumns = [JoinColumn(name = "cost_center_id")],
    )
    @AttributeOverride(name = "value", column = Column(name = "vehicle_usage_id"))
    private val _vehicleUsageIds: MutableList<VehicleUsageId> = vehicleUsageIds.toMutableList()

    @get:Transient
    val vehicleUsageIds: List<VehicleUsageId>
        get() = _vehicleUsageIds.toList()

    internal fun update(
        costCenterId: String,
        description: CostCenterDescription,
        vehicleTypes: List<VehicleType>,
        vehicleUsageIds: List<VehicleUsageId>,
    ) {
        this.costCenterId = costCenterId
        this.description = description
        this._vehicleTypes.clear()
        this._vehicleTypes.addAll(vehicleTypes)
        this._vehicleUsageIds.clear()
        this._vehicleUsageIds.addAll(vehicleUsageIds)
    }
}

@Embeddable
data class CostCenterId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable

@Converter
class VehicleTypeListConverter : AttributeConverter<MutableList<VehicleType>, String> {
    override fun convertToDatabaseColumn(attribute: MutableList<VehicleType>): String? =
        if (attribute.isEmpty()) {
            null
        } else {
            attribute.joinToString(separator = ",") { it.name }
        }

    override fun convertToEntityAttribute(dbData: String?): MutableList<VehicleType> =
        if (dbData.isNullOrEmpty()) {
            mutableListOf()
        } else {
            dbData
                .split(",")
                .map { VehicleType.valueOf(it) }
                .toMutableList()
        }
}
