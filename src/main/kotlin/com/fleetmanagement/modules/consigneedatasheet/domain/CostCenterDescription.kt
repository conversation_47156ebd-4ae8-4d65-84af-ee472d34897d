/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.consigneedatasheet.domain

import jakarta.persistence.Embeddable
import java.io.Serializable

/**
 * Dedicated CostCenterDescription representation, to ensure cost center satisfies business constraints.
 * Otherwise, external systems like PACE will error out due to maxlength constraints on their side.
 *
 * This class is a candidate for a shared artifact in the future.
 */
@Embeddable
class CostCenterDescription(
    description: String,
) : Serializable {
    val value: String

    init {
        require(description.isNotBlank()) { "Cost Center description may not be blank" }
        require(description.matches(PATTERN)) {
            "Provided cost center description:" +
                "$description does not match pattern: [${PATTERN.pattern}]"
        }
        value = description
    }

    override fun equals(other: Any?): <PERSON><PERSON><PERSON> {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as CostCenterDescription

        return value == other.value
    }

    override fun hashCode(): Int = value.hashCode()

    companion object {
        private val PATTERN = Regex("""00H001\d{4}""")
    }
}
