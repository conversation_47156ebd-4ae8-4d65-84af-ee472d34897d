/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain

import org.springframework.data.repository.Repository

@org.springframework.stereotype.Repository
interface CostCenterRepository : Repository<CostCenter, CostCenterId> {
    fun save(costCenter: CostCenter): CostCenter

    fun saveAndFlush(costCenter: CostCenter): CostCenter

    fun findById(id: CostCenterId): CostCenter?

    fun findAllByOrderByCostCenterIdAsc(): List<CostCenter>

    fun deleteById(id: CostCenterId)
}
