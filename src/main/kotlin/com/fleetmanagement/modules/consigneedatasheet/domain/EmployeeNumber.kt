/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain

import jakarta.persistence.Embeddable
import java.io.Serializable

/**
 * Dedicated EmployeeNumber representation, to ensure that employee numbers match user-service representation.
 * Otherwise, DB joins will have issues matching the correct rows.
 *
 * Also, always good to ensure consistent representation.
 * This class is a candidate for a shared artifact in the future.
 */
@Embeddable
class EmployeeNumber(
    number: String,
) : Serializable {
    val value: String

    init {
        require(number.isNotBlank()) { "Employee number may not be blank" }

        // strip any single leading P and pad start number with 0s until 8 digits are reached
        val sanitizedNumber =
            number
                .let { if (it.startsWith("p", true)) it.lowercase().replaceFirst("p", "", true) else it }
                .padStart(8, '0')
        // ensure employee number validity here
        require(sanitizedNumber.matches(PATTERN)) {
            "Provided employee number: $number (after sanitization: $sanitizedNumber) does not match pattern: [${PATTERN.pattern}]."
        }

        value = sanitizedNumber
    }

    override fun equals(other: Any?): Boolean {
        if (this === other) return true
        if (javaClass != other?.javaClass) return false

        other as EmployeeNumber

        return value == other.value
    }

    override fun hashCode(): Int = value.hashCode()

    companion object {
        val PATTERN = Regex(pattern = """^\d{8}$""")
    }
}
