/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain

import jakarta.persistence.Embeddable
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated

/**
 * A set of default values for pre-delivery inspection.
 * PDI is attached to consigneeData and gets maintained (updated) through consignee data as well.
 *
 * Do not confuse this class with the actual pre-delivery inspection (PDI attached to a vehicle) domain model,
 * that you can find within the respective module.
 */
@Embeddable
class PreDeliveryInspection(
    currentTireSet: TireSet?,
    isRelevant: Boolean?,
    foiling: Boolean?,
    refuel: Boolean?,
    recharge: Boolean?,
    digitalLogbook: Boolean?,
    licensePlateMounting: Boolean?,
) {
    /** the set of tires to install during PDI */
    @Enumerated(EnumType.STRING)
    var currentTireSet: TireSet? = currentTireSet
        private set

    /** flag indicating if PDI has to be done */
    var isRelevant: Boolean? = isRelevant
        private set

    /** flag indicating if foiling has to be applied during PDI */
    var foiling: Boolean? = foiling
        private set

    /** flag indicating if vehicle has to be refueled during PDI */
    var refuel: Boolean? = refuel
        private set

    /** flag indicating if vehicle has to be recharged during PDI */
    var recharge: Boolean? = recharge
        private set

    /** flag indicating if digital logbook has to be setup during PDI */
    var digitalLogbook: Boolean? = digitalLogbook
        private set

    /** flag indicating if license plates have to be mounted during PDI */
    var licensePlateMounting: Boolean? = licensePlateMounting
        private set

    fun update(
        currentTireSet: TireSet?,
        isRelevant: Boolean?,
        foiling: Boolean?,
        refuel: Boolean?,
        recharge: Boolean?,
        digitalLogbook: Boolean?,
        licensePlateMounting: Boolean?,
    ) {
        this.currentTireSet = currentTireSet
        this.isRelevant = isRelevant
        this.foiling = foiling
        this.refuel = refuel
        this.recharge = recharge
        this.digitalLogbook = digitalLogbook
        this.licensePlateMounting = licensePlateMounting
    }
}

enum class TireSet(
    val description: String,
) {
    SR("summer tires"),
    WR("winter tires"),
}
