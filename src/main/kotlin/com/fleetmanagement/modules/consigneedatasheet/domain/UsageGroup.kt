/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.domain

import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import jakarta.persistence.Version
import java.io.Serializable
import java.util.UUID

/**
 * UsageGroup Aggregate.
 *
 * This is a manually maintained mapping table, originating from SAP 'Ableitungstabelle Nutzungsgruppe'.
 * Data from these entities will be used to add descriptions to usageGroup mappings within [ConsigneeData]
 */
@Entity
@Table(name = "usage_group", schema = "consigneedatasheet")
class UsageGroup(
    usageGroupId: Long,
    description: String,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: UsageGroupId = UsageGroupId()

    // SAP id
    @Column(name = "usage_group_id")
    var usageGroupId: Long = usageGroupId
        private set

    @Column(name = "description")
    var description: String = description
        private set

    @Version
    val version: Int = 0

    internal fun update(
        usageGroupId: Long,
        description: String,
    ) {
        this.usageGroupId = usageGroupId
        this.description = description
    }
}

@Embeddable
data class UsageGroupId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable
