/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain

import org.springframework.data.repository.Repository

@org.springframework.stereotype.Repository
interface UsageGroupRepository : Repository<UsageGroup, UsageGroupId> {
    fun save(usageGroup: UsageGroup): UsageGroup

    fun saveAndFlush(usageGroup: UsageGroup): UsageGroup

    fun findById(id: UsageGroupId): UsageGroup?

    fun findAllByOrderByUsageGroupIdAsc(): List<UsageGroup>

    fun deleteById(id: UsageGroupId)
}
