/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.domain

import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import jakarta.persistence.Version
import java.io.Serializable
import java.util.UUID

/**
 * VehicleUsage Aggregate.
 *
 * This is a manually maintained mapping table, originating from SAP 'Fzg Verwendung'.
 * Data from these entities will be used to add descriptions to vehicleUsage mappings within [ConsigneeData]
 */
@Entity
@Table(name = "vehicle_usage", schema = "consigneedatasheet")
class VehicleUsage(
    usageId: Long,
    usage: String,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: VehicleUsageId = VehicleUsageId()

    // SAP id
    @Column(name = "usage_id")
    var usageId: Long = usageId
        private set

    @Column(name = "usage")
    var usage: String = usage
        private set

    @Version
    val version: Int = 0

    internal fun update(
        usageId: Long,
        usage: String,
    ) {
        this.usageId = usageId
        this.usage = usage
    }
}

@Embeddable
data class VehicleUsageId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable
