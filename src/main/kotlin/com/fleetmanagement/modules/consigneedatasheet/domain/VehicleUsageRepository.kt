/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain

import org.springframework.data.repository.Repository

@org.springframework.stereotype.Repository
interface VehicleUsageRepository : Repository<VehicleUsage, VehicleUsageId> {
    fun save(vehicleUsage: VehicleUsage): VehicleUsage

    fun saveAndFlush(vehicleUsage: VehicleUsage): VehicleUsage

    fun findById(id: VehicleUsageId): VehicleUsage?

    fun findAllByOrderByUsageIdAsc(): List<VehicleUsage>

    fun deleteById(id: VehicleUsageId)
}
