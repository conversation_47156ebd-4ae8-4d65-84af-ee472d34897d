/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.consigneedatasheet.domain.service.consigneedata

import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeData
import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeDataRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class ConsigneeDataCreateService(
    private val consigneeDataRepository: ConsigneeDataRepository,
) {
    fun createConsigneeData(consigneeData: ConsigneeData): ConsigneeData = consigneeDataRepository.save(consigneeData)
}
