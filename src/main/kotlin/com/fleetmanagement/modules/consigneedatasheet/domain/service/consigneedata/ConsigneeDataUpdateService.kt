/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.domain.service.consigneedata

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeData
import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeDataRepository
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.consigneedatasheet.domain.TireSet
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupId
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class ConsigneeDataUpdateService(
    private val consigneeDataRepository: ConsigneeDataRepository,
) {
    fun updateConsigneeData(
        consigneeData: ConsigneeData,
        consigneeDataUpdate: ConsigneeDataUpdate,
    ): ConsigneeData {
        consigneeData.update(
            consignee = consigneeDataUpdate.consignee,
            description = consigneeDataUpdate.description,
            maximumServiceLifeInMonths = consigneeDataUpdate.maximumServiceLifeInMonths,
            vehicleUsageId = consigneeDataUpdate.vehicleUsageId,
            vehicleResponsiblePerson = consigneeDataUpdate.vehicleResponsiblePerson,
            internalContactPerson = consigneeDataUpdate.internalContactPerson,
            isPreproductionVehicle = consigneeDataUpdate.isPreproductionVehicle,
            isBlockedForSale = consigneeDataUpdate.isBlockedForSale,
            isScrapped = consigneeDataUpdate.isScrapped,
            depreciationRelevantCostCenterId = consigneeDataUpdate.depreciationRelevantCostCenterId,
            usingCostCenter = consigneeDataUpdate.usingCostCenter,
            internalOrderNumber = consigneeDataUpdate.internalOrderNumber,
            usageGroupId = consigneeDataUpdate.usageGroupId,
            validationOfLeasingPrivileges = consigneeDataUpdate.validationOfLeasingPrivileges,
            vehicleType = consigneeDataUpdate.vehicleType,
            manufacturer = consigneeDataUpdate.manufacturer,
            pdiCurrentTireSet = consigneeDataUpdate.pdiCurrentTireSet,
            pdiRelevant = consigneeDataUpdate.pdiRelevant,
            pdiFoiling = consigneeDataUpdate.pdiFoiling,
            pdiRefuel = consigneeDataUpdate.pdiRefuel,
            pdiRecharge = consigneeDataUpdate.pdiRecharge,
            pdiDigitalLogbook = consigneeDataUpdate.pdiDigitalLogbook,
            pdiLicensePlateMounting = consigneeDataUpdate.pdiLicensePlateMounting,
        )

        // force flush to get updated version
        return consigneeDataRepository.saveAndFlush(consigneeData)
    }
}

data class ConsigneeDataUpdate(
    val consignee: String,
    val description: String,
    val maximumServiceLifeInMonths: Int?,
    val vehicleUsageId: VehicleUsageId?,
    val vehicleResponsiblePerson: EmployeeNumber?,
    val internalContactPerson: EmployeeNumber?,
    val isPreproductionVehicle: Boolean,
    val isBlockedForSale: Boolean,
    val isScrapped: Boolean,
    val depreciationRelevantCostCenterId: CostCenterId?,
    val usingCostCenter: CostCenterDescription?,
    val internalOrderNumber: String?,
    val validationOfLeasingPrivileges: String?,
    val usageGroupId: UsageGroupId?,
    val vehicleType: VehicleType?,
    val manufacturer: String?,
    val pdiCurrentTireSet: TireSet?,
    val pdiRelevant: Boolean?,
    val pdiFoiling: Boolean?,
    val pdiRefuel: Boolean?,
    val pdiRecharge: Boolean?,
    val pdiDigitalLogbook: Boolean?,
    val pdiLicensePlateMounting: Boolean?,
)
