/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain.service.costcenter

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenter
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterRepository
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class CostCenterUpdateService(
    private val costCenterRepository: CostCenterRepository,
) {
    fun updateCostCenter(
        costCenter: CostCenter,
        costCenterUpdate: CostCenterUpdate,
    ): CostCenter {
        costCenter.update(
            costCenterId = costCenterUpdate.costCenterId,
            description = costCenterUpdate.description,
            vehicleUsageIds = costCenterUpdate.vehicleUsageIds,
            vehicleTypes = costCenterUpdate.vehicleTypes,
        )

        // force flush to get updated version
        return costCenterRepository.saveAndFlush(costCenter)
    }
}

data class CostCenterUpdate(
    val costCenterId: String,
    val description: CostCenterDescription,
    val vehicleUsageIds: List<VehicleUsageId>,
    val vehicleTypes: List<VehicleType>,
)
