/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain.service.usagegroup

import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroup
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class UsageGroupCreateService(
    private val usageGroupRepository: UsageGroupRepository,
) {
    fun createUsageGroup(usageGroupNew: UsageGroup): UsageGroup = usageGroupRepository.save(usageGroupNew)
}
