/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain.service.usagegroup

import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroup
import com.fleetmanagement.modules.consigneedatasheet.domain.UsageGroupRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class UsageGroupUpdateService(
    private val usageGroupRepository: UsageGroupRepository,
) {
    fun updateUsageGroup(
        usageGroup: UsageGroup,
        update: UsageGroupUpdate,
    ): UsageGroup {
        usageGroup.update(
            usageGroupId = update.usageGroupId,
            description = update.description,
        )
        // force flush to get updated version
        return usageGroupRepository.saveAndFlush(usageGroup)
    }
}

data class UsageGroupUpdate(
    val usageGroupId: Long,
    val description: String,
)
