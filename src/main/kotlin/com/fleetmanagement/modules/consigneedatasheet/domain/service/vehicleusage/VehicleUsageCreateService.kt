/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.consigneedatasheet.domain.service.vehicleusage

import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsage
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class VehicleUsageCreateService(
    private val vehicleUsageRepository: VehicleUsageRepository,
) {
    fun createVehicleUsage(vehicleUsageNew: VehicleUsage): VehicleUsage = vehicleUsageRepository.save(vehicleUsageNew)
}
