/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.consigneedatasheet.domain.service.vehicleusage

import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@Transactional(propagation = Propagation.MANDATORY)
class VehicleUsageDeleteService(
    private val vehicleUsageRepository: VehicleUsageRepository,
) {
    fun deleteVehicleUsage(id: UUID) = vehicleUsageRepository.deleteById(VehicleUsageId(id))
}
