/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka

import org.apache.kafka.clients.producer.ProducerConfig
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.core.DefaultKafkaProducerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.core.ProducerFactory

@Configuration
class DMSVehicleMigrationKafkaConfig(
    private val props: DMSVehicleMigrationKafkaProperties,
) {
    @Bean
    fun dmsVehicleMigrationKafkaProducerFactory(): ProducerFactory<String, Any> {
        val config =
            mapOf<String, Any>(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG to props.bootstrapServers,
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG to props.keySerializer,
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG to props.valueSerializer,
                "sasl.mechanism" to props.saslMechanism,
                "sasl.jaas.config" to props.saslJaasConfig,
                "security.protocol" to props.securityProtocol,
                "session.timeout.ms" to props.sessionTimeoutMs,
            )
        return DefaultKafkaProducerFactory(config)
    }

    @Bean(name = ["dmsVehicleMigrationStreamzillaKafkaTemplate"])
    fun dmsVehicleMigrationStreamzillaKafkaTemplate(): KafkaTemplate<String, Any> = KafkaTemplate(dmsVehicleMigrationKafkaProducerFactory())
}

@ConfigurationProperties("dms-vehicle-migration.kafka-producer")
class DMSVehicleMigrationKafkaProperties(
    val bootstrapServers: String,
    val keySerializer: String,
    val valueSerializer: String,
    val saslMechanism: String,
    val saslJaasConfig: String,
    val securityProtocol: String,
    val sessionTimeoutMs: String,
)
