/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.module.kotlin.convertValue
import com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka.model.VehicleMigrationDto
import com.fleetmanagement.modules.dmsvehiclemigration.application.port.PublishVehicleUseCase
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.stereotype.Component
import java.util.concurrent.TimeUnit

@Component
class DMSVehicleMigrationKafkaPublishAdapter(
    @Qualifier("dmsVehicleMigrationStreamzillaKafkaTemplate")
    private val kafkaTemplate: KafkaTemplate<String, Any>,
    @Value("\${dms-vehicle-migration.topic}") val dmsVehicleMigrationTopic: String,
    private val objectMapper: ObjectMapper,
) : PublishVehicleUseCase {
    override fun publishVehicle(vehicleMigrationDto: VehicleMigrationDto) {
        val payload: JsonNode = objectMapper.convertValue(vehicleMigrationDto)
        kafkaTemplate.send(dmsVehicleMigrationTopic, payload).get(3, TimeUnit.SECONDS)
        log.info("Sent payload with vin ${vehicleMigrationDto.vehicle.vin} to topic $dmsVehicleMigrationTopic")
    }

    companion object {
        private val log = LoggerFactory.getLogger(DMSVehicleMigrationKafkaPublishAdapter::class.java)
    }
}
