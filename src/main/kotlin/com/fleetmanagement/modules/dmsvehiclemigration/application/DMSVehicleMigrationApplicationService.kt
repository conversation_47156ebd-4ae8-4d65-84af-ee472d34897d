/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.dmsvehiclemigration.application

import com.fleetmanagement.emhshared.LeasingArt
import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterFinder
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageFinder
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import com.fleetmanagement.modules.dmsvehiclemigration.application.port.PublishVehicleUseCase
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.events.VehicleStatusChangedEvent
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.port.CurrentVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferCreatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferInitializedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferLicensePlateEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferNotFoundException
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferUpdatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferDeliveredEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferLicensePlateEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferNotFoundException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferReturnedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferUpdatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.entities.AbstractVehicleTransferEntity
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener
import java.time.OffsetDateTime
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import com.fleetmanagement.modules.vehicletransfer.domain.entities.CostCenterId as VTCostCenterId
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleUsageId as VTVehicleUsageId

@Component
@ConditionalOnProperty(
    "dms-vehicle-migration.enabled",
    havingValue = "true",
    matchIfMissing = false,
)
class DMSVehicleMigrationApplicationService(
    private val publishVehicleUseCase: PublishVehicleUseCase,
    private val plannedVehicleTransferFinder: PlannedVehicleTransferFinder,
    private val vehicleTransferFinder: VehicleTransferFinder,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val readRegistration: ReadRegistrationOrder,
    private val costCenterFinder: CostCenterFinder,
    private val vehicleUsageFinder: VehicleUsageFinder,
    private val readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber,
    private val currentVehicleTransferUseCase: CurrentVehicleTransferUseCase,
) {
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handlePlannedVehicleTransferCreatedEvent(event: PlannedVehicleTransferCreatedEvent) {
        handlePlannedVehicleTransferEvent(event)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handlePlannedVehicleTransferInitializedEvent(event: PlannedVehicleTransferInitializedEvent) {
        handlePlannedVehicleTransferEvent(event)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handlePlannedVehicleTransferUpdatedEvent(event: PlannedVehicleTransferUpdatedEvent) {
        handlePlannedVehicleTransferEvent(event)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handlePlannedVehicleTransferLicensePlateEvent(event: PlannedVehicleTransferLicensePlateEvent) {
        handlePlannedVehicleTransferEvent(event)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handleVehicleTransferDeliveredEvent(event: VehicleTransferDeliveredEvent) {
        handleVehicleTransferEvent(event)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handleVehicleTransferReturnedEvent(event: VehicleTransferReturnedEvent) {
        handleVehicleTransferEvent(event)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handleVehicleTransferUpdatedEvent(event: VehicleTransferUpdatedEvent) {
        handleVehicleTransferEvent(event)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handleVehicleTransferLicensePlateEvent(event: VehicleTransferLicensePlateEvent) {
        handleVehicleTransferEvent(event)
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handleVehicleStatusChangedEvent(event: VehicleStatusChangedEvent) {
        val currentVehicleTransferKey = currentVehicleTransferUseCase.retrieveCurrentVehicleTransferKey(event.vehicleId)
        if (currentVehicleTransferKey == null) {
            logger.warn(
                "Cannot publish vehicle migration data, as currentVehicleTransferKey is missing, vehicleId: ${event.vehicleId}",
            )
            return
        }
        val depreciationRelevantCostCenterId: VTCostCenterId?
        val usingCostCenter: CostCenterDescription?
        val vehicleUsageId: VTVehicleUsageId?
        val vehicleResponsiblePerson: EmployeeNumber?
        val abstractVehicleTransfer: AbstractVehicleTransferEntity?

        val plannedVehicleTransfer = plannedVehicleTransferFinder.findPlannedVehicleTransfer(currentVehicleTransferKey)
        if (plannedVehicleTransfer == null) {
            val vehicleTransfer = vehicleTransferFinder.findVehicleTransferByKey(currentVehicleTransferKey)
            abstractVehicleTransfer = vehicleTransfer
        } else {
            abstractVehicleTransfer = plannedVehicleTransfer
        }
        if (abstractVehicleTransfer == null) {
            logger.warn(
                "Cannot publish vehicle migration data, as neither planned vehicle transfer nor vehicle transfer is present, vehicleTransferKey: ${currentVehicleTransferKey.value}, vehicleId: ${event.vehicleId}",
            )
            return
        }
        depreciationRelevantCostCenterId = abstractVehicleTransfer.depreciationRelevantCostCenterId
        usingCostCenter = abstractVehicleTransfer.usingCostCenter
        vehicleUsageId = abstractVehicleTransfer.vehicleUsage
        vehicleResponsiblePerson = abstractVehicleTransfer.vehicleResponsiblePerson

        prepareAndPublishVehicleMigrationData(
            event.vehicleId,
            depreciationRelevantCostCenterId,
            usingCostCenter,
            vehicleUsageId,
            vehicleResponsiblePerson,
        )
    }

    private fun handlePlannedVehicleTransferEvent(event: PlannedVehicleTransferEvent) {
        val plannedVehicleTransfer =
            try {
                plannedVehicleTransferFinder.getPlannedVehicleTransfer(event.plannedVehicleTransferKey)
            } catch (exception: PlannedVehicleTransferNotFoundException) {
                logger.warn(
                    "Cannot publish vehicle migration data, as plannedVehicleTransfer is missing, vehicleId: ${event.vehicleId}",
                )
                return
            }
        val depreciationRelevantCostCenterId = plannedVehicleTransfer.depreciationRelevantCostCenterId
        val usingCostCenter = plannedVehicleTransfer.usingCostCenter
        val vehicleUsageId = plannedVehicleTransfer.vehicleUsage

        prepareAndPublishVehicleMigrationData(
            event.vehicleId,
            depreciationRelevantCostCenterId,
            usingCostCenter,
            vehicleUsageId,
            plannedVehicleTransfer.vehicleResponsiblePerson,
        )
    }

    private fun handleVehicleTransferEvent(event: VehicleTransferEvent) {
        val vehicleTransfer =
            try {
                vehicleTransferFinder.getVehicleTransfer(event.vehicleTransferKey)
            } catch (exception: VehicleTransferNotFoundException) {
                logger.warn(
                    "Cannot publish vehicle migration data, as vehicleTransfer is missing, vehicleId: ${event.vehicleId}",
                )
                return
            }
        val depreciationRelevantCostCenterId = vehicleTransfer.depreciationRelevantCostCenterId
        val usingCostCenter = vehicleTransfer.usingCostCenter
        val vehicleUsageId = vehicleTransfer.vehicleUsage

        prepareAndPublishVehicleMigrationData(
            event.vehicleId,
            depreciationRelevantCostCenterId,
            usingCostCenter,
            vehicleUsageId,
            vehicleTransfer.vehicleResponsiblePerson,
        )
    }

    private fun prepareAndPublishVehicleMigrationData(
        vehicleId: UUID,
        depreciationRelevantCostCenterId: VTCostCenterId?,
        usingCostCenter: CostCenterDescription?,
        vehicleUsageId: VTVehicleUsageId?,
        vehicleResponsiblePerson: EmployeeNumber?,
    ) {
        val vehicleDto = readVehicleByVehicleId.readVehicleById(vehicleId)
        if (vehicleDto?.vin == null || vehicleDto.model?.description == null) {
            logger.warn("Cannot publish vehicle migration data as either vehicle, vin or model is missing, vehicleId: $vehicleId")
            return
        }
        if (vehicleDto.model.vehicleType == null) {
            logger.warn("Cannot publish vehicle migration data as vehicle type is missing, vin: ${vehicleDto.vin}")
            return
        }
        if (vehicleDto.model.manufacturer == null) {
            logger.warn("Cannot publish vehicle migration data as manufacturer is missing, vin: ${vehicleDto.vin}")
            return
        }
        if (depreciationRelevantCostCenterId == null) {
            logger.warn("Cannot publish vehicle migration data as depreciationRelevantCostCenterId is missing, vin: ${vehicleDto.vin}")
            return
        }
        if (vehicleUsageId == null) {
            logger.warn("Cannot publish vehicle migration data as vehicleUsageId is missing, vin: ${vehicleDto.vin}")
            return
        }
        val depreciationRelevantCostCenter =
            try {
                costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
            } catch (exception: CostCenterNotFoundException) {
                logger.warn(
                    "Cannot publish vehicle migration data as depreciationRelevantCostCenter is missing, vin: ${vehicleDto.vin}, " +
                        "depreciationRelevantCostCenterId: ${depreciationRelevantCostCenterId.value}, ${exception.message}",
                )
                return
            }
        val vehicleUsage =
            try {
                vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
            } catch (exception: VehicleUsageNotFoundException) {
                logger.warn(
                    "Cannot publish vehicle migration data as vehicleUsage is missing, vin: ${vehicleDto.vin}, " +
                        "vehicleUsageId: ${vehicleUsageId.value}, ${exception.message}",
                )
                return
            }

        val vehicleRegistrationOrder = readRegistration.getLatestOrderBy(vehicleDto.id).data
        val licensePlate = vehicleRegistrationOrder?.licencePlate?.getOrNull()
        val firstRegistrationDate =
            vehicleRegistrationOrder
                ?.firstRegistrationDate
                ?.getOrNull()
                ?.toOffsetDateTime()

        val vehicleResponsiblePersonDetails = findVehiclePersonDetail(vehicleResponsiblePerson)
        if (vehicleResponsiblePersonDetails == null) {
            logger.info(
                "Cannot find vehicle responsible person for vehicle migration, vin: ${vehicleDto.vin}, sending vehicle responsible person as null",
            )
        }

        /**
         *  vehicle is considered active only if vehicle status is valid AND
         *  vehicleResponsiblePersonDetails is not null
         */
        val isVehicleStatusValidForMigration = isVehicleStatusValidForMigration(vehicleDto.status)
        val isVehicleActive = isVehicleStatusValidForMigration && vehicleResponsiblePersonDetails != null

        val fleetInformation = determineFleetInformation(vehicleDto.status)

        val leasingArt =
            LeasingArt.of(
                vehicleUsage = vehicleUsage.usage,
                vehicleType = vehicleDto.model.vehicleType,
                manufacturer = vehicleDto.model.manufacturer,
            )

        val vehicleMigration =
            VehicleMigration(
                vehicle =
                    Vehicle(
                        vin = vehicleDto.vin,
                        vguid = vehicleDto.vguid,
                        model = vehicleDto.model.description,
                        licensePlate = licensePlate,
                        depreciationRelevantCostCenter = depreciationRelevantCostCenter.description,
                        usingCostCenter = usingCostCenter,
                        vehicleUsage = vehicleUsage.usage,
                        isActive = isVehicleActive,
                        fleetInformation = fleetInformation,
                        firstRegistrationDate = firstRegistrationDate,
                        vehicleType = vehicleDto.model.vehicleType,
                        manufacturer = vehicleDto.model.manufacturer,
                        leasingArt = leasingArt,
                    ),
                vehicleResponsiblePerson =
                    vehicleResponsiblePersonDetails?.let {
                        VehicleResponsiblePerson(
                            firstName = it.firstName,
                            lastName = it.lastName,
                            employeeNumber = EmployeeNumber(it.employeeNumber),
                            email = it.companyEmail,
                        )
                    },
            )

        publishVehicle(vehicleMigration)
    }

    private fun findVehiclePersonDetail(vehicleResponsiblePerson: EmployeeNumber?): VehiclePersonDetail? {
        if (vehicleResponsiblePerson == null) {
            return null
        }
        return try {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(vehicleResponsiblePerson.value)
        } catch (exception: ReadVehiclePersonException) {
            logger.warn(
                "Cannot fetch vehicle responsible person for vehicle migration, vehicleResponsiblePerson: ${vehicleResponsiblePerson.value}, ${exception.message}",
            )
            null
        }
    }

    private fun isVehicleStatusValidForMigration(status: String?) = validStatus.any { it.name == status }

    private fun determineFleetInformation(status: String?): FleetInformation =
        if (preUsageFleet.map { it.name }.contains(status)) {
            FleetInformation.PRE_USAGE
        } else if (postUsageFleet.map { it.name }.contains(status)) {
            FleetInformation.POST_USAGE
        } else if (usageFleet.map { it.name }.contains(status)) {
            FleetInformation.USAGE
        } else {
            FleetInformation.NONE
        }

    private fun publishVehicle(vehicleMigration: VehicleMigration) {
        val vehicleMigrationDto = vehicleMigration.toVehicleMigrationDto()
        logger.info("Publishing vehicle migration data for vin: ${vehicleMigrationDto.vehicle.vin}")
        publishVehicleUseCase.publishVehicle(vehicleMigrationDto)
    }

    companion object {
        val validStatus =
            setOf(
                VehicleStatus.S096,
                VehicleStatus.S094,
                VehicleStatus.S100,
                VehicleStatus.S120,
                VehicleStatus.S130,
                VehicleStatus.S140,
                VehicleStatus.S200,
                VehicleStatus.S290,
                VehicleStatus.S300,
                VehicleStatus.S310,
                VehicleStatus.S320,
                VehicleStatus.S330,
                VehicleStatus.S340,
                VehicleStatus.S350,
                VehicleStatus.S360,
                VehicleStatus.S600,
                VehicleStatus.S650,
                VehicleStatus.S680,
            )
        val preUsageFleet =
            setOf(
                VehicleStatus.S096,
                VehicleStatus.S094,
                VehicleStatus.S100,
                VehicleStatus.S120,
                VehicleStatus.S130,
                VehicleStatus.S140,
            )
        val usageFleet = setOf(VehicleStatus.S200)
        val postUsageFleet =
            setOf(
                VehicleStatus.S290,
                VehicleStatus.S300,
                VehicleStatus.S310,
                VehicleStatus.S320,
                VehicleStatus.S330,
                VehicleStatus.S340,
                VehicleStatus.S350,
                VehicleStatus.S360,
                VehicleStatus.S600,
                VehicleStatus.S650,
                VehicleStatus.S680,
            )
        private val logger = LoggerFactory.getLogger(DMSVehicleMigrationApplicationService::class.java)
    }
}

data class VehicleMigration(
    val vehicle: Vehicle,
    val vehicleResponsiblePerson: VehicleResponsiblePerson?,
)

data class Vehicle(
    val vin: String,
    val vguid: String?,
    val model: String,
    val depreciationRelevantCostCenter: CostCenterDescription,
    val usingCostCenter: CostCenterDescription?,
    val vehicleUsage: String,
    val isActive: Boolean,
    val firstRegistrationDate: OffsetDateTime?,
    val fleetInformation: FleetInformation,
    val licensePlate: String?,
    val vehicleType: VehicleType,
    val manufacturer: String,
    val leasingArt: LeasingArt,
)

data class VehicleResponsiblePerson(
    val employeeNumber: EmployeeNumber,
    val firstName: String,
    val lastName: String,
    val email: String,
)

enum class FleetInformation(
    val description: String,
) {
    PRE_USAGE("Vorlauf"),
    POST_USAGE("Nachlauf"),
    USAGE("Usage"),
    NONE("Inactive"),
}
