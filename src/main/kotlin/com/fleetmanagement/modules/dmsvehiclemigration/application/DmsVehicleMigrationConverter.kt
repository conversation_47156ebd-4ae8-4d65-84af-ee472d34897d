/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.dmsvehiclemigration.application

import com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka.model.VehicleMigrationDto
import com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka.model.VehicleMigrationVehicleDto
import com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka.model.VehicleMigrationVehicleResponsiblePersonDto

fun VehicleMigration.toVehicleMigrationDto(): VehicleMigrationDto =
    VehicleMigrationDto(
        vehicle = this.vehicle.toVehicleMigrationVehicleDto(),
        vehicleResponsiblePerson = this.vehicleResponsiblePerson?.toVehicleMigrationVehicleResponsiblePersonDto(),
    )

fun Vehicle.toVehicleMigrationVehicleDto(): VehicleMigrationVehicleDto =
    VehicleMigrationVehicleDto(
        vin = this.vin,
        vguid = this.vguid,
        model = this.model,
        licensePlate = this.licensePlate,
        vehicleUsage = this.vehicleUsage,
        depreciationRelevantCostCenter = this.depreciationRelevantCostCenter.value,
        usingCostCenter = this.usingCostCenter?.value,
        isActive = this.isActive,
        fleetInformation = this.fleetInformation.toVehicleMigrationDtoFleetInformation(),
        firstRegistrationDate = this.firstRegistrationDate,
        vehicleType = this.vehicleType.name,
        manufacturer = this.manufacturer,
        leasingArt = this.leasingArt.id,
    )

fun VehicleResponsiblePerson.toVehicleMigrationVehicleResponsiblePersonDto(): VehicleMigrationVehicleResponsiblePersonDto =
    VehicleMigrationVehicleResponsiblePersonDto(
        employeeNumber = this.employeeNumber.value,
        firstName = this.firstName,
        lastName = this.lastName,
        email = this.email,
    )

fun FleetInformation.toVehicleMigrationDtoFleetInformation() =
    when (this) {
        FleetInformation.PRE_USAGE -> VehicleMigrationVehicleDto.FleetInformation.PRE_USAGE
        FleetInformation.POST_USAGE -> VehicleMigrationVehicleDto.FleetInformation.POST_USAGE
        FleetInformation.USAGE -> VehicleMigrationVehicleDto.FleetInformation.USAGE
        FleetInformation.NONE -> VehicleMigrationVehicleDto.FleetInformation.NONE
    }
