/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.api

import com.aspose.words.Document

/**
 * provides document filled with data
 * @return Aspose Document containing the filled template in docx format
 * @throws GenerateDocumentException when form field is not present in template
 */

fun interface GenerateDocument {
    fun generateDocument(
        templateName: String,
        data: Map<String, String>,
    ): Document
}

class GenerateDocumentException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()
