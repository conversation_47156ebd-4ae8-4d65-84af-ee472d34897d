/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.api

import com.fleetmanagement.modules.documentgeneration.api.dtos.DocumentGenerationResponse
import java.util.UUID

/**
 * provides green insurance card in PDF format for given vehicleIds along with list of errors (if any)
 * @throws GenerateGreenInsuranceCardException in case of exceptions
 */
fun interface GenerateGreenInsuranceCard {
    fun generateGreenInsuranceCard(vehicleIds: List<UUID>): DocumentGenerationResponse
}

class GenerateGreenInsuranceCardException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()
