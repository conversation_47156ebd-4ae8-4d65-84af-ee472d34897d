/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.documentgeneration.api

import com.fleetmanagement.modules.documentgeneration.api.dtos.DocumentGenerationResponse
import java.util.*

/**
 * provides power of attorney in PDF format for given vehicleIds along with list of errors (if any)
 * @throws GeneratePowerOfAttorneyException in case of exceptions
 */
interface GeneratePowerOfAttorney {
    fun generatePowerOfAttorney(vehicleIds: List<UUID>): DocumentGenerationResponse
}

class GeneratePowerOfAttorneyException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()
