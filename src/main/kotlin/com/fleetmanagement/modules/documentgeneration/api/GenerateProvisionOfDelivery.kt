/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.api

import org.springframework.core.io.ByteArrayResource
import java.time.OffsetDateTime

/**
 * provides provision for delivery document in PDF format for given planned delivery date
 * @throws GenerateProvisionOfDeliveryDocumentException in case of exceptions
 * @throws NoScheduledTransfersException in case there are no planned vehicle transfers for the date supplied
 */
fun interface GenerateProvisionOfDelivery {
    fun generateProvisionOfDelivery(plannedDeliveryDate: OffsetDateTime): ByteArrayResource
}

class GenerateProvisionOfDeliveryDocumentException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()

class NoScheduledTransfersException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()
