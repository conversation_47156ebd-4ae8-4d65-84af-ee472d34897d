/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.api

import org.springframework.core.io.ByteArrayResource
import java.util.UUID

/**
 * provides TUV Document in PDF format for given vehicleId
 * @throws GenerateTUVDocumentException in case of exceptions
 */
fun interface GenerateTUVDocument {
    fun generateTUVDocument(vehicleId: UUID): ByteArrayResource
}

class GenerateTUVDocumentException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()
