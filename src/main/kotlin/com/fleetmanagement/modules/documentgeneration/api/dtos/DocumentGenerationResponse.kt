/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.documentgeneration.api.dtos

import org.springframework.core.io.ByteArrayResource
import java.util.*

data class DocumentGenerationResponse(
    val data: ByteArrayResource? = null,
    val errors: List<ErrorDetail>,
)

data class ErrorDetail(
    val vehicleId: UUID,
    val vin: String?,
    val message: String,
)
