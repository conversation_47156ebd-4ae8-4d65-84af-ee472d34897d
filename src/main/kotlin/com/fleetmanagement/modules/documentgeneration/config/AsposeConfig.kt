/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.config

import com.aspose.words.License
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.core.io.ClassPathResource
import com.aspose.barcode.License as BarcodeLicense

@Configuration
class AsposeConfig {
    @Bean
    fun asposeWordsLicense(): License {
        val license = License()
        val licenseStream = ClassPathResource("documentgeneration/license/Aspose.Words.Java.lic").inputStream
        license.setLicense(licenseStream)
        return license
    }

    @Bean
    fun asposeBarcodeLicense(): BarcodeLicense {
        val barcodeLicense = BarcodeLicense()
        val licenseStream = ClassPathResource("documentgeneration/license/Aspose.BarCode.Java.lic").inputStream
        barcodeLicense.setLicense(licenseStream)
        return barcodeLicense
    }
}
