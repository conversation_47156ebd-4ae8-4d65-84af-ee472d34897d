/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features

import com.aspose.barcode.generation.AutoSizeMode
import com.aspose.barcode.generation.BarCodeImageFormat
import com.aspose.barcode.generation.BarcodeGenerator
import com.aspose.barcode.generation.EncodeTypes
import com.aspose.words.Document
import com.aspose.words.DocumentBuilder
import com.aspose.words.FindReplaceOptions
import com.aspose.words.IReplacingCallback
import com.aspose.words.Paragraph
import com.aspose.words.ReplaceAction
import com.aspose.words.ReplacingArgs
import com.aspose.words.Row
import com.aspose.words.Run
import com.aspose.words.Table
import com.aspose.words.Underline
import org.springframework.core.io.ClassPathResource
import org.springframework.stereotype.Component
import java.io.ByteArrayOutputStream

@Component
class FillTemplateService {
    /**
     * fills template with provided data
     * @return A ByteArrayResource containing the filled template in docx format
     * @throws FormFieldNotFoundException when form field is not present in template
     */
    fun fillTemplate(
        templateName: String,
        data: Map<String, String>,
        barcodeText: String? = null,
    ): Document {
        val template = ClassPathResource("documentgeneration/templates/$templateName")
        val document = Document(template.inputStream)

        data.forEach { (key, value) ->
            val field = document.range.formFields.singleOrNull { it.name == key }
            if (field == null) {
                throw FormFieldNotFoundException("Cannot find form field $key in template $templateName")
            }

            field.result = value
        }

        if (barcodeText.isNullOrEmpty()) {
            return document
        }

        val barcodeByteArray = generateBarcode(barcodeText)
        val options =
            FindReplaceOptions().apply {
                replacingCallback = ReplaceWithBarcodeCallback(barcodeByteArray)
            }
        document.range.replace(BARCODE_PLACEHOLDER, "", options)
        return document
    }

    /**
     * fills document table with the tableData provided
     */
    fun fillTable(
        table: Table,
        tableData: List<List<String>>,
        document: Document,
    ) {
        val templateRow = table.rows[0].deepClone(true) as Row
        tableData.forEach {
            val newRow = templateRow.deepClone(true) as Row
            for ((cellIndex, value) in it.withIndex()) {
                val cell = newRow.cells[cellIndex]
                val paragraph = cell.firstParagraph
                paragraph.runs.clear()
                val newRun = Run(document, value)
                val headerRun =
                    table.rows[0]
                        .cells[cellIndex]
                        .firstParagraph.runs
                        .firstOrNull()
                headerRun?.font?.let {
                    newRun.font.apply {
                        bold = false
                        size = it.size
                        color = it.color
                    }
                }

                paragraph.appendChild(newRun)
            }
            table.appendChild(newRow)
        }
    }

    /**
     * appends hyperlink to the paragraph
     */
    fun appendHyperlinkInline(
        paragraph: Paragraph,
        displayText: String,
        url: String,
        document: Document,
    ) {
        DocumentBuilder(document).apply {
            moveTo(paragraph)
            write(" ")
            font.clearFormatting()
            font.color = java.awt.Color.BLUE
            font.underline = Underline.SINGLE
            insertHyperlink(displayText, url, false)
        }
    }

    private fun generateBarcode(barcodeText: String): ByteArray {
        val generator =
            BarcodeGenerator(EncodeTypes.CODE_128, barcodeText).apply {
                /**
                 * sets barcode image size as per 55x30mm key flag label. If there is a need
                 * to generate different kinds of barcode in the future, we should parametrize the method with required size
                 */
                parameters.autoSizeMode = AutoSizeMode.NEAREST
                parameters.imageHeight.millimeters = BARCODE_IMAGE_HEIGHT
                parameters.imageWidth.millimeters = BARCODE_IMAGE_WIDTH
                parameters.barcode.padding.left.millimeters - BARCODE_PADDING
                parameters.barcode.padding.right.millimeters = BARCODE_PADDING
                parameters.barcode.padding.top.millimeters = BARCODE_PADDING
                parameters.barcode.padding.bottom.millimeters = BARCODE_PADDING
                parameters.resolution = BARCODE_RESOLUTION
            }
        val barcodeStream = ByteArrayOutputStream()
        generator.save(barcodeStream, BarCodeImageFormat.PNG)
        return barcodeStream.toByteArray()
    }

    class ReplaceWithBarcodeCallback(
        private val barcodeBytes: ByteArray,
    ) : IReplacingCallback {
        override fun replacing(e: ReplacingArgs): Int {
            val builder = DocumentBuilder(e.matchNode.document as Document?)
            builder.moveTo(e.matchNode)
            builder.insertImage(barcodeBytes)
            return ReplaceAction.REPLACE
        }
    }

    companion object {
        private const val BARCODE_PLACEHOLDER = "[@BARCODE]"
        private const val BARCODE_IMAGE_WIDTH = 60.0f
        private const val BARCODE_IMAGE_HEIGHT = 10.0f
        private const val BARCODE_PADDING = 0f
        private const val BARCODE_RESOLUTION = 200f
    }
}

class FormFieldNotFoundException(
    override val message: String,
    override val cause: Throwable? = null,
) : NoSuchElementException()
