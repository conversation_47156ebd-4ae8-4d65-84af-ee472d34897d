/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generategreeninsurancecard

import com.fleetmanagement.modules.vehicleregistration.api.dto.LatestRegistrationOrder

fun LatestRegistrationOrder.toGreenInsuranceCardTemplateData(): Map<String, String> {
    /**
     * as per business (Luisa) if data fields are null, we treat them as exception
     */
    val lastRegistrationDate = requireNotNull(this.lastRegistrationDate) { "LastRegistrationDate is missing" }
    val licensePlate = requireNotNull(this.licencePlate) { "LicensePlate is missing" }
    val sfme = requireNotNull(this.sfme) { "SFME is missing" }
    val registrationType =
        RegistrationType.fromNumber(
            requireNotNull(registrationType) { "registrationType is missing" },
        )

    /**
     * validUntill is calculated from lastRegistrationDate using below logic:
     * if smfe is true, registration is either FirstRegistration or Reregistration, it is valid for 1 more year
     * if smfe is false, registrationType is FirstRegistration, it is valid till end of 3rd year
     * if smfe is false, registrationType is Reregistration, it is valid for 2 more years
     */
    val validUntil =
        when {
            sfme ->
                lastRegistrationDate.plusYears(1).minusDays(1)

            registrationType == RegistrationType.FIRST_REGISTRATION ->
                lastRegistrationDate.plusYears(3).withMonth(12).withDayOfMonth(31)

            else -> lastRegistrationDate.plusYears(2).minusDays(1)
        }

    return mapOf(
        GICTemplateFields.VALIDITY_START_DAY.fieldName to lastRegistrationDate.dayOfMonth.toString(),
        GICTemplateFields.VALIDITY_START_MONTH.fieldName to lastRegistrationDate.month.value.toString(),
        GICTemplateFields.VALIDITY_START_YEAR.fieldName to lastRegistrationDate.year.toString(),
        GICTemplateFields.VALIDITY_END_DAY.fieldName to validUntil.dayOfMonth.toString(),
        GICTemplateFields.VALIDITY_END_MONTH.fieldName to validUntil.month.value.toString(),
        GICTemplateFields.VALIDITY_END_YEAR.fieldName to validUntil.year.toString(),
        GICTemplateFields.LICENSE_PLATE.fieldName to licensePlate,
    )
}

enum class GICTemplateFields(
    val fieldName: String,
) {
    VALIDITY_START_DAY("validityStartDay"),
    VALIDITY_START_MONTH("validityStartMonth"),
    VALIDITY_START_YEAR("validityStartYear"),
    VALIDITY_END_DAY("validityEndDay"),
    VALIDITY_END_MONTH("validityEndMonth"),
    VALIDITY_END_YEAR("validityEndYear"),
    LICENSE_PLATE("licensePlate"),
}

enum class RegistrationType(
    val type: Int,
) {
    FIRST_REGISTRATION(1),
    REREGISTRATION(2),
    ;

    companion object {
        fun fromNumber(number: Int): RegistrationType =
            entries.singleOrNull { number == it.type }
                ?: throw IllegalArgumentException("Invalid Registration Type $number")
    }
}
