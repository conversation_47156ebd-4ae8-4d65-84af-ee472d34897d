/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generategreeninsurancecard

import com.aspose.words.Document
import com.aspose.words.ImportFormatMode
import com.aspose.words.SaveFormat
import com.aspose.words.SaveOptions
import com.fleetmanagement.modules.documentgeneration.api.GenerateGreenInsuranceCard
import com.fleetmanagement.modules.documentgeneration.api.GenerateGreenInsuranceCardException
import com.fleetmanagement.modules.documentgeneration.api.dtos.DocumentGenerationResponse
import com.fleetmanagement.modules.documentgeneration.api.dtos.ErrorDetail
import com.fleetmanagement.modules.documentgeneration.features.FillTemplateService
import com.fleetmanagement.modules.documentgeneration.features.FormFieldNotFoundException
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Component
import java.io.ByteArrayOutputStream
import java.util.*

@Component
class GenerateGreenInsuranceCardService(
    private val fillTemplateService: FillTemplateService,
    private val readRegistrationOrder: ReadRegistrationOrder,
) : GenerateGreenInsuranceCard {
    override fun generateGreenInsuranceCard(vehicleIds: List<UUID>): DocumentGenerationResponse {
        /**
         * vehicle registration service will either throw an exception or return response
         * containing all vehicleIds sent in the request with null data fields in case of vehicle related exceptions
         */
        val registrationOrder = readRegistrationOrder.getLatestOrdersBy(vehicleIds)
        val document = Document()
        document.removeAllChildren()
        var isDocumentAppended = false
        val errorsList = mutableListOf<ErrorDetail>()
        registrationOrder.data.forEach {
            try {
                val templateData: Map<String, String> = it.toGreenInsuranceCardTemplateData()
                val filledGICDocument = fillTemplateService.fillTemplate(TEMPLATE, templateData)
                document.appendDocument(filledGICDocument, ImportFormatMode.KEEP_SOURCE_FORMATTING)
                isDocumentAppended = true
            } catch (exception: IllegalArgumentException) {
                errorsList.add(
                    ErrorDetail(
                        vehicleId = it.vehicleId,
                        vin = it.vin,
                        message = "Error generating green insurance card. ${exception.message}",
                    ),
                )
            } catch (exception: FormFieldNotFoundException) {
                throw GenerateGreenInsuranceCardException(
                    message = "Error generating green insurance card. ${exception.message}",
                    cause = exception,
                )
            }
        }
        if (isDocumentAppended) {
            val outputStream = ByteArrayOutputStream()
            document.save(outputStream, SaveOptions.createSaveOptions(SaveFormat.PDF))
            return DocumentGenerationResponse(
                data = ByteArrayResource(outputStream.toByteArray()),
                errors = errorsList,
            )
        } else {
            return DocumentGenerationResponse(
                errors = errorsList,
            )
        }
    }

    companion object {
        private const val TEMPLATE = "GreenInsuranceCard.docx"
    }
}
