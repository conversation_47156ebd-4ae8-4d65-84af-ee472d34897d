/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generatekeyflag

fun KeyFlagVehicleData.toKeyFlagTemplateData(): Map<String, String> {
    /**
     * as per business (Luisa) if data fields are null, we treat them empty string
     */
    val licensePlate = this.licensePlate ?: ""
    val model = this.vehicleData?.model?.description ?: ""
    val exteriorColor = vehicleData?.color?.exteriorDescription ?: ""

    return mapOf(
        KeyFlagTemplateFields.LICENSE_PLATE.fieldName to licensePlate,
        KeyFlagTemplateFields.VEHICLE_MODEL.fieldName to model,
        KeyFlagTemplateFields.EXTERIOR_COLOR.fieldName to exteriorColor,
    )
}

enum class KeyFlagTemplateFields(
    val fieldName: String,
) {
    LICENSE_PLATE("licensePlate"),
    VEHICLE_MODEL("vehicleModel"),
    EXTERIOR_COLOR("exteriorColor"),
}
