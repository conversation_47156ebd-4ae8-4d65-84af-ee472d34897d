/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generatekeyflag

import com.aspose.words.Document
import com.aspose.words.ImportFormatMode
import com.aspose.words.SaveFormat
import com.aspose.words.SaveOptions
import com.fleetmanagement.modules.documentgeneration.api.GenerateKeyFlag
import com.fleetmanagement.modules.documentgeneration.api.GenerateKeyFlagException
import com.fleetmanagement.modules.documentgeneration.api.dtos.DocumentGenerationResponse
import com.fleetmanagement.modules.documentgeneration.api.dtos.ErrorDetail
import com.fleetmanagement.modules.documentgeneration.features.FillTemplateService
import com.fleetmanagement.modules.documentgeneration.features.FormFieldNotFoundException
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Component
import java.io.ByteArrayOutputStream
import java.util.UUID

@Component
class GenerateKeyFlagService(
    private val fillTemplateService: FillTemplateService,
    private val readRegistrationOrder: ReadRegistrationOrder,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
) : GenerateKeyFlag {
    override fun generateKeyFlag(vehicleIds: List<UUID>): DocumentGenerationResponse {
        val registrationOrder = readRegistrationOrder.getLatestOrdersBy(vehicleIds)

        val keyFlagData =
            vehicleIds.map { vehicleId ->
                KeyFlagVehicleData(
                    vehicleId = vehicleId,
                    vehicleData = readVehicleByVehicleId.readVehicleById(vehicleId),
                    licensePlate = registrationOrder.data.singleOrNull { it.vehicleId == vehicleId }?.licencePlate,
                )
            }

        val document =
            Document().apply {
                removeAllChildren()
            }
        var isDocumentAppended = false
        val errorsList = mutableListOf<ErrorDetail>()
        keyFlagData.forEach {
            try {
                val filledKeyFlagDocument = fillKeyFlagDocument(it)
                document.appendDocument(filledKeyFlagDocument, ImportFormatMode.KEEP_SOURCE_FORMATTING)
                isDocumentAppended = true
            } catch (exception: IllegalArgumentException) {
                errorsList.add(
                    ErrorDetail(
                        vehicleId = it.vehicleId,
                        vin = it.vehicleData?.vin,
                        message = "Error generating key flag document. ${exception.message}",
                    ),
                )
            } catch (exception: FormFieldNotFoundException) {
                throw GenerateKeyFlagException(
                    message = "Error generating key flag document. ${exception.message}",
                    cause = exception,
                )
            }
        }
        if (isDocumentAppended) {
            val outputStream = ByteArrayOutputStream()
            document.save(outputStream, SaveOptions.createSaveOptions(SaveFormat.PDF))
            return DocumentGenerationResponse(
                data = ByteArrayResource(outputStream.toByteArray()),
                errors = errorsList,
            )
        } else {
            return DocumentGenerationResponse(
                errors = errorsList,
            )
        }
    }

    /**
     * fills aspose document with text fields and barcode image
     */
    private fun fillKeyFlagDocument(keyFlagData: KeyFlagVehicleData): Document {
        val templateData = keyFlagData.toKeyFlagTemplateData()
        val vin = requireNotNull(keyFlagData.vehicleData?.vin) { "Vehicle Vin is missing" }
        val filledKeyFlagDocument = fillTemplateService.fillTemplate(TEMPLATE, templateData, vin)
        return filledKeyFlagDocument
    }

    companion object {
        private const val TEMPLATE = "KeyFlag.docx"
    }
}

data class KeyFlagVehicleData(
    val vehicleId: UUID,
    val vehicleData: VehicleDTO?,
    val licensePlate: String?,
)
