/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generatepowerofattorney

fun PowerOfAttorneyVehicleData.toPowerOfAttorneyTemplateData(): Map<String, String> {
    /**
     * as per business (Luisa) if data fields are null, we treat them as exception
     */
    val vehicleData = requireNotNull(this.vehicleData) { "Vehicle Data is missing" }
    val vin = requireNotNull(vehicleData.vin) { "Vehicle Vin is missing" }
    val model = requireNotNull(vehicleData.model?.description) { "Vehicle Model is missing" }
    val brand = requireNotNull(vehicleData.model?.manufacturer) { "Vehicle Brand is missing" }

    val vehiclePersonDetails = requireNotNull(this.vehiclePersonDetails) { "Vehicle Person detail is missing" }
    val name = vehiclePersonDetails.firstName + " " + vehiclePersonDetails.lastName
    val street = requireNotNull(vehiclePersonDetails.street) { "Vehicle Person: Street is missing" }
    val zip = requireNotNull(vehiclePersonDetails.postalCode) { "Vehicle Person: postalCode is missing" }
    val city = requireNotNull(vehiclePersonDetails.city) { "Vehicle Person: city is missing" }
    val country = requireNotNull(vehiclePersonDetails.country) { "Vehicle Person: country is missing" }

    val licensePlate = requireNotNull(this.licensePlate) { "LicensePlate is missing" }

    return mapOf(
        POATemplateFields.RECIPIENT_NAME.fieldName to name,
        POATemplateFields.RECIPIENT_STREET.fieldName to street,
        POATemplateFields.RECIPIENT_ZIP.fieldName to zip,
        POATemplateFields.RECIPIENT_CITY.fieldName to city,
        POATemplateFields.RECIPIENT_COUNTRY.fieldName to country,
        POATemplateFields.RESPONSIBLE_NAME.fieldName to name,
        POATemplateFields.RESPONSIBLE_STREET.fieldName to street,
        POATemplateFields.RESPONSIBLE_ZIP.fieldName to zip,
        POATemplateFields.RESPONSIBLE_CITY.fieldName to city,
        POATemplateFields.RESPONSIBLE_COUNTRY.fieldName to country,
        POATemplateFields.VEHICLE_BRAND.fieldName to brand,
        POATemplateFields.VEHICLE_MODEL.fieldName to model,
        POATemplateFields.VEHICLE_VIN.fieldName to vin,
        POATemplateFields.LICENSE_PLATE.fieldName to licensePlate,
    )
}

enum class POATemplateFields(
    val fieldName: String,
) {
    RECIPIENT_NAME("recipientName"),
    RECIPIENT_STREET("recipientStreet"),
    RECIPIENT_ZIP("recipientZip"),
    RECIPIENT_CITY("recipientCity"),
    RECIPIENT_COUNTRY("recipientCountry"),
    RESPONSIBLE_NAME("responsibleName"),
    RESPONSIBLE_STREET("responsibleStreet"),
    RESPONSIBLE_ZIP("responsibleZip"),
    RESPONSIBLE_CITY("responsibleCity"),
    RESPONSIBLE_COUNTRY("responsibleCountry"),
    VEHICLE_BRAND("vehicleBrand"),
    VEHICLE_MODEL("vehicleModel"),
    VEHICLE_VIN("vin"),
    LICENSE_PLATE("licensePlate"),
}
