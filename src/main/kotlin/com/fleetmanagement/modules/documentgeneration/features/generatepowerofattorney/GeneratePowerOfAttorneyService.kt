/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generatepowerofattorney

import com.aspose.words.BreakType
import com.aspose.words.Document
import com.aspose.words.DocumentBuilder
import com.aspose.words.ImportFormatMode
import com.aspose.words.SaveFormat
import com.aspose.words.SaveOptions
import com.fleetmanagement.modules.documentgeneration.api.GeneratePowerOfAttorney
import com.fleetmanagement.modules.documentgeneration.api.GeneratePowerOfAttorneyException
import com.fleetmanagement.modules.documentgeneration.api.dtos.DocumentGenerationResponse
import com.fleetmanagement.modules.documentgeneration.api.dtos.ErrorDetail
import com.fleetmanagement.modules.documentgeneration.features.FillTemplateService
import com.fleetmanagement.modules.documentgeneration.features.FormFieldNotFoundException
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadCurrentVehiclePersonDetailByVehicleId
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Component
import java.io.ByteArrayOutputStream
import java.util.*

@Component
class GeneratePowerOfAttorneyService(
    private val fillTemplateService: FillTemplateService,
    private val readRegistrationOrder: ReadRegistrationOrder,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val readCurrentVehiclePersonDetailByVehicleId: ReadCurrentVehiclePersonDetailByVehicleId,
) : GeneratePowerOfAttorney {
    override fun generatePowerOfAttorney(vehicleIds: List<UUID>): DocumentGenerationResponse {
        val registrationOrder = readRegistrationOrder.getLatestOrdersBy(vehicleIds)

        val powerOfAttorneyData =
            vehicleIds.map { vehicleId ->
                PowerOfAttorneyVehicleData(
                    vehicleId = vehicleId,
                    vehicleData = readVehicleByVehicleId.readVehicleById(vehicleId),
                    vehiclePersonDetails =
                        try {
                            readCurrentVehiclePersonDetailByVehicleId.readCurrentVehiclePersonDetailBy(vehicleId)
                        } catch (exception: ReadVehiclePersonException) {
                            null
                        },
                    licensePlate = registrationOrder.data.singleOrNull { it.vehicleId == vehicleId }?.licencePlate,
                )
            }
        var isDocumentAppended = false
        val errorsList = mutableListOf<ErrorDetail>()
        val document = Document()
        val documentBuilder = DocumentBuilder(document)
        document.removeAllChildren()

        powerOfAttorneyData.forEachIndexed { index, data ->
            try {
                val templateData = data.toPowerOfAttorneyTemplateData()
                val filledPOADocument = fillTemplateService.fillTemplate(TEMPLATE, templateData)
                document.appendDocument(filledPOADocument, ImportFormatMode.KEEP_SOURCE_FORMATTING)
                isDocumentAppended = true

                /**
                 * FPT1-999: If multiple documents are appended, there needs to be a page break. Else the pages will
                 * merge into the previous pages.
                 * The last page must not have a page break, else there would be a blank page.
                 */
                if (index < powerOfAttorneyData.size - 1) {
                    documentBuilder.moveToDocumentEnd()
                    documentBuilder.insertBreak(BreakType.PAGE_BREAK)
                }
            } catch (exception: IllegalArgumentException) {
                errorsList.add(
                    ErrorDetail(
                        vehicleId = data.vehicleId,
                        vin = data.vehicleData?.vin,
                        message = "Error generating power of attorney. ${exception.message}",
                    ),
                )
            } catch (exception: FormFieldNotFoundException) {
                throw GeneratePowerOfAttorneyException(
                    message = "Error generating power of attorney. ${exception.message}",
                    cause = exception,
                )
            }
        }
        if (isDocumentAppended) {
            val outputStream = ByteArrayOutputStream()
            document.save(outputStream, SaveOptions.createSaveOptions(SaveFormat.PDF))
            return DocumentGenerationResponse(
                data = ByteArrayResource(outputStream.toByteArray()),
                errors = errorsList,
            )
        } else {
            return DocumentGenerationResponse(
                errors = errorsList,
            )
        }
    }

    companion object {
        private const val TEMPLATE = "PowerOfAttorney.docx"
    }
}

data class PowerOfAttorneyVehicleData(
    val vehicleId: UUID,
    val vehicleData: VehicleDTO?,
    val vehiclePersonDetails: VehiclePersonDetail?,
    val licensePlate: String?,
)
