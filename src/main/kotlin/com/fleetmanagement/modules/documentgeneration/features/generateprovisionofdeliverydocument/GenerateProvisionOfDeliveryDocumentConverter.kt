/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generateprovisionofdeliverydocument

import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.format.DateTimeFormatter

fun ProvisionOfDeliveryTableRowData.toProvisionOfDeliveryTemplateTableRowData(): ProvisionOfDeliveryTemplateTableRowData =
    /**
     * as per business(Elena), all values are optional
     */

    ProvisionOfDeliveryTemplateTableRowData(
        data =
            listOf(
                transferData.plannedDeliveryDate?.toGermanDateFormat() ?: DEFAULT_VALUE,
                vehicleData?.status ?: DEFAULT_VALUE,
                vehicleResponsiblePersonDetails?.lastName ?: DEFAULT_VALUE,
                vehicleData?.vin ?: DEFAULT_VALUE,
                transferData.licensePlate ?: DEFAULT_VALUE,
                vehicleData?.returnInfo?.isUsedCar.toGermanText(),
                locationData?.compoundName ?: DEFAULT_VALUE,
                locationData?.parkingLot ?: DEFAULT_VALUE,
                transferData.desiredTireSet?.name ?: DEFAULT_VALUE,
                transferData.provisionForDeliveryComment ?: DEFAULT_VALUE,
            ),
    )

fun List<ProvisionOfDeliveryTemplateTableRowData>.toProvisionForDeliveryTemplateData() =
    ProvisionForDeliveryTemplateData(
        creationDateTime = LocalDateTime.now(ZoneId.of("Europe/Berlin")).toGermanDateTimeFormat(),
        deliveryListTableData = this.map { it.data },
    )

private fun Boolean?.toGermanText(): String =
    when (this) {
        true -> "Ja"
        false -> "Nein"
        null -> DEFAULT_VALUE
    }

/**
 * converts OffsetDateTime to LocalDate with Germany time zone.
 * This is done to ensure edge-cases e.g. OffsetDateTime is 22.07.2025 22:00 UTC,
 * it should be displayed as 23.07.2025 as per the LocalDate in pdf
 */
private fun OffsetDateTime.toGermanDateFormat(): String =
    this.atZoneSameInstant(ZoneId.of("Europe/Berlin")).format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))

private fun LocalDateTime.toGermanDateTimeFormat(): String = this.format(DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm"))

private const val DEFAULT_VALUE = ""

data class ProvisionForDeliveryTemplateData(
    val creationDateTime: String,
    val deliveryListTableData: List<List<String>>,
)

data class ProvisionOfDeliveryTemplateTableRowData(
    val data: List<String>,
)
