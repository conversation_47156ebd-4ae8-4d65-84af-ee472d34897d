/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generateprovisionofdeliverydocument

import com.aspose.words.*
import com.fleetmanagement.emhshared.USAGE_GROUP_DESCRIPTION_PERSON
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupFinder
import com.fleetmanagement.modules.documentgeneration.api.GenerateProvisionOfDelivery
import com.fleetmanagement.modules.documentgeneration.api.GenerateProvisionOfDeliveryDocumentException
import com.fleetmanagement.modules.documentgeneration.api.NoScheduledTransfersException
import com.fleetmanagement.modules.documentgeneration.features.FillTemplateService
import com.fleetmanagement.modules.documentgeneration.features.FormFieldNotFoundException
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehiclelocation.api.LastKnownLocation
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.domain.entities.PlannedVehicleTransfer
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Component
import java.io.ByteArrayOutputStream
import java.time.OffsetDateTime

@Component
class GenerateProvisionOfDeliveryDocumentService(
    private val plannedVehicleTransferFinder: PlannedVehicleTransferFinder,
    private val usageGroupFinder: UsageGroupFinder,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val fillTemplateService: FillTemplateService,
    private val lastKnownLocation: LastKnownLocation,
    private val readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber,
) : GenerateProvisionOfDelivery {
    override fun generateProvisionOfDelivery(plannedDeliveryDate: OffsetDateTime): ByteArrayResource {
        val plannedVehicleTransfers =
            plannedVehicleTransferFinder.findPlannedTransfersForProvisionForDelivery(plannedDeliveryDate)
        val usageGroups = usageGroupFinder.readAllUsageGroups()
        val listOfPlannedVehicleTransfers =
            plannedVehicleTransfers.filter { pvt ->
                pvt.usageGroup?.let { pvtUsageGroupId ->
                    usageGroups.singleOrNull { pvtUsageGroupId.value == it.id }?.description == USAGE_GROUP_DESCRIPTION_PERSON
                } ?: false
            }

        if (listOfPlannedVehicleTransfers.isEmpty()) {
            throw NoScheduledTransfersException(
                message = "Could not generate provision of delivery document. No planned vehicle transfer found.",
            )
        }
        val tableData =
            listOfPlannedVehicleTransfers.map { pvt ->
                val vehicle = readVehicleByVehicleId.readVehicleById(pvt.vehicleId)
                val locationData = lastKnownLocation.findLastKnownVehicleLocationBy(pvt.vehicleId)
                val vehicleResponsiblePersonDetails =
                    pvt.vehicleResponsiblePerson?.let {
                        try {
                            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(it.value)
                        } catch (_: ReadVehiclePersonException) {
                            null
                        }
                    }

                ProvisionOfDeliveryTableRowData(
                    vehicleData = vehicle,
                    locationData = locationData,
                    transferData = pvt,
                    vehicleResponsiblePersonDetails = vehicleResponsiblePersonDetails,
                ).toProvisionOfDeliveryTemplateTableRowData()
            }

        val provisionForDeliveryTemplateData = tableData.toProvisionForDeliveryTemplateData()

        val document = fillProvisionOfDeliveryDocument(provisionForDeliveryTemplateData)
        val outputStream = ByteArrayOutputStream()
        document.save(outputStream, SaveOptions.createSaveOptions(SaveFormat.PDF))
        return ByteArrayResource(outputStream.toByteArray())
    }

    private fun fillProvisionOfDeliveryDocument(templateData: ProvisionForDeliveryTemplateData): Document {
        val document =
            try {
                fillTemplateService.fillTemplate(
                    TEMPLATE_NAME,
                    mapOf(TEMPLATE_CREATION_DATE_TIME to templateData.creationDateTime),
                )
            } catch (exception: FormFieldNotFoundException) {
                throw GenerateProvisionOfDeliveryDocumentException(
                    message = "Error while generating provision of delivery document. ${exception.message}",
                    cause = exception,
                )
            }

        val tables = document.getChildNodes(NodeType.TABLE, true)
        fillTemplateService.fillTable(tables[0] as Table, templateData.deliveryListTableData, document)

        return document
    }

    companion object {
        private const val TEMPLATE_NAME = "ProvisionOfDeliveryDocument.docx"
        private const val TEMPLATE_CREATION_DATE_TIME = "creationDateTime"
    }
}

data class ProvisionOfDeliveryTableRowData(
    val vehicleData: VehicleDTO?,
    val locationData: VehicleLocation?,
    val transferData: PlannedVehicleTransfer,
    val vehicleResponsiblePersonDetails: VehiclePersonDetail?,
)
