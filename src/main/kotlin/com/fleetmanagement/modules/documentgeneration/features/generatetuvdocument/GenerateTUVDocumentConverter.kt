/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generatetuvdocument

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.jvm.optionals.getOrNull

fun TUVVehicleData.toTUVTemplateData(): TUVTemplateData {
    /**
     * as per business(Luisa), all values are optional except vin and will return empty strings wherever missing
     */
    val vin = requireNotNull(requireNotNull(vehicleData) { "VehicleData is missing" }.vin) { "Vehicle vin is missing" }

    val registrationOrdersRowData =
        registrationOrders.map { order ->
            listOf(
                order.registrationStatus?.getOrNull() ?: DEFAULT_VALUE,
                order.registrationType?.getOrNull()?.toString() ?: DEFAULT_VALUE,
                order.licencePlate?.getOrNull() ?: DEFAULT_VALUE,
                order.registrationDate?.getOrNull()?.toGermanDateFormat() ?: DEFAULT_VALUE,
                order.hsn?.getOrNull() ?: DEFAULT_VALUE,
                order.tsn?.getOrNull() ?: DEFAULT_VALUE,
                order.sfme?.getOrNull()?.toGermanText() ?: DEFAULT_VALUE,
                order.testVehicle?.getOrNull()?.toGermanText() ?: DEFAULT_VALUE,
                order.firstRegistrationDate?.getOrNull()?.toGermanDateFormat() ?: DEFAULT_VALUE,
            )
        }

    val vehicleOptionsRowData =
        if (vehicleData.options != null &&
            vehicleData.options.hasNonNull("current") &&
            vehicleData.options
                .get("current")
                .hasNonNull("individualOptions")
        ) {
            val options =
                vehicleData.options.get("current").get("individualOptions").map {
                    jacksonObjectMapper().treeToValue(it, Option::class.java)
                }
            options.map { option ->
                listOf(
                    option.type.takeIf { !it.isNullOrEmpty() } ?: DEFAULT_VALUE,
                    option.id.takeIf { !it.isNullOrEmpty() } ?: DEFAULT_VALUE,
                    option.orderType.takeIf { !it.isNullOrEmpty() } ?: DEFAULT_VALUE,
                    option.family.takeIf { !it.isNullOrEmpty() } ?: DEFAULT_VALUE,
                    option.optionDescription?.description.takeIf { !it.isNullOrEmpty() } ?: DEFAULT_VALUE,
                    option.optionPackage.toGermanText(),
                )
            }
        } else {
            emptyList()
        }

    return TUVTemplateData(
        registrationOrderTableData = registrationOrdersRowData,
        vehicleOptionTableData = vehicleOptionsRowData,
        vin = vin,
        repairFixCarId = vehicleData.repairfixCarId,
    )
}

private fun Boolean?.toGermanText(): String =
    when (this) {
        true -> "Ja"
        false -> "Nein"
        null -> DEFAULT_VALUE
    }

private fun ZonedDateTime.toGermanDateFormat(): String = this.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"))

private const val DEFAULT_VALUE = ""

data class TUVTemplateData(
    val vin: String,
    val registrationOrderTableData: List<List<String>>,
    val vehicleOptionTableData: List<List<String>>,
    val repairFixCarId: String? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Option(
    val type: String?,
    val id: String?,
    val orderType: String?,
    val family: String?,
    val optionDescription: OptionDescription?,
    @JsonProperty("package")
    val optionPackage: Boolean?,
)

data class OptionDescription(
    val description: String?,
    val language: String?,
)
