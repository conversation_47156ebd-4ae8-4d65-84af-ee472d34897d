/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generatetuvdocument

import com.aspose.words.Document
import com.aspose.words.NodeType
import com.aspose.words.Paragraph
import com.aspose.words.SaveFormat
import com.aspose.words.SaveOptions
import com.aspose.words.Table
import com.fleetmanagement.modules.documentgeneration.api.GenerateTUVDocument
import com.fleetmanagement.modules.documentgeneration.api.GenerateTUVDocumentException
import com.fleetmanagement.modules.documentgeneration.features.FillTemplateService
import com.fleetmanagement.modules.documentgeneration.features.FormFieldNotFoundException
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import org.springframework.beans.factory.annotation.Value
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Component
import java.io.ByteArrayOutputStream
import java.util.*

@Component
class GenerateTUVDocumentService(
    private val readRegistrationOrder: ReadRegistrationOrder,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val fillTemplateService: FillTemplateService,
    @Value("\${repair-fix.base-url}") val repairFixBaseUrl: String,
) : GenerateTUVDocument {
    override fun generateTUVDocument(vehicleId: UUID): ByteArrayResource {
        val completedRegistrationOrders = readRegistrationOrder.getCompletedOrdersBy(vehicleId)
        val vehicleData = readVehicleByVehicleId.readVehicleById(vehicleId)
        try {
            val tuvVehicleData =
                TUVVehicleData(
                    registrationOrders = completedRegistrationOrders.data,
                    vehicleData = vehicleData,
                )
            val templateData = tuvVehicleData.toTUVTemplateData()
            val document = fillTUVDocument(templateData)
            val outputStream = ByteArrayOutputStream()
            document.save(outputStream, SaveOptions.createSaveOptions(SaveFormat.PDF))
            return ByteArrayResource(outputStream.toByteArray())
        } catch (ex: IllegalArgumentException) {
            throw GenerateTUVDocumentException("Error creating TUV Document ${ex.message}", ex)
        } catch (ex: FormFieldNotFoundException) {
            throw GenerateTUVDocumentException("Error creating TUV Document ${ex.message}", ex)
        }
    }

    private fun fillTUVDocument(templateData: TUVTemplateData): Document {
        val document =
            fillTemplateService.fillTemplate(TEMPLATE_NAME, mapOf(TEMPLATE_VIN to templateData.vin))

        val tables = document.getChildNodes(NodeType.TABLE, true)
        fillTemplateService.fillTable(tables[0] as Table, templateData.registrationOrderTableData, document)
        fillTemplateService.fillTable(tables[1] as Table, templateData.vehicleOptionTableData, document)

        if (!templateData.repairFixCarId.isNullOrEmpty()) {
            document
                .getChildNodes(NodeType.PARAGRAPH, true)
                .map { it as Paragraph }
                .firstOrNull { it.text.contains(TEMPLATE_HYPERLINK_PARAGRAPH_TEXT) }
                ?.let {
                    fillTemplateService.appendHyperlinkInline(
                        it,
                        TEMPLATE_HYPERLINK_DISPLAY_TEXT,
                        "$repairFixBaseUrl/cars/${templateData.repairFixCarId}/damages",
                        document,
                    )
                }
        }

        return document
    }

    companion object {
        private const val TEMPLATE_NAME = "TUVDocument.docx"
        private const val TEMPLATE_VIN = "vin"
        private const val TEMPLATE_HYPERLINK_DISPLAY_TEXT = "Schäden in RepairFix"
        private const val TEMPLATE_HYPERLINK_PARAGRAPH_TEXT = "eingesehen werden:"
    }
}

data class TUVVehicleData(
    val registrationOrders: List<VehicleRegistrationOrder>,
    val vehicleData: VehicleDTO?,
)
