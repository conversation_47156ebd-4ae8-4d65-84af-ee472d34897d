/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.ecc.adapter.incoming.rest

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.ecc.EccService
import com.fleetmanagement.modules.ecc.adapter.rest.model.CurrentVehicleDto
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component

@Component
class EccAdapter(
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase,
    @Value("\${ecc.allow-incoming-traffic}")
    private val isVehicleTransferFetchingEnabled: Boolean,
) : EccService {
    override fun getCurrentVehiclesForEmployee(employeeNumber: String): List<CurrentVehicleDto> {
        if (!isVehicleTransferFetchingEnabled) {
            log.info("Vehicle transfer fetching is disabled by configuration.")
            throw RuntimeException("Vehicle transfer fetching is disabled by configuration.")
        }
        return readVehicleTransferUseCase
            .findActiveVehicleTransfersForEmployee(
                vehicleResponsiblePerson = EmployeeNumber(employeeNumber),
            ).mapNotNull {
                try {
                    it.toCurrentVehicleDto()
                } catch (exception: IllegalArgumentException) {
                    log.warn(exception.message)
                    null
                }
            }
    }

    companion object {
        private val log = LoggerFactory.getLogger(EccAdapter::class.java)
    }
}
