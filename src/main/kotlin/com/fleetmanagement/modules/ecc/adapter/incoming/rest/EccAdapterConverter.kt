/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.ecc.adapter.incoming.rest

import com.fleetmanagement.modules.ecc.adapter.rest.model.CurrentVehicleDto
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer

fun VehicleTransfer.toCurrentVehicleDto(): CurrentVehicleDto =
    CurrentVehicleDto(
        licensePlate = requireNotNull(this.licensePlate) { "License plate information is missing" },
        latestReturnDate = this.latestReturnDate,
        leasingPrivilege = this.leasingPrivilege,
    )
