/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm

import liquibase.integration.spring.SpringLiquibase
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.DependsOn
import javax.sql.DataSource

@Configuration
class FVMDBConfiguration {
    @Bean
    @DependsOn(
        "vehicleLocationLiquibase",
        "vehicleDataLiquibase",
        "preDeliveryLiquibase",
        "consigneeDatasheetLiquibase",
        "vehicleTransferLiquibase",
        "vehicleSalesLiquibase",
        "nonCustomerAdequateVehiclesLiquibase",
    )
    fun fvmLiquibase(datasource: DataSource): SpringLiquibase {
        val liquibase = SpringLiquibase()
        liquibase.changeLog = "classpath:/db/changelog/db.changelog-fvm.yml"
        liquibase.liquibaseSchema = "fvm"
        liquibase.dataSource = datasource
        return liquibase
    }
}
