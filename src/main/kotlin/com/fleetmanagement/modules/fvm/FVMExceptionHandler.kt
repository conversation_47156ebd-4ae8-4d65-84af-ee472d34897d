package com.fleetmanagement.modules.fvm

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.features.views.exceptions.PermissionDeniedException
import com.fleetmanagement.modules.fvm.features.views.exceptions.ViewNotFoundException
import org.slf4j.LoggerFactory
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.validation.FieldError
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.server.ResponseStatusException
import java.net.URI

@Order(Ordered.HIGHEST_PRECEDENCE)
@ControllerAdvice(basePackages = ["com.fleetmanagement.modules.fvm"])
class FVMExceptionHandler {
    @ExceptionHandler(org.springframework.security.access.AccessDeniedException::class)
    fun handleAccessDeniedException(ex: org.springframework.security.access.AccessDeniedException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("An Access Denied Exception occurred", ex)
        return ResponseEntity
            .status(HttpStatus.FORBIDDEN)
            .contentType(MediaType.APPLICATION_PROBLEM_JSON)
            .body(
                APIResponse.forErrors(
                    ProblemDetail
                        .forStatusAndDetail(
                            HttpStatus.FORBIDDEN,
                            ex.message ?: "Access denied.",
                        ).also { it.type = URI.create(ErrorType.FVM_ACCESS_DENIED.value) },
                ),
            )
    }

    @ExceptionHandler(ResponseStatusException::class)
    fun handleResponseStatusException(ex: ResponseStatusException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(ex.statusCode)
            .body(
                APIResponse.forErrors(
                    ProblemDetail
                        .forStatusAndDetail(
                            ex.statusCode,
                            ex.reason ?: "Error occurred while processing the request.",
                        ).apply {
                            type = URI.create(ErrorType.SYSTEM_ERROR.value)
                        },
                ),
            )

    @ExceptionHandler(ViewNotFoundException::class)
    fun handleViewNotFoundException(ex: ViewNotFoundException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    ex.statusCode,
                    ex.reason ?: "View Not Found",
                ).apply {
                    type = URI.create(ErrorType.FVM_VIEW_NOT_FOUND.value)
                }
        return ResponseEntity
            .status(ex.statusCode)
            .contentType(MediaType.APPLICATION_PROBLEM_JSON)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(PermissionDeniedException::class)
    fun handlePermissionDeniedException(ex: PermissionDeniedException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    ex.statusCode,
                    ex.reason ?: "Permission Denied",
                ).apply {
                    type = URI.create(ErrorType.FVM_RESTRICTED_VIEW.value)
                }
        return ResponseEntity
            .status(ex.statusCode)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(DataIntegrityViolationException::class)
    fun handleDataIntegrityExceptions(ex: Exception): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("A data integrity violation exception occurred", ex)
        val (errorMessage, type) =
            when {
                ex.message?.contains("unique constraint") == true ->
                    "An entry with this data already exists." to URI.create(ErrorType.FVM_ALREADY_EXISTS.value)
                else ->
                    "A data integrity violation exception occurred." to URI.create(ErrorType.FVM_DATA_INTEGRITY_VIOLATION.value)
            }
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.CONFLICT,
                    errorMessage,
                ).apply {
                    this.type = type
                }
        return ResponseEntity
            .status(HttpStatus.CONFLICT)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleMethodArgumentNotValidException(ex: MethodArgumentNotValidException): ResponseEntity<APIResponse<Nothing?>> {
        val allErrors = ex.bindingResult.allErrors

        // Check if the error is due to AccessControlledResource
        val isAccessControlViolation = allErrors.any { it.code == "AccessControlledResource" }
        val errorDetails =
            allErrors.joinToString(", ") {
                when (it) {
                    is FieldError -> "${it.field}: ${it.defaultMessage}"
                    else -> it.defaultMessage ?: "Unknown error"
                }
            }
        val status = if (isAccessControlViolation) HttpStatus.FORBIDDEN else HttpStatus.BAD_REQUEST
        val type =
            if (isAccessControlViolation) {
                URI.create(ErrorType.FVM_ACCESS_DENIED_TO_FIELDS.value)
            } else {
                URI.create(ErrorType.SYSTEM_ERROR.value)
            }
        logger.error("Validation error occurred: {}", errorDetails)
        val problemDetail =
            ProblemDetail.forStatusAndDetail(status, errorDetails).apply {
                this.type = type
            }
        return ResponseEntity
            .status(status)
            .contentType(MediaType.APPLICATION_PROBLEM_JSON)
            .body(APIResponse.forErrors(problemDetail))
    }

    companion object {
        private val logger = LoggerFactory.getLogger(FVMExceptionHandler::class.java)
    }
}
