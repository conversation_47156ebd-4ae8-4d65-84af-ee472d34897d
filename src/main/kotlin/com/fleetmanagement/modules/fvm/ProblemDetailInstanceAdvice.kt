package com.fleetmanagement.modules.fvm

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import org.springframework.core.MethodParameter
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageConverter
import org.springframework.http.server.ServerHttpRequest
import org.springframework.http.server.ServerHttpResponse
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyAdvice
import java.net.URI

@ControllerAdvice
class ProblemDetailInstanceAdvice : ResponseBodyAdvice<Any> {
    override fun supports(
        returnType: MethodParameter,
        converterType: Class<out HttpMessageConverter<*>>,
    ): Boolean = ResponseEntity::class.java.isAssignableFrom(returnType.parameterType)

    override fun beforeBodyWrite(
        body: Any?,
        returnType: MethodParameter,
        selectedContentType: MediaType,
        selectedConverterType: Class<out HttpMessageConverter<*>>,
        request: ServerHttpRequest,
        response: ServerHttpResponse,
    ): Any? {
        if (body is APIResponse<*>) {
            val uri = URI.create(request.uri.path)
            // set instance for each ProblemDetail in errors since spring will not do it for us
            // unless we return ProblemDetail directly as body
            body.errors?.forEach { problem ->
                if (problem.instance == null) {
                    problem.instance = uri
                }
            }
            body.warnings?.forEach { problem ->
                if (problem.instance == null) {
                    problem.instance = uri
                }
            }
        }
        return body
    }
}
