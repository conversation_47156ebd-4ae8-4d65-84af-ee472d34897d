# Fleet-Vehicle-Management Module 

## Purpose 
This module is only used by the **Fleet-Vehicle Management UI application**.

It's main responsibilities are: 

1. **Composition Layer:** between backend services/modules and the UI application
2. **Handle Shared Concerns:** like security and field-level access control for the UI
3. **Offer UI-Specific API:** for features like custom view configurations, the Vehicle-Manager/DLZ page, and Vehicle Details.

## Packaging and Architecture 

The module should follow a package-by-feature architecture. Each package internally can me packaged-by-layer. 

- All features should be available in the `features` package.
- Features should not have cyclic dependencies between them
- Common concerns between features should be pulled out to the module-level root if it is used only within the module, or the application level root if they are used across the application.

## Features 

### 1. Custom View Configurations 

### 2. Vehicle-Manager / DLZ Page

### 3. Vehicle Details Page

### 4. Pass through Functions




