/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto

import com.fasterxml.jackson.annotation.JsonInclude
import com.fleetmanagement.modules.fvm.dto.noncustomeradequate.FVMNonCustomerAdequateStatus
import com.fleetmanagement.modules.fvm.dto.peopledata.PeopleData
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspection
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.FVMVehicleCampaigns
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleData
import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocation
import com.fleetmanagement.modules.fvm.dto.vehicleregistration.FVMVehicleRegistrationOrder
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransfer

@JsonInclude(JsonInclude.Include.NON_NULL)
data class VehicleTransferUIRepresentation(
    val vehicleTransfer: FVMVehicleTransfer? = null,
    val vehicle: FVMVehicleData? = null,
    val vehicleCampaigns: FVMVehicleCampaigns? = null,
    val peopleData: PeopleData? = null,
    val vehicleRegistration: FVMVehicleRegistrationOrder? = null,
    val preDeliveryInspection: FVMPreDeliveryInspection? = null,
    val vehicleLastKnownLocation: FVMVehicleLocation? = null,
    val nonCustomerAdequate: FVMNonCustomerAdequateStatus? = null,
)
