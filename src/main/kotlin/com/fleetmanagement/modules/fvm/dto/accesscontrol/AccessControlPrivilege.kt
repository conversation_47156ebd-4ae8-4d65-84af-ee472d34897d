/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.accesscontrol

import com.fleetmanagement.security.api.dto.PrivilegePermission

data class AccessControlPrivilege(
    val resource: Resource,
    val permission: PrivilegePermission? = null,
    val fields: List<String>,
)
