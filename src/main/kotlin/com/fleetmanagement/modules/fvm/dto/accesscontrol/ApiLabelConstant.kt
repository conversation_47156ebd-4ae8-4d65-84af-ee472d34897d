/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.accesscontrol

import org.springframework.stereotype.Component

@Component("apiLabelConstant")
class ApiLabelConstant {
    companion object {
        const val API_CONSIGNEE_DATA_READ = "apis.consigneeData.read"
        const val API_CONSIGNEE_DATA_WRITE = "apis.consigneeData.write"
        const val API_CONSIGNEE_DATA_DELETE = "apis.consigneeData.delete"

        const val API_COST_CENTER_READ = "apis.costCenter.read"
        const val API_COST_CENTER_WRITE = "apis.costCenter.write"
        const val API_COST_CENTER_DELETE = "apis.costCenter.delete"

        const val API_PEOPLE_READ = "apis.people.read"

        const val API_PRE_DELIVERY_INSPECTION_READ = "apis.preDeliveryInspection.read"
        const val API_PRE_DELIVERY_INSPECTION_WRITE = "apis.preDeliveryInspection.write"

        const val API_ROLE_READ = "apis.role.read"
        const val API_ROLE_DELETE = "apis.role.delete"

        const val API_USAGE_GROUP_READ = "apis.usageGroup.read"
        const val API_USAGE_GROUP_WRITE = "apis.usageGroup.write"
        const val API_USAGE_GROUP_DELETE = "apis.usageGroup.delete"

        const val API_VEHICLE_READ = "apis.vehicle.read"
        const val API_VEHICLE_WRITE = "apis.vehicle.write"

        const val API_VEHICLE_REGISTRATION_READ = "apis.vehicleRegistration.read"
        const val API_VEHICLE_REGISTRATION_WRITE = "apis.vehicleRegistration.write"
        const val API_VEHICLE_REGISTRATION_DELETE = "apis.vehicleRegistration.delete"

        const val API_VEHICLE_TRANSFER_READ = "apis.vehicleTransfer.read"
        const val API_VEHICLE_TRANSFER_WRITE = "apis.vehicleTransfer.write"

        const val API_VEHICLE_USAGE_READ = "apis.vehicleUsage.read"
        const val API_VEHICLE_USAGE_WRITE = "apis.vehicleUsage.write"
        const val API_VEHICLE_USAGE_DELETE = "apis.vehicleUsage.delete"

        const val API_VIEW_READ = "apis.view.read"
        const val API_VIEW_WRITE = "apis.view.write"
        const val API_VIEW_DELETE = "apis.view.delete"

        const val API_CAMPAIGN_READ = "apis.campaign.read"
        const val API_CAMPAIGN_WRITE = "apis.campaign.write"

        const val API_ACCESS_CONTROL_READ = "apis.accessControl.read"
        const val API_ACCESS_CONTROL_WRITE = "apis.accessControl.write"

        const val API_VEHICLE_SALES_READ = "apis.vehicleSales.read"
        const val API_VEHICLE_SALES_WRITE = "apis.vehicleSales.write"
    }
}
