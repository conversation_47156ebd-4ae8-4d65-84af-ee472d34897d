/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.accesscontrol

data class ExcelCell(
    val header: String,
    val value: String?,
)

data class ExcelRow(
    val cells: List<ExcelCell>,
)

sealed class ExcelWorkSheet(
    val name: String,
    val headers: List<String>,
    open val rows: List<ExcelRow>,
) {
    companion object {
        const val RESOURCE_HEADER = "Resource"
        const val PERMISSION_HEADER = "Permission"
        const val DOMAIN_HEADER = "Domain"
        const val FIELD_NAME_HEADER = "Field Name"
        const val TRANSLATED_VALUE_HEADER = "Translated Value"

        const val PAGE_LEVEL_SHEET_NAME = "Page Level"
        const val ACTION_LEVEL_SHEET_NAME = "Action Level"
        const val FIELD_LEVEL_SHEET_NAME = "Field Level"

        val pageHeaders = listOf(RESOURCE_HEADER, PERMISSION_HEADER)
        val actionHeaders = listOf(RESOURCE_HEADER, PERMISSION_HEADER)
        val fieldHeaders = listOf(DOMAIN_HEADER, FIELD_NAME_HEADER, TRANSLATED_VALUE_HEADER, PERMISSION_HEADER)
    }
}

data class PagesWorkSheet(
    override val rows: List<ExcelRow>,
) : ExcelWorkSheet(PAGE_LEVEL_SHEET_NAME, pageHeaders, rows)

data class ActionsWorkSheet(
    override val rows: List<ExcelRow>,
) : ExcelWorkSheet(ACTION_LEVEL_SHEET_NAME, actionHeaders, rows)

data class FieldsWorkSheet(
    override val rows: List<ExcelRow>,
) : ExcelWorkSheet(FIELD_LEVEL_SHEET_NAME, fieldHeaders, rows)
