/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.accesscontrol

enum class FieldLabel(
    val label: String,
) {
    // Vehicle Fields
    VIN("vehicle.vin"),
    VGUID("vehicle.vguid"),
    EQUI_ID("vehicle.equiId"),
    SOURCE("vehicle.source"),
    REFERENCE_ID("vehicle.referenceId"),
    EQUIPMENT_NUMBER("vehicle.equipmentNumber"),
    FINANCIAL_ASSET_TYPE("vehicle.financialAssetType"),
    OPTIONS("vehicle.options"),
    CURRENT_TIRES("vehicle.currentTires"),
    EXTERNAL_LEASE_START("vehicle.externalLeaseStart"),
    EXTERNAL_LEASE_END("vehicle.externalLeaseEnd"),
    EXTERNAL_LEASE_RATE("vehicle.externalLeaseRate"),
    EXTERNAL_LEASE_LESSEE("vehicle.externalLeaseLessee"),
    CREATED_AT("vehicle.createdAt"),
    NUMBER_OF_DAMAGES("vehicle.numberOfDamages"),
    REPAIRFIX_CAR_ID("vehicle.repairfixCarId"),
    TUEV_APPOINTMENT("vehicle.tuevAppointment"),
    STATUS("vehicle.status"),

    // Vehicle Model Fields
    MODEL_DESCRIPTION("vehicle.model.description"),
    MODEL_YEAR("vehicle.model.year"),
    MODEL_PRODUCT_ID("vehicle.model.productId"),
    MODEL_PRODUCT_CODE("vehicle.model.productCode"),
    MODEL_ORDERTYPE("vehicle.model.orderType"),
    MODEL_VEHICLE_TYPE("vehicle.model.vehicleType"),
    MODEL_MANUFACTURER("vehicle.model.manufacturer"),
    MODEL_RANGE("vehicle.model.range"),
    MODEL_RANGE_DEVELOPMENT("vehicle.model.rangeDevelopment"),
    MODEL_DESCRIPTION_DEVELOPMENT("vehicle.model.descriptionDevelopment"),

    // Vehicle Consumption Fields
    CONSUMPTION_DRIVE_TYPE("vehicle.consumption.driveType"),
    CONSUMPTION_DATA_TYPIFICATION("vehicle.consumption.typification"),
    CONSUMPTION_PRIMARY_FUEL_TYPE("vehicle.consumption.primaryFuelType"),
    CONSUMPTION_SECONDARY_FUEL_TYPE("vehicle.consumption.secondaryFuelType"),

    // Vehicle Order Fields
    ORDER_DEPARTMENT("vehicle.order.department"),
    ORDER_TRADING_PARTNER_NUMBER("vehicle.order.tradingPartnerNumber"),
    ORDER_PURPOSE_ORDER_TYPE("vehicle.order.purposeOrderType"),
    ORDER_IMPORTER_SHORT_NAME("vehicle.order.importerShortName"),
    ORDER_PORT_CODE("vehicle.order.portCode"),
    ORDER_COMMISSION_NUMBER("vehicle.order.commissionNumber"),
    ORDER_INVOICE_NUMBER("vehicle.order.invoiceNumber"),
    ORDER_INVOICE_DATE("vehicle.order.invoiceDate"),
    ORDER_PURCHASE_ORDER_DATE("vehicle.order.purchaseOrderDate"),
    ORDER_REQUESTED_DELIVERY_DATE("vehicle.order.requestedDeliveryDate"),
    ORDER_CUSTOMER_DELIVERY_DATE("vehicle.order.customerDeliveryDate"),
    ORDER_DELIVERY_TYPE("vehicle.order.deliveryType"),
    ORDER_PRIMARY_STATUS("vehicle.order.primaryStatus"),
    ORDER_LEASING_TYPE("vehicle.order.leasingType"),
    ORDER_PREPRODUCTION_VEHICLE("vehicle.order.preproductionVehicle"),
    ORDER_BLOCKED_FOR_SALE("vehicle.order.blockedForSale"),

    // Vehicle Production Fields
    PRODUCTION_NUMBER("vehicle.production.number"),
    PRODUCTION_NUMBER_VW("vehicle.production.numberVW"),
    PRODUCTION_END_DATE("vehicle.production.endDate"),
    PRODUCTION_TECHNICAL_MODEL_YEAR("vehicle.production.technicalModelYear"),
    PRODUCTION_FACTORY("vehicle.production.factory"),
    PRODUCTION_FACTORY_VW("vehicle.production.factoryVW"),
    PRODUCTION_QUOTE_MONTH("vehicle.production.quoteMonth"),
    PRODUCTION_QUOTE_YEAR("vehicle.production.quoteYear"),
    PRODUCTION_PLANNED_END_DATE("vehicle.production.plannedEndDate"),
    PRODUCTION_GEAR_BOX_CLASS("vehicle.production.gearBoxClass"),

    // Vehicle Country Fields
    COUNTRY_BNR_VALUE("vehicle.country.bnrValue"),
    COUNTRY_CNR_VALUE("vehicle.country.cnrValue"),
    COUNTRY_CNR_DESCRIPTION("vehicle.country.cnrCountryDescription"),

    // Vehicle PMP Fields
    PMP_ODOMETER("vehicle.pmp.odometer"),
    PMP_TIMESTAMP("vehicle.pmp.timestamp"),

    // Vehicle Embargo Fields
    EMBARGO_IN_EMBARGO("vehicle.embargo.inEmbargo"),

    // Vehicle Color Fields
    COLOR_INTERIOR("vehicle.color.interior"),
    COLOR_EXTERIOR("vehicle.color.exterior"),
    COLOR_INTERIOR_DESCRIPTION("vehicle.color.interiorDescription"),
    COLOR_EXTERIOR_DESCRIPTION("vehicle.color.exteriorDescription"),

    // Vehicle Price Fields
    PRICE_GROSS_PRICE("vehicle.price.grossPrice"),
    PRICE_GROSS_PRICE_WITH_EXTRAS("vehicle.price.grossPriceWithExtras"),
    PRICE_GROSS_PRICE_NEW_CAR("vehicle.price.grossPriceNewCar"),
    PRICE_GROSS_PRICE_PLAN("vehicle.price.grossPricePlan"),
    PRICE_NET_PRICE("vehicle.price.netPrice"),
    PRICE_NET_PRICE_WITH_EXTRAS("vehicle.price.netPriceWithExtras"),
    PRICE_NET_PRICE_NEW_CAR("vehicle.price.newCar"),
    PRICE_VALUE_ADDED_TAX("vehicle.price.valueAddedTax"),
    PRICE_FACTORY_NET_PRICE_EUR("vehicle.price.factoryNetPriceEUR"),
    PRICE_FACTORY_GROSS_PRICE_EUR("vehicle.price.factoryGrossPriceEUR"),
    PRICE_PIA_EVENT("vehicle.price.piaEvent"),

    // Vehicle Technical Fields
    TECHNICAL_AMOUNT_SEATS("vehicle.technical.amountSeats"),
    TECHNICAL_ENGINE_CAPACITY("vehicle.technical.engineCapacity"),
    TECHNICAL_CARGO_VOLUME("vehicle.technical.cargoVolume"),
    TECHNICAL_VEHICLE_WIDTH_MIRRORS_EXTENDED("vehicle.technical.vehicleWidthMirrorsExtended"),
    TECHNICAL_MAXIMUM_CHARGING_POWER_DC("vehicle.technical.maximumChargingPowerDc"),
    TECHNICAL_GROSS_VEHICLE_WEIGHT("vehicle.technical.grossVehicleWeight"),
    TECHNICAL_CURB_WEIGHT_EU("vehicle.technical.curbWeightEu"),
    TECHNICAL_TOTAL_POWER_KW("vehicle.technical.totalPowerKw"),
    TECHNICAL_CURB_WEIGHT_DIN("vehicle.technical.curbWeightDin"),
    TECHNICAL_MAXIMUM_PAYLOAD("vehicle.technical.maximumPayload"),
    TECHNICAL_CHARGING_TIME_AC_22_KW("vehicle.technical.chargingTimeAc22Kw"),
    TECHNICAL_CHARGING_TIME_AC_11_KW_0100("vehicle.technical.chargingTimeAc11Kw0100"),
    TECHNICAL_CHARGING_TIME_AC_96_KW_0100("vehicle.technical.chargingTimeAc96Kw0100"),
    TECHNICAL_NET_BATTERY_CAPACITY("vehicle.technical.netBatteryCapacity"),
    TECHNICAL_GROSS_BATTERY_CAPACITY("vehicle.technical.grossBatteryCapacity"),
    TECHNICAL_ACCELERATION_0100_KMH("vehicle.technical.acceleration0100Kmh"),
    TECHNICAL_ACCELERATION_0100_KMH_LAUNCH_CONTROL("vehicle.technical.acceleration0100KmhLaunchControl"),
    TECHNICAL_TOP_SPEED("vehicle.technical.topSpeed"),
    TECHNICAL_HEIGHT("vehicle.technical.height"),
    TECHNICAL_WIDTH_MIRRORS_FOLDED("vehicle.technical.widthMirrorsFolded"),
    TECHNICAL_LENGTH("vehicle.technical.length"),
    TECHNICAL_ACCELERATION_80_120_KMH("vehicle.technical.acceleration80120Kmh"),
    TECHNICAL_MAX_ROOF_LOAD_WITH_PORSCHE_ROOF_TRANSPORT_SYSTEM("vehicle.technical.maxRoofLoadWithPorscheRoofTransportSystem"),
    TECHNICAL_CHARGING_TIME_DC_MAX_POWER_580("vehicle.technical.chargingTimeDcMaxPower580"),
    TECHNICAL_POWER_KW("vehicle.technical.powerKw"),

    FLEET_SCRAP_VEHICLE("vehicle.fleet.scrapVehicle"),
    FLEET_SOLD_DATE("vehicle.fleet.soldDate"),
    FLEET_SCRAPPED_DATE("vehicle.fleet.scrappedDate"),
    FLEET_STOLEN_DATE("vehicle.fleet.stolenDate"),
    FLEET_SOLD_CUP_CAR_DATE("vehicle.fleet.soldCupCarDate"),
    FLEET_APPROVED_FOR_SCRAPPING_DATE("vehicle.fleet.approvedForScrappingDate"),
    FLEET_SCRAPPED_VEHICLE_OFFERED_DATE("vehicle.fleet.scrappedVehicleOfferedDate"),
    FLEET_VEHICLE_SENT_TO_SALES_DATE("vehicle.fleet.vehicleSentToSalesDate"),
    FLEET_COST_ESTIMATION_ORDERED_DATE("vehicle.fleet.costEstimationOrderedDate"),
    FLEET_IS_RESIDUAL_VALUE_MARKET("vehicle.fleet.isResidualValueMarket"),
    FLEET_PROFITABILITY_AUDIT_DATE("vehicle.fleet.profitabilityAuditDate"),
    FLEET_COMMENT("vehicle.fleet.comment"),
    FLEET_RACE_CAR("vehicle.fleet.raceCar"),
    FLEET_CLASSIC("vehicle.fleet.classic"),

    // Vehicle Wltp Fields
    WLTP_VEHICLE_WEIGHT("vehicle.wltp.vehicleWeight"),
    WLTP_ELECTRIC_RANGE("vehicle.wltp.electricRange"),
    WLTP_CO2_COMBINED("vehicle.wltp.co2Combined"),
    WLTP_ELECTRIC_RANGE_CITY("vehicle.wltp.electricRangeCity"),

    EVALUATION_APPRAISAL_NET_PRICE("vehicle.evaluation.appraisalNetPrice"),
    EVALUATION_VEHICLE_EVALUATION_COMMENT("vehicle.evaluation.vehicleEvaluationComment"),
    EVALUATION_PC_COMPLAINT_CHECK_COMMENT("vehicle.evaluation.pcComplaintCheckComment"),

    // vehicle sales fields
    SALES_RESERVED_FOR_B2C("vehicleSales.reservedForB2C"),
    SALES_CONTRACT_SIGNED("vehicleSales.contractSigned"),
    SALES_PLANNED_DELIVERY_DATE("vehicleSales.plannedDeliveryDate"),
    SALES_COMMENT("vehicleSales.comment"),
    SALES_CUSTOMER_DELIVERY_DATE("vehicleSales.customerDeliveryDate"),
    SALES_SALES_DISCOUNT_PERCENTAGE("vehicleSales.salesDiscountPercentage"),
    SALES_NET_PRICE_EUR("vehicleSales.salesNetPriceEUR"),
    SALES_NET_PRICE_AFTER_DISCOUNT_EUR("vehicleSales.salesNetPriceAfterDiscountEUR"),
    SALES_WINTER_TIRES_DISCOUNT_PERCENTAGE("vehicleSales.winterTiresDiscountPercentage"),
    SALES_WINTER_TIRES_NET_PRICE_EUR("vehicleSales.winterTiresNetPriceEUR"),
    SALES_WINTER_TIRES_NET_PRICE_AFTER_DISCOUNT_EUR("vehicleSales.winterTiresNetPriceAfterDiscountEUR"),
    SALES_WINTER_TIRES_ID("vehicleSales.winterTiresId"),
    SALES_SALES_DISCOUNT_TYPE("vehicleSales.salesDiscountType"),
    SALES_CUSTOMER_INVOICE_RECIPIENT("vehicleSales.customerInvoiceRecipient"),
    SALES_PAYMENT_TYPE("vehicleSales.paymentType"),
    SALES_PAYMENT_RECEIVED("vehicleSales.paymentReceived"),
    SALES_REFERENCE_TRANSACTION_ID("vehicleSales.referenceTransactionId"),
    SALES_CUSTOMER_PARTNER_NUMBER("vehicleSales.customerPartnerNumber"),
    SALES_ERROR_MESSAGE("vehicleSales.sapErrorMessage"),
    SALES_INVOICE_RECIPIENT_NUMBER("vehicleSales.invoiceRecipientNumber"),
    SALES_SALESPERSON_NUMBER("vehicleSales.salesPersonNumber"),
    SALES_FVM_TRANSACTION_ID("vehicleSales.fvmTransactionId"),
    SALES_FVM_TRANSACTION_TYPE("vehicleSales.transactionType"),
    SALES_RECEIPT_NUMBER("vehicleSales.receiptNumber"),
    SALES_INVOICE_DATE("vehicleSales.invoiceDate"),
    SALES_INVOICE_NUMBER("vehicleSales.invoiceNumber"),
    SALES_FINAL_INVOICE_NUMBER("vehicleSales.finalInvoiceNumber"),
    SALES_INVOICE_STATUS("vehicleSales.invoiceStatus"),
    SALES_LOADING_COMPLETED_DATE("vehicleSales.loadingCompletedDate"),

    // Vehicle Delivery Fields
    DELIVERY_PREPARATION_DONE_DATE("vehicle.delivery.preparationDoneDate"),
    DELIVERY_IS_PREPARATION_NECESSARY("vehicle.delivery.isPreparationNecessary"),

    // Vehicle Return Fields
    RETURN_NEXT_PROCESS("vehicle.returnInfo.nextProcess"),
    RETURN_IS_USED_CAR("vehicle.returnInfo.isUsedCar"),
    RETURN_KEY_RETURNED("vehicle.returnInfo.keyReturned"),
    RETURN_FACTORY_CAR_PREPARATION_ORDER_NUMBER("vehicle.returnInfo.factoryCarPreparationOrderNumber"),

    // Action Fields
    CAMPAIGN_IDS("action.campaignIds"),

    // Campaign Fields
    CAMPAIGN_ID("campaign.id"),
    CAMPAIGN_DESCRIPTION("campaign.description"),
    CAMPAIGN_ISPROCESSABLE("campaign.isProcessable"),
    CAMPAIGN_CREATEDAT("campaign.createdAt"),

    // Vehicle TireSetChange Fields
    TIRE_SET_CHANGE_COMMENT("vehicle.tireSetChange.comment"),
    TIRE_SET_CHANGE_ORDERED_DATE("vehicle.tireSetChange.orderedDate"),
    TIRE_SET_CHANGE_COMPLETED_DATE("vehicle.tireSetChange.completedDate"),

    // Vehicle current mileage
    VEHICLE_CURRENT_MILEAGE_MILEAGE("vehicle.currentMileage.mileage"),
    VEHICLE_CURRENT_MILEAGE_READ_DATE("vehicle.currentMileage.readDate"),

    // vtstamm Fields
    VTSTAMM_SUBJECT_TO_CONFIDENTIALITY("vtstamm.subjectToConfidentiality"),
    VTSTAMM_CONFIDENTIALITY_CLASSIFICATION("vtstamm.confidentialityClassification"),
    VTSTAMM_SUBJECT_TO_CONFIDENTIALITY_START_DATE("vtstamm.subjectToConfidentialityStartDate"),
    VTSTAMM_SUBJECT_TO_CONFIDENTIALITY_END_DATE("vtstamm.subjectToConfidentialityEndDate"),
    VTSTAMM_RECORD_FACTORY_EXIT("vtstamm.recordFactoryExit"),
    VTSTAMM_CAMOUFLAGE_REQUIRED("vtstamm.camouflageRequired"),
    VTSTAMM_INTERNAL_DESIGNATION("vtstamm.internalDesignation"),
    VTSTAMM_STATUS_VTS("vtstamm.statusVTS"),
    VTSTAMM_TYPE_OF_USE_VTS("vtstamm.typeOfUseVTS"),

    // Vehicle Registration Fields
    VEHICLE_REGISTRATION_EQUI_ID("vehicleRegistration.equiId"),
    VEHICLE_REGISTRATION_TEST_NUMBER("vehicleRegistration.testNumber"),
    VEHICLE_REGISTRATION_EQUIPMENT_NUMBER("vehicleRegistration.equipmentNumber"),
    VEHICLE_REGISTRATION_BRIEF_NUMBER("vehicleRegistration.briefNumber"),
    VEHICLE_REGISTRATION_DRIVE_TYPE("vehicleRegistration.driveType"),
    VEHICLE_REGISTRATION_LEASING_TYPE("vehicleRegistration.leasingType"),
    VEHICLE_REGISTRATION_MODEL_TYPE("vehicleRegistration.modelType"),
    VEHICLE_REGISTRATION_DEPARTMENT("vehicleRegistration.department"),
    VEHICLE_REGISTRATION_REGISTRATION_OFFICE("vehicleRegistration.registrationOffice"),
    VEHICLE_REGISTRATION_PLANNED_LICENCE_PLATE("vehicleRegistration.plannedLicencePlate"),
    VEHICLE_REGISTRATION_PLANNED_REGISTRATION_DATE("vehicleRegistration.plannedRegistrationDate"),
    VEHICLE_REGISTRATION_COMMENTER("vehicleRegistration.commenter"),
    VEHICLE_REGISTRATION_ORDER_STATUS("vehicleRegistration.orderStatus"),
    VEHICLE_REGISTRATION_ORDER_TYPE("vehicleRegistration.orderType"),
    VEHICLE_REGISTRATION_STORAGE_LOCATION("vehicleRegistration.storageLocation"),
    VEHICLE_REGISTRATION_TSN("vehicleRegistration.tsn"),
    VEHICLE_REGISTRATION_HSN("vehicleRegistration.hsn"),
    VEHICLE_REGISTRATION_REGISTRATION_TYPE("vehicleRegistration.registrationType"),
    VEHICLE_REGISTRATION_REGISTRATION_AREA("vehicleRegistration.registrationArea"),
    VEHICLE_REGISTRATION_REGISTRATION_DATE("vehicleRegistration.registrationDate"),
    VEHICLE_REGISTRATION_FIRST_REGISTRATION_DATE("vehicleRegistration.firstRegistrationDate"),
    VEHICLE_REGISTRATION_LAST_REGISTRATION_DATE("vehicleRegistration.lastRegistrationDate"),
    VEHICLE_REGISTRATION_LAST_DE_REGISTRATION_DATE("vehicleRegistration.lastDeRegistrationDate"),
    VEHICLE_REGISTRATION_LICENCE_PLATE("vehicleRegistration.licencePlate"),
    VEHICLE_REGISTRATION_REMARK("vehicleRegistration.remark"),
    VEHICLE_REGISTRATION_SFME("vehicleRegistration.sfme"),
    VEHICLE_REGISTRATION_TEST_VEHICLE("vehicleRegistration.testVehicle"),
    VEHICLE_REGISTRATION_REGISTRATION_STATUS("vehicleRegistration.registrationStatus"),
    VEHICLE_REGISTRATION_DELETED("vehicleRegistration.deleted"),
    VEHICLE_REGISTRATION_CREATED_AT("vehicleRegistration.createdAt"),
    VEHICLE_REGISTRATION_UPDATED_AT("vehicleRegistration.updatedAt"),

    // Vehicle Location
    VEHICLE_LOCATION_COMPOUND_NAME("vehicleLocation.compoundName"),
    VEHICLE_LOCATION_PARKING_LOT("vehicleLocation.parkingLot"),
    VEHICLE_LOCATION_BUILDING("vehicleLocation.building"),
    VEHICLE_LOCATION_EVENT_TYPE("vehicleLocation.eventType"),
    VEHICLE_LOCATION_OCCURRED_ON("vehicleLocation.occurredOn"),
    VEHICLE_LOCATION_LEVEL("vehicleLocation.level"),
    VEHICLE_LOCATION_SOURCE("vehicleLocation.source"),
    VEHICLE_LOCATION_COMMENT("vehicleLocation.comment"),

    // Mileage Read Fields
    MILEAGE_READING_MILEAGE("mileageReading.mileage"),
    MILEAGE_READING_READ_DATE("mileageReading.readDate"),
    MILEAGE_READING_SOURCE("mileageReading.source"),
    MILEAGE_READING_CREATED_BY("mileageReading.createdBy"),

    // View
    VIEW_IS_PUBLIC("view.isPublic"),
    VIEW_IS_DEFAULT("view.isDefault"),

    // Vehicle Transfer Fields
    VEHICLE_TRANSFER_EMPLOYEE_NUMBER("vehicleTransfer.employeeNumber"),
    VEHICLE_TRANSFER_KEY("vehicleTransfer.key"),
    VEHICLE_TRANSFER_STATUS("vehicleTransfer.status"),
    VEHICLE_TRANSFER_PLANNED_RETURNED_DATE("vehicleTransfer.plannedReturnedDate"),
    VEHICLE_TRANSFER_RETURNED_DATE("vehicleTransfer.returnedDate"),
    VEHICLE_TRANSFER_DELIVERY_DATE("vehicleTransfer.deliveryDate"),
    VEHICLE_TRANSFER_MILEAGE_AT_RETURN("vehicleTransfer.mileageAtReturn"),
    VEHICLE_TRANSFER_MILEAGE_AT_DELIVERY("vehicleTransfer.mileageAtDelivery"),
    VEHICLE_TRANSFER_LICENSE_PLATE("vehicleTransfer.licensePlate"),
    VEHICLE_TRANSFER_FIRST_NAME("vehicleTransfer.firstName"),
    VEHICLE_TRANSFER_LAST_NAME("vehicleTransfer.lastName"),
    VEHICLE_TRANSFER_DEPARTMENT("vehicleTransfer.department"),
    VEHICLE_TRANSFER_VEHICLE_USAGE("vehicleTransfer.vehicleUsage"),
    VEHICLE_TRANSFER_USAGE_GROUP("vehicleTransfer.usageGroup"),
    VEHICLE_TRANSFER_COMPANY_MOBILE_NUMBER("vehicleTransfer.companyMobileNumber"),
    VEHICLE_TRANSFER_COMPANY_EMAIL("vehicleTransfer.companyEmail"),
    VEHICLE_TRANSFER_PRIVATE_EMAIL("vehicleTransfer.privateEmail"),
    VEHICLE_TRANSFER_LEASING_PRIVILEGE_LEASING("vehicleTransfer.leasingPrivilegeLeasing"),
    VEHICLE_TRANSFER_ACCOUNTING_AREA("vehicleTransfer.accountingArea"),
    VEHICLE_TRANSFER_MAXIMUM_SERVICE_LIFE_IN_MONTHS("vehicleTransfer.maximumServiceLifeInMonths"),
    VEHICLE_TRANSFER_VEHICLE_USAGE_ID("vehicleTransfer.vehicleUsageId"),
    VEHICLE_TRANSFER_INTERNAL_CONTACT_PERSON("vehicleTransfer.internalContactPerson"),
    VEHICLE_TRANSFER_DEPRECIATION_RELEVANT_COST_CENTER_ID("vehicleTransfer.depreciationRelevantCostCenterId"),
    VEHICLE_TRANSFER_USING_COST_CENTER("vehicleTransfer.usingCostCenter"),
    VEHICLE_TRANSFER_INTERNAL_ORDER_NUMBER("vehicleTransfer.internalOrderNumber"),
    VEHICLE_TRANSFER_USAGE_GROUP_ID("vehicleTransfer.usageGroupId"),
    VEHICLE_TRANSFER_RETURN_DATE("vehicleTransfer.returnDate"),
    VEHICLE_TRANSFER_PLANNED_DELIVERY_DATE("vehicleTransfer.plannedDeliveryDate"),
    VEHICLE_TRANSFER_PLANNED_RETURN_DATE("vehicleTransfer.plannedReturnDate"),
    VEHICLE_TRANSFER_LATEST_RETURN_DATE("vehicleTransfer.latestReturnDate"),
    VEHICLE_TRANSFER_REMARK("vehicleTransfer.remark"),
    VEHICLE_TRANSFER_VEHICLE_RESPONSIBLE_PERSON("vehicleTransfer.vehicleResponsiblePerson"),
    VEHICLE_TRANSFER_VEHICLE_TRANSFER_KEY("vehicleTransfer.vehicleTransferKey"),
    VEHICLE_TRANSFER_UTILIZATION_AREA("vehicleTransfer.utilizationArea"),
    VEHICLE_TRANSFER_DELIVERY_LEIPZIG("vehicleTransfer.deliveryLeipzig"),
    VEHICLE_TRANSFER_DESIRED_DELIVERY_DATE("vehicleTransfer.desiredDeliveryDate"),
    VEHICLE_TRANSFER_PROVISION_FOR_DELIVERY_COMMENT("vehicleTransfer.provisionForDeliveryComment"),
    VEHICLE_TRANSFER_DELIVERY_COMMENT("vehicleTransfer.deliveryComment"),
    VEHICLE_TRANSFER_DESIRED_TIRE_SET("vehicleTransfer.desiredTireSet"),
    VEHICLE_TRANSFER_TIRES_COMMENT("vehicleTransfer.tiresComment"),
    VEHICLE_TRANSFER_PREDECESSOR_LATEST_RETURN_DATE("vehicleTransfer.predecessorLatestReturnDate"),
    VEHICLE_TRANSFER_LEASING_PRIVILEGE("vehicleTransfer.leasingPrivilege"),
    VEHICLE_TRANSFER_LEASING_PRIVILEGE_VALIDATION_SUCCESSFUL("vehicleTransfer.leasingPrivilegeValidationSuccessful"),
    VEHICLE_TRANSFER_DESIRED_TIRES_SET_CHANGED_MANUALLY("vehicleTransfer.desiredTireSetChangedManually"),
    VEHICLE_TRANSFER_MAINTENANCE_ORDER_NUMBER("vehicleTransfer.maintenanceOrderNumber"),
    VEHICLE_TRANSFER_SUCCESSOR_ORDER_DATE("vehicleTransfer.successorOrderDate"),
    VEHICLE_TRANSFER_RETURN_COMMENT("vehicleTransfer.returnComment"),
    VEHICLE_TRANSFER_SERVICE_CARDS("vehicleTransfer.serviceCards"),
    VEHICLE_TRANSFER_REGISTRATION_NEEDED("vehicleTransfer.registrationNeeded"),
    VEHICLE_TRANSFER_USAGE_MHP("vehicleTransfer.usageMhp"),
    VEHICLE_TRANSFER_USAGE_VDW("vehicleTransfer.usageVdw"),
    VEHICLE_TRANSFER_PRIVATE_MONTHLY_KILOMETERS("vehicleTransfer.privateMonthlyKilometers"),

    // Pre-delivery Inspection Fields
    PRE_DELIVERY_INSPECTION_TIRE_SET("preDeliveryInspection.tireSet"),
    PRE_DELIVERY_INSPECTION_IS_RELEVANT("preDeliveryInspection.isRelevant"),
    PRE_DELIVERY_INSPECTION_FOILING("preDeliveryInspection.foiling"),
    PRE_DELIVERY_INSPECTION_REFUEL("preDeliveryInspection.refuel"),
    PRE_DELIVERY_INSPECTION_CHARGE("preDeliveryInspection.charge"),
    PRE_DELIVERY_INSPECTION_DIGITAL_LOGBOOK("preDeliveryInspection.digitalLogbook"),
    PRE_DELIVERY_INSPECTION_LICENCE_PLATE_MOUNTING("preDeliveryInspection.licencePlateMounting"),
    PRE_DELIVERY_INSPECTION_ORDERED_DATE("preDeliveryInspection.orderedDate"),
    PRE_DELIVERY_INSPECTION_COMPLETED_DATE("preDeliveryInspection.completedDate"),
    PRE_DELIVERY_INSPECTION_PLANNED_DATE("preDeliveryInspection.plannedDate"),
    PRE_DELIVERY_INSPECTION_COMMENT("preDeliveryInspection.comment"),
    PRE_DELIVERY_INSPECTION_WINTER_TIRES_ID("preDeliveryInspection.winterTiresId"),
    PRE_DELIVERY_INSPECTION_WINTER_TIRES_FROM_DATE("preDeliveryInspection.winterTiresFromDate"),
    PRE_DELIVERY_INSPECTION_WINTER_TIRES_TO_DATE("preDeliveryInspection.winterTiresToDate"),
    PRE_DELIVERY_INSPECTION_WINTER_TIRES_VERSION("pre-delivery-inspection.winterTiresVersion"),

    // Consignee Data Fields

    // Vehicle Usage Fields

    // Cost Center Fields

    // People Fields
    PEOPLE_EMPLOYEE_NUMBER("people.employeeNumber"),
    PEOPLE_EMPLOYEE_KEY("people.employeeKey"),
    PEOPLE_ACCOUNTING_AREA("people.accountingArea"),
    PEOPLE_BUSINESS_PARTNER_ID("people.businessPartnerId"),
    PEOPLE_FIRST_NAME("people.firstName"),
    PEOPLE_LAST_NAME("people.lastName"),
    PEOPLE_COMPANY_EMAIL("people.companyEmail"),
    PEOPLE_PRIVATE_EMAIL("people.privateEmail"),
    PEOPLE_STREET("people.street"),
    PEOPLE_POSTAL_CODE("people.postalCode"),
    PEOPLE_CITY("people.city"),
    PEOPLE_COUNTRY("people.country"),
    PEOPLE_INTERNAL_COMPANY_PHONE_NUMBER("people.internalCompanyPhoneNumber"),
    PEOPLE_COMPANY_MOBILE_NUMBER("people.companyMobileNumber"),
    PEOPLE_PRIVATE_MOBILE_NUMBER("people.privateMobileNumber"),
    PEOPLE_DEPARTMENT("people.department"),
    PEOPLE_PERSONAL_AREA("people.personalArea"),
    PEOPLE_COST_CENTER("people.costCenter"),
    PEOPLE_EMPLOYEE_GROUP("people.employeeGroup"),
    PEOPLE_COMPANY_CAR_AUTHORIZATION("people.companyCarAuthorization"),
    PEOPLE_LEASING_AUTHORIZATION("people.leasingAuthorization"),
    PEOPLE_SECONDARY_LEASING_AUTHORIZATION("people.secondaryLeasingAuthorization"),
    PEOPLE_SECONDARY_LEASING_SPECIAL_CONDITIONS_AUTHORIZATION("people.secondaryLeasingSpecialConditionsAuthorization"),
    PEOPLE_LOYALTY_LEASING("people.loyaltyLeasing"),
    PEOPLE_FUEL_CARD_AUTHORIZATION_DE("people.fuelCardAuthorizationDe"),
    PEOPLE_FUEL_CARD_AUTHORIZATION_EU("people.fuelCardAuthorizationEu"),
    PEOPLE_CHARGING_CARD_AUTHORIZATION_DE("people.chargingCardAuthorizationDe"),
    PEOPLE_CHARGING_CARD_AUTHORIZATION_EU("people.chargingCardAuthorizationEu"),
    PEOPLE_REPLACEMENT_VEHICLE_AUTHORIZATION("people.replacementVehicleAuthorization"),
    PEOPLE_PERMANENT_DRIVING_PERMIT("people.permanentDrivingPermit"),
    PEOPLE_ACCOUNTING_CLERK("people.accountingClerk"),
    PEOPLE_PERSONNEL_CLERK("people.personnelClerk"),
    PEOPLE_EXIT_DATE("people.exitDate"),
    PEOPLE_ID_CARD_NUMBER("people.idCardNumber"),
    PEOPLE_APPROVAL_GROUP("people.approvalGroup"),
    PEOPLE_SUBAREA("people.subarea"),
    PEOPLE_SUBAREA_TEXT("people.subareaText"),
    PEOPLE_LEASING_PRIVILEGE_LEASING("people.leasingPrivilegeLeasing"),
    PEOPLE_LEASING_PRIVILEGE_COMPANY_CAR_LEASING("people.leasingPrivilegeCompanyCarLeasing"),
    PEOPLE_LEASING_PRIVILEGE_SECONDARY_LEASING("people.leasingPrivilegeSecondaryLeasing"),
    PEOPLE_LEASING_PRIVILEGE_SECONDARY_LEASING_SPECIAL_CONDITIONS("people.leasingPrivilegeSecondaryLeasingSpecialConditions"),
    PEOPLE_LEASING_PRIVILEGE_LOYALTY_LEASING("people.leasingPrivilegeLoyaltyLeasing"),
    PEOPLE_LEASING_PRIVILEGE_REPLACEMENT_VEHICLE("people.leasingPrivilegeReplacementVehicle"),
    PEOPLE_STATE_OF_EMPLOYMENT("people.stateOfEmployment"),

    // non customer adequate fields
    NCA_STATUS("nonCustomerAdequate.ncaStatus"),
    NCA_PROFITABILITY_AUDIT_DONE("nonCustomerAdequate.profitabilityAuditDone"),
    NCA_REBUILD_STARTED("nonCustomerAdequate.rebuildStarted"),
    NCA_REBUILD_DONE("nonCustomerAdequate.rebuildDone"),
    NCA_COMMENT("nonCustomerAdequate.comment"),
    NCA_PLANNED_REBUILD_COST("nonCustomerAdequate.plannedRebuildCost"),
    NCA_ACTUAL_REBUILD_COST("nonCustomerAdequate.actualRebuildCost"),
    NCA_EXPECTED_REVENUE("nonCustomerAdequate.expectedRevenue"),
    NCA_SALES_PRICE("nonCustomerAdequate.salesPrice"),

    // FIXME: The security module depends on [FieldLabel], which is
    // defined in the fvm module. This is incorrect, because now a library
    // module depends on a application module (that should never be the case)
    // The reason why we have Student here is a symptom
    // this incorrect dependency.
    STUDENT_NAME("student.name"),
    STUDENT_AGE("student.age"),
    STUDENT_GRADE("student.division"),
    STUDENT_ADDRESS("student.address"),
    STUDENT_ADDRESS_STREET("student.address.street"),
    STUDENT_STATUS("student.status"),
    STUDENT_MATH_SCORE("student.mathScore"),
    STUDENT_SCIENCE_SCORE("student.scienceScore"),
    STUDENT_TEST_SCORE_PERCENTILE("student.testScore.percentile"),
    ;

    companion object {
        init {
            val duplicates =
                entries
                    .groupBy { it.label }
                    .filter { it.value.size > 1 }

            require(duplicates.isEmpty()) {
                "Duplicate labels found in FieldLabel enum: ${duplicates.keys}"
            }
        }
    }
}
