/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.accesscontrol

enum class PagesWithActions(
    val page: Page,
) {
    WELCOME(
        Page("welcome"),
    ),

    ADMIN(
        Page(
            "admin",
            virtualPage = true,
            subPages =
                listOf(
                    Page(
                        "derivationTables",
                        virtualPage = true,
                        subPages =
                            listOf(
                                Page("usageGroup", actions = listOf("addNewUsageGroup", "deleteUsageGroup")),
                                <PERSON>("consignee", actions = listOf("addNewConsignee", "deleteConsignee")),
                                <PERSON>("vehicleUsage", actions = listOf("addNewVehicleUsage", "deleteVehicleUsage")),
                                <PERSON>(
                                    "depreciationRelevantCostCenter",
                                    actions = listOf("addNewCostCenter", "deleteCostCenter"),
                                ),
                            ),
                    ),
                    <PERSON>("vehicleImport", actions = listOf("importVehicle")),
                    <PERSON>("roleManagement.roles"),
                    <PERSON>("roleManagement.history"),
                ),
        ),
    ),

    CLIENT_MANAGEMENT(
        Page(
            "clientManagement",
            virtualPage = true,
            subPages =
                listOf(
                    Page("createVehicle"),
                    Page("manualVehicleCreation"),
                    Page("campaigns"),
                    Page("terminating"),
                    Page("vehicleReturn"),
                    Page("vehicleDelivery"),
                ),
        ),
    ),

    SETTINGS(
        Page(
            "settings",
            subPages =
                listOf(
                    Page(
                        "gridViewConfig",
                        virtualPage = true,
                        actions = listOf("deletePublicView", "editPublicView"),
                    ),
                ),
        ),
    ),

    VEHICLE_MANAGEMENT(
        Page(
            "vehicleManagement",
            actions = listOf("exportRows", "changeLocation", "viewDocuments", "evaluateVehicles"),
        ),
    ),

    CREATE_VEHICLE(
        Page("createVehicle", actions = listOf("createVehicle")),
    ),

    VEHICLE_DETAILS(
        Page(
            "vehicleDetails",
            virtualPage = true,
            subPages =
                listOf(
                    Page("overview"),
                    Page("vehicle"),
                    Page("usage"),
                    Page("lifecycle"),
                    Page("location", actions = listOf("exportRows")),
                    Page("vehicleHistory", actions = listOf("exportRows")),
                    Page("documents"),
                ),
        ),
    ),

    PEOPLE(
        Page("people"),
    ),

    PEOPLE_DETAILS(
        Page(
            "peopleDetails",
            subPages =
                listOf(
                    Page("general"),
                    Page("roles"),
                    Page("vehicles"),
                    Page("history", actions = listOf("exportRows")),
                ),
        ),
    ),

    VEHICLE_REGISTRATION(
        Page(
            "vehicleRegistration",
            subPages =
                listOf(
                    Page("unregistered", actions = listOf("exportSelection", "importPsoList")),
                    Page("registered", actions = listOf("importRegistrations")),
                    Page("printing"),
                    Page("testNumberRenewal", actions = listOf("renewTestNumbers")),
                ),
            actions = listOf("addVehicle", "deleteRegistrationOrder"),
        ),
    ),

    VEHICLE_TRANSFER_MANAGEMENT(
        Page(
            "vehicleTransferManagement",
            actions =
                listOf(
                    "updateVehicleTransfer",
                    "updatePreDeliveryInspection",
                    "delete",
                    "addNewPlannedVehicleTransfer",
                    "cancel",
                    "exportRows",
                ),
        ),
    ),

    LOGISTICS(
        Page(
            "logistics",
        ),
    ),

    SCRAPPING(
        Page(
            "scrapping",
            subPages =
                listOf(
                    Page("monitoring"),
                    Page("identification"),
                    Page("offeredForReuse"),
                    Page("approvedForScrapping"),
                ),
        ),
    ),

    Evaluation(
        Page(
            "evaluation",
            subPages =
                listOf(
                    Page("tuevCommissioning"),
                    Page("pcComplaintChecks"),
                    Page("residualValueMarket"),
                ),
        ),
    ),

    ENQUIRY_MANAGEMENT(
        Page(
            "enquiryManagement",
        ),
    ),

    COMMON(
        Page(
            "common",
            virtualPage = true,
            actions = listOf("createVehicleTransfer", "printGreenInsuranceCard", "printPowerOfAttorney", "printProvisionOfDelivery"),
        ),
    ),

    NON_CUSTOMER_ADEQUATE_VEHICLES(
        Page(
            "nonCustomerAdequateVehicles",
            subPages =
                listOf(
                    Page("monitoring"),
                    Page("identification"),
                ),
        ),
    ),

    SALES(
        Page(
            "sales",
            subPages =
                listOf(
                    Page("vehicleSearch"),
                    Page("createInvoice"),
                    Page("soldVehicles", actions = listOf("createInvoice")),
                    Page(
                        "invoicedVehiclesTransactions",
                        actions = listOf("approveInvoice", "rejectInvoice", "cancelInvoice"),
                    ),
                    Page("customerDeliveredVehicles"),
                ),
        ),
    ),

    DAMAGE_MANAGEMENT(
        Page(
            "damageManagement",
        ),
    ),
    ;

    companion object {
        fun getAllPageKeys(): List<String> = entries.flatMap { it.page.generatePageKeys("page") }

        fun getAllActionKeys(): List<String> = entries.flatMap { it.page.generateActionKeys("page") }
    }
}

data class Page(
    val name: String,
    val subPages: List<Page> = emptyList(),
    val actions: List<String> = emptyList(),
    val virtualPage: Boolean = false,
) {
    fun generatePageKeys(prefix: String): List<String> {
        val currentKey = if (name.isNotEmpty()) "$prefix.$name" else prefix
        val subPageKeys = subPages.flatMap { it.generatePageKeys(currentKey) }
        return if (virtualPage) {
            subPageKeys
        } else {
            listOf(currentKey) + subPageKeys
        }
    }

    fun generateActionKeys(prefix: String): List<String> {
        val currentKey = if (name.isNotEmpty()) "$prefix.$name" else prefix
        val actionKeys = actions.map { "$currentKey.action.$it" }
        val subPageActions = subPages.flatMap { it.generateActionKeys(currentKey) }
        return actionKeys + subPageActions
    }
}
