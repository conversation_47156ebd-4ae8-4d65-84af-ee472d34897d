/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.accesscontrol

enum class Resource(
    val label: String,
) {
    // This should serve as the source of Privileges and permissions
    PAGES("pages"),
    ACTIONS("actions"),
    APIS("apis"),

    ROLE("role"),

    VEHICLE("vehicle"),

    VEHICLE_SALES("vehicleSales"),

    VEHICLE_CAMPAIGNS("vehicleCampaigns"),

    CAMPAIGN("campaign"),

    VEHICLE_REGISTRATION("vehicleRegistration"),

    VEHICLE_LOCATION("vehicleLocation"),

    PE<PERSON>LE("people"),

    NON_CUSTOMER_ADEQUATE_DATA("nonCustomerAdequate"),

    VIEW("view"),

    VEHICLE_TRANSFER("vehicleTransfer"),

    PRE_DELIVERY_INSPECTION("preDeliveryInspection"),

    CONSIGNEE_DATA("consigneeData"),

    USAGE_GROUP("usageGroup"),

    VEHICLE_USAGE("vehicleUsage"),

    COST_CENTER("costCenter"),

    // FIXME: The security module depends on [Resource], which is
    // defined in the fvm module. This is incorrect, because now a library
    // module depends on a application module (that should never be the case)
    // The reason why we have Student here is a symptom
    // this incorrect dependency.
    STUDENT("student"),
    STUDENT_TEST_SCORE("student.testScore"),
    STUDENT_ADDRESS("address"),
    ;

    companion object {
        fun fromLabel(label: String): Resource = entries.find { it.label == label } ?: error("Invalid resource label: $label")
    }
}
