/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.api

import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonValue
import jakarta.validation.Valid
import org.springframework.http.ProblemDetail

data class APIResponse<T>(
    @field:Valid
    val data: T,
    val errors: List<ProblemDetail>? = null,
    @JsonInclude(JsonInclude.Include.NON_NULL)
    val rowCount: Long? = null,
    val warnings: List<ProblemDetail>? = null,
) {
    companion object {
        fun forErrors(vararg errors: ProblemDetail): APIResponse<Nothing?> =
            APIResponse(
                data = null,
                errors = errors.toList(),
                warnings = null,
            )
    }
}

enum class ErrorType(
    @JsonValue val value: String,
) {
    // technical errors
    SYSTEM_ERROR("error.system"),
    SYSTEM_ERROR_MISSING_REQUEST_HEADER("error.system.missingRequestHeader"),
    SYSTEM_ERROR_INTERNAL_SERVER_ERROR("error.system.internalServerError"),
    SYSTEM_ERROR_REMOTE_SYSTEM_PROBLEM("error.system.remoteSystemProblem"),
    SYSTEM_ERROR_BAD_REQUEST("error.system.badRequest"),
    SYSTEM_ERROR_ASYNC_REQUEST_TIMEOUT("error.system.asyncRequestTimeout"),

    // business errors
    FVM_RESTRICTED_VIEW("error.fvm.restrictedViewResource"),
    FVM_VIEW_NOT_FOUND("error.fvm.viewNotFound"),
    FVM_ACCESS_DENIED("error.fvm.accessDenied"),
    FVM_ACCESS_DENIED_TO_FIELDS("error.fvm.accessDenied.fields"),
    FVM_ALREADY_EXISTS("error.fvm.alreadyExists"),
    FVM_DATA_INTEGRITY_VIOLATION("error.fvm.dataIntegrityViolation"),
    FVM_INPUT_LIMIT_EXCEEDED("error.fvm.inputLimitExceeded"),
    FVM_LOCATION_HISTORY_FETCH_ERROR("error.fvm.locationHistoryFetch"),
    FVM_TRANSFER_DETAIL_USAGE_FETCH_ERROR("error.fvm.transferDetailUsageFetch"),
    FVM_MILEAGE_READING_FETCH_ERROR("error.fvm.mileageReadingFetch"),
    FVM_VEHICLE_LOCATION_ERROR("error.fvm.vehicleLocation"),
    FVM_INVALID_INPUT_ERROR("error.fvm.invalidInput"),
    FVM_LOCATION_SERVICE_UNREACHABLE_ERROR("error.fvm.locationServiceUnreachable"),
    FVM_LOCATION_DOES_NOT_EXIST_ERROR("error.fvm.locationDoesNotExist"),
    FVM_MULTIPLE_LOCATIONS_FOR_INPUTS_ERROR("error.fvm.multipleLocationsForInputs"),
    FVM_CONSIGNEE_NOT_FOUND("error.fvm.consigneeNotFound"),
    FVM_CONSIGNEE_UPDATE_FAILED("error.fvm.consigneeUpdateFailed"),
    FVM_VEHICLE_TRANSFER_NOT_FOUND("error.fvm.vehicleTransferNotFound"),
    FVM_VEHICLE_TRANSFER_ILLEGAL_STATUS("error.fvm.vehicleTransferIllegalStatus"),
    FVM_VEHICLE_TRANSFER_UPDATE_FAILED("error.fvm.vehicleTransferUpdateFailed"),
    FVM_COST_CENTER_NOT_FOUND("error.fvm.costCenterNotFound"),
    FVM_VEHICLE_USAGE_NOT_FOUND("error.fvm.vehicleUsageNotFound"),
    FVM_VEHICLE_ALREADY_EXISTS("error.fvm.vehicleAlreadyExists"),
    FVM_VEHICLE_PVH_NOT_FOUND("error.fvm.pvhVehicleNotFound"),
    FVM_USAGE_GROUP_NOT_FOUND("error.fvm.usageGroupNotFound"),
    FVM_DATA_MISSING("error.fvm.dataMissing"),
    FVM_EXPORT_COLUMN_LIMIT_EXCEEDED("error.fvm.exportColumnLimitExceeded"),
    FVM_EXPORT_NO_COLUMNS_SELECTED("error.fvm.noColumnsSelectedToExport"),
    FVM_EXPORT_FAILED("error.fvm.exportFailed"),
    FVM_ACCESS_CONTROL_EXPORT_FAILED("error.fvm.accessControlExportFailed"),
    FVM_ACCESS_CONTROL_IMPORT_FAILED("error.fvm.accessControlImportFailed"),
    FVM_ACCESS_CONTROL_INVALID_FILE("error.fvm.accessControlInvalidFile"),
    FVM_ACCESS_CONTROL_MISSING_REQUIRED_SHEET("error.fvm.accessControlMissingRequiredSheet"),
    FVM_ACCESS_CONTROL_MISSING_REQUIRED_HEADER("error.fvm.accessControlMissingRequiredHeader"),
    FVM_ACCESS_CONTROL_INVALID_DATA("error.fvm.accessControlInvalidData"),
    FVM_ROLE_NOT_FOUND("error.fvm.roleNotFound"),
    FVM_ROLE_ENTRA_ID_USER_NOT_FOUND("error.fvm.entraIdUserNotFound"),
    FVM_ROLE_USER_DETAILS_NOT_FOUND("error.fvm.userDetailsNotFound"),
    FVM_ACTIVE_ROLE_CANNOT_BE_DELETED("error.fvm.activeRoleCannotBeDeleted"),
    FVM_VEHICLE_CREATION_PARTIAL_SUCCESS("error.fvm.vehicleCreationPartialSuccess"),

    // PDI
    FVM_PRE_DELIVERY_INSPECTION_PDI_LEAD_TIME_VIOLATION("error.fvm.pdi.leadTimeViolation"),
    FVM_PRE_DELIVERY_INSPECTION_PDI_ALREADY_ORDERED("error.fvm.pdi.alreadyOrdered"),
    FVM_PRE_DELIVERY_INSPECTION_PDI_INVALID_PLANNED_DATE("error.fvm.pdi.invalidPlannedDate"),
    FVM_PRE_DELIVERY_INSPECTION_PDI_EMAIL_GENERATION_FAILED("error.fvm.pdi.emailGenerationFailed"),

    // Vehicle update
    FVM_VEHICLE_VEHICLE_MARKED_AS_SCRAP("error.fvm.vehicle.vehicleMarkedAsScrap"),
    FVM_VEHICLE_VEHICLE_BLOCKED_FOR_SALE("error.fvm.vehicle.vehicleBlockedForSale"),
    FVM_VEHICLE_VEHICLE_NOT_MARKED_AS_SCRAP("error.fvm.vehicle.vehicleNotMarkedAsScrap"),
    FVM_VEHICLE_VEHICLE_NOT_BLOCKED_FOR_SALE("error.fvm.vehicle.vehicleNotBlockedForSale"),
    FVM_FUTURE_DATE_NOT_ALLOWED("error.fvm.futureDateNotAllowed"),
    FVM_VEHICLE_UPDATE_FAILED("error.fvm.vehicle.vehicleUpdateFailed"),
    FVM_VEHICLE_UPDATE_NOT_ALLOWED_ACTIVE_VEHICLE_TRANSFER_EXISTS("error.fvm.vehicle.vehicleUpdateNotAllowedActiveTransferExists"),

    // WINTER TIRES TIME FRAME
    FVM_WINTER_TIRES_TIMEFRAME_NOTFOUND("error.fvm.wintertires.winterTriesTimeframeNotFound"),

    // people manager
    FVM_PERSON_HISTORY_FETCH_ERROR("error.fvm.personHistoryFetch"),

    FVM_PEOPLE_SEARCH_ERROR("error.fvm.peopleSearch"),

    // vehicle transfer
    FVM_VEHICLE_TRANSFER_DELIVERY_LEAD_TIME_VIOLATION_FOR_DESIRED_DELIVERY_DATE(
        "error.fvm.vehicleTransfer.deliveryLeadTimeViolation.desiredDeliveryDate",
    ),
    FVM_VEHICLE_TRANSFER_DELIVERY_LEAD_TIME_VIOLATION_FOR_PLANNED_DELIVERY_DATE(
        "error.fvm.vehicleTransfer.deliveryLeadTimeViolation.plannedDeliveryDate",
    ),
    FVM_VEHICLE_TRANSFER_DELIVERY_POWER_OF_ATTORNEY_CREATION_FAILED(
        "error.fvm.vehicleTransfer.deliveryPowerOfAttorneyCreationFailed",
    ),
    FVM_VEHICLE_TRANSFER_DELIVERY_RESPONSIBLE_PERSON_DETAILS_COULD_NOT_BE_RETRIEVED(
        "error.fvm.vehicleTransfer.deliveryResponsiblePersonDetailsCouldNotBeRetrieved",
    ),
    FVM_VEHICLE_TRANSFER_DELIVERY_EMAIL_NOT_SENT(
        "error.fvm.vehicleTransfer.deliveryEmailNotSent",
    ),
    FVM_VEHICLE_TRANSFER_DELIVERY_ERROR(
        "error.fvm.vehicleTransferDeliveryFailed",
    ),
    FVM_VEHICLE_TRANSFER_RETURN_ERROR(
        "error.fvm.vehicleTransferReturnFailed",
    ),
    FVM_MANUAL_CREATE_VEHICLE_TRANSFER_ERROR(
        "error.fvm.manualCreateVehicleTransferError",
    ),
    FVM_INVALID_USING_COST_CENTER_ERROR(
        "error.fvm.invalidCostCenterError",
    ),
    FVM_INVALID_USING_COST_CENTER_ERROR_FOR_USAGE_GROUP_PERSON(
        "error.fvm.invalidCostCenterErrorForUsageGroupPerson",
    ),
    FVM_VEHICLE_TRANSFER_DLZ_DELIVERY_LEASING_PRIVILEGES_EXCEPTION(
        value = "error.fvm.vehicleTransferDLZDeliveryLeasingPrivilegesException",
    ),
    FVM_VEHICLE_TRANSFER_FUTURE_DATE_NOT_ALLOWED(
        value = "error.fvm.vehicleTransfer.futureDateNotAllowed",
    ),

    // vehicle evaluation
    FVM_VEHICLE_EVALUATION_PDF_GENERATION_ERROR("error.fvm.vehicle.evaluation.pdfGenerationError"),
    FVM_VEHICLE_EVALUATION_TUV_EMAIL_ERROR("error.fvm.vehicle.evaluation.appraisalServiceEmailError"),
    FVM_VEHICLE_EVALUATION_LOGISTICS_EMAIL_ERROR("error.fvm.vehicle.evaluation.logisticsServiceEmailError"),

    // campaigns
    FVM_CAMPAIGNS_READ_ERROR(
        "error.fvm.campaigns.readError",
    ),
    FVM_CAMPAIGNS_ALREADY_EXISTS_ERROR(
        "error.fvm.campaigns.alreadyExists",
    ),
    FVM_CAMPAIGNS_NOT_FOUND_ERROR(
        "error.fvm.campaigns.notFoundError",
    ),
    FVM_CAMPAIGNS_BAD_REQUEST_ERROR(
        "error.fvm.campaigns.badRequestError",
    ),

    // request-management
    FVM_ENQUIRY_MANAGEMENT_INVALID_DATE("error.fvm.enquiryManagement.invalidDate"),
    FVM_ENQUIRY_MANAGEMENT_RESPONSIBILITY_DETAILS_FETCH_ERROR("error.fvm.enquiryManagement.responsibilityDetailsFetchError"),

    // non-customer adequate
    FVM_NON_CUSTOMER_ADEQUATE_OPTIONS_READ_ERROR(
        "error.fvm.nonCustomerAdequate.options.readError",
    ),
    FVM_NON_CUSTOMER_ADEQUATE_OPTIONS_WRITE_ERROR(
        "error.fvm.nonCustomerAdequate.options.writeError",
    ),

    // vehicle sales
    FVM_SALES_VEHICLE_NOT_FOUND("error.fvm.vehicleSales.vehicleNotFound"),
    FVM_SALES_INVOICE_VEHICLE_STATUS_ERROR("error.fvm.vehicleSales.vehicleStatusError"),
    FVM_SALES_INVOICE_RESERVED_FOR_B2C_MISSING("error.fvm.vehicleSales.reservedForB2CMissing"),
    FVM_SALES_INVOICE_CONTRACT_NOT_SIGNED("error.fvm.vehicleSales.contractNotSigned"),
    FVM_SALES_VEHICLE_INVOICE_READ_ERROR("error.fvm.vehicleSales.invoiceReadError"),
    FVM_SALES_VEHICLE_INVOICE_NOT_FOUND("error.fvm.vehicleSales.invoiceNotFound"),
    FVM_SALES_VEHICLE_INVOICE_UPDATE_ERROR("error.fvm.vehicleSales.invoiceUpdateError"),
    FVM_SALES_VEHICLE_INVOICE_ALREADY_EXISTS("error.fvm.vehicleSales.invoiceAlreadyExists"),

    // provision of delivery
    FVM_PROVISION_OF_DELIVERY_NO_SCHEDULED_TRANSFERS("error.fvm.provisionOfDelivery.noScheduledTransfers"),
}
