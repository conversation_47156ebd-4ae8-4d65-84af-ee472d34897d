/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.consigneedata

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataDto
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterDto
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupDto
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageDto
import com.fleetmanagement.modules.consigneedatasheet.domain.TireSet
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import java.util.*

@AccessControlledResource(Resource.CONSIGNEE_DATA)
data class FVMConsigneeData(
    val id: UUID,
    val version: Int,
    val consignee: String,
    val description: String,
    val maximumServiceLifeInMonths: Int?,
    val vehicleUsage: VehicleUsageDto?,
    val vehicleResponsiblePerson: String?,
    val internalContactPerson: String?,
    val isPreproductionVehicle: Boolean,
    val isBlockedForSale: Boolean,
    val isScrapped: Boolean,
    val depreciationRelevantCostCenter: CostCenterDto?,
    val usingCostCenter: String?,
    val internalOrderNumber: String?,
    val validationOfLeasingPrivileges: String?,
    val usageGroup: UsageGroupDto?,
    val vehicleType: FVMVehicleType?,
    val manufacturer: String?,
    val pdiCurrentTireSet: FVMTireSet?,
    val pdiRelevant: Boolean?,
    val pdiFoiling: Boolean?,
    val pdiRefuel: Boolean?,
    val pdiRecharge: Boolean?,
    val pdiDigitalLogbook: Boolean?,
    val pdiLicensePlateMounting: Boolean?,
)

enum class FVMTireSet {
    SR,
    WR,
}

enum class FVMVehicleType {
    PKW,
    TRUCK,
    TRAILER,
    MOTORCYCLE,
    TRANSPORTER,
}

fun ConsigneeDataDto.toFVMConsigneeData(): FVMConsigneeData =
    FVMConsigneeData(
        id = this.id,
        version = this.version,
        consignee = this.consignee,
        description = this.description,
        isScrapped = this.isScrapped,
        depreciationRelevantCostCenter = this.depreciationRelevantCostCenter,
        usageGroup = this.usageGroupDto,
        vehicleUsage = this.vehicleUsageDto,
        usingCostCenter = this.usingCostCenter,
        internalContactPerson = this.internalContactPerson,
        internalOrderNumber = this.internalOrderNumber,
        isBlockedForSale = this.isBlockedForSale,
        isPreproductionVehicle = this.isPreproductionVehicle,
        vehicleResponsiblePerson = this.vehicleResponsiblePerson,
        maximumServiceLifeInMonths = this.maximumServiceLifeInMonths,
        manufacturer = this.manufacturer,
        validationOfLeasingPrivileges = this.validationOfLeasingPrivileges,
        vehicleType = this.vehicleType.toFVMVehicleType(),
        pdiCurrentTireSet = this.pdiCurrentTireSet?.toFVMTireSet(),
        pdiRelevant = this.pdiRelevant,
        pdiRefuel = this.pdiRefuel,
        pdiRecharge = this.pdiRecharge,
        pdiFoiling = this.pdiFoiling,
        pdiDigitalLogbook = this.pdiDigitalLogbook,
        pdiLicensePlateMounting = this.pdiLicensePlateMounting,
    )

private fun VehicleType?.toFVMVehicleType(): FVMVehicleType? =
    when (this) {
        VehicleType.PKW -> FVMVehicleType.PKW
        VehicleType.TRUCK -> FVMVehicleType.TRUCK
        VehicleType.TRAILER -> FVMVehicleType.TRAILER
        VehicleType.MOTORCYCLE -> FVMVehicleType.MOTORCYCLE
        VehicleType.TRANSPORTER -> FVMVehicleType.TRANSPORTER
        else -> null
    }

fun TireSet.toFVMTireSet(): FVMTireSet =
    when (this) {
        TireSet.WR -> FVMTireSet.WR
        TireSet.SR -> FVMTireSet.SR
    }

fun FVMTireSet.toTireSet(): TireSet =
    when (this) {
        FVMTireSet.WR -> TireSet.WR
        FVMTireSet.SR -> TireSet.SR
    }
