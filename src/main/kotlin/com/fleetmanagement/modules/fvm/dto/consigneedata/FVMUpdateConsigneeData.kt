/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.consigneedata

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataNewOrUpdate
import java.util.UUID

class FVMUpdateConsigneeData(
    val id: UUID,
    val version: Int,
    val consignee: String,
    val description: String,
    val maximumServiceLifeInMonths: Int?,
    val vehicleUsageId: UUID?,
    val vehicleResponsiblePerson: String?,
    val internalContactPerson: String?,
    val isPreproductionVehicle: Boolean,
    val isBlockedForSale: Boolean,
    val isScrapped: Boolean,
    val depreciationRelevantCostCenterId: UUID?,
    val usingCostCenter: String?,
    val internalOrderNumber: String?,
    val validationOfLeasingPrivileges: String?,
    val usageGroupId: UUID?,
    val vehicleType: FVMVehicleType?,
    val manufacturer: String?,
    val pdiCurrentTireSet: FVMTireSet?,
    val pdiRelevant: Boolean?,
    val pdiFoiling: Boolean?,
    val pdiRefuel: Boolean?,
    val pdiRecharge: Boolean?,
    val pdiDigitalLogbook: Boolean?,
    val pdiLicensePlateMounting: Boolean?,
)

fun FVMUpdateConsigneeData.toConsigneeDataNewOrUpdate(): ConsigneeDataNewOrUpdate =
    ConsigneeDataNewOrUpdate(
        consignee = this.consignee,
        description = this.description,
        isScrapped = this.isScrapped,
        depreciationRelevantCostCenterId = this.depreciationRelevantCostCenterId,
        usageGroupId = this.usageGroupId,
        vehicleUsageId = this.vehicleUsageId,
        usingCostCenter = this.usingCostCenter,
        internalContactPerson = this.internalContactPerson,
        internalOrderNumber = this.internalOrderNumber,
        isBlockedForSale = this.isBlockedForSale,
        isPreproductionVehicle = this.isPreproductionVehicle,
        vehicleResponsiblePerson = this.vehicleResponsiblePerson,
        maximumServiceLifeInMonths = this.maximumServiceLifeInMonths,
        manufacturer = this.manufacturer,
        validationOfLeasingPrivileges = this.validationOfLeasingPrivileges,
        vehicleType = this.vehicleType?.toVehicleType(),
        pdiCurrentTireSet = this.pdiCurrentTireSet?.toTireSet(),
        pdiRelevant = this.pdiRelevant,
        pdiRefuel = this.pdiRefuel,
        pdiRecharge = this.pdiRecharge,
        pdiFoiling = this.pdiFoiling,
        pdiDigitalLogbook = this.pdiDigitalLogbook,
        pdiLicensePlateMounting = this.pdiLicensePlateMounting,
    )

fun FVMVehicleType.toVehicleType(): VehicleType =
    when (this) {
        FVMVehicleType.PKW -> VehicleType.PKW
        FVMVehicleType.TRUCK -> VehicleType.TRUCK
        FVMVehicleType.TRAILER -> VehicleType.TRAILER
        FVMVehicleType.MOTORCYCLE -> VehicleType.MOTORCYCLE
        FVMVehicleType.TRANSPORTER -> VehicleType.TRANSPORTER
    }
