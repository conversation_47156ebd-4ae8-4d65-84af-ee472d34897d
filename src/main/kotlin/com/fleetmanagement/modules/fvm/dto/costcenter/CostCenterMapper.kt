/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.costcenter

import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterDto
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterNewOrUpdate
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

@Mapper
interface CostCenterMapper {
    companion object {
        val INSTANCE: CostCenterMapper = Mappers.getMapper(CostCenterMapper::class.java)
    }

    fun map(costCenterDto: CostCenterDto): FVMCostCenter

    fun map(fvmCreateOrUpdateCostCenter: FVMCreateOrUpdateCostCenter): CostCenterNewOrUpdate
}
