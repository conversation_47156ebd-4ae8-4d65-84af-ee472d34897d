package com.fleetmanagement.modules.fvm.dto.enquirymanagement

import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationPeriod
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import java.time.OffsetDateTime

data class LicensePlateUsage(
    val transfer: VehicleTransfer,
    val registrationPeriod: RegistrationPeriod,
    val licensePlateValidFrom: OffsetDateTime?,
    val licensePlateValidUntil: OffsetDateTime?,
)
