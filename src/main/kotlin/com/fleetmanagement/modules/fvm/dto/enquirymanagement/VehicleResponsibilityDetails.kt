package com.fleetmanagement.modules.fvm.dto.enquirymanagement

import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupDto
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationPeriod
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import java.time.OffsetDateTime

data class VehicleResponsibilityDetails(
    val vin: String?,
    val licencePlate: String,
    val registrationDate: OffsetDateTime? = null,
    val vehicleTransferKey: Long,
    val status: String,
    val licencePlateValidFrom: OffsetDateTime? = null,
    val licencePlateValidUntil: OffsetDateTime? = null,
    val usageGroup: String? = null,
    val employeeNumber: String? = null,
    val firstName: String? = null,
    val lastName: String? = null,
    val department: String? = null,
    val companyEmail: String? = null,
    val privateEmail: String? = null,
    val street: String? = null,
    val city: String? = null,
    val postalCode: String? = null,
    val country: String? = null,
    val internalContactPerson: String? = null,
) {
    companion object {
        fun from(
            registrationPeriod: RegistrationPeriod,
            vehicleTransfer: VehicleTransfer,
            personDetails: VehiclePersonDetail?,
            usageGroups: List<UsageGroupDto>,
            lpFromDate: OffsetDateTime?,
            lpToDate: OffsetDateTime?,
        ): VehicleResponsibilityDetails =
            VehicleResponsibilityDetails(
                vin = registrationPeriod.vin,
                licencePlate = registrationPeriod.licencePlate,
                registrationDate = registrationPeriod.fromDate?.toOffsetDateTime(),
                vehicleTransferKey = vehicleTransfer.key.value,
                status = vehicleTransfer.status.name,
                licencePlateValidFrom = lpFromDate,
                licencePlateValidUntil = lpToDate,
                usageGroup = usageGroups.firstOrNull { it.id == vehicleTransfer.usageGroup?.value }?.description,
                employeeNumber = personDetails?.employeeNumber,
                firstName = personDetails?.firstName,
                lastName = personDetails?.lastName,
                department = personDetails?.department,
                companyEmail = personDetails?.companyEmail,
                privateEmail = personDetails?.privateEmail,
                street = personDetails?.street,
                city = personDetails?.city,
                postalCode = personDetails?.postalCode,
                country = personDetails?.country,
                internalContactPerson = vehicleTransfer.internalContactPerson?.value,
            )
    }
}
