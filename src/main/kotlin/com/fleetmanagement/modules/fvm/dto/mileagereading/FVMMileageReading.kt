package com.fleetmanagement.modules.fvm.dto.mileagereading

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
@JsonInclude(JsonInclude.Include.NON_NULL)
class FVMMileageReading(
    @AccessControlledField(FieldLabel.MILEAGE_READING_MILEAGE)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val mileage: Int,
    @AccessControlledField(FieldLabel.MILEAGE_READING_READ_DATE)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val readDate: OffsetDateTime,
    @AccessControlledField(FieldLabel.MILEAGE_READING_SOURCE)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val source: MileageReadingSource,
    @AccessControlledField(FieldLabel.MILEAGE_READING_CREATED_BY)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val createdBy: String? = null,
)
