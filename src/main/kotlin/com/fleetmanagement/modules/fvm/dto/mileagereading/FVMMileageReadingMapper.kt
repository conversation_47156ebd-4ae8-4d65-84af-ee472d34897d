package com.fleetmanagement.modules.fvm.dto.mileagereading

import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingDTO
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

@Mapper
interface FVMMileageReadingMapper {
    companion object {
        val INSTANCE: FVMMileageReadingMapper = Mappers.getMapper(FVMMileageReadingMapper::class.java)
    }

    fun map(mileageReading: MileageReadingDTO): FVMMileageReading
}
