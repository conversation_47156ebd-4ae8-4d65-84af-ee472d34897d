package com.fleetmanagement.modules.fvm.dto.noncustomeradequate

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.math.BigDecimal
import java.time.OffsetDateTime

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.NON_CUSTOMER_ADEQUATE_DATA)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class FVMNonCustomerAdequateData(
    @field:JsonView(VehicleJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_STATUS)
    val ncaStatus: String?,
    @field:JsonView(VehicleJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_PROFITABILITY_AUDIT_DONE)
    val profitabilityAuditDone: OffsetDateTime?,
    @field:JsonView(VehicleJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_REBUILD_STARTED)
    val rebuildStarted: OffsetDateTime?,
    @field:JsonView(VehicleJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_REBUILD_DONE)
    val rebuildDone: OffsetDateTime?,
    @field:JsonView(VehicleJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_COMMENT)
    val comment: String?,
    @field:JsonView(VehicleJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_PLANNED_REBUILD_COST)
    val plannedRebuildCost: BigDecimal?,
    @field:JsonView(VehicleJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_ACTUAL_REBUILD_COST)
    val actualRebuildCost: BigDecimal?,
    @field:JsonView(VehicleJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_EXPECTED_REVENUE)
    val expectedRevenue: BigDecimal?,
    @field:JsonView(VehicleJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_SALES_PRICE)
    val salesPrice: BigDecimal?,
)
