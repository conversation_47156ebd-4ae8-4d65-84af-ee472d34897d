package com.fleetmanagement.modules.fvm.dto.noncustomeradequate

import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded.DLZViewNonCustomerAdequateData
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVTNonCustomerAdequateData

fun DLZViewNonCustomerAdequateData.toFVMNonCustomerAdequateData(): FVMNonCustomerAdequateData =
    FVMNonCustomerAdequateData(
        ncaStatus = this.ncaStatus,
        profitabilityAuditDone = this.profitabilityAuditDone,
        rebuildStarted = this.rebuildStarted,
        rebuildDone = this.rebuildDone,
        comment = this.comment,
        plannedRebuildCost = this.plannedRebuildCost,
        actualRebuildCost = this.actualRebuildCost,
        expectedRevenue = this.expectedRevenue,
        salesPrice = this.salesPrice,
    )

fun FVMVTNonCustomerAdequateData.toFVMNonCustomerAdequate(): FVMNonCustomerAdequateStatus =
    FVMNonCustomerAdequateStatus(
        ncaStatus = this.ncaStatus,
    )
