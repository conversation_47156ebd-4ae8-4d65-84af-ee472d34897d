package com.fleetmanagement.modules.fvm.dto.noncustomeradequate

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleTransferJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.NON_CUSTOMER_ADEQUATE_DATA)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class FVMNonCustomerAdequateStatus(
    @field:JsonView(VehicleTransferJsonView.DLZPage::class)
    @AccessControlledField(FieldLabel.NCA_STATUS)
    val ncaStatus: String?,
)
