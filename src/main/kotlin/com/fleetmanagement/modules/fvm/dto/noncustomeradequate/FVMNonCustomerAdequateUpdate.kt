/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto.noncustomeradequate

import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.Optional

@AccessControlledResource(Resource.NON_CUSTOMER_ADEQUATE_DATA)
data class FVMNonCustomerAdequateUpdate(
    @AccessControlledField(FieldLabel.NCA_PROFITABILITY_AUDIT_DONE)
    val profitabilityAuditDone: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.NCA_REBUILD_STARTED)
    val rebuildStarted: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.NCA_REBUILD_DONE)
    val rebuildDone: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.NCA_COMMENT)
    val comment: Optional<String>?,
    @AccessControlledField(FieldLabel.NCA_PLANNED_REBUILD_COST)
    val plannedRebuildCost: Optional<BigDecimal>?,
    @AccessControlledField(FieldLabel.NCA_ACTUAL_REBUILD_COST)
    val actualRebuildCost: Optional<BigDecimal>?,
    @AccessControlledField(FieldLabel.NCA_EXPECTED_REVENUE)
    val expectedRevenue: Optional<BigDecimal>?,
    @AccessControlledField(FieldLabel.NCA_SALES_PRICE)
    val salesPrice: Optional<BigDecimal>?,
    @AccessControlledField(FieldLabel.NCA_STATUS)
    val ncaStatus: Optional<String>?,
)
