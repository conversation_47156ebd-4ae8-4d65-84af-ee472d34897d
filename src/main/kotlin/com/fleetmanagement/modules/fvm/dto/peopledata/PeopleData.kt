/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.peopledata

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.PeopleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleSalesJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleTransferJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.LocalDate

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.PEOPLE)
data class PeopleData(
    @AccessControlledField(FieldLabel.PEOPLE_EMPLOYEE_NUMBER)
    @field:JsonView(
        PeopleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val employeeNumber: String?,
    @AccessControlledField(FieldLabel.PEOPLE_ACCOUNTING_AREA)
    @field:JsonView(
        PeopleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val accountingArea: String?,
    @AccessControlledField(FieldLabel.PEOPLE_BUSINESS_PARTNER_ID)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleJsonView.DLZPage::class)
    val businessPartnerId: String?,
    @AccessControlledField(FieldLabel.PEOPLE_FIRST_NAME)
    @field:JsonView(
        PeopleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val firstName: String?,
    @AccessControlledField(FieldLabel.PEOPLE_LAST_NAME)
    @field:JsonView(
        PeopleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val lastName: String?,
    @AccessControlledField(FieldLabel.PEOPLE_COMPANY_EMAIL)
    @field:JsonView(
        PeopleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val companyEmail: String?,
    @AccessControlledField(FieldLabel.PEOPLE_PRIVATE_EMAIL)
    @field:JsonView(
        PeopleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val privateEmail: String?,
    @AccessControlledField(FieldLabel.PEOPLE_STREET)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val street: String?,
    @AccessControlledField(FieldLabel.PEOPLE_POSTAL_CODE)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val postalCode: String?,
    @AccessControlledField(FieldLabel.PEOPLE_CITY)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val city: String?,
    @AccessControlledField(FieldLabel.PEOPLE_COUNTRY)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val country: String?,
    @AccessControlledField(FieldLabel.PEOPLE_INTERNAL_COMPANY_PHONE_NUMBER)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val internalCompanyPhoneNumber: String?,
    @AccessControlledField(FieldLabel.PEOPLE_COMPANY_MOBILE_NUMBER)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val companyMobileNumber: String?,
    @AccessControlledField(FieldLabel.PEOPLE_PRIVATE_MOBILE_NUMBER)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val privateMobileNumber: String?,
    @AccessControlledField(FieldLabel.PEOPLE_DEPARTMENT)
    @field:JsonView(
        PeopleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val department: String?,
    @AccessControlledField(FieldLabel.PEOPLE_PERSONAL_AREA)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val personalArea: String?,
    @AccessControlledField(FieldLabel.PEOPLE_COST_CENTER)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val costCenter: String?,
    @AccessControlledField(FieldLabel.PEOPLE_EMPLOYEE_GROUP)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val employeeGroup: String?,
    @AccessControlledField(FieldLabel.PEOPLE_COMPANY_CAR_AUTHORIZATION)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val companyCarAuthorization: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_LEASING_AUTHORIZATION)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val leasingAuthorization: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_SECONDARY_LEASING_AUTHORIZATION)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val secondaryLeasingAuthorization: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_SECONDARY_LEASING_SPECIAL_CONDITIONS_AUTHORIZATION)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val secondaryLeasingSpecialConditionsAuthorization: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_LOYALTY_LEASING)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val loyaltyLeasing: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_FUEL_CARD_AUTHORIZATION_DE)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val fuelCardAuthorizationDe: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_FUEL_CARD_AUTHORIZATION_EU)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val fuelCardAuthorizationEu: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_CHARGING_CARD_AUTHORIZATION_DE)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val chargingCardAuthorizationDe: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_CHARGING_CARD_AUTHORIZATION_EU)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val chargingCardAuthorizationEu: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_REPLACEMENT_VEHICLE_AUTHORIZATION)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val replacementVehicleAuthorization: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_PERMANENT_DRIVING_PERMIT)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val permanentDrivingPermit: Boolean?,
    @AccessControlledField(FieldLabel.PEOPLE_ACCOUNTING_CLERK)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val accountingClerk: String?,
    @AccessControlledField(FieldLabel.PEOPLE_PERSONNEL_CLERK)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val personnelClerk: String?,
    @AccessControlledField(FieldLabel.PEOPLE_EXIT_DATE)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val exitDate: LocalDate?,
    @AccessControlledField(FieldLabel.PEOPLE_ID_CARD_NUMBER)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val idCardNumber: String?,
    @AccessControlledField(FieldLabel.PEOPLE_APPROVAL_GROUP)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val approvalGroup: String?,
    @AccessControlledField(FieldLabel.PEOPLE_SUBAREA)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val subarea: String?,
    @AccessControlledField(FieldLabel.PEOPLE_SUBAREA_TEXT)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val subareaText: String?,
    @AccessControlledField(FieldLabel.PEOPLE_LEASING_PRIVILEGE_LEASING)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val leasingPrivilegeLeasing: String?,
    @AccessControlledField(FieldLabel.PEOPLE_LEASING_PRIVILEGE_COMPANY_CAR_LEASING)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val leasingPrivilegeCompanyCarLeasing: String?,
    @AccessControlledField(FieldLabel.PEOPLE_LEASING_PRIVILEGE_SECONDARY_LEASING)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val leasingPrivilegeSecondaryLeasing: String?,
    @AccessControlledField(FieldLabel.PEOPLE_LEASING_PRIVILEGE_SECONDARY_LEASING_SPECIAL_CONDITIONS)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val leasingPrivilegeSecondaryLeasingSpecialConditions: String?,
    @AccessControlledField(FieldLabel.PEOPLE_LEASING_PRIVILEGE_LOYALTY_LEASING)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val leasingPrivilegeLoyaltyLeasing: String?,
    @AccessControlledField(FieldLabel.PEOPLE_LEASING_PRIVILEGE_REPLACEMENT_VEHICLE)
    @field:JsonView(PeopleJsonView.DLZPage::class, VehicleTransferJsonView.DLZPage::class)
    val leasingPrivilegeReplacementVehicle: String?,
    @AccessControlledField(FieldLabel.PEOPLE_STATE_OF_EMPLOYMENT)
    @field:JsonView(PeopleJsonView.DLZPage::class)
    val stateOfEmployment: String?,
)
