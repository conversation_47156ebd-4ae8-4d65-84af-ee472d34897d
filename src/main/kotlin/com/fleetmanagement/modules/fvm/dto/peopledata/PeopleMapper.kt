/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.peopledata

import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMEmployeeData
import com.fleetmanagement.modules.fvm.features.peoplemanager.entities.PDMViewPeopleRow
import com.fleetmanagement.modules.vehicleperson.api.dtos.People
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonHistoryEntry
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehicleSalesPersonDTO
import org.mapstruct.*
import org.mapstruct.factory.Mappers

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
interface PeopleMapper {
    companion object {
        val INSTANCE: PeopleMapper = Mappers.getMapper(PeopleMapper::class.java)
    }

    @Mappings(
        Mapping(source = "peopleData", target = "."),
    )
    fun map(pdmViewPeopleRow: PDMViewPeopleRow): PeopleData

    @Mappings(
        Mapping(source = "fuelCardAuthorizationDE", target = "fuelCardAuthorizationDe"),
        Mapping(source = "fuelCardAuthorizationEU", target = "fuelCardAuthorizationEu"),
        Mapping(source = "chargingCardAuthorizationDE", target = "chargingCardAuthorizationDe"),
        Mapping(source = "chargingCardAuthorizationEU", target = "chargingCardAuthorizationEu"),
    )
    fun map(vehiclePersonDetail: VehiclePersonDetail): PeopleData

    @Mappings(
        Mapping(source = "propertyName", target = "field"),
        Mapping(source = "changedBy", target = "modifiedBy"),
        Mapping(source = "occurredOn", target = "modifiedAt"),
    )
    fun map(history: VehiclePersonHistoryEntry): PersonHistory

    fun map(history: List<VehiclePersonHistoryEntry>): List<PersonHistory>

    fun map(people: People): PeopleSearchResult

    fun map(employeeData: FVMEmployeeData): PeopleData

    fun map(salesPerson: VehicleSalesPersonDTO): PeopleData
}
