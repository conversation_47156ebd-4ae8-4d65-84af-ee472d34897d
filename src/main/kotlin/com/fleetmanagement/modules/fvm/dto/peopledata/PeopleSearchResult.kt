/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.peopledata

import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource

@AccessControlledResource(Resource.PEOPLE)
data class PeopleSearchResult(
    val employeeNumber: String?,
    val firstName: String?,
    val lastName: String?,
    val companyEmail: String?,
)
