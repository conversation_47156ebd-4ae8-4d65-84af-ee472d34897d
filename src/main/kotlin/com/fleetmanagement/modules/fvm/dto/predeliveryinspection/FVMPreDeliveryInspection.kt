package com.fleetmanagement.modules.fvm.dto.predeliveryinspection

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonInclude
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime
import java.util.*

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.PRE_DELIVERY_INSPECTION)
@JsonInclude(JsonInclude.Include.NON_NULL)
class FVMPreDeliveryInspection(
    val vehicleId: UUID?,
    val id: UUID?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_TIRE_SET)
    val tireSet: String?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_IS_RELEVANT)
    val isRelevant: Boolean?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_FOILING)
    val foiling: Boolean?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_REFUEL)
    val refuel: Boolean?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_CHARGE)
    val charge: Boolean?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_DIGITAL_LOGBOOK)
    val digitalLogbook: Boolean?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_LICENCE_PLATE_MOUNTING)
    val licencePlateMounting: Boolean?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_ORDERED_DATE)
    val orderedDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_COMPLETED_DATE)
    val completedDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_PLANNED_DATE)
    val plannedDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_COMMENT)
    val comment: String?,
)
