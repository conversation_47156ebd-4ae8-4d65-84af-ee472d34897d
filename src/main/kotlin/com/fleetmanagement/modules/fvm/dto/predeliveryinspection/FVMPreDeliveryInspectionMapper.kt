package com.fleetmanagement.modules.fvm.dto.predeliveryinspection

import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded.DLZViewPreDeliveryInspectionData
import java.util.*

fun DLZViewPreDeliveryInspectionData.toFVMPreDeliveryInspection(vehicleId: UUID?): FVMPreDeliveryInspection =
    FVMPreDeliveryInspection(
        vehicleId = vehicleId,
        id = this.id,
        tireSet = this.tireSet,
        isRelevant = this.isRelevant,
        foiling = this.foiling,
        refuel = this.refuel,
        charge = this.charge,
        digitalLogbook = this.digitalLogbook,
        licencePlateMounting = this.licencePlateMounting,
        orderedDate = this.orderedDate,
        completedDate = this.completedDate,
        plannedDate = this.plannedDate,
        comment = this.comment,
    )
