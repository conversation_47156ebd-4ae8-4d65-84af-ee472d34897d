/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto.predeliveryinspection

import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime
import java.util.*

@AccessControlledResource(Resource.PRE_DELIVERY_INSPECTION)
data class FVMPreDeliveryInspectionUpdate(
    val id: UUID,
    val version: Int,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_TIRE_SET)
    val tireSet: Optional<String>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_IS_RELEVANT)
    val isRelevant: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_FOILING)
    val foiling: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_REFUEL)
    val refuel: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_CHARGE)
    val charge: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_DIGITAL_LOGBOOK)
    val digitalLogbook: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_LICENCE_PLATE_MOUNTING)
    val licencePlateMounting: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_ORDERED_DATE)
    val orderedDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_COMPLETED_DATE)
    val completedDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_PLANNED_DATE)
    val plannedDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_COMMENT)
    val comment: Optional<String>?,
)
