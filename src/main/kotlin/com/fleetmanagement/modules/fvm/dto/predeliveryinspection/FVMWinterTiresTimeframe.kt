/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto.predeliveryinspection

import com.fasterxml.jackson.annotation.JsonFilter
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.predeliveryinspection.application.port.WinterTiresTimeframeDto
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.LocalDate
import java.util.*

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.PRE_DELIVERY_INSPECTION)
data class FVMWinterTiresTimeframe(
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_WINTER_TIRES_ID)
    val id: UUID,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_WINTER_TIRES_FROM_DATE)
    val fromDate: LocalDate,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_WINTER_TIRES_TO_DATE)
    val toDate: LocalDate,
    @AccessControlledField(FieldLabel.PRE_DELIVERY_INSPECTION_WINTER_TIRES_VERSION)
    val version: Int,
)

data class FVMWinterTiresTimeframeUpdate(
    val data: List<FVMWinterTiresTimeframe>,
)

fun FVMWinterTiresTimeframe.toWinterTiresTimeframeDto(): WinterTiresTimeframeDto =
    WinterTiresTimeframeDto(
        id = this.id,
        fromDate = this.fromDate,
        toDate = this.toDate,
        version = version,
    )

fun WinterTiresTimeframeDto.toFVMWinterTiresTimeframe(): FVMWinterTiresTimeframe =
    FVMWinterTiresTimeframe(
        id = this.id,
        fromDate = this.fromDate,
        toDate = this.toDate,
        version = this.version,
    )
