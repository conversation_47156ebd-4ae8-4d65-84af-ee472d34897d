/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.usagegroup

import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupDto
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupNewOrUpdate
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

@Mapper
interface UsageGroupMapper {
    companion object {
        val INSTANCE: UsageGroupMapper = Mappers.getMapper(UsageGroupMapper::class.java)
    }

    fun map(usageGroupDto: UsageGroupDto): FVMUsageGroup

    fun map(fvmCreateOrUpdateUsageGroup: FVMCreateOrUpdateUsageGroup): UsageGroupNewOrUpdate
}
