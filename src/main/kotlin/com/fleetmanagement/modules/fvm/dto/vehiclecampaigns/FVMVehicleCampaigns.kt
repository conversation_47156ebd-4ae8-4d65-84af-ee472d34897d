/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehiclecampaigns

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleTransferJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.vehiclecampaigns.api.dtos.VehicleCampaign
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE_CAMPAIGNS)
data class FVMVehicleCampaigns(
    @AccessControlledField(FieldLabel.CAMPAIGN_IDS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val campaignIds: String?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.CAMPAIGN)
data class FVMCampaign(
    @AccessControlledField(FieldLabel.CAMPAIGN_ID)
    val id: String,
    @AccessControlledField(FieldLabel.CAMPAIGN_DESCRIPTION)
    val description: String?,
    @AccessControlledField(FieldLabel.CAMPAIGN_DESCRIPTION)
    val isProcessable: Boolean?,
    @AccessControlledField(FieldLabel.CAMPAIGN_ISPROCESSABLE)
    val createdAt: OffsetDateTime?,
)

fun VehicleCampaign.toDTO() =
    FVMCampaign(
        id = this.campaignId,
        description = this.campaignDescription,
        isProcessable = this.isProcessable,
        createdAt = this.createdAt,
    )
