/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehiclecampaigns

import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.DLZViewVehicleRow
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface FVMVehicleCampaignsDataMapper {
    companion object {
        val INSTANCE: FVMVehicleCampaignsDataMapper = Mappers.getMapper(FVMVehicleCampaignsDataMapper::class.java)
    }

    @Mappings(
        Mapping(target = "campaignIds", source = "vehicleCampaigns.campaignIds"),
    )
    fun map(dlzViewVehicleRow: DLZViewVehicleRow): FVMVehicleCampaigns

    fun map(
        dlzViewVehicleRow: com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVehicleCampaignsData,
    ): FVMVehicleCampaigns
}
