/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehicledata

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonView
import com.fasterxml.jackson.databind.JsonNode
import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleSalesJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleTransferJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleData(
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val id: String,
    @AccessControlledField(FieldLabel.VIN)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val vin: String?,
    @AccessControlledField(FieldLabel.VGUID)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val vguid: String?,
    @AccessControlledField(FieldLabel.EQUI_ID)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val equiId: String?,
    @AccessControlledField(FieldLabel.SOURCE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val source: VehicleSource? = VehicleSource.UNKNOWN,
    @AccessControlledField(FieldLabel.REFERENCE_ID)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val referenceId: String?,
    @AccessControlledField(FieldLabel.EQUIPMENT_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val equipmentNumber: Long?,
    @AccessControlledField(FieldLabel.FINANCIAL_ASSET_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val financialAssetType: FinancialAssetType?,
    @AccessControlledField(FieldLabel.OPTIONS)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val options: JsonNode?,
    @AccessControlledField(FieldLabel.CURRENT_TIRES)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val currentTires: TireSet?,
    @AccessControlledField(FieldLabel.EXTERNAL_LEASE_START)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val externalLeaseStart: String?,
    @AccessControlledField(FieldLabel.EXTERNAL_LEASE_END)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val externalLeaseEnd: String?,
    @AccessControlledField(FieldLabel.EXTERNAL_LEASE_RATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val externalLeaseRate: Float?,
    @AccessControlledField(FieldLabel.EXTERNAL_LEASE_LESSEE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val externalLeaseLessee: String?,
    @AccessControlledField(FieldLabel.CREATED_AT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val createdAt: String?,
    @AccessControlledField(FieldLabel.NUMBER_OF_DAMAGES)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val numberOfDamages: Int?,
    @AccessControlledField(FieldLabel.REPAIRFIX_CAR_ID)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val repairfixCarId: String?,
    @AccessControlledField(FieldLabel.TUEV_APPOINTMENT)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val tuevAppointment: LocalDate?,
    @AccessControlledField(FieldLabel.STATUS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val status: String?,
    val version: Int,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val model: FVMVehicleModelData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val consumption: FVMVehicleConsumptionData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val order: FVMVehicleOrderData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val production: FVMVehicleProductionData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val country: FVMVehicleCountryData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val pmp: FVMVehiclePMPData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val embargo: FVMVehicleEmbargoData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val color: FVMVehicleColorData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val price: FVMVehiclePriceData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val technical: FVMVehicleTechnicalData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val fleet: FVMVehicleFleetData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val delivery: FVMVehicleDeliveryData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val returnInfo: FVMVehicleReturnData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val tireSetChange: FVMTireSetChangeData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val wltp: FVMVehicleWltpData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val evaluation: FVMVehicleEvaluationData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val currentMileage: FVMVehicleCurrentMileageData?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val vtstamm: FVMVehicleVTStammData?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleModelData(
    @AccessControlledField(FieldLabel.MODEL_DESCRIPTION)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val description: String?,
    @AccessControlledField(FieldLabel.MODEL_YEAR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val year: Int?,
    @AccessControlledField(FieldLabel.MODEL_PRODUCT_ID)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val productId: String?,
    @AccessControlledField(FieldLabel.MODEL_PRODUCT_CODE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val productCode: String?,
    @AccessControlledField(FieldLabel.MODEL_ORDERTYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val orderType: String?,
    @AccessControlledField(FieldLabel.MODEL_VEHICLE_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val vehicleType: VehicleType?,
    @AccessControlledField(FieldLabel.MODEL_MANUFACTURER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val manufacturer: String?,
    @AccessControlledField(FieldLabel.MODEL_RANGE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val range: String?,
    @AccessControlledField(FieldLabel.MODEL_DESCRIPTION_DEVELOPMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val modelDescriptionDevelopment: String?,
    @AccessControlledField(FieldLabel.MODEL_RANGE_DEVELOPMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val rangeDevelopment: String?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleConsumptionData(
    @AccessControlledField(FieldLabel.CONSUMPTION_DRIVE_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val driveType: String?,
    @AccessControlledField(FieldLabel.CONSUMPTION_DATA_TYPIFICATION)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val typification: String?,
    @AccessControlledField(FieldLabel.CONSUMPTION_PRIMARY_FUEL_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val primaryFuelType: FuelType?,
    @AccessControlledField(FieldLabel.CONSUMPTION_SECONDARY_FUEL_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val secondaryFuelType: FuelType?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleOrderData(
    @AccessControlledField(FieldLabel.ORDER_DEPARTMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val department: String?,
    @AccessControlledField(FieldLabel.ORDER_TRADING_PARTNER_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val tradingPartnerNumber: String?,
    @AccessControlledField(FieldLabel.ORDER_PURPOSE_ORDER_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val purposeOrderType: String?,
    @AccessControlledField(FieldLabel.ORDER_IMPORTER_SHORT_NAME)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val importerShortName: String?,
    @AccessControlledField(FieldLabel.ORDER_PORT_CODE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val portCode: String?,
    @AccessControlledField(FieldLabel.ORDER_COMMISSION_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val commissionNumber: String?,
    @AccessControlledField(FieldLabel.ORDER_INVOICE_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val invoiceNumber: String?,
    @AccessControlledField(FieldLabel.ORDER_INVOICE_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val invoiceDate: String?,
    @AccessControlledField(FieldLabel.ORDER_PURCHASE_ORDER_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val purchaseOrderDate: String?,
    @AccessControlledField(FieldLabel.ORDER_REQUESTED_DELIVERY_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val requestedDeliveryDate: String?,
    @AccessControlledField(FieldLabel.ORDER_CUSTOMER_DELIVERY_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val customerDeliveryDate: String?,
    @AccessControlledField(FieldLabel.ORDER_DELIVERY_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val deliveryType: String?,
    @AccessControlledField(FieldLabel.ORDER_PRIMARY_STATUS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val primaryStatus: String?,
    @AccessControlledField(FieldLabel.ORDER_LEASING_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val leasingType: String?,
    @AccessControlledField(FieldLabel.ORDER_PREPRODUCTION_VEHICLE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val preproductionVehicle: Boolean?,
    @AccessControlledField(FieldLabel.ORDER_BLOCKED_FOR_SALE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val blockedForSale: Boolean?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleProductionData(
    @AccessControlledField(FieldLabel.PRODUCTION_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val number: String?,
    @AccessControlledField(FieldLabel.PRODUCTION_NUMBER_VW)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val numberVW: String?,
    @AccessControlledField(FieldLabel.PRODUCTION_END_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val endDate: String?,
    @AccessControlledField(FieldLabel.PRODUCTION_TECHNICAL_MODEL_YEAR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val technicalModelYear: Int?,
    @AccessControlledField(FieldLabel.PRODUCTION_FACTORY)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val factory: String?,
    @AccessControlledField(FieldLabel.PRODUCTION_FACTORY_VW)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val factoryVW: Int?,
    @AccessControlledField(FieldLabel.PRODUCTION_QUOTE_MONTH)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val quoteMonth: Int?,
    @AccessControlledField(FieldLabel.PRODUCTION_QUOTE_YEAR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val quoteYear: Int?,
    @AccessControlledField(FieldLabel.PRODUCTION_PLANNED_END_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val plannedEndDate: String?,
    @AccessControlledField(FieldLabel.PRODUCTION_GEAR_BOX_CLASS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val gearBoxClass: Char?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleCountryData(
    @AccessControlledField(FieldLabel.COUNTRY_BNR_VALUE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val bnrValue: String?,
    @AccessControlledField(FieldLabel.COUNTRY_CNR_VALUE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val cnrValue: String?,
    @AccessControlledField(FieldLabel.COUNTRY_CNR_DESCRIPTION)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val cnrCountryDescription: String?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehiclePMPData(
    @AccessControlledField(FieldLabel.PMP_ODOMETER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val odometer: Long?,
    @AccessControlledField(FieldLabel.PMP_TIMESTAMP)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val timestamp: String?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleEmbargoData(
    @AccessControlledField(FieldLabel.EMBARGO_IN_EMBARGO)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val inEmbargo: Boolean?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleColorData(
    @AccessControlledField(FieldLabel.COLOR_EXTERIOR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val exterior: String?,
    @AccessControlledField(FieldLabel.COLOR_EXTERIOR_DESCRIPTION)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val exteriorDescription: String?,
    @AccessControlledField(FieldLabel.COLOR_INTERIOR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val interior: String?,
    @AccessControlledField(FieldLabel.COLOR_INTERIOR_DESCRIPTION)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val interiorDescription: String?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehiclePriceData(
    @AccessControlledField(FieldLabel.PRICE_GROSS_PRICE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val grossPrice: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_GROSS_PRICE_WITH_EXTRAS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val grossPriceWithExtras: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_GROSS_PRICE_PLAN)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val grossPricePlan: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_GROSS_PRICE_NEW_CAR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val grossPriceNewCar: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_NET_PRICE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val netPrice: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_NET_PRICE_WITH_EXTRAS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val netPriceWithExtras: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_NET_PRICE_NEW_CAR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val netPriceNewCar: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_VALUE_ADDED_TAX)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val valueAddedTax: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_FACTORY_NET_PRICE_EUR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val factoryNetPriceEUR: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_FACTORY_GROSS_PRICE_EUR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val factoryGrossPriceEUR: BigDecimal?,
    @AccessControlledField(FieldLabel.PRICE_PIA_EVENT)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val piaEvent: JsonNode?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleTechnicalData(
    @AccessControlledField(FieldLabel.TECHNICAL_AMOUNT_SEATS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val amountSeats: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_ENGINE_CAPACITY)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val engineCapacity: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_CARGO_VOLUME)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val cargoVolume: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_VEHICLE_WIDTH_MIRRORS_EXTENDED)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val vehicleWidthMirrorsExtended: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_MAXIMUM_CHARGING_POWER_DC)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val maximumChargingPowerDc: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_GROSS_VEHICLE_WEIGHT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val grossVehicleWeight: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_CURB_WEIGHT_EU)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val curbWeightEu: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_TOTAL_POWER_KW)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val totalPowerKw: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_CURB_WEIGHT_DIN)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val curbWeightDin: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_MAXIMUM_PAYLOAD)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val maximumPayload: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_CHARGING_TIME_AC_22_KW)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val chargingTimeAc22Kw: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_CHARGING_TIME_AC_11_KW_0100)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val chargingTimeAc11Kw0100: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_CHARGING_TIME_AC_96_KW_0100)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val chargingTimeAc96Kw0100: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_NET_BATTERY_CAPACITY)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val netBatteryCapacity: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_GROSS_BATTERY_CAPACITY)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val grossBatteryCapacity: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_ACCELERATION_0100_KMH)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val acceleration0100Kmh: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_ACCELERATION_0100_KMH_LAUNCH_CONTROL)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val acceleration0100KmhLaunchControl: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_TOP_SPEED)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val topSpeed: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_HEIGHT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val height: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_WIDTH_MIRRORS_FOLDED)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val widthMirrorsFolded: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_LENGTH)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val length: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_ACCELERATION_80_120_KMH)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val acceleration80120Kmh: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_MAX_ROOF_LOAD_WITH_PORSCHE_ROOF_TRANSPORT_SYSTEM)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val maxRoofLoadWithPorscheRoofTransportSystem: Int?,
    @AccessControlledField(FieldLabel.TECHNICAL_CHARGING_TIME_DC_MAX_POWER_580)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val chargingTimeDcMaxPower580: Float?,
    @AccessControlledField(FieldLabel.TECHNICAL_POWER_KW)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val powerKw: Int?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleFleetData(
    @AccessControlledField(FieldLabel.FLEET_SCRAP_VEHICLE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val scrapVehicle: Boolean?,
    @AccessControlledField(FieldLabel.FLEET_SOLD_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val soldDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.FLEET_SCRAPPED_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val scrappedDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.FLEET_STOLEN_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val stolenDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.FLEET_SOLD_CUP_CAR_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val soldCupCarDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.FLEET_APPROVED_FOR_SCRAPPING_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val approvedForScrappingDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.FLEET_SCRAPPED_VEHICLE_OFFERED_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val scrappedVehicleOfferedDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.FLEET_VEHICLE_SENT_TO_SALES_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val vehicleSentToSalesDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.FLEET_COST_ESTIMATION_ORDERED_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val costEstimationOrderedDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.FLEET_IS_RESIDUAL_VALUE_MARKET)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val isResidualValueMarket: Boolean?,
    @AccessControlledField(FieldLabel.FLEET_PROFITABILITY_AUDIT_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val profitabilityAuditDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.FLEET_COMMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val comment: String?,
    @AccessControlledField(FieldLabel.FLEET_RACE_CAR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val raceCar: Boolean,
    @AccessControlledField(FieldLabel.FLEET_CLASSIC)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val classic: Boolean,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleEvaluationData(
    @AccessControlledField(FieldLabel.EVALUATION_APPRAISAL_NET_PRICE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val appraisalNetPrice: BigDecimal?,
    @AccessControlledField(FieldLabel.EVALUATION_VEHICLE_EVALUATION_COMMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val vehicleEvaluationComment: String?,
    @AccessControlledField(FieldLabel.EVALUATION_PC_COMPLAINT_CHECK_COMMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val pcComplaintCheckComment: String?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleDeliveryData(
    @AccessControlledField(FieldLabel.DELIVERY_PREPARATION_DONE_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val preparationDoneDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.DELIVERY_IS_PREPARATION_NECESSARY)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val isPreparationNecessary: Boolean?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleReturnData(
    @AccessControlledField(FieldLabel.RETURN_NEXT_PROCESS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val nextProcess: NextProcess?,
    @AccessControlledField(FieldLabel.RETURN_IS_USED_CAR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val isUsedCar: Boolean?,
    @AccessControlledField(FieldLabel.RETURN_KEY_RETURNED)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val keyReturned: OffsetDateTime?,
    @AccessControlledField(FieldLabel.RETURN_FACTORY_CAR_PREPARATION_ORDER_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val factoryCarPreparationOrderNumber: String?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMTireSetChangeData(
    @AccessControlledField(FieldLabel.TIRE_SET_CHANGE_COMMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val comment: String?,
    @AccessControlledField(FieldLabel.TIRE_SET_CHANGE_ORDERED_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val orderedDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.TIRE_SET_CHANGE_COMPLETED_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val completedDate: OffsetDateTime?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleWltpData(
    @AccessControlledField(FieldLabel.WLTP_VEHICLE_WEIGHT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val vehicleWeight: Int?,
    @AccessControlledField(FieldLabel.WLTP_ELECTRIC_RANGE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val electricRange: Int?,
    @AccessControlledField(FieldLabel.WLTP_CO2_COMBINED)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val co2Combined: Int?,
    @AccessControlledField(FieldLabel.WLTP_ELECTRIC_RANGE_CITY)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val electricRangeCity: Int?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleCurrentMileageData(
    @AccessControlledField(FieldLabel.VEHICLE_CURRENT_MILEAGE_MILEAGE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val mileage: Int?,
    @AccessControlledField(FieldLabel.VEHICLE_CURRENT_MILEAGE_READ_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val readDate: OffsetDateTime?,
)

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleVTStammData(
    @AccessControlledField(FieldLabel.VTSTAMM_SUBJECT_TO_CONFIDENTIALITY)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val subjectToConfidentiality: Boolean?,
    @AccessControlledField(FieldLabel.VTSTAMM_CONFIDENTIALITY_CLASSIFICATION)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val confidentialityClassification: String?,
    @AccessControlledField(FieldLabel.VTSTAMM_SUBJECT_TO_CONFIDENTIALITY_START_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val subjectToConfidentialityStartDate: LocalDate?,
    @AccessControlledField(FieldLabel.VTSTAMM_SUBJECT_TO_CONFIDENTIALITY_END_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val subjectToConfidentialityEndDate: LocalDate?,
    @AccessControlledField(FieldLabel.VTSTAMM_RECORD_FACTORY_EXIT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val recordFactoryExit: Boolean?,
    @AccessControlledField(FieldLabel.VTSTAMM_CAMOUFLAGE_REQUIRED)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val camouflageRequired: Boolean?,
    @AccessControlledField(FieldLabel.VTSTAMM_INTERNAL_DESIGNATION)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val internalDesignation: String?,
    @AccessControlledField(FieldLabel.VTSTAMM_TYPE_OF_USE_VTS)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val typeOfUseVTS: String?,
    @AccessControlledField(FieldLabel.VTSTAMM_STATUS_VTS)
    @field:JsonView(
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val statusVTS: String?,
)
