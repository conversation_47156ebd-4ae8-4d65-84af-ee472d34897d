/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehicledata

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.DLZViewVehicleRow
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import org.mapstruct.*
import org.mapstruct.factory.Mappers
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.format.DateTimeFormatter

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE, nullValueMappingStrategy = NullValueMappingStrategy.RETURN_DEFAULT)
interface FVMVehicleDataMapper {
    companion object {
        val INSTANCE: FVMVehicleDataMapper = Mappers.getMapper(FVMVehicleDataMapper::class.java)
        private val objectMapper = jacksonObjectMapper()

        @Named("formatLocalDate")
        @JvmStatic
        fun formatLocalDate(date: LocalDate?): String? = date?.format(DateTimeFormatter.ofPattern("yyyy-MM-dd"))

        @Named("formatLocalDateWithTime")
        @JvmStatic
        fun formatLocalDateWithTime(date: LocalDateTime?): String? = date?.format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.nnn"))

        @Named("stringToObject")
        @JvmStatic
        fun stringToJsonNode(json: String?): JsonNode? = json?.let { objectMapper.readTree(it) }
    }

    @Mappings(
        Mapping(target = "vin", source = "vehicle.vin"),
        Mapping(target = "vguid", source = "vehicle.vguid"),
        Mapping(target = "equiId", source = "vehicle.equiId"),
        Mapping(target = "source", source = "vehicle.source"),
        Mapping(target = "referenceId", source = "vehicle.referenceId"),
        Mapping(target = "equipmentNumber", source = "vehicle.equipmentNumber"),
        Mapping(target = "financialAssetType", source = "vehicle.financialAssetType"),
        Mapping(target = "currentTires", source = "vehicle.currentTires"),
        Mapping(target = "externalLeaseStart", source = "vehicle.externalLeaseStart"),
        Mapping(target = "externalLeaseEnd", source = "vehicle.externalLeaseEnd"),
        Mapping(target = "externalLeaseRate", source = "vehicle.externalLeaseRate"),
        Mapping(target = "externalLeaseLessee", source = "vehicle.externalLeaseLessee"),
        Mapping(target = "createdAt", source = "vehicle.createdAt"),
        Mapping(target = "numberOfDamages", source = "vehicle.numberOfDamages"),
        Mapping(target = "repairfixCarId", source = "vehicle.repairfixCarId"),
        Mapping(target = "tuevAppointment", source = "vehicle.tuevAppointment"),
        Mapping(target = "status", source = "vehicle.status"),
        Mapping(target = "version", source = "vehicle.version"),
        Mapping(target = "model.description", source = "vehicle.model.description"),
        // Mapping(target = "model.year", source = "vehicle.model.year"),
        Mapping(target = "model.productId", source = "vehicle.model.productId"),
        Mapping(target = "model.productCode", source = "vehicle.model.productCode"),
        Mapping(target = "model.orderType", source = "vehicle.model.orderType"),
        Mapping(target = "model.vehicleType", source = "vehicle.model.vehicleType"),
        Mapping(target = "model.manufacturer", source = "vehicle.model.manufacturer"),
        Mapping(target = "model.range", source = "vehicle.model.range"),
        Mapping(target = "consumption.typification", source = "vehicle.consumption.typification"),
        Mapping(target = "consumption.driveType", source = "vehicle.consumption.driveType"),
        Mapping(target = "consumption.primaryFuelType", source = "vehicle.consumption.primaryFuelType"),
        Mapping(target = "consumption.secondaryFuelType", source = "vehicle.consumption.secondaryFuelType"),
        Mapping(target = "order.department", source = "vehicle.order.department"),
        Mapping(target = "order.tradingPartnerNumber", source = "vehicle.order.tradingPartnerNumber"),
        Mapping(target = "order.purposeOrderType", source = "vehicle.order.purposeOrderType"),
        Mapping(target = "order.importerShortName", source = "vehicle.order.importerShortName"),
        // Mapping(target = "order.portCode", source = "vehicle.order.portCode"),
        Mapping(target = "order.commissionNumber", source = "vehicle.order.commissionNumber"),
        Mapping(target = "order.invoiceNumber", source = "vehicle.order.invoiceNumber"),
        Mapping(
            target = "order.invoiceDate",
            source = "vehicle.order.invoiceDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        Mapping(
            target = "order.purchaseOrderDate",
            source = "vehicle.order.purchaseOrderDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        Mapping(
            target = "order.requestedDeliveryDate",
            source = "vehicle.order.requestedDeliveryDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        /*
        Mapping(
            target = "order.customerDeliveryDate",
            source = "vehicle.order.customerDeliveryDate",
            qualifiedByName = ["formatLocalDate"]
        ),

         */
        Mapping(target = "order.deliveryType", source = "vehicle.order.deliveryType"),
        Mapping(target = "order.primaryStatus", source = "vehicle.order.primaryStatus"),
        Mapping(target = "order.leasingType", source = "vehicle.order.leasingType"),
        Mapping(target = "order.preproductionVehicle", source = "vehicle.order.preproductionVehicle"),
        Mapping(target = "order.blockedForSale", source = "vehicle.order.blockedForSale"),
        Mapping(target = "production.number", source = "vehicle.production.number"),
        // Mapping(target = "production.numberVW", source = "vehicleData.production.numberVW"),
        Mapping(
            target = "production.endDate",
            source = "vehicle.production.endDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        Mapping(target = "production.technicalModelYear", source = "vehicle.production.technicalModelYear"),
        Mapping(target = "production.factory", source = "vehicle.production.factory"),
        Mapping(target = "production.factoryVW", source = "vehicle.production.factoryVW"),
        Mapping(target = "production.quoteMonth", source = "vehicle.production.quoteMonth"),
        Mapping(target = "production.quoteYear", source = "vehicle.production.quoteYear"),
        Mapping(
            target = "production.plannedEndDate",
            source = "vehicle.production.plannedEndDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        Mapping(target = "production.gearBoxClass", source = "vehicle.production.gearBoxClass"),
        Mapping(target = "country.bnrValue", source = "vehicle.country.bnrValue"),
        Mapping(target = "country.cnrValue", source = "vehicle.country.cnrValue"),
        Mapping(
            target = "country.cnrCountryDescription",
            source = "vehicle.country.cnrCountryDescription",
        ),
        Mapping(target = "pmp.odometer", source = "vehicle.pmp.odometer"),
        Mapping(
            target = "pmp.timestamp",
            source = "vehicle.pmp.timestamp",
            qualifiedByName = ["formatLocalDateWithTime"],
        ),
        Mapping(target = "embargo.inEmbargo", source = "vehicle.embargo.inEmbargo"),
        Mapping(target = "color.exterior", source = "vehicle.color.exterior"),
        Mapping(target = "color.exteriorDescription", source = "vehicle.color.exteriorDescription"),
        Mapping(target = "color.interior", source = "vehicle.color.interior"),
        Mapping(target = "color.interiorDescription", source = "vehicle.color.interiorDescription"),
        Mapping(target = "price.grossPriceWithExtras", source = "vehicle.price.grossPriceWithExtras"),
        Mapping(target = "price.netPriceWithExtras", source = "vehicle.price.netPriceWithExtras"),
        Mapping(target = "price.factoryNetPriceEUR", source = "vehicle.price.factoryNetPriceEUR"),
        Mapping(target = "price.factoryGrossPriceEUR", source = "vehicle.price.factoryGrossPriceEUR"),
        /*Mapping(target = "price.grossPriceWithExtras", source = "vehicle.price.grossPriceWithExtras"),
        Mapping(target = "price.grossPricePlan", source = "vehicle.price.grossPricePlan"),
        Mapping(target = "price.grossPriceNewCar", source = "vehicle.price.grossPriceNewCar"),
        Mapping(target = "price.netPrice", source = "vehicle.price.netPrice"),
        Mapping(target = "price.netPriceNewCar", source = "vehicle.price.netPriceNewCar"),
        Mapping(target = "price.valueAddedTax", source = "vehicle.price.valueAddedTax"),
         */
        Mapping(target = "technical.amountSeats", source = "vehicle.technical.amountSeats"),
        Mapping(target = "technical.engineCapacity", source = "vehicle.technical.engineCapacity"),
        Mapping(target = "technical.cargoVolume", source = "vehicle.technical.cargoVolume"),
        Mapping(target = "technical.vehicleWidthMirrorsExtended", source = "vehicle.technical.vehicleWidthMirrorsExtended"),
        Mapping(target = "technical.maximumChargingPowerDc", source = "vehicle.technical.maximumChargingPowerDc"),
        Mapping(target = "technical.grossVehicleWeight", source = "vehicle.technical.grossVehicleWeight"),
        Mapping(target = "technical.curbWeightEu", source = "vehicle.technical.curbWeightEu"),
        Mapping(target = "technical.totalPowerKw", source = "vehicle.technical.totalPowerKw"),
        Mapping(target = "technical.curbWeightDin", source = "vehicle.technical.curbWeightDin"),
        Mapping(target = "technical.maximumPayload", source = "vehicle.technical.maximumPayload"),
        Mapping(target = "technical.chargingTimeAc22Kw", source = "vehicle.technical.chargingTimeAc22Kw"),
        Mapping(target = "technical.chargingTimeAc11Kw0100", source = "vehicle.technical.chargingTimeAc11kw0100"),
        Mapping(target = "technical.chargingTimeAc96Kw0100", source = "vehicle.technical.chargingTimeAc96kw0100"),
        Mapping(target = "technical.netBatteryCapacity", source = "vehicle.technical.netBatteryCapacity"),
        Mapping(target = "technical.grossBatteryCapacity", source = "vehicle.technical.grossBatteryCapacity"),
        Mapping(target = "technical.acceleration0100Kmh", source = "vehicle.technical.acceleration0100Kmh"),
        Mapping(target = "technical.acceleration0100KmhLaunchControl", source = "vehicle.technical.acceleration0100KmhLaunchControl"),
        Mapping(target = "technical.topSpeed", source = "vehicle.technical.topSpeed"),
        Mapping(target = "technical.height", source = "vehicle.technical.height"),
        Mapping(target = "technical.widthMirrorsFolded", source = "vehicle.technical.widthMirrorsFolded"),
        Mapping(target = "technical.length", source = "vehicle.technical.length"),
        Mapping(target = "technical.acceleration80120Kmh", source = "vehicle.technical.acceleration80120Kmh"),
        Mapping(
            target = "technical.maxRoofLoadWithPorscheRoofTransportSystem",
            source = "vehicle.technical.maxRoofLoadWithPorscheRoofTransportSystem",
        ),
        Mapping(target = "technical.chargingTimeDcMaxPower580", source = "vehicle.technical.chargingTimeDcMaxPower580"),
        Mapping(target = "technical.powerKw", source = "vehicle.technical.powerKw"),
        Mapping(target = "fleet.scrapVehicle", source = "vehicle.fleet.scrapVehicle"),
        Mapping(target = "fleet.soldDate", source = "vehicle.fleet.soldDate"),
        Mapping(target = "fleet.scrappedDate", source = "vehicle.fleet.scrappedDate"),
        Mapping(target = "fleet.stolenDate", source = "vehicle.fleet.stolenDate"),
        Mapping(target = "fleet.soldCupCarDate", source = "vehicle.fleet.soldCupCarDate"),
        Mapping(target = "fleet.approvedForScrappingDate", source = "vehicle.fleet.approvedForScrappingDate"),
        Mapping(target = "fleet.scrappedVehicleOfferedDate", source = "vehicle.fleet.scrappedVehicleOfferedDate"),
        Mapping(target = "fleet.vehicleSentToSalesDate", source = "vehicle.fleet.vehicleSentToSalesDate"),
        Mapping(target = "fleet.costEstimationOrderedDate", source = "vehicle.fleet.costEstimationOrderedDate"),
        Mapping(target = "fleet.isResidualValueMarket", source = "vehicle.fleet.residualValueMarket"),
        Mapping(target = "fleet.profitabilityAuditDate", source = "vehicle.fleet.profitabilityAuditDate"),
        Mapping(target = "fleet.comment", source = "vehicle.fleet.comment"),
        Mapping(target = "fleet.raceCar", source = "vehicle.fleet.raceCar"),
        Mapping(target = "fleet.classic", source = "vehicle.fleet.classic"),
        Mapping(target = "delivery.preparationDoneDate", source = "vehicle.delivery.preparationDoneDate"),
        Mapping(target = "delivery.isPreparationNecessary", source = "vehicle.delivery.preparationNecessary"),
        Mapping(target = "returnInfo.nextProcess", source = "vehicle.returnInfo.nextProcess"),
        Mapping(target = "returnInfo.isUsedCar", source = "vehicle.returnInfo.usedCar"),
        Mapping(target = "returnInfo.keyReturned", source = "vehicle.returnInfo.keyReturned"),
        Mapping(target = "returnInfo.factoryCarPreparationOrderNumber", source = "vehicle.returnInfo.factoryCarPreparationOrderNumber"),
        Mapping(target = "tireSetChange.comment", source = "vehicle.tireSetChange.comment"),
        Mapping(target = "tireSetChange.orderedDate", source = "vehicle.tireSetChange.orderedDate"),
        Mapping(target = "tireSetChange.completedDate", source = "vehicle.tireSetChange.completedDate"),
        Mapping(target = "wltp", source = "vehicle.wltp"),
        Mapping(target = "evaluation.appraisalNetPrice", source = "vehicle.evaluation.appraisalNetPrice"),
        Mapping(target = "evaluation.vehicleEvaluationComment", source = "vehicle.evaluation.vehicleEvaluationComment"),
        Mapping(target = "evaluation.pcComplaintCheckComment", source = "vehicle.evaluation.pcComplaintCheckComment"),
        Mapping(target = "currentMileage.mileage", source = "vehicle.currentMileage.mileage"),
        Mapping(target = "currentMileage.readDate", source = "vehicle.currentMileage.readDate"),
        Mapping(target = "vtstamm", source = "vehicle.vtstamm"),
    )
    fun map(dlzViewVehicleRow: DLZViewVehicleRow): FVMVehicleData

    @Mappings(
        Mapping(target = "externalLeaseStart", source = "externalLeaseStart", dateFormat = "yyyy-MM-dd"),
        Mapping(target = "externalLeaseEnd", source = "externalLeaseEnd", dateFormat = "yyyy-MM-dd"),
        Mapping(target = "order.invoiceDate", source = "order.invoiceDate", dateFormat = "yyyy-MM-dd"),
        Mapping(target = "order.purchaseOrderDate", source = "order.purchaseOrderDate", dateFormat = "yyyy-MM-dd"),
        Mapping(
            target = "order.requestedDeliveryDate",
            source = "order.requestedDeliveryDate",
            dateFormat = "yyyy-MM-dd",
        ),
        Mapping(
            target = "order.customerDeliveryDate",
            source = "order.customerDeliveryDate",
            dateFormat = "yyyy-MM-dd",
        ),
        Mapping(target = "production.endDate", source = "production.endDate", dateFormat = "yyyy-MM-dd"),
        Mapping(
            target = "production.plannedEndDate",
            source = "production.plannedEndDate",
            dateFormat = "yyyy-MM-dd",
        ),
        Mapping(target = "pmp.timestamp", source = "pmp.timestamp", dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS"),
        Mapping(target = "returnInfo.isUsedCar", source = "returnInfo.usedCar"),
        Mapping(target = "delivery.isPreparationNecessary", source = "delivery.preparationNecessary"),
    )
    fun map(vehicle: VehicleDTO): FVMVehicleData

    @Mappings(
        Mapping(
            target = "order.invoiceDate",
            source = "order.invoiceDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        Mapping(
            target = "order.purchaseOrderDate",
            source = "order.purchaseOrderDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        Mapping(
            target = "order.requestedDeliveryDate",
            source = "order.requestedDeliveryDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        Mapping(
            target = "pmp.timestamp",
            source = "pmp.timestamp",
            qualifiedByName = ["formatLocalDateWithTime"],
        ),
        Mapping(
            target = "production.endDate",
            source = "production.endDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        Mapping(
            target = "production.plannedEndDate",
            source = "production.plannedEndDate",
            qualifiedByName = ["formatLocalDate"],
        ),
        // because mapstruct can´t deal with 'is'Booleans
        Mapping(
            target = "fleet.isResidualValueMarket",
            source = "fleet.residualValueMarket",
        ),
        Mapping(
            target = "delivery.isPreparationNecessary",
            source = "delivery.preparationNecessary",
        ),
        Mapping(
            target = "returnInfo.isUsedCar",
            source = "returnInfo.usedCar",
        ),
        Mapping(
            target = "options",
            source = "options",
            qualifiedByName = ["stringToObject"],
        ),
    )
    fun map(
        fvmVehicleData: com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVehicleData,
    ): FVMVehicleData
}
