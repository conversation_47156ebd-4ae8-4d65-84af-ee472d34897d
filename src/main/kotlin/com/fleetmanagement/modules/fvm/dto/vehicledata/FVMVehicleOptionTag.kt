/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehicledata

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleOptionTagDTO

data class FVMVehicleOptionTag(
    val id: String?,
    val description: String?,
) {
    companion object {
        fun from(vehicleOptionTagDTO: VehicleOptionTagDTO): FVMVehicleOptionTag =
            FVMVehicleOptionTag(
                id = vehicleOptionTagDTO.optionId,
                description = vehicleOptionTagDTO.optionDescription,
            )
    }
}
