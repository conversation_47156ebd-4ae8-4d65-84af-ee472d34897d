/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto.vehicledata

import com.fleetmanagement.modules.fvm.dto.noncustomeradequate.FVMNonCustomerAdequateUpdate
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspectionUpdate
import com.fleetmanagement.modules.fvm.dto.vehiclesales.FVMVehicleSalesUpdate
import jakarta.validation.Valid

data class FVMVehicleRowUpdate(
    @field:Valid val vehicle: FVMVehicleUpdate,
    @field:Valid val preDeliveryInspection: FVMPreDeliveryInspectionUpdate?,
    @field:Valid val nonCustomerAdequate: FVMNonCustomerAdequateUpdate?,
    @field:Valid val vehicleSales: FVMVehicleSalesUpdate?,
)
