/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto.vehicledata

import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import jakarta.validation.Valid
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZonedDateTime
import java.util.Date
import java.util.Optional
import java.util.UUID

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleUpdate(
    val id: UUID,
    @AccessControlledField(FieldLabel.REFERENCE_ID)
    val referenceId: Optional<String>?,
    @AccessControlledField(FieldLabel.CURRENT_TIRES)
    val currentTires: Optional<String>?,
    @AccessControlledField(FieldLabel.EXTERNAL_LEASE_START)
    val externalLeaseStart: Optional<ZonedDateTime>?,
    @AccessControlledField(FieldLabel.EXTERNAL_LEASE_END)
    val externalLeaseEnd: Optional<ZonedDateTime>?,
    @AccessControlledField(FieldLabel.EXTERNAL_LEASE_RATE)
    val externalLeaseRate: Optional<Float>?,
    @AccessControlledField(FieldLabel.EXTERNAL_LEASE_LESSEE)
    val externalLeaseLessee: Optional<String>?,
    @AccessControlledField(FieldLabel.VTSTAMM_INTERNAL_DESIGNATION)
    val tuevAppointment: Optional<LocalDate>?,
    @AccessControlledField(FieldLabel.VTSTAMM_INTERNAL_DESIGNATION)
    val internalDesignation: Optional<String>?,
    val version: Int,
    @field:Valid val order: FVMVehicleOrderDataUpdate?,
    @field:Valid val consumption: FVMVehicleConsumptionDataUpdate?,
    @field:Valid val price: FVMVehiclePriceDataUpdate?,
    @field:Valid val technical: FVMVehicleTechnicalDataUpdate?,
    @field:Valid val fleet: FVMVehicleFleetDataUpdate?,
    @field:Valid val delivery: FVMVehicleDeliveryDataUpdate?,
    @field:Valid val returnInfo: FVMVehicleReturnDataUpdate?,
    @field:Valid val tireSetChange: FVMTireSetChangeUpdate?,
    @field:Valid val evaluation: FVMVehicleEvaluationDataUpdate?,
    @field:Valid val currentMileage: FVMVehicleCurrentMileageDataUpdate?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleOrderDataUpdate(
    @AccessControlledField(FieldLabel.ORDER_PURCHASE_ORDER_DATE)
    val purchaseOrderDate: Optional<Date>?,
    @AccessControlledField(FieldLabel.ORDER_REQUESTED_DELIVERY_DATE)
    val requestedDeliveryDate: Optional<Date>?,
    @AccessControlledField(FieldLabel.ORDER_DELIVERY_TYPE)
    val deliveryType: Optional<String>?,
    @AccessControlledField(FieldLabel.ORDER_PRIMARY_STATUS)
    val primaryStatus: Optional<String>?,
    @AccessControlledField(FieldLabel.ORDER_PREPRODUCTION_VEHICLE)
    val preproductionVehicle: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.ORDER_BLOCKED_FOR_SALE)
    val blockedForSale: Optional<Boolean>?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehiclePriceDataUpdate(
    @AccessControlledField(FieldLabel.PRICE_NET_PRICE_WITH_EXTRAS)
    val netPriceWithExtras: Optional<BigDecimal>?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleTechnicalDataUpdate(
    @AccessControlledField(FieldLabel.TECHNICAL_AMOUNT_SEATS)
    val amountSeats: Optional<Int>?,
    @AccessControlledField(FieldLabel.TECHNICAL_ENGINE_CAPACITY)
    val engineCapacity: Optional<Float>?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleFleetDataUpdate(
    @AccessControlledField(FieldLabel.FLEET_SCRAP_VEHICLE)
    val scrapVehicle: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.FLEET_SOLD_DATE)
    val soldDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.FLEET_SCRAPPED_DATE)
    val scrappedDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.FLEET_STOLEN_DATE)
    val stolenDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.FLEET_SOLD_CUP_CAR_DATE)
    val soldCupCarDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.FLEET_APPROVED_FOR_SCRAPPING_DATE)
    val approvedForScrappingDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.FLEET_SCRAPPED_VEHICLE_OFFERED_DATE)
    val scrappedVehicleOfferedDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.FLEET_VEHICLE_SENT_TO_SALES_DATE)
    val vehicleSentToSalesDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.FLEET_COST_ESTIMATION_ORDERED_DATE)
    val costEstimationOrderedDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.FLEET_IS_RESIDUAL_VALUE_MARKET)
    val isResidualValueMarket: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.FLEET_PROFITABILITY_AUDIT_DATE)
    val profitabilityAuditDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.FLEET_COMMENT)
    val comment: Optional<String>?,
    @AccessControlledField(FieldLabel.FLEET_RACE_CAR)
    val raceCar: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.FLEET_CLASSIC)
    val classic: Optional<Boolean>?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleDeliveryDataUpdate(
    @AccessControlledField(FieldLabel.DELIVERY_PREPARATION_DONE_DATE)
    val preparationDoneDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.DELIVERY_IS_PREPARATION_NECESSARY)
    val isPreparationNecessary: Optional<Boolean>?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleReturnDataUpdate(
    @AccessControlledField(FieldLabel.RETURN_NEXT_PROCESS)
    val nextProcess: Optional<NextProcess>?,
    @AccessControlledField(FieldLabel.RETURN_KEY_RETURNED)
    val keyReturned: Optional<OffsetDateTime>?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleEvaluationDataUpdate(
    @AccessControlledField(FieldLabel.EVALUATION_APPRAISAL_NET_PRICE)
    val appraisalNetPrice: Optional<BigDecimal>?,
    @AccessControlledField(FieldLabel.EVALUATION_VEHICLE_EVALUATION_COMMENT)
    val vehicleEvaluationComment: Optional<String>?,
    @AccessControlledField(FieldLabel.EVALUATION_PC_COMPLAINT_CHECK_COMMENT)
    val pcComplaintCheckComment: Optional<String>?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMTireSetChangeUpdate(
    @AccessControlledField(FieldLabel.TIRE_SET_CHANGE_COMPLETED_DATE)
    val completedDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.TIRE_SET_CHANGE_ORDERED_DATE)
    val orderedDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.TIRE_SET_CHANGE_COMMENT)
    val comment: Optional<String>?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleConsumptionDataUpdate(
    @AccessControlledField(FieldLabel.CONSUMPTION_PRIMARY_FUEL_TYPE)
    val primaryFuelType: Optional<String>?,
    @AccessControlledField(FieldLabel.CONSUMPTION_SECONDARY_FUEL_TYPE)
    val secondaryFuelType: Optional<String>?,
)

@AccessControlledResource(Resource.VEHICLE)
data class FVMVehicleCurrentMileageDataUpdate(
    @AccessControlledField(FieldLabel.VEHICLE_CURRENT_MILEAGE_MILEAGE)
    val mileage: Optional<Int>?,
    @AccessControlledField(FieldLabel.VEHICLE_CURRENT_MILEAGE_READ_DATE)
    val readDate: Optional<OffsetDateTime>?,
)
