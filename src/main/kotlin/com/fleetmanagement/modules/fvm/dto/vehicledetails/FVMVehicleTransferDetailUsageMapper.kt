/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehicledetails

import com.fleetmanagement.modules.fvm.features.vehicledetails.VehicleTransferUsageDetail
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface FVMVehicleTransferDetailUsageMapper {
    companion object {
        val INSTANCE: FVMVehicleTransferDetailUsageMapper = Mappers.getMapper(FVMVehicleTransferDetailUsageMapper::class.java)
    }

    fun map(currentTransfer: VehicleTransferUsageDetail?): FVMVehicleTransferUsageDetail?

    fun map(transfers: List<VehicleTransferUsageDetail>): List<FVMVehicleTransferUsageDetail>
}
