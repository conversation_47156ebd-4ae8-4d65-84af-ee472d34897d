package com.fleetmanagement.modules.fvm.dto.vehicledetails

import com.fasterxml.jackson.annotation.JsonFilter
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE_TRANSFER)
data class FVMVehicleTransferUsageDetail(
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_EMPLOYEE_NUMBER)
    val employeeNumber: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_KEY)
    val key: Long,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_STATUS)
    val status: String,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PLANNED_RETURNED_DATE)
    val plannedReturnedDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_RETURNED_DATE)
    val returnedDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DELIVERY_DATE)
    val deliveryDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MILEAGE_AT_RETURN)
    val mileageAtReturn: Int?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MILEAGE_AT_DELIVERY)
    val mileageAtDelivery: Int?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PLANNED_DELIVERY_DATE)
    val plannedDeliveryDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_LICENSE_PLATE)
    val licensePlate: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_INTERNAL_ORDER_NUMBER)
    val internalOrderNumber: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USING_COST_CENTER)
    val usingCostCenter: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_FIRST_NAME)
    val firstName: String? = null,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_LAST_NAME)
    val lastName: String? = null,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DEPARTMENT)
    val department: String? = null,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_VEHICLE_USAGE)
    val vehicleUsage: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USAGE_GROUP)
    val usageGroup: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_COMPANY_MOBILE_NUMBER)
    val companyMobileNumber: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_COMPANY_EMAIL)
    val companyEmail: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PRIVATE_EMAIL)
    val privateEmail: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_LEASING_PRIVILEGE_LEASING)
    val leasingPrivilegeLeasing: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_ACCOUNTING_AREA)
    val accountingArea: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MAINTENANCE_ORDER_NUMBER)
    val maintenanceOrderNumber: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DEPRECIATION_RELEVANT_COST_CENTER_ID)
    val depreciationRelevantCostCenterId: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USAGE_MHP)
    val usageMhp: Boolean?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USAGE_VDW)
    val usageVdw: Boolean?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PRIVATE_MONTHLY_KILOMETERS)
    val privateMonthlyKilometers: Int?,
)
