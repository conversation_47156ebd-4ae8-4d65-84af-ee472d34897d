/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehiclehistory

import java.time.OffsetDateTime
import java.util.*

data class FVMVehicleDelta(
    val vehicleId: UUID,
    val field: String,
    val oldValue: String?,
    val newValue: String?,
    val modifiedAt: OffsetDateTime,
    val modifiedBy: String,
)
