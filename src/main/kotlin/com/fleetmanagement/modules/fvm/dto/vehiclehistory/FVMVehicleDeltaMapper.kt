/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehiclehistory

import com.fleetmanagement.modules.vehiclehistory.api.dto.VehicleDelta
import org.mapstruct.Mapper
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface FVMVehicleDeltaMapper {
    companion object {
        val INSTANCE: FVMVehicleDeltaMapper = Mappers.getMapper(FVMVehicleDeltaMapper::class.java)
    }

    fun map(vehicleDelta: VehicleDelta): FVMVehicleDelta

    fun map(vehicleDeltas: List<VehicleDelta>): List<FVMVehicleDelta>
}
