/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehiclelocation

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleTransferJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime
import java.util.*

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE_LOCATION)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class FVMVehicleLocation(
    val vehicleId: UUID,
    @AccessControlledField(FieldLabel.VEHICLE_LOCATION_COMPOUND_NAME)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val compoundName: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_LOCATION_PARKING_LOT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val parkingLot: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_LOCATION_BUILDING)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val building: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_LOCATION_EVENT_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val eventType: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_LOCATION_OCCURRED_ON)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val occurredOn: Optional<OffsetDateTime>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_LOCATION_LEVEL)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val level: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_LOCATION_SOURCE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val source: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_LOCATION_COMMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
        VehicleTransferJsonView.DLZPage::class,
    )
    val comment: Optional<String>? = null,
)
