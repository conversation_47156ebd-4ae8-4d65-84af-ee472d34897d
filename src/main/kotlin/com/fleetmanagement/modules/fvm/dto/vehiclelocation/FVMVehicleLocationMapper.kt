/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehiclelocation

import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.DLZViewVehicleRow
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import org.mapstruct.*
import org.mapstruct.factory.Mappers
import java.time.OffsetDateTime
import java.util.*

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface FVMVehicleLocationMapper {
    companion object {
        val INSTANCE: FVMVehicleLocationMapper = Mappers.getMapper(FVMVehicleLocationMapper::class.java)

        @JvmStatic
        @Named("mapToOptional")
        fun <T : Any> mapToOptional(value: T?): Optional<T> = Optional.ofNullable(value)

        @JvmStatic
        fun map(value: String?): Optional<String> = Optional.ofNullable(value)

        @JvmStatic
        fun map(value: OffsetDateTime?): Optional<OffsetDateTime> = Optional.ofNullable(value)
    }

    @Mappings(
        Mapping(
            target = "vehicleId",
            source = "id",
        ),
        Mapping(
            target = "compoundName",
            source = "vehicleLastKnownLocation.compoundName",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "parkingLot",
            source = "vehicleLastKnownLocation.parkingLot",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "building",
            source = "vehicleLastKnownLocation.building",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "eventType",
            source = "vehicleLastKnownLocation.eventType",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "occurredOn",
            source = "vehicleLastKnownLocation.occurredOn",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "level",
            source = "vehicleLastKnownLocation.level",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "comment",
            source = "vehicleLastKnownLocation.comment",
            qualifiedByName = ["mapToOptional"],
        ),
    )
    fun map(dlzViewVehicleRow: DLZViewVehicleRow): FVMVehicleLocation

    fun map(lastKnownLocation: VehicleLocation): FVMVehicleLocation
}
