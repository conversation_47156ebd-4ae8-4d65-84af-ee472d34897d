/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehicleregistration

import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.DLZViewVehicleRow
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVehicleRegistrationData
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import org.mapstruct.*
import org.mapstruct.factory.Mappers
import java.util.*

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface FVMVehicleRegistrationMapper {
    companion object {
        val INSTANCE: FVMVehicleRegistrationMapper = Mappers.getMapper(FVMVehicleRegistrationMapper::class.java)

        @JvmStatic
        @Named("mapToOptional")
        fun <T : Any> mapToOptional(value: T?): Optional<T> = Optional.ofNullable(value)
    }

    @Mappings(
        Mapping(
            target = "id",
            ignore = true,
        ),
        Mapping(
            target = "vin",
            source = "vehicle.vin",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "licencePlate",
            source = "vehicleRegistration.licencePlate",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "firstRegistrationDate",
            source = "vehicleRegistration.firstRegistrationDate",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "lastRegistrationDate",
            source = "vehicleRegistration.lastRegistrationDate",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "lastDeRegistrationDate",
            source = "vehicleRegistration.lastDeRegistrationDate",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "hsn",
            source = "vehicleRegistration.hsn",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "tsn",
            source = "vehicleRegistration.tsn",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "sfme",
            source = "vehicleRegistration.sfme",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "testVehicle",
            source = "vehicleRegistration.testVehicle",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "storageLocation",
            source = "vehicleRegistration.storageLocation",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "registrationType",
            source = "vehicleRegistration.registrationType",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "registrationStatus",
            source = "vehicleRegistration.registrationStatus",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "registrationDate",
            source = "vehicleRegistration.registrationDate",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "testNumber",
            source = "vehicleRegistration.testNumber",
            qualifiedByName = ["mapToOptional"],
        ),
    )
    fun map(dlzViewVehicleRow: DLZViewVehicleRow): FVMVehicleRegistrationOrder

    fun map(orders: List<VehicleRegistrationOrder>): List<FVMVehicleRegistrationOrder>

    fun map(order: VehicleRegistrationOrder): FVMVehicleRegistrationOrder

    @Mappings(
        Mapping(
            target = "licencePlate",
            source = "licencePlate",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "firstRegistrationDate",
            source = "firstRegistrationDate",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "lastRegistrationDate",
            source = "lastRegistrationDate",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "registrationType",
            source = "registrationType",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "registrationStatus",
            source = "registrationStatus",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "registrationDate",
            source = "registrationDate",
            qualifiedByName = ["mapToOptional"],
        ),
        Mapping(
            target = "lastDeRegistrationDate",
            source = "vehicleRegistration.lastDeRegistrationDate",
            qualifiedByName = ["mapToOptional"],
        ),
    )
    fun map(vehicleRegistration: FVMVehicleRegistrationData?): FVMVehicleRegistrationOrder
}
