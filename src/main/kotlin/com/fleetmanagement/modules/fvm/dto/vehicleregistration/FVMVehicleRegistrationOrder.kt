/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehicleregistration

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleTransferJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.ZonedDateTime
import java.util.*

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE_REGISTRATION)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class FVMVehicleRegistrationOrder(
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val id: Long,
    val vin: Optional<String>? = null,
    val vehicleId: Optional<String>? = null,
    val equiId: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_TEST_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val testNumber: Optional<Long>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_EQUIPMENT_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val equipmentNumber: Optional<Long>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_BRIEF_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val briefNumber: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_DRIVE_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val driveType: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_LEASING_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val leasingType: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_MODEL_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val modelType: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_DEPARTMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val department: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_REGISTRATION_OFFICE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val registrationOffice: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_PLANNED_LICENCE_PLATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val plannedLicencePlate: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_PLANNED_REGISTRATION_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val plannedRegistrationDate: Optional<ZonedDateTime>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_COMMENTER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val commenter: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_ORDER_STATUS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val orderStatus: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_ORDER_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val orderType: Optional<Int>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_STORAGE_LOCATION)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val storageLocation: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_TSN)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val tsn: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_HSN)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val hsn: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_REGISTRATION_TYPE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val registrationType: Optional<Int>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_REGISTRATION_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val registrationDate: Optional<ZonedDateTime>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_FIRST_REGISTRATION_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val firstRegistrationDate: Optional<ZonedDateTime>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_LAST_REGISTRATION_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val lastRegistrationDate: Optional<ZonedDateTime>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_LAST_DE_REGISTRATION_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val lastDeRegistrationDate: Optional<ZonedDateTime>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_LICENCE_PLATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val licencePlate: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_REGISTRATION_AREA)
    val registrationArea: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_REMARK)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val remark: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_SFME)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val sfme: Optional<Boolean>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_TEST_VEHICLE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val testVehicle: Optional<Boolean>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_REGISTRATION_STATUS)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val registrationStatus: Optional<String>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_DELETED)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val deleted: Optional<Boolean>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_CREATED_AT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val createdAt: Optional<ZonedDateTime>? = null,
    @AccessControlledField(FieldLabel.VEHICLE_REGISTRATION_UPDATED_AT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val updatedAt: Optional<ZonedDateTime>? = null,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val version: Long,
) {
    fun toVehicleRegistrationOrder(): VehicleRegistrationOrder =
        VehicleRegistrationOrder(
            id = id,
            vin = vin,
            vehicleId = vehicleId,
            equiId = equiId,
            testNumber = testNumber,
            equipmentNumber = equipmentNumber,
            briefNumber = briefNumber,
            driveType = driveType,
            leasingType = leasingType,
            modelType = modelType,
            department = department,
            registrationOffice = registrationOffice,
            plannedLicencePlate = plannedLicencePlate,
            plannedRegistrationDate = plannedRegistrationDate,
            commenter = commenter,
            orderStatus = orderStatus,
            orderType = orderType,
            storageLocation = storageLocation,
            tsn = tsn,
            hsn = hsn,
            registrationType = registrationType,
            registrationArea = registrationArea,
            registrationDate = registrationDate,
            firstRegistrationDate = firstRegistrationDate,
            lastRegistrationDate = lastRegistrationDate,
            lastDeRegistrationDate = lastDeRegistrationDate,
            licencePlate = licencePlate,
            remark = remark,
            sfme = sfme,
            testVehicle = testVehicle,
            registrationStatus = registrationStatus,
            deleted = deleted,
            createdAt = createdAt,
            updatedAt = updatedAt,
            version = version,
        )
}
