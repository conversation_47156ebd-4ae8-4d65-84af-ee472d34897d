/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehiclesales

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonInclude
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.vehiclesales.api.domain.PaymentType
import com.fleetmanagement.modules.vehiclesales.api.domain.SalesDiscountType
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import jakarta.validation.constraints.DecimalMax
import jakarta.validation.constraints.DecimalMin
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.Size
import java.math.BigDecimal
import java.util.UUID

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE_SALES)
@JsonInclude(JsonInclude.Include.NON_NULL)
data class FVMVehicleInvoiceCreateDto(
    val vehicleId: UUID,
    @AccessControlledField(FieldLabel.SALES_SALES_DISCOUNT_PERCENTAGE)
    @field:DecimalMax("100.0", message = "Discount percentage must be between 0 and 100")
    @field:DecimalMin("0.0", message = "Discount percentage must be between 0 and 100")
    val salesDiscountPercentage: Float = 0.0f,
    @AccessControlledField(FieldLabel.SALES_NET_PRICE_EUR)
    val salesNetPriceEUR: BigDecimal,
    @AccessControlledField(FieldLabel.SALES_WINTER_TIRES_DISCOUNT_PERCENTAGE)
    @field:DecimalMax("100.0", message = "Discount percentage must be between 0 and 100")
    @field:DecimalMin("0.0", message = "Discount percentage must be between 0 and 100")
    val winterTiresDiscountPercentage: Float = 0.0f,
    @AccessControlledField(FieldLabel.SALES_WINTER_TIRES_NET_PRICE_EUR)
    val winterTiresNetPriceEUR: BigDecimal? = null,
    @AccessControlledField(FieldLabel.SALES_WINTER_TIRES_ID)
    val winterTiresId: Long? = null,
    @AccessControlledField(FieldLabel.SALES_SALES_DISCOUNT_TYPE)
    val salesDiscountType: SalesDiscountType = SalesDiscountType.NO_DISCOUNT,
    @AccessControlledField(FieldLabel.SALES_CUSTOMER_INVOICE_RECIPIENT)
    val customerInvoiceRecipient: Boolean,
    @AccessControlledField(FieldLabel.SALES_PAYMENT_TYPE)
    val paymentType: PaymentType,
    @AccessControlledField(FieldLabel.SALES_CUSTOMER_PARTNER_NUMBER)
    @field:Size(max = 10, message = "Customer partner number must be a maximum of 10 digits")
    @field:Pattern(regexp = "\\d+", message = "Must contain only digits")
    val customerPartnerNumber: String,
    @AccessControlledField(FieldLabel.SALES_INVOICE_RECIPIENT_NUMBER)
    @field:Size(max = 10, message = "Invoice Recipient number must be a maximum of 10 digits")
    @field:Pattern(regexp = "\\d+", message = "Must contain only digits")
    val invoiceRecipientNumber: String,
    @AccessControlledField(FieldLabel.SALES_SALESPERSON_NUMBER)
    val salesPersonNumber: String,
)
