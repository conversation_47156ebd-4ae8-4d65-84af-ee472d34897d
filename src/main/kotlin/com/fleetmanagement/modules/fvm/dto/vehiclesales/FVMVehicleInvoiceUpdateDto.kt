/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehiclesales

import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime
import java.util.*

@AccessControlledResource(Resource.VEHICLE_SALES)
data class FVMVehicleInvoiceUpdateDto(
    @AccessControlledField(FieldLabel.SALES_RECEIPT_NUMBER)
    val receiptNumber: Optional<String>?,
    @AccessControlledField(FieldLabel.SALES_INVOICE_NUMBER)
    val invoiceNumber: Optional<String>?,
    @AccessControlledField(FieldLabel.SALES_CUSTOMER_DELIVERY_DATE)
    val customerDeliveryDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.SALES_PAYMENT_RECEIVED)
    val paymentReceived: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.SALES_FINAL_INVOICE_NUMBER)
    val finalInvoiceNumber: Optional<String>?,
    @AccessControlledField(FieldLabel.SALES_INVOICE_DATE)
    val invoiceDate: Optional<OffsetDateTime>?,
)
