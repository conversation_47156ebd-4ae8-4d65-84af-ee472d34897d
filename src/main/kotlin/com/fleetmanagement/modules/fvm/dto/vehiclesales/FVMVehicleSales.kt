package com.fleetmanagement.modules.fvm.dto.vehiclesales

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleSalesJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.vehiclesales.api.domain.InvoiceStatus
import com.fleetmanagement.modules.vehiclesales.api.domain.PaymentType
import com.fleetmanagement.modules.vehiclesales.api.domain.SalesDiscountType
import com.fleetmanagement.modules.vehiclesales.api.domain.TransactionType
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.UUID

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE_SALES)
data class FVMVehicleSales(
    val vehicleId: UUID? = null,
    val invoiceId: UUID? = null,
    @AccessControlledField(FieldLabel.SALES_RESERVED_FOR_B2C)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
    )
    val reservedForB2C: Boolean? = null,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
    )
    val reservedForB2B: Boolean? = null,
    @AccessControlledField(FieldLabel.SALES_COMMENT)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val comment: String?,
    @AccessControlledField(FieldLabel.SALES_CONTRACT_SIGNED)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val contractSigned: Boolean? = null,
    @AccessControlledField(FieldLabel.SALES_PLANNED_DELIVERY_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val plannedDeliveryDate: OffsetDateTime? = null,
    @AccessControlledField(FieldLabel.SALES_CUSTOMER_DELIVERY_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val customerDeliveryDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.SALES_NET_PRICE_EUR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val salesNetPriceEUR: BigDecimal?,
    @AccessControlledField(FieldLabel.SALES_NET_PRICE_AFTER_DISCOUNT_EUR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val salesNetPriceAfterDiscountEUR: BigDecimal?,
    @AccessControlledField(FieldLabel.SALES_WINTER_TIRES_NET_PRICE_EUR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val winterTiresNetPriceEUR: BigDecimal?,
    @AccessControlledField(FieldLabel.SALES_WINTER_TIRES_NET_PRICE_AFTER_DISCOUNT_EUR)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val winterTiresNetPriceAfterDiscountEUR: BigDecimal?,
    @AccessControlledField(FieldLabel.SALES_WINTER_TIRES_ID)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val winterTiresId: Long?,
    @AccessControlledField(FieldLabel.SALES_CUSTOMER_PARTNER_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val customerPartnerNumber: String?,
    @AccessControlledField(FieldLabel.SALES_INVOICE_RECIPIENT_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val invoiceRecipientNumber: String?,
    @AccessControlledField(FieldLabel.SALES_SALESPERSON_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val salesPersonNumber: String?,
    @AccessControlledField(FieldLabel.SALES_FVM_TRANSACTION_ID)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val fvmTransactionId: String?,
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val auctionId: String?,
    @AccessControlledField(FieldLabel.SALES_RECEIPT_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val receiptNumber: String?,
    @AccessControlledField(FieldLabel.SALES_INVOICE_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val invoiceDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.SALES_INVOICE_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val invoiceNumber: String?,
    @AccessControlledField(FieldLabel.SALES_FINAL_INVOICE_NUMBER)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleSalesJsonView.InvoicePage::class,
    )
    val finalInvoiceNumber: String?,
    @AccessControlledField(FieldLabel.SALES_INVOICE_STATUS)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class)
    val invoiceStatus: InvoiceStatus = InvoiceStatus.WAITING_FOR_APPROVAL,
    @AccessControlledField(FieldLabel.SALES_SALES_DISCOUNT_PERCENTAGE)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class, VehicleJsonView.VehicleDetailsPage::class)
    val salesDiscountPercentage: Float = 0.0F,
    @AccessControlledField(FieldLabel.SALES_WINTER_TIRES_DISCOUNT_PERCENTAGE)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class, VehicleJsonView.VehicleDetailsPage::class)
    val winterTiresDiscountPercentage: Float = 0.0F,
    @AccessControlledField(FieldLabel.SALES_SALES_DISCOUNT_TYPE)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class, VehicleJsonView.VehicleDetailsPage::class)
    val salesDiscountType: SalesDiscountType = SalesDiscountType.NO_DISCOUNT,
    @AccessControlledField(FieldLabel.SALES_CUSTOMER_INVOICE_RECIPIENT)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class, VehicleJsonView.VehicleDetailsPage::class)
    val customerInvoiceRecipient: Boolean = true,
    @AccessControlledField(FieldLabel.SALES_PAYMENT_TYPE)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class)
    val paymentType: PaymentType = PaymentType.CASH,
    @AccessControlledField(FieldLabel.SALES_PAYMENT_RECEIVED)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class)
    val paymentReceived: Boolean? = null,
    @AccessControlledField(FieldLabel.SALES_REFERENCE_TRANSACTION_ID)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class)
    val referenceTransactionId: String? = null,
    @AccessControlledField(FieldLabel.SALES_ERROR_MESSAGE)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class)
    val errorMessage: String? = null,
    @AccessControlledField(FieldLabel.SALES_FVM_TRANSACTION_TYPE)
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class)
    val transactionType: TransactionType = TransactionType.INVOICE,
    @field:JsonView(VehicleSalesJsonView.InvoicePage::class)
    val isCurrent: Boolean = false,
    @AccessControlledField(FieldLabel.SALES_LOADING_COMPLETED_DATE)
    @field:JsonView(
        VehicleJsonView.DLZPage::class,
        VehicleJsonView.VehicleDetailsPage::class,
    )
    val loadingCompletedDate: OffsetDateTime? = null,
)
