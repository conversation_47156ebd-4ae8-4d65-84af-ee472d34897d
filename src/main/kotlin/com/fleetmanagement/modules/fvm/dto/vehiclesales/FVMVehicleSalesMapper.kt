package com.fleetmanagement.modules.fvm.dto.vehiclesales

import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded.DLZViewVehicleSalesData
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoiceCreateDto
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoiceUpdateDto
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSaleUpdateDto
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSalesDto
import java.util.UUID

fun DLZViewVehicleSalesData.toFVMVehicleSales(): FVMVehicleSales =
    FVMVehicleSales(
        reservedForB2C = this.reservedForB2C,
        reservedForB2B = this.reservedForB2B,
        comment = this.comment,
        contractSigned = this.contractSigned,
        plannedDeliveryDate = this.plannedDeliveryDate,
        customerDeliveryDate = this.customerDeliveryDate,
        salesNetPriceEUR = this.salesNetPriceEUR,
        salesNetPriceAfterDiscountEUR = this.salesNetPriceAfterDiscountEUR,
        winterTiresNetPriceEUR = this.winterTiresNetPriceEUR,
        winterTiresId = this.winterTiresId,
        customerPartnerNumber = this.customerPartnerNumber,
        invoiceRecipientNumber = this.invoiceRecipientNumber,
        salesPersonNumber = this.salesPersonNumber,
        fvmTransactionId = this.fvmTransactionId,
        auctionId = this.auctionId,
        receiptNumber = this.receiptNumber,
        invoiceDate = this.invoiceDate,
        invoiceNumber = this.invoiceNumber,
        finalInvoiceNumber = this.finalInvoiceNumber,
        winterTiresNetPriceAfterDiscountEUR = this.winterTiresNetPriceAfterDiscountEUR,
        loadingCompletedDate = this.loadingCompletedDate,
    )

fun FVMVehicleSalesUpdate.toVehicleSalesUpdateDto(vehicleId: UUID): VehicleSaleUpdateDto =
    VehicleSaleUpdateDto(
        vehicleId = vehicleId,
        reservedForB2C = this.reservedForB2C,
        comment = this.comment,
        contractSigned = this.contractSigned,
        plannedDeliveryDate = this.plannedDeliveryDate,
        loadingCompletedDate = this.loadingCompletedDate,
    )

fun FVMVehicleInvoiceCreateDto.toVehicleSalesCreateDto(): VehicleInvoiceCreateDto =
    VehicleInvoiceCreateDto(
        vehicleId = this.vehicleId,
        salesNetPriceEUR = this.salesNetPriceEUR,
        salesDiscountPercentage = this.salesDiscountPercentage,
        winterTiresNetPriceEUR = this.winterTiresNetPriceEUR,
        winterTiresDiscountPercentage = this.winterTiresDiscountPercentage,
        winterTiresId = this.winterTiresId,
        salesDiscountType = this.salesDiscountType,
        customerInvoiceRecipient = this.customerInvoiceRecipient,
        paymentType = this.paymentType,
        customerPartnerNumber = this.customerPartnerNumber,
        invoiceRecipientNumber = this.invoiceRecipientNumber,
        salesPersonNumber = this.salesPersonNumber,
    )

fun FVMVehicleInvoiceUpdateDto.toVehicleInvoiceUpdateDto(): VehicleInvoiceUpdateDto =
    VehicleInvoiceUpdateDto(
        receiptNumber = this.receiptNumber,
        invoiceNumber = this.invoiceNumber,
        customerDeliveryDate = this.customerDeliveryDate,
        paymentReceived = this.paymentReceived,
        finalInvoiceNumber = this.finalInvoiceNumber,
        invoiceDate = this.invoiceDate,
    )

fun VehicleInvoice.toFVMVehicleSales(): FVMVehicleSales =
    FVMVehicleSales(
        vehicleId = this.vehicleId,
        invoiceId = this.invoiceId,
        comment = this.comment,
        customerDeliveryDate = this.customerDeliveryDate,
        salesDiscountPercentage = this.salesDiscountPercentage,
        salesNetPriceEUR = this.salesNetPriceEUR,
        salesNetPriceAfterDiscountEUR = this.salesNetPriceAfterDiscountEUR,
        winterTiresDiscountPercentage = this.winterTiresDiscountPercentage,
        winterTiresNetPriceEUR = this.winterTiresNetPriceEUR,
        winterTiresNetPriceAfterDiscountEUR = this.winterTiresNetPriceAfterDiscountEUR,
        winterTiresId = this.winterTiresId,
        customerPartnerNumber = this.customerPartnerNumber,
        invoiceRecipientNumber = this.invoiceRecipientNumber,
        salesPersonNumber = this.salesPersonNumber,
        fvmTransactionId = this.transactionId,
        auctionId = this.auctionId,
        receiptNumber = this.receiptNumber,
        invoiceDate = this.invoiceDate,
        invoiceNumber = this.invoiceNumber,
        finalInvoiceNumber = this.finalInvoiceNumber,
        invoiceStatus = this.invoiceStatus,
        salesDiscountType = this.salesDiscountType,
        customerInvoiceRecipient = this.customerInvoiceRecipient,
        paymentType = this.paymentType,
        paymentReceived = this.paymentReceived,
        referenceTransactionId = this.referenceTransactionId,
        errorMessage = this.errorMessage,
        transactionType = this.transactionType,
        isCurrent = this.isCurrent,
        plannedDeliveryDate = plannedDeliveryDate,
        loadingCompletedDate = loadingCompletedDate,
    )

fun VehicleSalesDto.toFVMVehicleSales(): FVMVehicleSales =
    FVMVehicleSales(
        vehicleId = this.vehicleId,
        comment = this.comment,
        customerDeliveryDate = null,
        salesNetPriceEUR = null,
        salesNetPriceAfterDiscountEUR = null,
        winterTiresNetPriceEUR = null,
        winterTiresNetPriceAfterDiscountEUR = null,
        winterTiresId = null,
        customerPartnerNumber = null,
        invoiceRecipientNumber = null,
        salesPersonNumber = null,
        fvmTransactionId = null,
        auctionId = null,
        receiptNumber = null,
        invoiceDate = null,
        invoiceNumber = null,
        finalInvoiceNumber = null,
        paymentReceived = null,
        referenceTransactionId = null,
        errorMessage = null,
        plannedDeliveryDate = plannedDeliveryDate,
        loadingCompletedDate = loadingCompletedDate,
    )
