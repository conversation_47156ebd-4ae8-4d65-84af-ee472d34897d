package com.fleetmanagement.modules.fvm.dto.vehiclesales

import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime
import java.util.Optional

@AccessControlledResource(Resource.VEHICLE_SALES)
data class FVMVehicleSalesUpdate(
    @AccessControlledField(FieldLabel.SALES_RESERVED_FOR_B2C)
    val reservedForB2C: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.SALES_COMMENT)
    val comment: Optional<String>?,
    @AccessControlledField(FieldLabel.SALES_CONTRACT_SIGNED)
    val contractSigned: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.SALES_PLANNED_DELIVERY_DATE)
    val plannedDeliveryDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.SALES_CUSTOMER_DELIVERY_DATE)
    val customerDeliveryDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.SALES_LOADING_COMPLETED_DATE)
    val loadingCompletedDate: Optional<OffsetDateTime>?,
)
