/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto.vehicletransfer

import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import java.time.OffsetDateTime
import java.util.*

@AccessControlledResource(Resource.VEHICLE_TRANSFER)
data class FVMPlannedVehicleTransfersNew(
    val vehicleIds: List<UUID>,
)

@AccessControlledResource(Resource.VEHICLE_TRANSFER)
data class FVMPlannedVehicleTransferNew(
    val vehicleId: UUID,
    val vehicleUsageId: UUID,
    val internalOrderNumber: String? = null,
    val vehicleResponsiblePerson: String,
    val internalContactPerson: String? = null,
    val deliveryLeipzig: Boolean,
    val maximumServiceLifeInMonths: Int,
    val responsibleCostCenter: String?,
    val desiredTireSet: TireSet?,
    val serviceCards: List<ServiceCard>?,
    val registrationNeeded: Boolean?,
    val provisionForDeliveryComment: String?,
    val desiredDeliveryDate: OffsetDateTime?,
    val plannedDeliveryDate: OffsetDateTime?,
    val plannedReturnDate: OffsetDateTime?,
    val remark: String?,
)
