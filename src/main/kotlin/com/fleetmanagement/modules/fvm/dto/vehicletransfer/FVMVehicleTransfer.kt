package com.fleetmanagement.modules.fvm.dto.vehicletransfer

import com.fasterxml.jackson.annotation.JsonFilter
import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleTransferJsonView
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime
import java.util.UUID

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VEHICLE_TRANSFER)
data class FVMVehicleTransfer(
    val vehicleId: UUID,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MAXIMUM_SERVICE_LIFE_IN_MONTHS)
    val maximumServiceLifeInMonths: Int?,
    @field:JsonView(
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.DLZPage::class,
    )
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_VEHICLE_USAGE_ID)
    val vehicleUsageId: UUID?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_INTERNAL_CONTACT_PERSON)
    val internalContactPerson: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DEPRECIATION_RELEVANT_COST_CENTER_ID)
    val depreciationRelevantCostCenterId: UUID?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USING_COST_CENTER)
    val usingCostCenter: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_INTERNAL_ORDER_NUMBER)
    val internalOrderNumber: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USAGE_GROUP_ID)
    val usageGroupId: UUID?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DELIVERY_DATE)
    val deliveryDate: OffsetDateTime?,
    @field:JsonView(
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.DLZPage::class,
    )
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_RETURN_DATE)
    val returnDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PLANNED_DELIVERY_DATE)
    val plannedDeliveryDate: OffsetDateTime?,
    @field:JsonView(
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.DLZPage::class,
    )
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PLANNED_RETURN_DATE)
    val plannedReturnDate: OffsetDateTime?,
    @field:JsonView(
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.DLZPage::class,
    )
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_LATEST_RETURN_DATE)
    val latestReturnDate: OffsetDateTime?,
    @field:JsonView(
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.DLZPage::class,
    )
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MILEAGE_AT_DELIVERY)
    val mileageAtDelivery: Int?,
    @field:JsonView(
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.DLZPage::class,
    )
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MILEAGE_AT_RETURN)
    val mileageAtReturn: Int?,
    @field:JsonView(
        VehicleTransferJsonView.DLZPage::class,
        VehicleJsonView.DLZPage::class,
    )
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_STATUS)
    val status: FVMVehicleTransferStatus?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_REMARK)
    val remark: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_VEHICLE_RESPONSIBLE_PERSON)
    val vehicleResponsiblePerson: String?,
    val vehicleTransferKey: Long,
    val version: Int?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_UTILIZATION_AREA)
    val utilizationArea: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DELIVERY_LEIPZIG)
    val deliveryLeipzig: Boolean?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DESIRED_DELIVERY_DATE)
    val desiredDeliveryDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PROVISION_FOR_DELIVERY_COMMENT)
    val provisionForDeliveryComment: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DELIVERY_COMMENT)
    val deliveryComment: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DESIRED_TIRE_SET)
    val desiredTireSet: FVMVehicleTransferTiresSet?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_TIRES_COMMENT)
    val tiresComment: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PREDECESSOR_LATEST_RETURN_DATE)
    val predecessorLatestReturnDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_LEASING_PRIVILEGE)
    val leasingPrivilege: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_LEASING_PRIVILEGE_VALIDATION_SUCCESSFUL)
    val leasingPrivilegeValidationSuccessful: Boolean?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DESIRED_TIRES_SET_CHANGED_MANUALLY)
    val desiredTireSetChangedManually: Boolean?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MAINTENANCE_ORDER_NUMBER)
    val maintenanceOrderNumber: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_LICENSE_PLATE)
    val licensePlate: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_SUCCESSOR_ORDER_DATE)
    val successorOrderDate: OffsetDateTime?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_RETURN_COMMENT)
    val returnComment: String?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_SERVICE_CARDS)
    val serviceCards: List<FVMVehicleTransferServiceCard>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_REGISTRATION_NEEDED)
    val registrationNeeded: Boolean?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USAGE_MHP)
    val usageMhp: Boolean?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USAGE_VDW)
    val usageVdw: Boolean?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PRIVATE_MONTHLY_KILOMETERS)
    val privateMonthlyKilometers: Int?,
)

/**
 * Combined states of PlannedVehicleTransfer and VehicleTransfer that are possible values for UI.
 */
enum class FVMVehicleTransferStatus {
    PLANNED,
    ACTIVE,
    FINISHED,
}

enum class FVMVehicleTransferServiceCard {
    FUELING_CARD,
    CHARGING_CARD,
    WASHING_CARD,
}

enum class FVMVehicleTransferTiresSet(
    val description: String,
) {
    SR("summer wheels"),
    WR("winter wheels"),
}
