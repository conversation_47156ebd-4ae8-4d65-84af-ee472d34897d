package com.fleetmanagement.modules.fvm.dto.vehicletransfer

import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocation
import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded.DLZViewVehicleTransferData
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMPreDeliveryInspection
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVehicleLastKnownLocationData
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVehicleTransferData
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVehicleTransferDataServiceCard
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVehicleTransferDataStatus
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVehicleTransferDataTiresSet
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.toServiceCard
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.toTireSet
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferDto
import java.util.*

fun FVMVehicleTransferData.toFVMVehicleTransfer(): FVMVehicleTransfer =
    FVMVehicleTransfer(
        vehicleId = vehicleId,
        maximumServiceLifeInMonths = maximumServiceLifeInMonths,
        vehicleUsageId = vehicleUsageId,
        internalContactPerson = internalContactPerson,
        depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
        usingCostCenter = usingCostCenter,
        internalOrderNumber = internalOrderNumber,
        usageGroupId = usageGroupId,
        deliveryDate = deliveryDate,
        returnDate = returnDate,
        plannedDeliveryDate = plannedDeliveryDate,
        plannedReturnDate = plannedReturnDate,
        latestReturnDate = latestReturnDate,
        mileageAtDelivery = mileageAtDelivery,
        mileageAtReturn = mileageAtReturn,
        status = status.toFVMVehicleTransferStatus(),
        remark = remark,
        vehicleResponsiblePerson = vehicleResponsiblePerson,
        vehicleTransferKey = vehicleTransferKey,
        version = version,
        utilizationArea = utilizationArea,
        deliveryLeipzig = deliveryLeipzig,
        desiredDeliveryDate = desiredDeliveryDate,
        provisionForDeliveryComment = provisionForDeliveryComment,
        leasingPrivilege = leasingPrivilege,
        deliveryComment = deliveryComment,
        desiredTireSet = desiredTireSet?.toFVMVehicleTransferTiresSet(),
        tiresComment = tiresComment,
        predecessorLatestReturnDate = predecessorLatestReturnDate,
        leasingPrivilegeValidationSuccessful = leasingPrivilegeValidationSuccessful,
        desiredTireSetChangedManually = desiredTireSetChangedManually,
        maintenanceOrderNumber = this.maintenanceOrderNumber,
        licensePlate = this.licensePlate,
        successorOrderDate = this.successorOrderDate,
        returnComment = this.returnComment,
        serviceCards = this.serviceCards?.map { it.toFVMVehicleTransferServiceCard() },
        registrationNeeded = this.registrationNeeded,
        usageMhp = this.usageMhp,
        usageVdw = this.usageVdw,
        privateMonthlyKilometers = this.privateMonthlyKilometers,
    )

fun DLZViewVehicleTransferData.toFVMVehicleTransfer(): FVMVehicleTransfer =
    FVMVehicleTransfer(
        vehicleId = vehicleId,
        vehicleTransferKey = vehicleTransferKey,
        version = version,
        vehicleUsageId = vehicleUsageId,
        returnDate = returnDate,
        plannedReturnDate = plannedReturnDate,
        latestReturnDate = latestReturnDate,
        mileageAtDelivery = mileageAtDelivery,
        mileageAtReturn = mileageAtReturn,
        status = status?.toFVMVehicleTransferStatus() ?: FVMVehicleTransferStatus.PLANNED,
        plannedDeliveryDate = null,
        maximumServiceLifeInMonths = null,
        internalContactPerson = null,
        depreciationRelevantCostCenterId = null,
        usingCostCenter = null,
        internalOrderNumber = null,
        usageGroupId = null,
        deliveryDate = null,
        remark = null,
        vehicleResponsiblePerson = null,
        utilizationArea = null,
        deliveryLeipzig = null,
        desiredDeliveryDate = null,
        provisionForDeliveryComment = null,
        deliveryComment = null,
        desiredTireSet = null,
        tiresComment = null,
        predecessorLatestReturnDate = null,
        leasingPrivilege = null,
        leasingPrivilegeValidationSuccessful = null,
        desiredTireSetChangedManually = null,
        maintenanceOrderNumber = null,
        licensePlate = null,
        successorOrderDate = null,
        returnComment = null,
        serviceCards = null,
        registrationNeeded = null,
        usageMhp = false,
        usageVdw = false,
        privateMonthlyKilometers = null,
    )

private fun FVMVehicleTransferDataStatus.toFVMVehicleTransferStatus(): FVMVehicleTransferStatus =
    when (this) {
        FVMVehicleTransferDataStatus.ACTIVE -> FVMVehicleTransferStatus.ACTIVE
        FVMVehicleTransferDataStatus.FINISHED -> FVMVehicleTransferStatus.FINISHED
        FVMVehicleTransferDataStatus.PLANNED -> FVMVehicleTransferStatus.PLANNED
    }

private fun FVMVehicleTransferDataTiresSet.toFVMVehicleTransferTiresSet(): FVMVehicleTransferTiresSet =
    when (this) {
        FVMVehicleTransferDataTiresSet.SR -> FVMVehicleTransferTiresSet.SR
        FVMVehicleTransferDataTiresSet.WR -> FVMVehicleTransferTiresSet.WR
    }

private fun FVMVehicleTransferDataServiceCard.toFVMVehicleTransferServiceCard(): FVMVehicleTransferServiceCard =
    when (this) {
        FVMVehicleTransferDataServiceCard.FUELING_CARD -> FVMVehicleTransferServiceCard.FUELING_CARD
        FVMVehicleTransferDataServiceCard.CHARGING_CARD -> FVMVehicleTransferServiceCard.CHARGING_CARD
        FVMVehicleTransferDataServiceCard.WASHING_CARD -> FVMVehicleTransferServiceCard.WASHING_CARD
    }

fun FVMPreDeliveryInspection.toFVMPreDeliveryInspection(
    vehicleId: UUID?,
): com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspection =
    com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspection(
        vehicleId = vehicleId,
        id = id,
        tireSet = tireSet,
        isRelevant = isRelevant,
        foiling = foiling,
        refuel = refuel,
        charge = charge,
        digitalLogbook = digitalLogbook,
        licencePlateMounting = licencePlateMounting,
        orderedDate = orderedDate,
        completedDate = completedDate,
        plannedDate = plannedDate,
        comment = comment,
    )

fun FVMVehicleLastKnownLocationData.toFVMVehicleLocation(vehicleId: UUID?): FVMVehicleLocation? =
    if (vehicleId == null) {
        null
    } else {
        FVMVehicleLocation(
            vehicleId = vehicleId,
            compoundName = compoundName?.let { Optional.of(it) },
            parkingLot = parkingLot?.let { Optional.of(it) },
            building = building?.let { Optional.of(it) },
            eventType = eventType?.let { Optional.of(it) },
            occurredOn = occurredOn?.let { Optional.of(it) },
            level = level?.let { Optional.of(it) },
            comment = comment?.let { Optional.of(it) },
        )
    }

fun FVMPlannedVehicleTransferNew.toCreatePlannedVehicleTransferDto(): CreatePlannedVehicleTransferDto =
    CreatePlannedVehicleTransferDto(
        vehicleId = vehicleId,
        vehicleUsageId = vehicleUsageId,
        vehicleResponsiblePerson = vehicleResponsiblePerson,
        internalContactPerson = internalContactPerson,
        deliveryLeipzig = deliveryLeipzig,
        maximumServiceLifeInMonths = maximumServiceLifeInMonths,
        usingCostCenter = responsibleCostCenter,
        desiredTireSet = desiredTireSet?.toTireSet(),
        serviceCards = serviceCards?.map { it.toServiceCard() }.orEmpty(),
        registrationNeeded = registrationNeeded,
        provisionForDeliveryComment = provisionForDeliveryComment,
        desiredDeliveryDate = desiredDeliveryDate,
        plannedDeliveryDate = plannedDeliveryDate,
        plannedReturnDate = plannedReturnDate,
        remark = remark,
    )
