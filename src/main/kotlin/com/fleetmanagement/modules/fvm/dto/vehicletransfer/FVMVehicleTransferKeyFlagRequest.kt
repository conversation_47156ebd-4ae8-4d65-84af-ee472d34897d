/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto.vehicletransfer

import com.fleetmanagement.modules.documentgeneration.api.dtos.ErrorDetail
import java.util.UUID

data class FVMVehicleTransferKeyFlagRequest(
    val vehicleIds: List<UUID>,
)

class FVMVehicleTransferKeyFlagException(
    val errors: List<ErrorDetail>,
) : RuntimeException()
