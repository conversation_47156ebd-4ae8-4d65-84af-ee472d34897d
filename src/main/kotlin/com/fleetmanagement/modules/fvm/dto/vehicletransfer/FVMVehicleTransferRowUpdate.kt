/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto.vehicletransfer

import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspectionUpdate
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleUpdate
import jakarta.validation.Valid

data class FVMVehicleTransferRowUpdate(
    @field:Valid val preDeliveryInspection: FVMPreDeliveryInspectionUpdate?,
    @field:Valid val vehicleTransfer: FVMVehicleTransferUpdate?,
    @field:Valid val vehicle: FVMVehicleUpdate?,
)
