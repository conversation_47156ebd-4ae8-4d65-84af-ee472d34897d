/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.dto.vehicletransfer

import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import java.time.OffsetDateTime
import java.util.*

@AccessControlledResource(Resource.VEHICLE_TRANSFER)
data class FVMVehicleTransferUpdate(
    val key: Long,
    val version: Int,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MAXIMUM_SERVICE_LIFE_IN_MONTHS)
    val maximumServiceLifeInMonths: Optional<Int>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_VEHICLE_USAGE_ID)
    val vehicleUsageId: Optional<UUID>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_VEHICLE_RESPONSIBLE_PERSON)
    val vehicleResponsiblePerson: Optional<String>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_INTERNAL_CONTACT_PERSON)
    val internalContactPerson: Optional<String>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USING_COST_CENTER)
    val usingCostCenter: Optional<String>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_INTERNAL_ORDER_NUMBER)
    val internalOrderNumber: Optional<String>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PLANNED_DELIVERY_DATE)
    val plannedDeliveryDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PLANNED_RETURN_DATE)
    val plannedReturnDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_REMARK) val remark: Optional<String>?,
    // setting this will trigger delivery-process
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DELIVERY_DATE)
    val deliveryDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MILEAGE_AT_DELIVERY)
    val mileageAtDelivery: Optional<Int>?,
    // setting this will trigger return-process
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_RETURN_DATE)
    val returnDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_MILEAGE_AT_RETURN)
    val mileageAtReturn: Optional<Int>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DESIRED_DELIVERY_DATE)
    val desiredDeliveryDate: Optional<OffsetDateTime>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PROVISION_FOR_DELIVERY_COMMENT)
    val provisionForDeliveryComment: Optional<String>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DELIVERY_COMMENT)
    val deliveryComment: Optional<String>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DELIVERY_LEIPZIG)
    val deliveryLeipzig: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_TIRES_COMMENT)
    val tiresComment: Optional<String>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_LEASING_PRIVILEGE)
    val leasingPrivilege: Optional<String>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_DESIRED_TIRE_SET)
    val desiredTireSet: Optional<TireSet>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_RETURN_COMMENT)
    val returnComment: Optional<String>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_SERVICE_CARDS)
    val serviceCards: Optional<List<ServiceCard>>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_REGISTRATION_NEEDED)
    val registrationNeeded: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USAGE_MHP)
    val usageMhp: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_USAGE_VDW)
    val usageVdw: Optional<Boolean>?,
    @AccessControlledField(FieldLabel.VEHICLE_TRANSFER_PRIVATE_MONTHLY_KILOMETERS)
    val privateMonthlyKilometers: Optional<Int>?,
)

enum class TireSet {
    SR,
    WR,
}

enum class ServiceCard {
    FUELING_CARD,
    CHARGING_CARD,
    WASHING_CARD,
}
