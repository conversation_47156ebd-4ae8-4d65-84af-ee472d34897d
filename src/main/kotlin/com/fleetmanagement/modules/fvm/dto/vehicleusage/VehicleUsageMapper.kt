/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.dto.vehicleusage

import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageDto
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageNewOrUpdate
import org.mapstruct.Mapper
import org.mapstruct.factory.Mappers

@Mapper
interface VehicleUsageMapper {
    companion object {
        val INSTANCE: VehicleUsageMapper = Mappers.getMapper(VehicleUsageMapper::class.java)
    }

    fun map(vehicleUsage: VehicleUsageDto): FVMVehicleUsage

    fun map(fvmCreateOrUpdateVehicleUsage: FVMCreateOrUpdateVehicleUsage): VehicleUsageNewOrUpdate
}
