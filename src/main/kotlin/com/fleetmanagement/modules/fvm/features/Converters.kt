package com.fleetmanagement.modules.fvm.features

import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import java.net.URI

fun ViolationType.toProblemDetailType(): URI =
    when (this) {
        ViolationType.VEHICLE_MARKED_AS_SCRAP -> URI.create(ErrorType.FVM_VEHICLE_VEHICLE_MARKED_AS_SCRAP.value)
        ViolationType.VEHICLE_NOT_MARKED_AS_SCRAP -> URI.create(ErrorType.FVM_VEHICLE_VEHICLE_NOT_MARKED_AS_SCRAP.value)
        ViolationType.VEHICLE_BLOCKED_FOR_SALE -> URI.create(ErrorType.FVM_VEHICLE_VEHICLE_BLOCKED_FOR_SALE.value)
        ViolationType.VEHICLE_NOT_BLOCKED_FOR_SALE -> URI.create(ErrorType.FVM_VEHICLE_VEHICLE_NOT_BLOCKED_FOR_SALE.value)
        ViolationType.FUTURE_DATE_NOT_ALLOWED -> URI.create(ErrorType.FVM_FUTURE_DATE_NOT_ALLOWED.value)
    }
