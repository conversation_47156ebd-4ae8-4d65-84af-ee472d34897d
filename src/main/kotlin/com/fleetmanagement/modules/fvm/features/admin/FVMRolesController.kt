/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin

import com.fleetmanagement.modules.fvm.dto.RoleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.accesscontrol.RolesByEmployeeNumber
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.FVMUserDetailsNotFound
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.security.api.ActiveRoleCannotBeDeletedException
import com.fleetmanagement.security.api.DeleteRolesByGroupId
import com.fleetmanagement.security.api.ReadRoles
import com.fleetmanagement.security.api.RoleNotFoundException
import com.fleetmanagement.security.integration.EntraIdUserNotFoundException
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI
import java.util.UUID

@RestController
@RequestMapping("/ui")
class FVMRolesController(
    private val readRoles: ReadRoles,
    private val deleteRolesByGroupId: DeleteRolesByGroupId,
    private val vehiclePersonDetailReadService: ReadVehiclePersonDetailByEmployeeNumber,
) {
    @GetMapping("/roles")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_ROLE_READ)")
    fun viewRoles(): ResponseEntity<APIResponse<List<RoleUIRepresentation>>> {
        val roles = readRoles.readAllRoles()
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(
            APIResponse(
                data =
                    roles.map {
                        RoleUIRepresentation(
                            id = it.id,
                            displayName = it.displayName,
                            description = it.description,
                            createdAt = it.createdAt,
                            active = it.active,
                        )
                    },
            ),
        )
    }

    @PostMapping("/roles/by-employee-number")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_ROLE_READ)")
    fun viewRolesByEmployeeNumber(
        @RequestBody request: RolesByEmployeeNumber,
    ): ResponseEntity<APIResponse<List<RoleUIRepresentation>>> {
        val email =
            vehiclePersonDetailReadService.readVehiclePersonDetailByEmployeeNumber(request.employeeNumber)?.companyEmail
                ?: throw FVMUserDetailsNotFound(request.employeeNumber)
        val roles = readRoles.readAllRolesByEmail(email)
        return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(
            APIResponse(
                data =
                    roles.map {
                        RoleUIRepresentation(
                            id = it.id,
                            displayName = it.displayName,
                            description = it.description,
                            createdAt = it.createdAt,
                            active = it.active,
                        )
                    },
            ),
        )
    }

    @DeleteMapping("/roles/{roleId}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_ROLE_DELETE)")
    fun deleteRole(
        @PathVariable("roleId") roleId: UUID,
    ): ResponseEntity<APIResponse<UUID>> {
        deleteRolesByGroupId.deleteRoleAndPermissionsBy(roleId)
        return ResponseEntity.ok(APIResponse(roleId))
    }

    @ExceptionHandler(RoleNotFoundException::class)
    fun handleRoleNotFoundException(ex: RoleNotFoundException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatusAndDetail(HttpStatus.NOT_FOUND, ex.message).apply {
                type = URI.create(ErrorType.FVM_ROLE_NOT_FOUND.value)
            }
        return ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(ActiveRoleCannotBeDeletedException::class)
    fun handleRoleDeletionNotAllowedException(ex: ActiveRoleCannotBeDeletedException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatusAndDetail(HttpStatus.FORBIDDEN, ex.message).apply {
                type = URI.create(ErrorType.FVM_ACTIVE_ROLE_CANNOT_BE_DELETED.value)
            }
        return ResponseEntity
            .status(HttpStatus.FORBIDDEN)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(EntraIdUserNotFoundException::class)
    fun handleEntraIdUserNotFoundException(ex: EntraIdUserNotFoundException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatusAndDetail(HttpStatus.NOT_FOUND, ex.message).apply {
                type = URI.create(ErrorType.FVM_ROLE_ENTRA_ID_USER_NOT_FOUND.value)
            }
        return ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(FVMUserDetailsNotFound::class)
    fun handleFVMUserDetailsNotFound(ex: FVMUserDetailsNotFound): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatusAndDetail(HttpStatus.NOT_FOUND, ex.reason).apply {
                type = URI.create(ErrorType.FVM_ROLE_USER_DETAILS_NOT_FOUND.value)
            }
        return ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(APIResponse.forErrors(problemDetail))
    }
}
