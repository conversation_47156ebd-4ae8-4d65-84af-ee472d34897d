/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.AccessControlExportException
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.AccessControlImportException
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.InvalidFileException
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.InvalidPrivilegeDataException
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.MissingRequiredHeadersException
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.MissingRequiredSheetsException
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.export.AccessControlExportService
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.importdata.AccessControlImportService
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.util.JsonUtil
import com.fleetmanagement.security.api.ReadPrivilegeHistory
import com.fleetmanagement.security.api.dto.PrivilegeHistoryDTO
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import jakarta.validation.constraints.NotBlank
import org.slf4j.LoggerFactory
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.net.URI
import java.util.UUID

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMAccessControlController(
    private val exportService: AccessControlExportService,
    private val importService: AccessControlImportService,
    private val readPrivilegeHistory: ReadPrivilegeHistory,
    private val objectMapper: ObjectMapper,
) {
    private val logger = LoggerFactory.getLogger(FVMAccessControlController::class.java)

    @PostMapping("/access-control/export/{groupId}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_ACCESS_CONTROL_READ)")
    fun exportPermissions(
        @PathVariable groupId: UUID,
        @RequestBody translations: String,
    ): ResponseEntity<ByteArrayResource> {
        val translationMap = JsonUtil.flattenJson(objectMapper.readTree(translations).get("translations"))
        val fileResource = exportService.exportAccessPermissions(groupId, translationMap)
        val fileName = "access-control-permissions.xlsx"

        return ResponseEntity
            .ok()
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"$fileName\"")
            .body(fileResource)
    }

    @PostMapping("/access-control/import/{groupId}", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_ACCESS_CONTROL_WRITE)")
    fun importPermissions(
        @PathVariable("groupId") groupId: UUID,
        @RequestPart("file") file: MultipartFile,
        @NotBlank @RequestPart("comment") comment: String,
    ): ResponseEntity<APIResponse<String>> {
        importService.import(groupId, file, comment)
        return ResponseEntity.ok(APIResponse("File uploaded and processed successfully"))
    }

    @GetMapping("/access-control-history")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_ACCESS_CONTROL_READ)")
    fun getPermissionsHistory(): ResponseEntity<APIResponse<List<PrivilegeHistoryDTO>>> {
        val privilegeHistory = readPrivilegeHistory.readPrivilegeHistory()
        return ResponseEntity.ok(APIResponse(privilegeHistory))
    }

    @GetMapping("/access-control-history/{id}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_ACCESS_CONTROL_READ)")
    fun downloadPermissionsHistoryFile(
        @PathVariable id: Long,
    ): ResponseEntity<ByteArrayResource> {
        val privilegeHistory = readPrivilegeHistory.readPrivilegeHistoryFileBy(id)

        return ResponseEntity
            .ok()
            .contentType(MediaType.APPLICATION_OCTET_STREAM)
            .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=\"${privilegeHistory.fileName}\"")
            .body(privilegeHistory.data)
    }

    @ExceptionHandler(AccessControlExportException::class)
    fun handleAccessControlExportException(ex: AccessControlExportException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("AccessControlExportException occurred", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "Error while exporting access control permissions",
                ).apply {
                    type = URI.create(ErrorType.FVM_ACCESS_CONTROL_EXPORT_FAILED.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(AccessControlImportException::class)
    fun handleAccessControlImportException(ex: AccessControlImportException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("AccessControlImportException occurred", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    ex.message,
                ).apply {
                    type = URI.create(ErrorType.FVM_ACCESS_CONTROL_IMPORT_FAILED.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(InvalidFileException::class)
    fun handleInvalidFileException(ex: InvalidFileException): ResponseEntity<APIResponse<Nothing?>> {
        logger.warn("InvalidFileException occurred", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.BAD_REQUEST,
                    ex.message,
                ).apply {
                    type = URI.create(ErrorType.FVM_ACCESS_CONTROL_INVALID_FILE.value)
                }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(MissingRequiredHeadersException::class)
    fun handleMissingRequiredHeadersException(ex: MissingRequiredHeadersException): ResponseEntity<APIResponse<Nothing?>> {
        logger.warn("MissingRequiredHeadersException occurred", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.BAD_REQUEST,
                    ex.message,
                ).apply {
                    type = URI.create(ErrorType.FVM_ACCESS_CONTROL_MISSING_REQUIRED_HEADER.value)
                    setProperty("missingFields", ex.missingFields)
                }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(MissingRequiredSheetsException::class)
    fun handleMissingRequiredSheetsException(ex: MissingRequiredSheetsException): ResponseEntity<APIResponse<Nothing?>> {
        logger.warn("MissingRequiredSheetsException occurred", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.BAD_REQUEST,
                    ex.message,
                ).apply {
                    type = URI.create(ErrorType.FVM_ACCESS_CONTROL_MISSING_REQUIRED_SHEET.value)
                    setProperty("missingFields", ex.missingFields)
                }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(InvalidPrivilegeDataException::class)
    fun handleInvalidPrivilegeDataException(ex: InvalidPrivilegeDataException): ResponseEntity<APIResponse<Nothing?>> {
        logger.warn("InvalidPrivilegeDataException occurred", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.BAD_REQUEST,
                    ex.message,
                ).apply {
                    type = URI.create(ErrorType.FVM_ACCESS_CONTROL_INVALID_DATA.value)
                    setProperty("invalidFields", ex.invalidFields)
                }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(APIResponse.forErrors(problemDetail))
    }
}
