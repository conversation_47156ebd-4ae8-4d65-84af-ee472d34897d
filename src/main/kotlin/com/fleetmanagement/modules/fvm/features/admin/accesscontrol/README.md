# Access Control Module - Overview
## Introduction
The Access Control Module provides a way to manage user-group permissions using an Excel file. This module allows administrators to define which resources users can access and what actions they can perform.

Currently, the VPM department is responsible for maintaining and updating these permissions

## Permission Layers
Access control is structured into three key layers:
* Page Permissions: Determines which pages a user can view (READ access).
* Action Permissions: Defines which actions a user can execute (EXECUTE access) within the system.
* Field Permissions: Controls which fields a user can READ and WRITE in different domains of the application

The following documentation provides the key functionalities on how to use access control for the above layers which ensures alignment of translations, permissions and history in a reliable manner

## Access Control Export Service
The **Access Control Export Service** is responsible for exporting access permission data into an Excel file. 
It generates multiple sheets representing different levels of permissions: **How To**, **Page Level**, **Action Level**, and **Field Level**.

## Exported Sheets
1. **How To Sheet** - Provides instructions on how to use the work with the Excel and manage permissions.
2. **Page Level Sheet** - Lists page-level access permissions.
3. **Action Level Sheet** - Lists action-specific access permissions.
4. **Field Level Sheet** - Captures permissions for fields marked as access-controlled.

## How It Works
- The service creates a new Excel workbook.
- It loads a predefined **How To** template and copies it into the workbook.
- It retrieves all page-level and action-level permission keys and writes them into respective sheets based on Resource enum.
- It extracts access-controlled fields from annotated classes and writes them to the **Field Level** sheet.
- The final workbook is written to a byte array and returned as a downloadable resource.

## Key Functions
- `exportAccessPermissions()`: Generates the Excel file and returns it as a `ByteArrayResource`.
- `addHowToSheet()`: Loads the predefined **How To** sheet template.
- `createPermissionSheet()`: Writes permissions for pages and actions.
- `createFieldLevelSheet()`: Extracts field-level permissions from annotated classes.

# Resource Enumeration

The `Resource` enum defines a structured way to represent access-controlled resources within the application. 
Each resource corresponds to a `Page`, which may contain:
- **Subpages**: Nested pages under a main resource.
- **Actions**: Specific operations that can be performed on the page.

## Adding a New Page
To add a new page:
1. Define a new entry in the `Resource` enum.
2. Use the `Page` class to structure subPages, and actions as lists
3. Use camel-case to specify page keys and action keys to be compatible with FE

### Example
```kotlin
TEST_PAGE(
    Page("newPage", 
        subPages = listOf(
            Page("subPage", actions = listOf("someAction"))
        ),
        actions = listOf("mainPageAction"))
)
```

## Key Methods:
- **getAllPageKeys()**: Returns all page-related keys.
- **getAllActionKeys()**: Returns all action-related keys.
- **getAllApiKeys()**: Returns API-related keys (TBD).

## `Page` Class
The `Page` class helps structure permissions by defining:
- **generatePageKeys()**: Creates hierarchical page permission keys.
- **generateActionKeys()**: Creates action-specific permission keys.

## Understanding virtualPage
The `virtualPage` flag determines whether a page is just a container for subpages and actions, without having its own key.

- **When `true`**: The page itself is not assigned a key, but its subpages and actions are included.
- **When `false`**: The page and its subpages/actions are all included normally.

This is useful to avoid page keys while still tracking permissions for subpages and actions.

## ApiLabelConstant - API Access Control
The ApiLabelConstant class defines security labels used in Spring Security's `hasAuthority` function to enforce access control at the API level. These labels represent different API permissions, and Spring Security verifies them to determine whether a user is authorized to perform a specific action 

### Functionality
- Each constant in `ApiLabelConstant` represents a specific **read, write, or delete** permission for an API.
- The `@PreAuthorize` annotation is used in controller methods to verify if the user has the required **authority** before executing the request.
- If the user lacks the required permission, the request is denied with a **403 Forbidden** response.

### Future Enhancements
* Currently, API permissions are not managed in the Excel file. Every API that is created in the system, the user-group will get the permissions automatically. This is happening in the `AccessControlImportService` class with `getDefaultApiPrivileges()` method.

* The decision of whether we need this layer or not needs to be revisited based on the inputs from IT security and from the business people.

## Excel Import Validator
The ExcelImportValidator is a service component responsible for validating Excel files used in the Fleet Management system. It ensures that uploaded Excel files conform to the required structure before processing.

### Checks for standard file validations:
* Ensures the file is not empty
* Ensures the file has a .xlsx extension
* Limits the file size to 5MB
* Validates that required sheets exist in the workbook
* Validates that each sheet contains the expected headers and entries (pages, actions and fields)

### Errors
- `InvalidFileException` error will be thrown to the UI in case the file is empty or if the input file is not in xlsx format or if it exceeds 5MB limit
- `MissingRequiredSheetsException` error will be thrown if the required sheets in the workbook are missing
- `MissingRequiredHeadersException` error will be thrown in case of missing headers
- `InvalidPrivilegeDataException` error will be thrown in case the Excel contains invalid entries

## Excel Import Parser
The `ExcelImportParser` is a service class that processes **Excel files** containing access control privileges and parses them into structured data. The parsed data is used to define **page-level, action-level, and field-level permissions** for access control in the system.

## Functionality
- Reads an **Excel file** from an uploaded `MultipartFile`.
- Extracts **page-level, action-level, and field-level** permissions.
- Ensures required headers are present and validates the input data.
- Groups extracted data by **resources, domains, and permissions**.
- Returns a list of structured `AccessControlPrivilege` objects.

## Key Features
✅ **Supports multiple levels of access control** (Page, Action, Field)  
✅ **Efficiently processes Excel sheets** using Apache POI  
✅ **Automatically maps permissions** to corresponding resources and provides to the persistence layer

## Excel Export DataGenerator
`ExportSheetDataGenerator` is a Spring service responsible for generating Excel workbooks containing access control privilege data. It supports exporting permissions at different levels, including pages, actions, and fields.

This service generates Excel workbooks based on access control privileges:
- **Pages Workbook:** Lists resources and permissions related to page-level access for the user-group
- **Actions Workbook:** Lists actions with their associated permissions.
- **Fields Workbook:** Lists domain fields along with their translations and the associated permissions.

Once exported, the admin can manage the permissions of the user-group and can use the same file for importing

## How will the permission data be persisted
Currently, the system performs a full overwrite when importing an Excel file. This means that all pre-existing privileges for the specified user group will be completely removed before applying the new permissions from the imported file. After the import, only the permissions explicitly defined in the Excel file, along with the default API privilege permissions, will be granted to the user group. Any previously assigned permissions not present in the imported file will be lost.
