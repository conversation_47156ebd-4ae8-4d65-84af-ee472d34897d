/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception

open class AccessControlImportException(
    override val message: String? = null,
    override val cause: Throwable? = null,
) : RuntimeException(message)

class InvalidFileException(
    override val message: String?,
) : AccessControlImportException(message)

class MissingRequiredSheetsException(
    override val message: String? = null,
    val missingFields: Set<String>,
) : AccessControlImportException("Missing Required sheets : $missingFields")

class MissingRequiredHeadersException(
    override val message: String? = null,
    val missingFields: Set<String>,
) : AccessControlImportException("Missing Required headers : $missingFields")

class InvalidPrivilegeDataException(
    override val message: String?,
    val invalidFields: Set<String>,
) : AccessControlImportException()
