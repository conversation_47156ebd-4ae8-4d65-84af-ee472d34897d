/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception

import org.springframework.http.HttpStatusCode
import org.springframework.web.server.ResponseStatusException

class FVMUserDetailsNotFound(
    val employeeNumber: String?,
) : ResponseStatusException(
        HttpStatusCode.valueOf(404),
        "People with ID $employeeNumber not found",
    )
