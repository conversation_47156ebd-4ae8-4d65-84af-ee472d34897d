/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.export

import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.AccessControlExportException
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.privilege.AccessControlPrivilegeService
import org.apache.poi.ss.usermodel.CellStyle
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.ss.usermodel.WorkbookFactory
import org.slf4j.LoggerFactory
import org.springframework.core.io.ByteArrayResource
import org.springframework.core.io.ClassPathResource
import org.springframework.stereotype.Service
import java.io.ByteArrayOutputStream
import java.util.UUID

@Service
class AccessControlExportService(
    private val dataGenerator: ExportSheetDataGenerator,
    private val privilegeService: AccessControlPrivilegeService,
) {
    private val logger = LoggerFactory.getLogger(AccessControlExportService::class.java)

    companion object {
        const val HOW_TO_SHEET_NAME = "How To"
        private const val HOW_TO_TEMPLATE_PATH = "accesscontrol/Permissions-Export-How-To-Sheet-Template.xlsx"
    }

    fun exportAccessPermissions(
        groupId: UUID,
        translations: Map<String, String> = emptyMap(),
    ): ByteArrayResource {
        val workbook = WorkbookFactory.create(true)
        try {
            val allPrivileges = privilegeService.getCurrentPrivileges(groupId)
            val pagePrivileges = allPrivileges.filter { it.resource == Resource.PAGES }
            val actionPrivileges = allPrivileges.filter { it.resource == Resource.ACTIONS }
            val fieldPrivileges =
                allPrivileges.filterNot { it.resource == Resource.PAGES || it.resource == Resource.ACTIONS }

            addHowToSheet(workbook)
            createWorkbookSheet(workbook, dataGenerator.pages(pagePrivileges))
            createWorkbookSheet(workbook, dataGenerator.actions(actionPrivileges))
            createWorkbookSheet(workbook, dataGenerator.fields(fieldPrivileges, translations))

            val byteArrayOutputStream = ByteArrayOutputStream()
            workbook.write(byteArrayOutputStream)
            return ByteArrayResource(byteArrayOutputStream.toByteArray())
        } catch (e: RuntimeException) {
            throw AccessControlExportException("Error during generating access permission sheet", e)
        } finally {
            workbook.close()
        }
    }

    fun createWorkbookSheet(
        workbook: Workbook,
        excelWorkSheet: ExcelWorkSheet,
    ) {
        val sheet = workbook.createSheet(excelWorkSheet.name)

        val headerFont = workbook.createFont().apply { bold = true }
        val headerStyle = workbook.createCellStyle().apply { setFont(headerFont) }

        val headerRow = sheet.createRow(0)
        excelWorkSheet.headers.forEachIndexed { index, header ->
            val cell = headerRow.createCell(index)
            cell.setCellValue(header)
            cell.cellStyle = headerStyle
        }

        val headerIndexMap = sheet.getRow(0).mapIndexed { index, cell -> cell.stringCellValue to index }.toMap()

        excelWorkSheet.rows.forEachIndexed { rowIndex, row ->
            val excelRow = sheet.createRow(rowIndex + 1)
            row.cells.forEach { cell ->
                val colIndex = headerIndexMap[cell.header] ?: return
                val excelCell = excelRow.createCell(colIndex)
                excelCell.setCellValue(cell.value)
            }
        }

        excelWorkSheet.headers.indices.forEach { sheet.autoSizeColumn(it) }
    }

    private fun addHowToSheet(workbook: Workbook) {
        val howToSheet = loadHowToSheet() ?: return
        val newSheet = workbook.createSheet(HOW_TO_SHEET_NAME)
        copySheet(howToSheet, newSheet, workbook)
        workbook.setSheetOrder(newSheet.sheetName, 0)
    }

    private fun loadHowToSheet(): Sheet? {
        val howToResource = ClassPathResource(HOW_TO_TEMPLATE_PATH)
        howToResource.inputStream.use { inputStream ->
            val howToWorkbook = WorkbookFactory.create(inputStream)
            return howToWorkbook.getSheet(HOW_TO_SHEET_NAME)
        }
    }

    private fun copySheet(
        sourceSheet: Sheet,
        targetSheet: Sheet,
        workbook: Workbook,
    ) {
        val cellStyleMap = mutableMapOf<Short, CellStyle>() // Cache styles to optimize performance

        for (rowIndex in 0..sourceSheet.lastRowNum) {
            val sourceRow = sourceSheet.getRow(rowIndex) ?: continue
            val targetRow = targetSheet.createRow(rowIndex)

            for (cellIndex in 0 until sourceRow.lastCellNum) {
                val sourceCell = sourceRow.getCell(cellIndex) ?: continue
                val targetCell = targetRow.createCell(cellIndex)

                when (sourceCell.cellType) {
                    CellType.STRING -> targetCell.setCellValue(sourceCell.stringCellValue)
                    CellType.NUMERIC -> targetCell.setCellValue(sourceCell.numericCellValue)
                    CellType.BOOLEAN -> targetCell.setCellValue(sourceCell.booleanCellValue)
                    CellType.FORMULA -> targetCell.cellFormula = sourceCell.cellFormula
                    else -> targetCell.setCellValue(sourceCell.toString())
                }

                // Copy cell style
                val sourceStyle = sourceCell.cellStyle
                val targetStyle =
                    cellStyleMap.getOrPut(sourceStyle.index) {
                        val newStyle = workbook.createCellStyle()
                        newStyle.cloneStyleFrom(sourceStyle)
                        newStyle.wrapText = sourceStyle.wrapText
                        newStyle
                    }
                targetCell.cellStyle = targetStyle
            }
        }

        // Copy merged regions
        for (i in 0 until sourceSheet.numMergedRegions) {
            val mergedRegion = sourceSheet.getMergedRegion(i)
            targetSheet.addMergedRegion(mergedRegion)
        }

        // Copy column widths
        for (colIndex in 0..sourceSheet.getRow(0).lastCellNum) {
            targetSheet.setColumnWidth(colIndex, sourceSheet.getColumnWidth(colIndex))
        }
    }
}
