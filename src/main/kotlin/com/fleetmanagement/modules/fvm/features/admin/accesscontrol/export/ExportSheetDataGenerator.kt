/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.export

import com.fleetmanagement.modules.fvm.dto.accesscontrol.AccessControlPrivilege
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ActionsWorkSheet
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelCell
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelRow
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.DOMAIN_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.FIELD_NAME_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.PERMISSION_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.RESOURCE_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.TRANSLATED_VALUE_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldsWorkSheet
import com.fleetmanagement.modules.fvm.dto.accesscontrol.PagesWorkSheet
import org.springframework.stereotype.Service

@Service
class ExportSheetDataGenerator {
    fun pages(privileges: List<AccessControlPrivilege>): ExcelWorkSheet {
        val rows =
            privileges
                .flatMap { privilege ->
                    privilege.fields.map { field ->
                        ExcelRow(
                            cells =
                                listOf(
                                    ExcelCell(header = RESOURCE_HEADER, value = field),
                                    ExcelCell(header = PERMISSION_HEADER, value = privilege.permission?.name),
                                ),
                        )
                    }
                }

        return PagesWorkSheet(rows = rows)
    }

    fun actions(privileges: List<AccessControlPrivilege>): ExcelWorkSheet {
        val rows =
            privileges
                .flatMap { privilege ->
                    privilege.fields.map { field ->
                        ExcelRow(
                            cells =
                                listOf(
                                    ExcelCell(header = RESOURCE_HEADER, value = field),
                                    ExcelCell(header = PERMISSION_HEADER, value = privilege.permission?.name),
                                ),
                        )
                    }
                }

        return ActionsWorkSheet(rows = rows)
    }

    fun fields(
        privileges: List<AccessControlPrivilege>,
        translations: Map<String, String>,
    ): ExcelWorkSheet {
        val rows =
            privileges
                .sortedBy { it.resource }
                .flatMap { privilege ->
                    privilege.fields.map { field ->
                        ExcelRow(
                            listOf(
                                ExcelCell(header = DOMAIN_HEADER, value = privilege.resource.label),
                                ExcelCell(header = PERMISSION_HEADER, value = privilege.permission?.name),
                                ExcelCell(header = FIELD_NAME_HEADER, value = field),
                                ExcelCell(
                                    header = TRANSLATED_VALUE_HEADER,
                                    value = translations.getOrDefault(field, ""),
                                ),
                            ),
                        )
                    }
                }.distinct()

        return FieldsWorkSheet(rows = rows)
    }
}
