/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.importdata

import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.AccessControlImportException
import com.fleetmanagement.security.api.CreateOrUpdateGroupPrivileges
import com.fleetmanagement.security.api.dto.GroupPrivilegeDTO
import com.fleetmanagement.security.api.dto.PrivilegeFilter
import com.fleetmanagement.security.api.dto.PrivilegePermission
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.util.UUID

@Service
class AccessControlImportService(
    private val parser: ExcelImportParser,
    private val createOrUpdateGroupPrivileges: CreateOrUpdateGroupPrivileges,
) {
    private val logger = LoggerFactory.getLogger(AccessControlImportService::class.java)

    fun import(
        groupId: UUID,
        file: MultipartFile,
        comment: String,
    ) {
        runCatching {
            val privileges = parseValidPrivileges(file) + getDefaultApiPrivileges()
            createOrUpdateGroupPrivileges.createOrUpdateGroupPrivilegesBy(
                groupId = groupId,
                privileges = privileges,
                file = file,
                comment = comment,
            )
        }.fold(
            onSuccess = { logger.info("Import completed successfully for group $groupId") },
            onFailure = { e ->
                logger.warn(
                    "Failed to import access control file [${file.originalFilename}] for group $groupId",
                    e,
                )
                when (e) {
                    is AccessControlImportException -> throw e
                    else -> throw AccessControlImportException(
                        message = "Failed to process access control file: ${e.message}",
                        cause = e,
                    )
                }
            },
        )
    }

    private fun getDefaultApiPrivileges(): List<GroupPrivilegeDTO> {
        val requiredPermissions =
            listOf(
                PrivilegePermission.READ,
                PrivilegePermission.WRITE,
                PrivilegePermission.DELETE,
            )

        return requiredPermissions
            .map { permission ->
                GroupPrivilegeDTO(
                    resource = Resource.APIS.label,
                    permission = permission,
                    filter = null,
                )
            }
    }

    private fun parseValidPrivileges(file: MultipartFile): List<GroupPrivilegeDTO> =
        parser
            .parse(file)
            .mapNotNull { parsedPrivilege ->
                parsedPrivilege.permission?.let { validPermission ->
                    GroupPrivilegeDTO(
                        resource = parsedPrivilege.resource.label,
                        permission = validPermission,
                        filter = PrivilegeFilter(parsedPrivilege.fields),
                    )
                }
            }.also {
                if (it.isEmpty()) {
                    logger.warn("No valid privileges found in uploaded file [${file.originalFilename}]")
                }
            }
}
