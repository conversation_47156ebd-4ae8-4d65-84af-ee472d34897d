/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.importdata

import com.fleetmanagement.modules.fvm.dto.accesscontrol.AccessControlPrivilege
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelCell
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelRow
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.ACTION_LEVEL_SHEET_NAME
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.DOMAIN_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.FIELD_LEVEL_SHEET_NAME
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.FIELD_NAME_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.PAGE_LEVEL_SHEET_NAME
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.PERMISSION_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.RESOURCE_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.actionHeaders
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.fieldHeaders
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.pageHeaders
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.InvalidPrivilegeDataException
import com.fleetmanagement.security.api.dto.PrivilegePermission
import org.apache.poi.ss.usermodel.Sheet
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.ss.usermodel.WorkbookFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

@Service
class ExcelImportParser(
    private val privilegeFileValidator: PrivilegeFileValidator,
    private val privilegeDataValidator: PrivilegeDataValidator,
) {
    fun parse(file: MultipartFile): List<AccessControlPrivilege> {
        val errors = mutableSetOf<String>()
        privilegeFileValidator.validate(file)

        file.inputStream.use { inputStream ->
            val workbook = WorkbookFactory.create(inputStream)
            privilegeDataValidator.validateSheetsAndHeaders(workbook)
            val permissions =
                buildList {
                    addAll(parsePageLevelSheet(workbook, errors))
                    addAll(parseActionLevelSheet(workbook, errors))
                    addAll(parseFieldLevelSheet(workbook, errors))
                }

            if (errors.isNotEmpty()) {
                throw InvalidPrivilegeDataException("Invalid data in the file", errors)
            }

            return permissions
        }
    }

    private fun parsePageLevelSheet(
        workbook: Workbook,
        errors: MutableSet<String>,
    ): List<AccessControlPrivilege> {
        val sheet = workbook.getSheet(PAGE_LEVEL_SHEET_NAME) ?: return emptyList()

        val data = parseSheet(sheet, pageHeaders)
        val invalidValues = privilegeDataValidator.validatePages(data)

        if (invalidValues.isNotEmpty()) {
            errors.addAll(invalidValues)
            return emptyList()
        }

        return groupByResourceAndPermission(data, Resource.PAGES)
    }

    private fun parseActionLevelSheet(
        workbook: Workbook,
        errors: MutableSet<String>,
    ): List<AccessControlPrivilege> {
        val sheet = workbook.getSheet(ACTION_LEVEL_SHEET_NAME) ?: return emptyList()

        val data = parseSheet(sheet, actionHeaders)
        val invalidValues = privilegeDataValidator.validateActions(data)

        if (invalidValues.isNotEmpty()) {
            errors.addAll(invalidValues)
            return emptyList()
        }
        return groupByResourceAndPermission(data, Resource.ACTIONS)
    }

    private fun parseFieldLevelSheet(
        workbook: Workbook,
        errors: MutableSet<String>,
    ): List<AccessControlPrivilege> {
        val sheet = workbook.getSheet(FIELD_LEVEL_SHEET_NAME) ?: return emptyList()

        val data = parseSheet(sheet, fieldHeaders)
        val invalidValues = privilegeDataValidator.validateFields(data)

        if (invalidValues.isNotEmpty()) {
            errors.addAll(invalidValues)
            return emptyList()
        }

        return groupByDomainAndPermission(data)
    }

    private fun parseSheet(
        sheet: Sheet,
        headers: List<String>,
    ): List<ExcelRow> {
        val headerIndexMap =
            sheet.getRow(0).mapIndexed { index, cell -> cell.stringCellValue to index }.toMap()

        return sheet
            .drop(1) // Skip the header row
            .filter { it.getCell(0) != null } // Ignore empty rows
            .map { row ->
                ExcelRow(
                    cells =
                        headers.mapNotNull { header ->
                            headerIndexMap[header]?.let {
                                ExcelCell(header = header, value = row.getCell(it)?.stringCellValue)
                            }
                        },
                )
            }
    }

    private fun groupByResourceAndPermission(
        data: List<ExcelRow>,
        resource: Resource,
    ): List<AccessControlPrivilege> =
        data
            .groupBy { row -> row.cells.find { it.header == PERMISSION_HEADER }?.value ?: "" }
            .map { (permission, rows) ->
                AccessControlPrivilege(
                    resource = resource,
                    permission = parsePermission(permission),
                    fields =
                        rows
                            .mapNotNull { row -> row.cells.find { it.header == RESOURCE_HEADER }?.value }
                            .distinct(),
                )
            }

    private fun groupByDomainAndPermission(data: List<ExcelRow>): List<AccessControlPrivilege> =
        data
            .groupBy { row ->
                row.cells
                    .associateBy { it.header }
                    .let { it[DOMAIN_HEADER]?.value.orEmpty() to it[PERMISSION_HEADER]?.value.orEmpty() }
            }.mapNotNull { (key, rows) ->
                val (domain, permission) = key
                if (domain.isEmpty()) return@mapNotNull null
                AccessControlPrivilege(
                    resource = Resource.fromLabel(domain),
                    permission = parsePermission(permission),
                    fields =
                        rows
                            .flatMap { it.cells }
                            .mapNotNull { cell -> cell.takeIf { it.header == FIELD_NAME_HEADER }?.value }
                            .distinct(),
                )
            }

    private fun parsePermission(permission: String?) = permission?.takeIf { it.isNotEmpty() }?.let { PrivilegePermission.valueOf(it) }
}
