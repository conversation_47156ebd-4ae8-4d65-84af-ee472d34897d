/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.importdata

import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelRow
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.ACTION_LEVEL_SHEET_NAME
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.DOMAIN_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.FIELD_LEVEL_SHEET_NAME
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.FIELD_NAME_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.PAGE_LEVEL_SHEET_NAME
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.PERMISSION_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ExcelWorkSheet.Companion.RESOURCE_HEADER
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.PagesWithActions
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.MissingRequiredHeadersException
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.MissingRequiredSheetsException
import com.fleetmanagement.security.api.dto.PrivilegePermission
import org.apache.poi.ss.usermodel.Workbook
import org.springframework.stereotype.Component
import org.springframework.stereotype.Service

interface ExcelRowValidator {
    fun validate(rows: List<ExcelRow>): Set<String>
}

@Component
class PageSheetValidator : ExcelRowValidator {
    private val defaultPageRules =
        mapOf(
            RESOURCE_HEADER to PagesWithActions.getAllPageKeys().toSet(),
            PERMISSION_HEADER to setOf(PrivilegePermission.READ.name),
        )

    override fun validate(rows: List<ExcelRow>): Set<String> = findInvalidEntries(rows, defaultPageRules)
}

@Component
class ActionSheetValidator : ExcelRowValidator {
    private val defaultActionRules =
        mapOf(
            RESOURCE_HEADER to PagesWithActions.getAllActionKeys().toSet(),
            PERMISSION_HEADER to setOf(PrivilegePermission.EXECUTE.name),
        )

    override fun validate(rows: List<ExcelRow>): Set<String> = findInvalidEntries(rows, defaultActionRules)
}

@Component
class FieldSheetValidator : ExcelRowValidator {
    private val defaultFieldRules =
        mapOf(
            DOMAIN_HEADER to Resource.entries.map { it.label }.toSet(),
            FIELD_NAME_HEADER to FieldLabel.entries.map { it.label }.toSet(),
            PERMISSION_HEADER to setOf(PrivilegePermission.READ.name, PrivilegePermission.WRITE.name),
        )

    override fun validate(rows: List<ExcelRow>): Set<String> = findInvalidEntries(rows, defaultFieldRules)
}

@Service
class PrivilegeDataValidator(
    private val pageValidator: PageSheetValidator,
    private val actionValidator: ActionSheetValidator,
    private val fieldValidator: FieldSheetValidator,
) {
    companion object {
        val requiredSheetsWithHeaders =
            mapOf(
                PAGE_LEVEL_SHEET_NAME to ExcelWorkSheet.pageHeaders,
                ACTION_LEVEL_SHEET_NAME to ExcelWorkSheet.actionHeaders,
                FIELD_LEVEL_SHEET_NAME to ExcelWorkSheet.fieldHeaders,
            )
    }

    fun validatePages(rows: List<ExcelRow>): Set<String> = pageValidator.validate(rows)

    fun validateActions(rows: List<ExcelRow>): Set<String> = actionValidator.validate(rows)

    fun validateFields(rows: List<ExcelRow>): Set<String> = fieldValidator.validate(rows)

    fun validateSheetsAndHeaders(workbook: Workbook) {
        val sheetNames = (0 until workbook.numberOfSheets).map { workbook.getSheetAt(it).sheetName }
        val missingSheets = requiredSheetsWithHeaders.keys - sheetNames.toSet()
        val missingHeaders = mutableSetOf<String>()

        if (missingSheets.isNotEmpty()) {
            throw MissingRequiredSheetsException(missingFields = missingSheets, message = "Missing required sheets: $missingSheets")
        }

        requiredSheetsWithHeaders.forEach { (sheetName, expectedHeaders) ->
            val sheet = workbook.getSheet(sheetName)
            val firstRow =
                sheet.getRow(0)
                    ?: throw MissingRequiredHeadersException(
                        missingFields = expectedHeaders.toSet(),
                        message = "Missing headers in sheet: $sheetName",
                    )

            expectedHeaders.forEachIndexed { index, expectedHeader ->
                val actualHeader = firstRow.getCell(index)?.stringCellValue
                if (actualHeader == null) {
                    missingHeaders.add(expectedHeader)
                } else if (actualHeader != expectedHeader) {
                    missingHeaders.add(expectedHeader)
                }
            }
        }

        if (missingHeaders.isNotEmpty()) {
            throw MissingRequiredHeadersException(
                missingFields = missingHeaders,
                message = "The following headers are missing or incorrect: ${missingHeaders.joinToString(", ")}",
            )
        }
    }
}

fun findInvalidEntries(
    rows: List<ExcelRow>,
    rules: Map<String, Set<String>>,
): Set<String> =
    rows
        .flatMap { row: ExcelRow ->
            rules.keys.mapNotNull { header ->
                row.cells
                    .firstOrNull { it.header == header }
                    ?.value
                    ?.takeIf { value -> value.isNotEmpty() && value !in rules[header].orEmpty() }
            }
        }.toSet()
