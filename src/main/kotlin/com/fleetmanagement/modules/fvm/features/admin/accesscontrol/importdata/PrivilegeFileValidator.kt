/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.importdata

import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.InvalidFileException
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile

@Service
class PrivilegeFileValidator {
    fun validate(file: MultipartFile) {
        if (file.isEmpty) throw InvalidFileException("File is empty")
        if (!file.originalFilename!!.endsWith(".xlsx")) throw InvalidFileException("Invalid file type")
        if (file.size > 5 * 1024 * 1024) throw InvalidFileException("File size exceeds 5MB limit")
    }
}
