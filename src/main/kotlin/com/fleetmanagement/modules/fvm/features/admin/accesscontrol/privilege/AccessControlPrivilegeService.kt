/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.privilege

import com.fleetmanagement.modules.fvm.dto.accesscontrol.AccessControlPrivilege
import com.fleetmanagement.modules.fvm.dto.accesscontrol.PagesWithActions
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.fvm.features.admin.accesscontrol.exception.AccessControlExportException
import com.fleetmanagement.security.api.ReadGroupPrivileges
import com.fleetmanagement.security.api.dto.PrivilegePermission
import com.fleetmanagement.security.features.accesscontrol.domain.Privilege
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import org.reflections.Reflections
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.UUID

private const val ACCESS_CONTROLLED_PACKAGE = "com.fleetmanagement.modules.fvm"

@Service
class AccessControlPrivilegeService(
    private val readGroupPrivileges: ReadGroupPrivileges,
) {
    private val logger: Logger = LoggerFactory.getLogger(AccessControlPrivilegeService::class.java)

    fun getCurrentPrivileges(groupId: UUID): List<AccessControlPrivilege> {
        try {
            val defaultPrivileges = getDefaultPrivileges()
            return updatePermissionsFromDb(groupId, defaultPrivileges)
        } catch (e: RuntimeException) {
            logger.warn("Error while generating privileges", e)
            throw AccessControlExportException("Error while generating privileges", e)
        }
    }

    fun getDefaultPrivileges(): List<AccessControlPrivilege> = listOf(getPages(), getActions()) + getFields()

    private fun updatePermissionsFromDb(
        groupId: UUID,
        defaultPermissions: List<AccessControlPrivilege>,
    ): List<AccessControlPrivilege> {
        val dbPrivileges = readGroupPrivileges.findAllByGroupIdWithFilters(listOf(groupId))
        return defaultPermissions
            .flatMap { privilege ->
                val matchedDbPrivileges = dbPrivileges.filter { it.resource == privilege.resource.label }
                if (matchedDbPrivileges.isNotEmpty()) {
                    setPermissionForSelectedFields(privilege, matchedDbPrivileges)
                } else {
                    listOf(privilege)
                }
            }.sortedBy { it.resource }
    }

    private fun setPermissionForSelectedFields(
        privilege: AccessControlPrivilege,
        matchedDbPrivileges: List<Privilege>,
    ): List<AccessControlPrivilege> {
        val allFields = privilege.fields.toMutableSet()
        val fieldToPermissionMap = mutableMapOf<String, PrivilegePermission?>()

        // Assign permissions to specific fields based on matched DB privileges
        matchedDbPrivileges.forEach { dbPrivilege ->
            val fields =
                dbPrivilege.filter?.fields?.filter { it in allFields }
                    ?: allFields // If filter is null, apply to all fields
            fields.forEach { field -> fieldToPermissionMap[field] = dbPrivilege.permission }
        }

        // Identify fields that were NOT assigned a permission in DB
        val remainingFields = allFields - fieldToPermissionMap.keys

        val groupedPrivileges =
            fieldToPermissionMap.entries
                .groupBy({ it.value }, { it.key }) // Group by permission, collecting field names
                .map { (permission, fields) ->
                    privilege.copy(permission = permission, fields = fields)
                }.toMutableList()

        // Add a privilege entry for remaining fields (if any) with empty default permission
        if (remainingFields.isNotEmpty()) {
            groupedPrivileges.add(privilege.copy(permission = null, fields = remainingFields.toList()))
        }

        return groupedPrivileges
    }

    private fun getActions() = AccessControlPrivilege(resource = Resource.ACTIONS, fields = PagesWithActions.getAllActionKeys())

    private fun getPages() = AccessControlPrivilege(resource = Resource.PAGES, fields = PagesWithActions.getAllPageKeys())

    private fun getFields(): List<AccessControlPrivilege> {
        val classes = findAllAnnotatedClasses()

        return classes
            .mapNotNull { clazz ->
                val domain =
                    clazz.getAnnotation(AccessControlledResource::class.java)?.uniqueLabel?.label
                        ?: return@mapNotNull null

                val fields =
                    clazz.declaredFields.mapNotNull { property ->
                        property.getAnnotation(AccessControlledField::class.java)?.fieldNameOverride?.label
                    }

                AccessControlPrivilege(
                    resource = Resource.fromLabel(domain),
                    fields = fields,
                )
            }.groupBy { it.resource to it.permission }
            .map { (key, privileges) ->
                AccessControlPrivilege(
                    resource = key.first,
                    permission = key.second,
                    fields = privileges.flatMap { it.fields }.distinct(),
                )
            }
    }

    private fun findAllAnnotatedClasses(): Set<Class<*>> {
        val reflections = Reflections(ACCESS_CONTROLLED_PACKAGE)
        return reflections.getTypesAnnotatedWith(AccessControlledResource::class.java)
    }
}
