/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.util

import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.api.CreateOrUpdatePrivileges
import com.fleetmanagement.security.api.ReadPrivileges
import com.fleetmanagement.security.api.dto.PrivilegePermission
import com.fleetmanagement.security.api.exceptions.PrivilegeNotFoundException
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.stereotype.Component

@Component
class CreateDefaultPrivileges(
    private val createOrUpdatePrivileges: CreateOrUpdatePrivileges,
    private val readPrivileges: ReadPrivileges,
) {
    @EventListener(ApplicationReadyEvent::class)
    fun createDefaultPrivileges() {
        val missingPrivileges = findMissingPrivileges()

        missingPrivileges.map { missingPrivilege ->
            runCatching {
                createOrUpdatePrivileges.createPrivilegeBy(missingPrivilege.resource.label, missingPrivilege.permission)
            }.onFailure {
                when (it) {
                    // Ignore race condition error in case of clustered environment execution
                    is DataIntegrityViolationException -> {}
                    else -> throw IllegalStateException(
                        "Default privileges that are required for application cannot be created",
                        it,
                    )
                }
            }
        }
    }

    private fun findMissingPrivileges(): List<MissingPrivilege> {
        val readPrivileges = listOf(PrivilegePermission.READ)
        val executePrivileges = listOf(PrivilegePermission.EXECUTE)
        val apiPrivileges = listOf(PrivilegePermission.READ, PrivilegePermission.WRITE, PrivilegePermission.DELETE)
        val defaultPrivileges = listOf(PrivilegePermission.READ, PrivilegePermission.WRITE)

        return Resource.entries.flatMap { resource ->
            when (resource) {
                Resource.PAGES -> checkMissingPrivileges(resource, readPrivileges)
                Resource.ACTIONS -> checkMissingPrivileges(resource, executePrivileges)
                Resource.APIS -> checkMissingPrivileges(resource, apiPrivileges)
                else -> checkMissingPrivileges(resource, defaultPrivileges)
            }
        }
    }

    private fun checkMissingPrivileges(
        resource: Resource,
        privileges: List<PrivilegePermission>,
    ): List<MissingPrivilege> =
        privileges.mapNotNull { privilege ->
            runCatching {
                readPrivileges.readPrivilegesBy(resource.label, privilege)
            }.exceptionOrNull()
                ?.takeIf { it is PrivilegeNotFoundException }
                ?.let { MissingPrivilege(resource = resource, permission = privilege) }
        }
}

data class MissingPrivilege(
    val resource: Resource,
    val permission: PrivilegePermission,
)
