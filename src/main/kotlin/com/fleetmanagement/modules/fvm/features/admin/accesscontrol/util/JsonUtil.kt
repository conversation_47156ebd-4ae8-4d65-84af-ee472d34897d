/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.admin.accesscontrol.util

import com.fasterxml.jackson.databind.JsonNode

object JsonUtil {
    fun flattenJson(node: JsonNode): Map<String, String> = flattenJsonNode(node, "")

    private fun flattenJsonNode(
        node: JsonNode,
        prefix: String,
    ): Map<String, String> {
        val flattenedMap = mutableMapOf<String, String>()
        when {
            node.isObject -> {
                node.fields().forEach { (fieldName, fieldValue) ->
                    flattenedMap.putAll(flattenJsonNode(fieldValue, "$prefix$fieldName."))
                }
            }

            node.isArray -> {
                node.forEachIndexed { index, element ->
                    flattenedMap.putAll(flattenJsonNode(element, "$prefix[$index]."))
                }
            }

            else -> {
                val finalKey = prefix.dropLast(1)
                if (finalKey.endsWith("_one")) {
                    val singularKey = finalKey.substring(0, finalKey.length - 4)
                    flattenedMap[singularKey] = node.asText()
                } else if (finalKey.endsWith("_other")) {
                    val pluralKey = finalKey.substring(0, finalKey.length - 6) + "s"
                    flattenedMap[pluralKey] = node.asText()
                } else {
                    flattenedMap[finalKey] = node.asText()
                }
            }
        }

        return flattenedMap
    }
}
