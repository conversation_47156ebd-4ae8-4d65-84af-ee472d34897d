package com.fleetmanagement.modules.fvm.features.campaigns

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.api.ErrorType.FVM_CAMPAIGNS_BAD_REQUEST_ERROR
import com.fleetmanagement.modules.fvm.dto.api.ErrorType.FVM_CAMPAIGNS_NOT_FOUND_ERROR
import com.fleetmanagement.modules.fvm.dto.api.ErrorType.FVM_CAMPAIGNS_READ_ERROR
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.CreateCampaignDTO
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.FVMCampaign
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.FVMUpdateCampaign
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException.CampaignNotFoundException
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException.CannotCreateCampaignWithCampaignId
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException.FailedToReadCampaignsException
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI

/**
 * Controller for managing campaign related operations such as  creating, updating and retrieving.
 */
@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMCampaignController(
    private val fvmCampaignService: FVMCampaignService,
) {
    /**
     *  Retrieves all campaigns available in FVM system.
     *
     *  @return List of campaigns [FVMCampaign] wrapped in {@link APIResponse}.
     *
     */
    @GetMapping("/campaigns")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_CAMPAIGN_READ)")
    fun getAllCampaigns(): ResponseEntity<APIResponse<List<FVMCampaign>>> =
        ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(
            APIResponse(
                data = fvmCampaignService.getAllCampaigns(),
            ),
        )

    /**
     *  Create a campaigin in the FVM system.
     *
     *  @return Created [FVMCampaign] wrapped in {@link APIResponse}.
     *
     */
    @PostMapping("/campaigns")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_CAMPAIGN_WRITE)")
    fun createCampaign(
        @Valid @RequestBody createCampaignDTO: CreateCampaignDTO,
    ): ResponseEntity<APIResponse<FVMCampaign>> {
        val createdCampaign = fvmCampaignService.createCampaign(createCampaignDTO)
        return ResponseEntity
            .created(URI.create(""))
            .body(APIResponse(createdCampaign))
    }

    /**
     *  Updates a campaign that is already in the FVM system.
     *
     *  @return updated [FVMCampaign] wrapped in [APIResponse].
     *
     */
    @PutMapping("/campaigns/{campaignId}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_CAMPAIGN_WRITE)")
    fun updateCampaign(
        @RequestBody fvmUpdateCampaign: FVMUpdateCampaign,
        @PathVariable campaignId: String,
    ): ResponseEntity<APIResponse<FVMCampaign>> {
        validateInput(campaignId, fvmUpdateCampaign)
        val updatedCampaign = fvmCampaignService.updateCampaign(fvmUpdateCampaign)
        return ResponseEntity.ok(APIResponse(data = updatedCampaign))
    }

    /**
     * Handles exceptions when there is a failure in reading campaigns.
     *
     * This method catches [FailedToReadCampaignsException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [FailedToReadCampaignsException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(FailedToReadCampaignsException::class)
    fun handleReadCampaignsException(ex: FailedToReadCampaignsException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(
                APIResponse.forErrors(
                    ProblemDetail
                        .forStatus(HttpStatus.NOT_FOUND)
                        .apply {
                            title = "Campaigns read error"
                            detail = ex.message
                            type = URI.create(FVM_CAMPAIGNS_READ_ERROR.value)
                        },
                ),
            )

    /**
     * Handles exceptions when there is a failure in creating a campaign.
     *
     * This method catches [CannotCreateCampaignWithCampaignId] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [CannotCreateCampaignWithCampaignId] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(CannotCreateCampaignWithCampaignId::class)
    fun handleCannotCreateCampaignException(ex: CannotCreateCampaignWithCampaignId): ResponseEntity<ProblemDetail> =
        ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(
                ProblemDetail
                    .forStatus(HttpStatus.NOT_FOUND)
                    .apply {
                        title = "Cannot create campaign"
                        detail = ex.message
                        type = URI.create(ErrorType.FVM_CAMPAIGNS_ALREADY_EXISTS_ERROR.value)
                        setProperty("campaignId", ex.campaignId)
                    },
            )

    /**
     * Handles exceptions when there is a failure in updating campaign.
     *
     * This method catches [CampaignNotFoundException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [CampaignNotFoundException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(CampaignNotFoundException::class)
    fun handleCampaignNotFoundException(ex: CampaignNotFoundException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .contentType(MediaType.APPLICATION_PROBLEM_JSON)
            .body(
                APIResponse.forErrors(
                    ProblemDetail
                        .forStatus(HttpStatus.NOT_FOUND)
                        .apply {
                            title = "Campaign not found"
                            detail = ex.message
                            type = URI.create(FVM_CAMPAIGNS_NOT_FOUND_ERROR.value)
                            setProperty("campaignId", ex.campaignId)
                        },
                ),
            )

    /**
     * Handles input validation for campaign update.
     *
     * @throws ex The exception instance of [IllegalArgumentException] when input validation fails.
     */
    private fun validateInput(
        campaignId: String,
        fvmUpdateCampaign: FVMUpdateCampaign,
    ) {
        require(campaignId == fvmUpdateCampaign.id) {
            "Campaign ID in path $campaignId doesn't match with body ${fvmUpdateCampaign.id}"
        }
    }

    /**
     * Handles exceptions when there is bad input received from campaign update request.
     *
     * This method catches [IllegalArgumentException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [IllegalArgumentException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(IllegalArgumentException::class)
    private fun handleCampaignUpdateBadRequest(ex: IllegalArgumentException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(
                APIResponse.forErrors(
                    ProblemDetail
                        .forStatus(HttpStatus.BAD_REQUEST)
                        .apply {
                            title = "Campaign can't be updated"
                            detail = ex.message
                            type = URI.create(FVM_CAMPAIGNS_BAD_REQUEST_ERROR.value)
                        },
                ),
            )
}
