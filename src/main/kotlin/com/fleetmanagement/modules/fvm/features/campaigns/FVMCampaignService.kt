package com.fleetmanagement.modules.fvm.features.campaigns

import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.CreateCampaignDTO
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.FVMCampaign
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.FVMUpdateCampaign
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.toDTO
import com.fleetmanagement.modules.vehiclecampaigns.api.CreateCampaign
import com.fleetmanagement.modules.vehiclecampaigns.api.ReadAllCampaigns
import com.fleetmanagement.modules.vehiclecampaigns.api.UpdateCampaign
import org.springframework.stereotype.Service

/**
 * Service class for managing campaign operations.
 * This class provides business logic for creating, updating and retrieving campaigns.
 *
 */
@Service
class FVMCampaignService(
    private var readAllCampaigns: ReadAllCampaigns,
    private val createCampaign: CreateCampaign,
    private val updateCampaign: UpdateCampaign,
) {
    /**
     * Retrieves all campaigns from the VehicleCampaign module.
     *
     * @return A list of [FVMCampaign] representing the campaigns.
     */
    fun getAllCampaigns(): List<FVMCampaign> {
        val campaigns = readAllCampaigns.readAllCampaigns()
        return campaigns.map {
            it.toDTO()
        }
    }

    /**
     * Updates a campaign that is already there in FVM system.
     *
     * @return updated [FVMCampaign].
     */
    fun createCampaign(campaignDTO: CreateCampaignDTO): FVMCampaign {
        val campaign =
            createCampaign.createCampaign(
                campaignDTO.id,
                campaignDTO.description,
                campaignDTO.isProcessable,
            )
        return campaign.toDTO()
    }

    fun updateCampaign(fvmUpdateCampaign: FVMUpdateCampaign): FVMCampaign =
        updateCampaign
            .updateCampaign(
                campaignId = fvmUpdateCampaign.id,
                isProcessable = fvmUpdateCampaign.isProcessable,
            ).toDTO()
}
