# FVM Campaigns Feature
This feature enables FVM UI to manage the campaign data in FVM system
<br> To know more about how the campaign data is pulled from external systems refer to Campaigns [module documentation](../../../vehiclecampaigns/README.md).

## APIs
This feature provides REST APIs to handle following use cases
1. [View Campaigns](#view-campaigns)
2. [Update Campaign](#update-campaigns)
3. [Add Campaign](#add-campaigns)
4. ~~Delete Campaign (out of scope)~~

### View Campaigns
FVM UI can get all the existing campaign data using API resource `/ui/campaigns`

```plantuml
@startuml
title "FVM Campaign Management: View Campaigns"
actor "FVM UI" as UI
box "FVM Backend"
participant "FVM Module" as FVM
participant "Vehicle Campaigns \n Module" as VCM
end box
database "FVM Database" as DB

group "View All Campaigns"
UI -> FVM: Get all campaigns \n (/ui/campaigns)
FVM -> FVM: Authorize
FVM -> VCM: List all campaigns
VCM -> DB: Fetch all campaigns (JPA)
DB -> VCM: List<JPACampaignEntity>
VCM -> FVM: List<VehicleCampaign>
FVM -> UI: List<FVMCampaign>
end
@enduml
```

### Update Campaigns
FVM UI can update the existing campaign data using API resource `/ui/campaigns/{campaign-id}`. <br>
At the moment only `isProcessable` field can be updated.


```plantuml
@startuml
title "FVM Campaign Management: Update Campaign"
actor "FVM UI" as UI
box "FVM Backend"
participant "FVM Module" as FVM
participant "Vehicle Campaigns \n Module" as VCM
end box
database "FVM Database" as DB

group "update campaign"
UI -> FVM: Update campaign \n (/ui/campaigns/{campaign-id})
FVM -> FVM: Authorize
FVM -> VCM: update campaign
VCM -> DB: look for campaign
alt "campaign found"
    VCM -> DB: update campaign entity
    DB -> VCM: updated JPACampaignEntity
    VCM -> FVM: updated VehicleCampaign
    FVM -> UI: updated FVMCampaign
end
alt "campaign NOT found"
    VCM -> FVM: CampaignNotFound error
    FVM -> UI: campaign not found error
end
end
@enduml
```


### Add Campaigns
FVM UI can add the campaign data using API resource `/ui/campaigns`. <br>
Campaign ID is unique across the system, add campaign request fails if campaign exists with same ID


```plantuml
@startuml
title "FVM Campaign Management: Add Campaign"
actor "FVM UI" as UI
box "FVM Backend"
participant "FVM Module" as FVM
participant "Vehicle Campaigns \n Module" as VCM
end box
database "FVM Database" as DB

group "add campaign"
UI -> FVM: Add campaign \n (/ui/campaigns)
FVM -> FVM: Authorize
FVM -> VCM: create campaign
VCM -> DB: look for campaign if already exists
alt "campaign doesn't exist"
    VCM -> DB: create campaign entity
    DB -> VCM: created JPACampaignEntity
    VCM -> FVM: created VehicleCampaign
    FVM -> UI: created FVMCampaign
end
alt "campaign with ID already exists"
    VCM -> FVM: CampaignAlreadyExists error
    FVM -> UI: campaign exists with same id error
end
end
@enduml
```
