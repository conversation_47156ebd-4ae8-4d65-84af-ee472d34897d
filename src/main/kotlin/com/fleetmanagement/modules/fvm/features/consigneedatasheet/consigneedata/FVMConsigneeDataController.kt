/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.consigneedatasheet.consigneedata

import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.application.CreateConsigneeDataException
import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.CreateConsigneeDataUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.DeleteConsigneeDataUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.ReadConsigneeDataUseCase
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.consigneedata.FVMConsigneeData
import com.fleetmanagement.modules.fvm.dto.consigneedata.FVMCreateConsigneeData
import com.fleetmanagement.modules.fvm.dto.consigneedata.FVMUpdateConsigneeData
import com.fleetmanagement.modules.fvm.dto.consigneedata.toConsigneeDataNewOrUpdate
import com.fleetmanagement.modules.fvm.dto.consigneedata.toFVMConsigneeData
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI
import java.util.UUID

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMConsigneeDataController(
    private val createConsigneeDataUseCase: CreateConsigneeDataUseCase,
    private val readConsigneeDataUseCase: ReadConsigneeDataUseCase,
    private val deleteConsigneeDataUseCase: DeleteConsigneeDataUseCase,
    private val fvmConsigneeDataService: FVMConsigneeDataService,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(FVMConsigneeDataController::class.java)
    }

    @GetMapping("/vehicle-transfer-maintenance/consignee-data")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_CONSIGNEE_DATA_READ)")
    fun readAllConsigneeData(): ResponseEntity<APIResponse<List<FVMConsigneeData>>> {
        val consigneeData = readConsigneeDataUseCase.readAllConsigneeData()
        return ResponseEntity.ok(APIResponse(consigneeData.map { it.toFVMConsigneeData() }))
    }

    @PostMapping("/vehicle-transfer-maintenance/consignee-data")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_CONSIGNEE_DATA_WRITE)")
    fun createConsigneeData(
        @RequestBody fvmCreateConsigneeData: FVMCreateConsigneeData,
    ): ResponseEntity<APIResponse<FVMConsigneeData?>>? {
        val consigneeDataNew =
            createConsigneeDataUseCase.createConsigneeData(
                fvmCreateConsigneeData.toConsigneeDataNewOrUpdate(),
            )
        return ResponseEntity.ok(APIResponse(consigneeDataNew.toFVMConsigneeData()))
    }

    @PutMapping("/vehicle-transfer-maintenance/consignee-data")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_CONSIGNEE_DATA_WRITE)")
    fun updateConsigneeData(
        @RequestBody updateConsigneeData: List<FVMUpdateConsigneeData>,
    ): ResponseEntity<APIResponse<List<FVMConsigneeData>>> {
        val response = fvmConsigneeDataService.updateConsigneeData(updateConsigneeData)
        return when {
            !response.errors.isNullOrEmpty() -> ResponseEntity(response, HttpStatus.MULTI_STATUS)
            else -> ResponseEntity.ok(response)
        }
    }

    @DeleteMapping("/vehicle-transfer-maintenance/consignee-data/{id}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_CONSIGNEE_DATA_DELETE)")
    fun deleteConsigneeData(
        @PathVariable("id") id: UUID,
    ): ResponseEntity<APIResponse<UUID>> {
        deleteConsigneeDataUseCase.deleteConsigneeData(id)
        return ResponseEntity.ok(APIResponse(id))
    }

    @ExceptionHandler(CreateConsigneeDataException::class)
    fun handleCreateConsigneeDataException(ex: CreateConsigneeDataException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("Create Consignee Data failed for consignee with error", ex)
        return ResponseEntity
            .status(HttpStatus.CONFLICT)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatusAndDetail(HttpStatus.CONFLICT, ex.message).apply {
                        type = URI.create(ErrorType.FVM_DATA_INTEGRITY_VIOLATION.value)
                    },
                ),
            )
    }

    @ExceptionHandler(ConsigneeDataNotFoundException::class)
    fun handleConsigneeDataNotFoundException(ex: ConsigneeDataNotFoundException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatusAndDetail(HttpStatus.NOT_FOUND, ex.message).apply {
                        type = URI.create(ErrorType.FVM_CONSIGNEE_NOT_FOUND.value)
                    },
                ),
            )
}
