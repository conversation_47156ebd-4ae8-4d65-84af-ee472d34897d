/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.consigneedatasheet.consigneedata

import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.application.UpdateConsigneeDataException
import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.UpdateConsigneeDataUseCase
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.consigneedata.FVMConsigneeData
import com.fleetmanagement.modules.fvm.dto.consigneedata.FVMUpdateConsigneeData
import com.fleetmanagement.modules.fvm.dto.consigneedata.toConsigneeDataNewOrUpdate
import com.fleetmanagement.modules.fvm.dto.consigneedata.toFVMConsigneeData
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.stereotype.Component
import java.net.URI

@Component
class FVMConsigneeDataService(
    private val updateConsigneeDataUseCase: UpdateConsigneeDataUseCase,
) {
    fun updateConsigneeData(updateConsigneeDataList: List<FVMUpdateConsigneeData>): APIResponse<List<FVMConsigneeData>> {
        val consigneeDataUpdateList = mutableListOf<FVMConsigneeData>()

        val errors =
            updateConsigneeDataList.mapNotNull {
                try {
                    val consigneeDataUpdate =
                        updateConsigneeDataUseCase.updateConsigneeData(it.id, it.toConsigneeDataNewOrUpdate(), it.version)
                    consigneeDataUpdateList.add(consigneeDataUpdate.toFVMConsigneeData())
                    null
                } catch (exception: DataIntegrityViolationException) {
                    ProblemDetail.forStatusAndDetail(HttpStatus.CONFLICT, exception.message).apply {
                        type = URI.create(ErrorType.FVM_DATA_INTEGRITY_VIOLATION.value)
                        properties = mapOf("id" to it.id)
                    }
                } catch (exception: ConsigneeDataNotFoundException) {
                    ProblemDetail
                        .forStatusAndDetail(HttpStatus.NOT_FOUND, exception.message)
                        .apply {
                            type = URI.create(ErrorType.FVM_CONSIGNEE_NOT_FOUND.value)
                            properties = mapOf("id" to it.id)
                        }
                } catch (exception: UpdateConsigneeDataException) {
                    ProblemDetail
                        .forStatusAndDetail(HttpStatus.CONFLICT, exception.message)
                        .apply {
                            type = URI.create(ErrorType.FVM_CONSIGNEE_UPDATE_FAILED.value)
                            properties = mapOf("id" to it.id)
                        }
                }
            }

        return if (errors.isEmpty()) {
            APIResponse(
                data = consigneeDataUpdateList,
            )
        } else {
            APIResponse(data = consigneeDataUpdateList, errors = errors)
        }
    }
}
