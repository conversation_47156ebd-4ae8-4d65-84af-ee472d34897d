/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.consigneedatasheet.costcenter

import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.application.CreateCostCenterException
import com.fleetmanagement.modules.consigneedatasheet.application.UpdateCostCenterException
import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.CreateCostCenterUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.DeleteCostCenterUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.ReadCostCenterUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.UpdateCostCenterUseCase
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.costcenter.CostCenterMapper
import com.fleetmanagement.modules.fvm.dto.costcenter.FVMCostCenter
import com.fleetmanagement.modules.fvm.dto.costcenter.FVMCreateOrUpdateCostCenter
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.net.URI
import java.util.*

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMCostCenterController(
    private val createCostCenterUseCase: CreateCostCenterUseCase,
    private val readCostCenterUseCase: ReadCostCenterUseCase,
    private val updateCostCenterUseCase: UpdateCostCenterUseCase,
    private val deleteCostCenterUseCase: DeleteCostCenterUseCase,
) {
    @GetMapping("/vehicle-transfer-maintenance/cost-center")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_COST_CENTER_READ)")
    fun readAllCostCenter(): ResponseEntity<APIResponse<List<FVMCostCenter>>> {
        val costCenter = readCostCenterUseCase.readAllCostCenter()
        return ResponseEntity.ok(APIResponse(costCenter.map { CostCenterMapper.INSTANCE.map(it) }))
    }

    @PostMapping("/vehicle-transfer-maintenance/cost-center")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_COST_CENTER_WRITE)")
    fun createCostCenter(
        @RequestBody fvmCreateCostCenter: FVMCreateOrUpdateCostCenter,
    ): ResponseEntity<APIResponse<FVMCostCenter>> {
        try {
            val costCenterNew =
                createCostCenterUseCase.createCostCenter(CostCenterMapper.INSTANCE.map(fvmCreateCostCenter))
            return ResponseEntity.ok(APIResponse(CostCenterMapper.INSTANCE.map(costCenterNew)))
        } catch (ex: CreateCostCenterException) {
            logger.error(
                "Create Cost Center failed for cost center ${fvmCreateCostCenter.costCenterId} with error",
                ex,
            )
            val problemDetail =
                ProblemDetail.forStatusAndDetail(HttpStatus.CONFLICT, ex.message)
            problemDetail.type = URI.create(ErrorType.FVM_DATA_INTEGRITY_VIOLATION.value)
            return ResponseEntity.of(problemDetail).build()
        }
    }

    @PutMapping("/vehicle-transfer-maintenance/cost-center/{id}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_COST_CENTER_WRITE)")
    fun updateCostCenter(
        @RequestHeader("version") version: Int,
        @PathVariable("id") id: UUID,
        @RequestBody fvmUpdateCostCenter: FVMCreateOrUpdateCostCenter,
    ): ResponseEntity<APIResponse<FVMCostCenter>> {
        try {
            val costCenterUpdate =
                updateCostCenterUseCase.updateCostCenter(
                    id,
                    CostCenterMapper.INSTANCE.map(fvmUpdateCostCenter),
                    version,
                )
            return ResponseEntity.ok(APIResponse(CostCenterMapper.INSTANCE.map(costCenterUpdate)))
        } catch (ex: UpdateCostCenterException) {
            logger.error(
                "Update Cost Center failed for cost center ${fvmUpdateCostCenter.costCenterId} with error",
                ex,
            )
            val problemDetail =
                ProblemDetail.forStatusAndDetail(HttpStatus.CONFLICT, ex.message)
            problemDetail.type = URI.create(ErrorType.FVM_DATA_INTEGRITY_VIOLATION.value)
            return ResponseEntity.of(problemDetail).build()
        }
    }

    @DeleteMapping("/vehicle-transfer-maintenance/cost-center/{id}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_COST_CENTER_DELETE)")
    fun deleteCostCenter(
        @PathVariable("id") id: UUID,
    ): ResponseEntity<APIResponse<UUID>> {
        deleteCostCenterUseCase.deleteCostCenter(id)
        return ResponseEntity.ok(APIResponse(id))
    }

    @ExceptionHandler(CostCenterNotFoundException::class)
    fun handleCostCenterNotFoundException(ex: CostCenterNotFoundException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatusAndDetail(HttpStatus.NOT_FOUND, ex.message).apply {
                        type = URI.create(ErrorType.FVM_COST_CENTER_NOT_FOUND.value)
                    },
                ),
            )

    companion object {
        private val logger = LoggerFactory.getLogger(FVMCostCenterController::class.java)
    }
}
