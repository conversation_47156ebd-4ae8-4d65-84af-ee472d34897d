/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.consigneedatasheet.usagegroup

import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup.ReadUsageGroupUseCase
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.usagegroup.FVMUsageGroup
import com.fleetmanagement.modules.fvm.dto.usagegroup.UsageGroupMapper
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.net.URI

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMUsageGroupController(
    private val readUsageGroupUseCase: ReadUsageGroupUseCase,
) {
    @GetMapping("/vehicle-transfer-maintenance/usage-group")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_USAGE_GROUP_READ)")
    fun readAllUsageGroup(): ResponseEntity<APIResponse<List<FVMUsageGroup>>> {
        val usageGroupList = readUsageGroupUseCase.readAllUsageGroups()
        return ResponseEntity.ok(
            APIResponse(usageGroupList.map { UsageGroupMapper.INSTANCE.map(it) }),
        )
    }

    @ExceptionHandler(UsageGroupNotFoundException::class)
    fun handleUsageGroupNotFoundException(ex: UsageGroupNotFoundException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatusAndDetail(HttpStatus.NOT_FOUND, ex.message).apply {
                        type = URI.create(ErrorType.FVM_USAGE_GROUP_NOT_FOUND.value)
                    },
                ),
            )
}
