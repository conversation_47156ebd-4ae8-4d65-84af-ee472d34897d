/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.consigneedatasheet.vehicleusage

import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage.ReadVehicleUsageUseCase
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.vehicleusage.FVMVehicleUsage
import com.fleetmanagement.modules.fvm.dto.vehicleusage.VehicleUsageMapper
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.net.URI

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMVehicleUsageController(
    private val readVehicleUsageUseCase: ReadVehicleUsageUseCase,
) {
    @GetMapping("/vehicle-transfer-maintenance/vehicle-usage")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_USAGE_READ)")
    fun readAllVehicleUsage(): ResponseEntity<APIResponse<List<FVMVehicleUsage>>> {
        val vehicleUsageList = readVehicleUsageUseCase.readAllVehicleUsage()
        return ResponseEntity.ok(
            APIResponse(vehicleUsageList.map { VehicleUsageMapper.INSTANCE.map(it) }),
        )
    }

    @ExceptionHandler(VehicleUsageNotFoundException::class)
    fun handleVehicleUsageNotFoundException(ex: VehicleUsageNotFoundException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatusAndDetail(HttpStatus.NOT_FOUND, ex.message).apply {
                        type = URI.create(ErrorType.FVM_VEHICLE_USAGE_NOT_FOUND.value)
                    },
                ),
            )
}
