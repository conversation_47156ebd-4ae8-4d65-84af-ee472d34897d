/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.documents

import com.fleetmanagement.modules.documentgeneration.api.GenerateGreenInsuranceCard
import com.fleetmanagement.modules.documentgeneration.api.GenerateGreenInsuranceCardException
import com.fleetmanagement.modules.documentgeneration.api.GeneratePowerOfAttorney
import com.fleetmanagement.modules.documentgeneration.api.GeneratePowerOfAttorneyException
import com.fleetmanagement.modules.documentgeneration.api.GenerateProvisionOfDelivery
import com.fleetmanagement.modules.documentgeneration.api.GenerateProvisionOfDeliveryDocumentException
import com.fleetmanagement.modules.documentgeneration.api.NoScheduledTransfersException
import com.fleetmanagement.modules.documentgeneration.api.dtos.ErrorDetail
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import io.opentelemetry.instrumentation.annotations.SpanAttribute
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.net.URI

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMDocumentGenerationController(
    private val greenInsuranceCard: GenerateGreenInsuranceCard,
    private val generatePowerOfAttorney: GeneratePowerOfAttorney,
    private val generateProvisionOfDelivery: GenerateProvisionOfDelivery,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(FVMDocumentGenerationController::class.java)
    }

    @WithSpan("read.vehicle")
    @PostMapping("/vehicles/insurance-card")
    fun downloadPdf(
        @SpanAttribute("vehicleIds") @RequestBody request: DocumentGenerationRequest,
    ): ResponseEntity<ByteArray> {
        val insuranceCards = this.greenInsuranceCard.generateGreenInsuranceCard(request.vehicleIds)

        if (insuranceCards.errors.isNotEmpty()) {
            throw DocumentGenerationException(insuranceCards.errors)
        }

        return ResponseEntity
            .status(HttpStatus.OK)
            .header("Content-Disposition", "attachment; filename=document.pdf")
            .contentType(MediaType.APPLICATION_PDF)
            .body(insuranceCards.data?.byteArray)
    }

    @WithSpan("read.vehicle")
    @PostMapping("/vehicles/power-of-attorney")
    fun downloadPowerOfAttorney(
        @SpanAttribute("vehicleIds") @RequestBody request: DocumentGenerationRequest,
    ): ResponseEntity<ByteArray> {
        val powerOfAttorney = this.generatePowerOfAttorney.generatePowerOfAttorney(request.vehicleIds)

        if (powerOfAttorney.errors.isNotEmpty()) {
            throw DocumentGenerationException(powerOfAttorney.errors)
        }

        return ResponseEntity
            .status(HttpStatus.OK)
            .header("Content-Disposition", "attachment; filename=document.pdf")
            .contentType(MediaType.APPLICATION_PDF)
            .body(powerOfAttorney.data?.byteArray)
    }

    @WithSpan("read.vehicle")
    @PostMapping("/vehicles/provision-of-delivery")
    fun downloadProvisionOfDelivery(
        @SpanAttribute("plannedDeliveryDate") @RequestBody request: ProvisionOfDeliveryDocumentGenerationRequest,
    ): ResponseEntity<ByteArray> {
        val provisionOfDeliveryDocument = this.generateProvisionOfDelivery.generateProvisionOfDelivery(request.plannedDeliveryDate)

        return ResponseEntity
            .status(HttpStatus.OK)
            .header("Content-Disposition", "attachment; filename=document.pdf")
            .contentType(MediaType.APPLICATION_PDF)
            .body(provisionOfDeliveryDocument.byteArray)
    }

    @ExceptionHandler(GenerateGreenInsuranceCardException::class)
    fun handleGenerateGreenInsuranceCardException(ex: GenerateGreenInsuranceCardException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("Error while generating green insurance card", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "Error generating insurance card",
                ).apply {
                    type = URI.create(ErrorType.SYSTEM_ERROR_INTERNAL_SERVER_ERROR.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(GeneratePowerOfAttorneyException::class)
    fun handleGeneratePowerOfAttorneyException(ex: GeneratePowerOfAttorneyException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("Error generating power of attorney", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "Error generating power of attorney",
                ).apply {
                    type = URI.create(ErrorType.SYSTEM_ERROR_INTERNAL_SERVER_ERROR.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(DocumentGenerationException::class)
    fun handleDocumentGenerationException(ex: DocumentGenerationException): ResponseEntity<APIResponse<Nothing?>> {
        val errorDetails =
            ex.errors.map { error ->
                ProblemDetail.forStatus(HttpStatus.INTERNAL_SERVER_ERROR).apply {
                    type = URI.create(ErrorType.FVM_DATA_MISSING.value)
                    title = "Problem generating PDF"
                    detail = "${error.vehicleId} : ${error.message}"
                    properties =
                        mapOf(
                            "vehicleId" to error.vehicleId,
                            "vin" to error.vin,
                        )
                }
            }
        val result =
            APIResponse(
                data = null,
                errors = errorDetails,
            )
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result)
    }

    @ExceptionHandler(GenerateProvisionOfDeliveryDocumentException::class)
    fun handleGenerateProvisionOfDeliveryDocumentException(
        ex: GenerateProvisionOfDeliveryDocumentException,
    ): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "Error generating provision of delivery document. ${ex.message}",
                ).apply {
                    type = URI.create(ErrorType.SYSTEM_ERROR_INTERNAL_SERVER_ERROR.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(NoScheduledTransfersException::class)
    fun handleNoScheduledTransfersException(ex: NoScheduledTransfersException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "Error generating provision of delivery document. ${ex.message}",
                ).apply {
                    type = URI.create(ErrorType.FVM_PROVISION_OF_DELIVERY_NO_SCHEDULED_TRANSFERS.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }
}

class DocumentGenerationException(
    val errors: List<ErrorDetail>,
) : RuntimeException()
