package com.fleetmanagement.modules.fvm.features.enquirymanagement

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.enquirymanagement.VehicleResponsibilityDetails
import com.fleetmanagement.modules.fvm.dto.enquirymanagement.VehicleResponsibilityDetailsSearchRequest
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI
import java.time.format.DateTimeParseException

@RestController
@RequestMapping(value = ["/ui/enquiries"])
@AccessControlledController
class FVMEnquiryManagementController(
    private val service: FVMEnquiryManagementService,
) {
    private val logger = LoggerFactory.getLogger(FVMEnquiryManagementController::class.java)

    @PostMapping("/vehicle-responsibility-details")
    fun getVehicleResponsibilityDetails(
        @RequestBody request: VehicleResponsibilityDetailsSearchRequest,
    ): ResponseEntity<APIResponse<List<VehicleResponsibilityDetails>>> =
        ResponseEntity.ok(APIResponse(service.getVehicleResponsibilityDetails(request.licencePlate, request.date)))

    @ExceptionHandler(DateTimeParseException::class)
    fun handleInvalidDate(ex: DateTimeParseException): ResponseEntity<APIResponse<Nothing?>> {
        logger.warn("Error parsing date", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.BAD_REQUEST,
                    "Invalid Date",
                ).apply {
                    type = URI.create(ErrorType.FVM_ENQUIRY_MANAGEMENT_INVALID_DATE.value)
                }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(VehicleResponsibilityDetailsFetchException::class)
    fun handleVehicleResponsibilityDetailsFetchException(
        ex: VehicleResponsibilityDetailsFetchException,
    ): ResponseEntity<APIResponse<Nothing?>> {
        logger.warn("Error fetching vehicle responsibility details", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "Error getting vehicle responsibility details",
                ).apply {
                    type = URI.create(ErrorType.FVM_ENQUIRY_MANAGEMENT_RESPONSIBILITY_DETAILS_FETCH_ERROR.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }
}
