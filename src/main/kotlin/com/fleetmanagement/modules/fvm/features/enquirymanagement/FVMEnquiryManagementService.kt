package com.fleetmanagement.modules.fvm.features.enquirymanagement

import com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup.ReadUsageGroupUseCase
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.fvm.dto.enquirymanagement.VehicleResponsibilityDetails
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import org.springframework.stereotype.Service
import java.time.OffsetDateTime

@Service
class FVMEnquiryManagementService(
    private val enquiryMatcherService: FVMLicensePlateUsageFinder,
    private val readUsageGroup: ReadUsageGroupUseCase,
    private val readVehiclePerson: ReadVehiclePersonDetailByEmployeeNumber,
) {
    fun getVehicleResponsibilityDetails(
        licencePlate: String,
        date: OffsetDateTime?,
    ): List<VehicleResponsibilityDetails> {
        val personCache = mutableMapOf<String, VehiclePersonDetail?>()
        val matches = enquiryMatcherService.getUsagesForLicensePlate(licencePlate, date)
        val usageGroups = readUsageGroup.readAllUsageGroups()

        return matches.map {
            val personDetails = getOrFetchPersonDetails(it.transfer.vehicleResponsiblePerson, personCache)
            return@map VehicleResponsibilityDetails.from(
                it.registrationPeriod,
                it.transfer,
                personDetails,
                usageGroups,
                it.licensePlateValidFrom,
                it.licensePlateValidUntil,
            )
        }
    }

    fun getOrFetchPersonDetails(
        employeeNumber: EmployeeNumber?,
        personCache: MutableMap<String, VehiclePersonDetail?>,
    ): VehiclePersonDetail? =
        employeeNumber?.value?.let { empNum ->
            personCache.getOrPut(empNum) {
                readVehiclePerson.readVehiclePersonDetailByEmployeeNumber(empNum)
            }
        }
}

class VehicleResponsibilityDetailsFetchException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()
