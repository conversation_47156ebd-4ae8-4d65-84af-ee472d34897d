package com.fleetmanagement.modules.fvm.features.enquirymanagement

import com.fleetmanagement.modules.fvm.dto.enquirymanagement.LicensePlateUsage
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationPeriod
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.OffsetDateTime

@Service
class FVMLicensePlateUsageFinder(
    private val readRegistrationOrder: ReadRegistrationOrder,
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase,
) {
    private val logger = LoggerFactory.getLogger(FVMLicensePlateUsageFinder::class.java)

    fun getUsagesForLicensePlate(
        licencePlate: String,
        atUsageDate: OffsetDateTime?,
    ): List<LicensePlateUsage> {
        try {
            val registrationPeriods =
                readRegistrationOrder.getRegistrationPeriodByLicencePlate(licencePlate, atUsageDate?.toString()).data

            val licensePlateUsages: List<LicensePlateUsage> =
                registrationPeriods.flatMap { reg: RegistrationPeriod ->
                    val vehicleId = reg.vehicleId ?: return@flatMap emptyList()
                    val transfers = readVehicleTransferUseCase.findVehicleTransfersByVehicleId(vehicleId)
                    transfers.mapNotNull { transfer ->

                        val lpUsageStart = getLaterDate(transfer.deliveryDate, reg.fromDate?.toOffsetDateTime())
                        val lpUsageEnd = getEarlierDate(transfer.returnDate, reg.toDate?.toOffsetDateTime())

                        return@mapNotNull when {
                            isValidOverlappingPeriod(lpUsageStart, lpUsageEnd) ->
                                LicensePlateUsage(
                                    transfer,
                                    reg,
                                    lpUsageStart,
                                    lpUsageEnd,
                                )
                            else -> null
                        }
                    }
                }

            return atUsageDate?.let {
                licensePlateUsages.filter {
                    isDateWithinPeriod(
                        it.licensePlateValidFrom,
                        it.licensePlateValidUntil,
                        atUsageDate,
                    )
                }
            } ?: licensePlateUsages
        } catch (ex: RuntimeException) {
            logger.error("Error while getting vehicle responsibility details", ex)
            throw VehicleResponsibilityDetailsFetchException(
                "Error while getting vehicle responsibility details",
                ex.cause,
            )
        }
    }

    private fun isValidOverlappingPeriod(
        lpStartDate: OffsetDateTime?,
        lpEndDate: OffsetDateTime?,
    ): Boolean =
        lpStartDate == null ||
            lpEndDate == null ||
            lpStartDate.toLocalDate().isBefore(lpEndDate.toLocalDate())

    private fun isDateWithinPeriod(
        lpFromDate: OffsetDateTime?,
        lpToDate: OffsetDateTime?,
        date: OffsetDateTime,
    ): Boolean {
        val localDate = date.toLocalDate()
        val fromDate = lpFromDate?.toLocalDate()
        val toDate = lpToDate?.toLocalDate()

        val isEqualOrAfterFrom = fromDate?.let { !localDate.isBefore(it) } ?: true
        val isEqualOrBeforeTo = toDate?.let { !localDate.isAfter(it) } ?: true

        return isEqualOrAfterFrom && isEqualOrBeforeTo
    }

    private fun getEarlierDate(
        firstDate: OffsetDateTime?,
        secondDate: OffsetDateTime?,
    ) = when {
        secondDate == null -> firstDate
        firstDate == null -> secondDate
        firstDate.toLocalDate().isBefore(secondDate.toLocalDate()) -> firstDate
        else -> secondDate
    }

    private fun getLaterDate(
        firstDate: OffsetDateTime?,
        secondDate: OffsetDateTime?,
    ) = when {
        secondDate == null -> firstDate
        firstDate == null -> secondDate
        secondDate.toLocalDate().isAfter(firstDate.toLocalDate()) -> secondDate
        else -> firstDate
    }
}
