### Overview

FVMEnquiryManagementService is a service that helps determine vehicle responsibility details for a given license plate and an optional reference date. 
It combines registration period data with vehicle transfer data to identify who was responsible for the vehicle during a specific time.

### Key Components
**Registration Period Lookup**

Uses ReadRegistrationOrder to fetch one or more RegistrationPeriod entries for a license plate. Each period defines when the plate was registered and (optionally) deregistered.

**Vehicle Transfer Matching**

For each registration, the corresponding vehicle transfers are fetched using ReadVehicleTransferUseCase. Transfer windows are then matched against the registration period to find overlapping usage periods.

**Responsibility Details Construction**

For each valid overlapping usage period, the responsible employee's details are fetched using ReadVehiclePersonDetailByEmployeeNumber and mapped to VehicleResponsibilityDetails.

**Caching**

A simple in-memory cache is used during a request to avoid fetching the same person details multiple times.

### Test Setup
Test setup covers various vehicle registration and transfer scenarios.

The purpose of these tests is to validate how the system handles:
1. Registrations and de-registrations
2. Relabeling and re-registration events
3. Lookup based on licensePlate-to-VIN-to-vehicle relationships
4. Vehicle transfer flows

The test data is visualized in the attached image and is organized per case, where each case highlights a specific business scenario.
Relevant registrations and vehicle transfers that are expected as output are highlighted in the same colors.

In the unit tests for the enquiry management services we have tried to cover all these relevant scenarios, registration scenarios are handle in vehicle registration service.

[Reference Story](https://skyway.porsche.com/jira/browse/FPT3-2270)

![test_cases.png](./docs/registration_period_test_cases.png)

