/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager

import com.fleetmanagement.integrations.aggrid.api.dto.InputLimitExceededException
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.ColumnLimitExceededException
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.ExportException
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.NoColumnsSelectedException
import com.fleetmanagement.modules.vehicledata.api.VehicleAlreadyExists
import com.fleetmanagement.modules.vehicledata.api.exceptions.PVHVehicleNotFoundException
import org.springframework.core.Ordered
import org.springframework.core.annotation.Order
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType.APPLICATION_PROBLEM_JSON
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import java.net.URI

@ControllerAdvice(basePackages = ["com.fleetmanagement.modules.fvm.features.manager"])
@Order(Ordered.HIGHEST_PRECEDENCE)
class FVMManagerExceptionHandler {
    @ExceptionHandler(VehicleAlreadyExists::class)
    fun handleVehicleAlreadyExistsException(ex: VehicleAlreadyExists): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                title = "Conflict"
                detail = ex.message
                setProperty("properties", ex.properties)
                type = URI.create(ErrorType.FVM_VEHICLE_ALREADY_EXISTS.value)
            }
        return ResponseEntity
            .status(HttpStatus.CONFLICT)
            .contentType(APPLICATION_PROBLEM_JSON)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(PVHVehicleNotFoundException::class)
    fun handleVehicleProcessingException(ex: PVHVehicleNotFoundException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatus(HttpStatus.INTERNAL_SERVER_ERROR).apply {
                title = "Vehicle not found on PVH"
                detail = ex.message
                type = URI.create(ErrorType.FVM_VEHICLE_PVH_NOT_FOUND.value)
            }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .contentType(APPLICATION_PROBLEM_JSON)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(InputLimitExceededException::class)
    fun handleInputLimitExceededException(ex: InputLimitExceededException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                title = "Bad Request"
                detail = ex.message
                setProperty("properties", mapOf("limit" to ex.limit))
                type = URI.create(ErrorType.FVM_INPUT_LIMIT_EXCEEDED.value)
            }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(APPLICATION_PROBLEM_JSON)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(ColumnLimitExceededException::class)
    fun handleColumnLimitExceededException(ex: ColumnLimitExceededException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                title = "Bad Request"
                detail = ex.message
                setProperty("limit", ex.limit)
                type = URI.create(ErrorType.FVM_EXPORT_COLUMN_LIMIT_EXCEEDED.value)
            }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(APPLICATION_PROBLEM_JSON)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(ExportException::class)
    fun handleExportException(ex: ExportException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                title = "Bad Request"
                detail = ex.message
                type = URI.create(ErrorType.FVM_EXPORT_FAILED.value)
            }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(APPLICATION_PROBLEM_JSON)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(NoColumnsSelectedException::class)
    fun handleNoColumnsSelectedException(ex: NoColumnsSelectedException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                title = "Bad Request"
                detail = ex.message
                type = URI.create(ErrorType.FVM_EXPORT_NO_COLUMNS_SELECTED.value)
            }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .contentType(APPLICATION_PROBLEM_JSON)
            .body(APIResponse.forErrors(problemDetail))
    }
}
