# Data Managers

## What is it?
The Fleet-Vehicle-Management application includes a feature that lets users view all system data at a glance and perform various data operations, such as sorting, filtering, and customizing column preferences.

There are two manager pages that are currently available in the system 
- **Vehicle Manager** [Staging UI](https://mobilityservicesdev.porsche.services/vehicle-management)
- **People Manager** [Staging UI](https://mobilityservicesdev.porsche.services/people)

## How is it designed?

### Technical Details 
| Tooling                                | Why is it used?                                                                             |
|----------------------------------------|---------------------------------------------------------------------------------------------|
| **AG-Grid**<br/>SSRM Model             | Used to display data in a tabular format on the browser                                     |
| **PostgreSQL views**                   | Used to combine data from various schemas                                                   |
| **Spring Data**<br/>JPA Specifications | Used to convert a request from AG-Grid for data to it's respective SQL query for PostgreSQL |


## Concept
- **We have a single PostgreSQL view that is a as-is copy of the FVM Data-Manager grid**
 
All data-management functionality like filtering, sorting and pagination are delegated to the database, and not handled through application code. 

The central concept that we like to follow is
 > what you see on the Data-Manager page is exactly what you can also see after a SELECT db-query to a combined PostgreSQL view to our database.

### Data-Manager PostgreSQL View Requirements

#### Single Identifier for SQL JOINs 
The top-level data-manager PostgreSQL view is designed with a single identifier. This identifier is the key used for all SQL JOIN operations within the system.  

#### Module-Level Public View Contribution
Each module must provide a module-level public view. This view should expose **only** the columns contributed by the module to the data-manager. 

Think of this as an module-level view as an "API" for the the top-level view that exposes the data required on the data-manager page.

### High Level Overview
![High Level Overview](./docs/view-overview.drawio.svg)

### Data Flow Diagram

Below is a sequence diagram that visualizes the data flow between the frontend and backend:

```mermaid
sequenceDiagram
    participant User
    participant AGGrid as AG-Grid (UI)
    participant API as /search endpoint (FVM Module)
    participant DB as PostgreSQL Database

    User->>AGGrid: Interacts (filter/sort/page)
    AGGrid->>API: Send request (with parameters)
    API->>API: Translate AG-Grid request to PostgreSQL query on data-manager view
    API->>DB: Execute SELECT query 
    Note over DB: Postgres view query execution
    DB->>API: Return query results
    API->>AGGrid: Send (formatted) data
    AGGrid->>AGGrid: Render data
    AGGrid->>User: Display updated grid
```

## Examples 

### Current Setup
There are two data-managers operational in Fleet-Vehicle-Management
![Overview](./docs/current-state-of-views.drawio.svg)

### Future Setup
A third data-manager is planned to be introduced Q1 2025. 

#### Vehicle-Transfer Manager 
TODO 

### Entity Relationship Diagram (ERD):

The ERD shows the relationships between all tables in the view, with the VEHICLE table at the center and all other tables connected to it.

```mermaid
erDiagram
    VEHICLE {
        int id PK
        string vin
        string vguid
        int vehicle_data_id
    }
    VEHICLE_DATA {
        int id PK
        string column_1
        string column_2
    }
    REGISTRATION_DATA {
        int id PK
        int vehicle_id FK
        string licence_plate
    }
    FAKE_DIMENSIONS {
        int id PK
        int vehicle_id FK
        string fake_column_1
        string fake_column_25
    }
    VEHICLE ||--o{ VEHICLE_DATA : has
    VEHICLE ||--o{ REGISTRATION_DATA : left_join
    VEHICLE ||--o{ FAKE_DIMENSIONS : left_join

```

This setup is available in the development environment, allowing you to perform and play around with it. You can use the development link provided above to access the Vehicle Manager and explore its features, test its performance with large datasets and experiment with different functionalities like filtering, sorting and pagination. This hands-on approach helps in understanding the system's behavior and ensures it meets the performance and usability expectations.

## View Management 

There are two kinds of PostgreSQL views in the system. 

1. **FVM-Level Views:** They reside in the `FVM` Module and are used to compose the `Vehicle-Manager` page using **Module-Level Views**

2. **Module-Level Views:** They reside in individual application modules and expose module specific columns that need to be a part of the top-level `Vehicle-Manager` view. All Module-Level views will contain a `vehicle-id` column that is used by the FVM-Level View in it's `JOIN` queries 


### FVM-Level View
- These views are re-created every time the application runs. 
- There is an empty-view implementation of every Module-Level view that the FVM-Level view depends on.

### Module-Level View  
When creating a Module-Level view we must always 'dereference' it from the FVM-Level view first. This is due to the way PostgreSQL views work. PostgreSQL will not allow dropping or altering a view, if it is referenced by another view (which in this case will be the FVM-Level view) 

Module-Level views are to be created through separate migration scripts - and they must not be re-run. Liquibase should be able to take care of this for us.

#### Adding / Updating a Module-Level View 

Module level views are created in specific migration SQL scripts. There are usually found in /resources/db/changelog/<module_name>

Add a new SQL file with the following content: 

```sql
-- Break dependency with FVM-Level View
ALTER VIEW IF EXISTS <module-level-view> RENAME TO <module-level-view>_deprecated;
                     
-- Recreate view with new columns 
CREATE OR REPLACE VIEW <module-level-view> AS 
  -- select from  
  -- internal schema tables
```

This SQL file then needs to be added to your module's Liquibase changeset 

```yaml
  - changeSet:
      id: set-<module_level_view>-for-vehicle-manager-<optional-revision>
      author: John Doe
      runAlways: false
      runOnChange: true
      changes:
        - sqlFile:
            relativeToChangelogFile: true
            path: '<module_name>/<sql_file_name>.sql'
            stripComments: true
```

This changeset will be executed whenever there is a change in the sql file. So in case of any modifications in the view, update the sql file. 
If already commented, uncomment the ALTER view query and changeset will be re-executed.

#### Cleaning Up Deprecated Views

Add a cleanup script to remove **deprecated** postgres views that:

- Searches for views with the naming pattern _deprecated.
- Attempts to drop these views. 
- Logs errors if any issues arise during the deletion.

```sql
DO
$$
    DECLARE
        r RECORD;
    BEGIN
        FOR r IN
            SELECT table_schema, table_name
            FROM information_schema.views
            WHERE table_schema = '<schema-name>'
              AND table_name LIKE '<module-level-view>_deprecated%'
        LOOP
            BEGIN
                EXECUTE 'DROP VIEW IF EXISTS ' || r.table_schema || '.' || r.table_name;
            EXCEPTION
                WHEN OTHERS THEN
                    RAISE NOTICE 'Error while dropping view: %, %', r.table_name, SQLERRM;
            END;
        END LOOP;
    END
$$;
```

Also add another changeset to execute the cleanup script.

```yaml
  - changeSet:
      id: drop-deprecated-<module_level_view>-if-exists-<optional-revision>
      failOnError: false
      runAlways: true
      author: John Doe
      changes:
        - sql:
            relativeToChangelogFile: true
            path: '<module_name>/<clean-up-deprecated-postgres-views>.sql'
            splitStatements: false
            endDelimiter: '$$;'
```

**Note:** Depending on the nature of the changes in the Module-Level view, the FVM-Level-View might need to be updated or else the deployment will fail.

Please do no change view configurations outside DB migration scripts in Liquibase. 

