/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.people

import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.integrations.aggrid.api.DataManagerService
import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.fvm.dto.PeopleJsonView
import com.fleetmanagement.modules.fvm.dto.PeopleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.peopledata.PeopleMapper
import com.fleetmanagement.modules.fvm.features.peoplemanager.entities.PDMViewPeopleRow
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMPeopleManagerController(
    private val service: DataManagerService,
) {
    @PostMapping("/people/search", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_PEOPLE_READ)")
    @JsonView(PeopleJsonView.DLZPage::class)
    fun search(
        @RequestBody agGridRequest: SearchRequest,
    ): ResponseEntity<APIResponse<List<PeopleUIRepresentation>>> {
        val (results, rowCount) = service.search(agGridRequest, PDMViewPeopleRow::class.java)

        val rows =
            results.map {
                PeopleUIRepresentation(
                    peopleData = PeopleMapper.INSTANCE.map(it),
                )
            }

        return ResponseEntity.ok(APIResponse(data = rows, rowCount = rowCount))
    }
}
