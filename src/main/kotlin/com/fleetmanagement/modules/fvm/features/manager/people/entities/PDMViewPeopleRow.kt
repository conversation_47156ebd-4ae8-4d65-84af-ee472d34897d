/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.peoplemanager.entities

import com.fleetmanagement.modules.fvm.features.peoplemanager.entities.embedded.PDMViewPeopleData
import jakarta.persistence.Column
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table

@Entity
@Table(name = "people_manager", schema = "fvm")
open class PDMViewPeopleRow(
    @Id
    @Column(name = "peopledata_employeenumber")
    open var id: String? = null,
    @Embedded
    open var peopleData: PDMViewPeopleData? = null,
)
