/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.peoplemanager.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import java.time.LocalDate

@Embeddable
open class PDMViewPeopleData(
    @Column(name = "peopledata_employeenumber", insertable = false, updatable = false)
    open var employeeNumber: String? = null,
    @Column(name = "peopledata_accountingarea")
    open var accountingArea: String? = null,
    @Column(name = "peopledata_firstname")
    open var firstName: String? = null,
    @Column(name = "peopledata_lastname")
    open var lastName: String? = null,
    @Column(name = "peopledata_companyemail")
    open var companyEmail: String? = null,
    @Column(name = "peopledata_privateemail")
    open var privateEmail: String? = null,
    @Column(name = "peopledata_street")
    open var street: String? = null,
    @Column(name = "peopledata_postalcode")
    open var postalCode: String? = null,
    @Column(name = "peopledata_city")
    open var city: String? = null,
    @Column(name = "peopledata_country")
    open var country: String? = null,
    @Column(name = "peopledata_internalcompanyphonenumber")
    open var internalCompanyPhoneNumber: String? = null,
    @Column(name = "peopledata_companymobilenumber")
    open var companyMobileNumber: String? = null,
    @Column(name = "peopledata_privatemobilenumber")
    open var privateMobileNumber: String? = null,
    @Column(name = "peopledata_department")
    open var department: String? = null,
    @Column(name = "peopledata_personalarea")
    open var personalArea: String? = null,
    @Column(name = "peopledata_costcenter")
    open var costCenter: String? = null,
    @Column(name = "peopledata_employeegroup")
    open var employeeGroup: String? = null,
    @Column(name = "peopledata_companycarauthorization")
    open var companyCarAuthorization: Boolean? = null,
    @Column(name = "peopledata_leasingauthorization")
    open var leasingAuthorization: Boolean? = null,
    @Column(name = "peopledata_secondaryleasingauthorization")
    open var secondaryLeasingAuthorization: Boolean? = null,
    @Column(name = "peopledata_loyaltyleasing")
    open var loyaltyLeasing: Boolean? = null,
    @Column(name = "peopledata_fuelcardauthorizationde")
    open var fuelCardAuthorizationDe: Boolean? = null,
    @Column(name = "peopledata_fuelcardauthorizationeu")
    open var fuelCardAuthorizationEu: Boolean? = null,
    @Column(name = "peopledata_chargingcardauthorizationde")
    open var chargingCardAuthorizationDe: Boolean? = null,
    @Column(name = "peopledata_chargingcardauthorizationeu")
    open var chargingCardAuthorizationEu: Boolean? = null,
    @Column(name = "peopledata_replacementvehicleauthorization")
    open var replacementVehicleAuthorization: Boolean? = null,
    @Column(name = "peopledata_permanentdrivingpermit")
    open var permanentDrivingPermit: Boolean? = null,
    @Column(name = "peopledata_accountingclerk")
    open var accountingClerk: String? = null,
    @Column(name = "peopledata_personnelclerk")
    open var personnelClerk: String? = null,
    @Column(name = "peopledata_exitdate")
    open var exitDate: LocalDate? = null,
    @Column(name = "peopledata_idcardnumber")
    open var idCardNumber: String? = null,
    @Column(name = "peopledata_approvalgroup")
    open var approvalGroup: String? = null,
    @Column(name = "peopledata_subarea")
    open var subarea: String? = null,
    @Column(name = "peopledata_subareatext")
    open var subareaText: String? = null,
    @Column(name = "peopledata_leasingprivilegeleasing")
    open var leasingPrivilegeLeasing: String? = null,
    @Column(name = "peopledata_leasingprivilegeloyaltyleasing")
    open var leasingPrivilegeLoyaltyLeasing: String? = null,
    @Column(name = "peopledata_leasingprivilegecompanycarleasing")
    open var leasingPrivilegeCompanyCarLeasing: String? = null,
    @Column(name = "peopledata_leasingprivilegesecondaryleasing")
    open var leasingPrivilegeSecondaryLeasing: String? = null,
    @Column(name = "peopledata_leasingprivilegesecondaryleasingspecialconditions")
    open var leasingPrivilegeSecondaryLeasingSpecialConditions: String?,
    @Column(name = "peopledata_leasingprivilegereplacementvehicle")
    open var leasingPrivilegeReplacementVehicle: String? = null,
    @Column(name = "peopledata_secondaryleasingspecialconditionsauthorization")
    open var secondaryLeasingSpecialConditionsAuthorization: Boolean? = null,
    @Column(name = "peopledata_stateofemployment")
    open var stateOfEmployment: String? = null,
)
