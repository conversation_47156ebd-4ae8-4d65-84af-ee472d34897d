package com.fleetmanagement.modules.fvm.features.manager.shared.export

import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.ExportService
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.GenericMapExportStrategy
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.dto.ExportRequest
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.springframework.http.HttpHeaders
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMExportController(
    private val exportService: ExportService,
    private val exportStrategy: GenericMapExportStrategy,
) {
    @PostMapping("/export")
    fun getDataAsCsv(
        @RequestHeader("Authorization") authorization: String?,
        @RequestBody exportRequest: ExportRequest<Map<String, Any?>>,
    ): ResponseEntity<StreamingResponseBody> {
        exportStrategy.validateRequest(exportRequest)
        val streamingResponseBody = exportService.export(exportRequest, exportStrategy)
        return ResponseEntity
            .ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"${exportStrategy.fileNameWithoutExtension}.csv\"",
            ).header(HttpHeaders.CONTENT_TYPE, "text/csv")
            .body(streamingResponseBody)
    }
}
