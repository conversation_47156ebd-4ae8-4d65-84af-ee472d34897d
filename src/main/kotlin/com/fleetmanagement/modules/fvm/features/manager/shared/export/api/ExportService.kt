/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.shared.export.api

import com.fleetmanagement.integrations.aggrid.api.dto.AgGridColumnState
import com.fleetmanagement.integrations.aggrid.api.dto.AgGridSortModel
import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.dto.ExportRequest
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.io.OutputStreamWriter
import java.math.BigDecimal
import java.math.RoundingMode
import java.nio.charset.StandardCharsets
import java.time.OffsetDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Optional
import kotlin.jvm.optionals.getOrDefault

@Service
class ExportService {
    @Suppress("ktlint:standard:property-naming")
    val UTF8_BOM_PREFIX = byteArrayOf(0xEF.toByte(), 0xBB.toByte(), 0xBF.toByte())

    private val logger = LoggerFactory.getLogger(ExportService::class.java)

    fun <T : Any> export(
        exportRequest: ExportRequest<T>,
        exportStrategy: ExportStrategy<T>,
    ): StreamingResponseBody =
        StreamingResponseBody { outputStream ->
            try {
                outputStream.write(UTF8_BOM_PREFIX)
                val writer = OutputStreamWriter(outputStream, StandardCharsets.UTF_8)
                if (exportRequest.selectedRows.isEmpty()) {
                    exportAll(exportRequest, exportStrategy, writer)
                    writer.flush()
                } else {
                    val rows = exportSelectedRows(exportRequest)
                    writer.write(rows.joinToString("\n"))
                    writer.flush()
                }
            } catch (e: RuntimeException) {
                logger.error("Error occurred while streaming the export: ${e.message}", e)
                throw ExportException(e.message ?: "Unknown error occurred during export", e)
            } finally {
                logger.info("Closing the output stream for export")
                outputStream.close()
                logger.info("Output stream for export is closed")
            }
        }

    fun <T : Any> exportAll(
        exportRequest: ExportRequest<T>,
        exportStrategy: ExportStrategy<T>,
        writer: OutputStreamWriter,
    ) {
        val pageSize = exportStrategy.pageSize

        val separator = getSeparator(exportRequest.language)
        val header = generateCsvHeaders(exportRequest.columnState, separator)

        writer.write(header)

        var pageNumber = 0
        while (true) {
            val searchRequest =
                SearchRequest(
                    pageNumber * pageSize,
                    (pageNumber + 1) * pageSize,
                    exportRequest.sortModel,
                    exportRequest.filterModel,
                )

            val rowsFromDb = exportStrategy.getDataInChunks(searchRequest)

            if (rowsFromDb.isEmpty()) break

            val updatedExportRequest = exportRequest.copy(selectedRows = rowsFromDb)
            val rows = generateCsvRows(separator, updatedExportRequest)

            writer.appendLine()
            writer.write(rows.joinToString("\n"))
            writer.flush()
            pageNumber++
        }
    }

    fun <T : Any> exportSelectedRows(exportRequest: ExportRequest<T>): List<String> {
        val separator = getSeparator(exportRequest.language)
        val header = generateCsvHeaders(exportRequest.columnState, separator)
        val rows = generateCsvRows(separator, exportRequest)
        return listOf(header) + rows
    }

    private fun generateCsvHeaders(
        columnState: List<AgGridColumnState>,
        separator: String,
    ): String {
        val columnsToShow = columnState.filterNot { it.hide ?: true }.map { it }
        val translatedHeaders = columnsToShow.map { escapeForCsv(it.translation ?: it.colId, separator) }
        return translatedHeaders.joinToString(separator)
    }

    private fun <T : Any> generateCsvRows(
        separator: String,
        exportRequest: ExportRequest<T>,
    ): List<String> {
        val columnState = exportRequest.columnState

        if (columnState.isEmpty() || exportRequest.selectedRows.isEmpty()) return emptyList()

        val sortedRows = sortRows(exportRequest.sortModel, exportRequest.selectedRows)

        val columnsToShow = columnState.filterNot { it.hide ?: true }.map { it }

        val rows =
            sortedRows.map { row ->
                columnsToShow.joinToString(separator) { column ->
                    val rowValueForColumn = getNestedPropertyValue(row, column.colId)
                    formatAndTranslateRowValue(rowValueForColumn, exportRequest.booleanTranslations, column, separator)
                }
            }

        return rows
    }

    fun Float.hasDecimalPart(): Boolean = this % 1 != 0f

    fun BigDecimal.toLocalizedString(separator: String): String {
        val roundedValue = this.setScale(2, RoundingMode.HALF_EVEN)

        return if (separator == ";") {
            roundedValue.toPlainString().replace(".", ",")
        } else {
            roundedValue.toPlainString()
        }
    }

    fun Float.toLocalizedString(separator: String): String =
        if (this.hasDecimalPart()) {
            if (separator == ";") {
                this.toString().replace(".", ",")
            } else {
                this.toString()
            }
        } else {
            this.toInt().toString()
        }

    private fun formatAndTranslateRowValue(
        rowValueForColumn: Any?,
        booleanTranslations: Map<Boolean, String>,
        column: AgGridColumnState,
        separator: String,
    ) = when (rowValueForColumn) {
        null -> ""
        is Boolean -> booleanTranslations[rowValueForColumn].toString()
        is ZonedDateTime -> rowValueForColumn.toDayMonthYearTimeString()
        is OffsetDateTime -> rowValueForColumn.toDayMonthYearTimeString()
        is Float -> rowValueForColumn.toLocalizedString(separator)
        is BigDecimal -> rowValueForColumn.toLocalizedString(separator)
        else -> handleValueAsString(rowValueForColumn.toString(), column.enumValues, separator)
    }

    private fun handleValueAsString(
        value: Any,
        enumValues: Map<String, String>?,
        separator: String,
    ): String {
        val enumValue = enumValues?.get(value)?.toString() ?: value.toString()
        return escapeForCsv(enumValue, separator)
    }

    private fun <T : Any> sortRows(
        sortModels: List<AgGridSortModel>?,
        rows: List<T>?,
    ): List<T> {
        if (rows.isNullOrEmpty() || sortModels.isNullOrEmpty()) return rows ?: emptyList()

        require(sortModels.size <= 1) { "Multiple sorts are currently not supported" }

        val sortModel = sortModels.first()
        val propertyPath = sortModel.colId
        val ascending = sortModel.sort.lowercase() == "asc"

        return rows
            .sortedWith(
                compareBy { row ->
                    getNestedPropertyValue(row, propertyPath) as? Comparable<*>
                },
            ).let { if (ascending) it else it.reversed() }
    }

    private fun escapeForCsv(
        input: String,
        separator: String,
    ): String =
        if (input.contains("\"") || input.contains(separator)) {
            "\"${input.replace("\"", "\"\"")}\""
        } else {
            input
        }

    private fun <T : Any> getNestedPropertyValue(
        instance: T,
        propertyPath: String,
    ): Any? {
        if (instance is Map<*, *>) {
            return getMapValue(instance, propertyPath)
        }
        var currentInstance: Any = instance

        for (property in propertyPath.split(".")) {
            currentInstance = currentInstance.let { obj ->
                obj::class.members.find { it.name == property }?.call(obj)
            } ?: return null

            if (currentInstance is Optional<*>) {
                currentInstance = currentInstance.getOrDefault("")
            }
        }

        return currentInstance
    }

    private fun getMapValue(
        map: Map<*, *>,
        propertyPath: String,
    ): Any? {
        var currentMap: Any? = map
        for (property in propertyPath.split(".")) {
            if (currentMap is Map<*, *>) {
                currentMap = currentMap[property]
            } else {
                return null
            }
        }
        return currentMap
    }

    private fun getSeparator(language: String) = if (language.lowercase() == "en") "," else ";"
}

class ColumnLimitExceededException(
    message: String,
    val limit: Int,
) : RuntimeException(message)

class NoColumnsSelectedException(
    message: String,
) : RuntimeException(message)

class ExportException(
    message: String?,
    cause: Throwable? = null,
) : RuntimeException(message)

private fun ZonedDateTime.toDayMonthYearTimeString(): String = this.format(DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm"))

private fun OffsetDateTime.toDayMonthYearTimeString(): String = this.format(DateTimeFormatter.ofPattern("dd-MM-yyyy HH:mm"))
