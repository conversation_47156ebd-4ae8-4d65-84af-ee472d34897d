package com.fleetmanagement.modules.fvm.features.manager.shared.export.api

import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.dto.ExportRequest
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.Locale

interface ExportStrategy<T : Any> {
    val columnLimit: Int
        get() = 100

    val pageSize: Int
        get() = 10000

    val fileNameWithoutExtension: String
        get() {
            val timestamp =
                ZonedDateTime.now(ZoneId.of("Europe/Berlin")).format(
                    DateTimeFormatter.ofPattern("yyyy-MM-dd_HHmmss_z", Locale.GERMAN),
                )
            return "${timestamp}_confidential"
        }

    fun getDataInChunks(searchRequest: SearchRequest): List<T>

    fun validateRequest(exportRequest: ExportRequest<T>) {
        val columnsToShow = exportRequest.columnState.filterNot { it.hide ?: true }.map { it }
        require(columnsToShow.isNotEmpty()) { throw NoColumnsSelectedException("No columns are selected to export") }
        if (columnsToShow.size > columnLimit) {
            throw ColumnLimitExceededException(
                "Too many columns selected for export. The maximum allowed is $columnLimit, but ${columnsToShow.size} were selected.",
                columnLimit,
            )
        }
    }
}

@Service
class GenericMapExportStrategy : ExportStrategy<Map<String, Any?>> {
    private val logger = LoggerFactory.getLogger(GenericMapExportStrategy::class.java)

    override fun getDataInChunks(searchRequest: SearchRequest): List<Map<String, Any?>> {
        logger.info("GenericMapExportStrategy returning empty rows")
        return emptyList()
    }
}
