/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.shared.export.api.dto

import com.fleetmanagement.integrations.aggrid.api.dto.AgGridColumnState
import com.fleetmanagement.integrations.aggrid.api.dto.AgGridFilterModel
import com.fleetmanagement.integrations.aggrid.api.dto.AgGridSortModel
import java.lang.Boolean.FALSE
import java.lang.Boolean.TRUE

data class ExportRequest<T>(
    val columnState: List<AgGridColumnState> = emptyList(),
    val filterModel: Map<String, AgGridFilterModel> = emptyMap(),
    val sortModel: List<AgGridSortModel> = emptyList(),
    val language: String,
    val selectedRows: List<T> = emptyList(),
    val booleanTranslations: Map<Boolean, String> = mapOf(TRUE to "Ja", FALSE to "Nein"),
)
