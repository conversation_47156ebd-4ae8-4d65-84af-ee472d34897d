package com.fleetmanagement.modules.fvm.features.manager.vehicle

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.FVMCampaign
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException.CampaignInformationNoSyncedException
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI
import java.util.UUID

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMVehicleCampaignsController(
    private val fvmVehicleCampaignsService: FVMVehicleCampaignsService,
) {
    @GetMapping("/vehicles/{id}/campaigns")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    fun getAllCampaignsForVehicleById(
        @PathVariable("id") @Valid vehicleId: UUID,
    ): ResponseEntity<APIResponse<List<FVMCampaign>>> {
        val campaigns = fvmVehicleCampaignsService.getCampaignsForVehicle(vehicleId)
        return ResponseEntity.ok(APIResponse(campaigns))
    }

    @ExceptionHandler
    fun handleCampaignInformationNotFound(exception: CampaignInformationNoSyncedException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(
                APIResponse.forErrors(
                    ProblemDetail
                        .forStatus(HttpStatus.NOT_FOUND)
                        .also {
                            it.type = URI.create("fvm.error.noCampaigns")
                            it.setProperty("vehicleId", exception.vehicleId)
                        },
                ),
            )
}
