package com.fleetmanagement.modules.fvm.features.manager.vehicle

import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.FVMCampaign
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.toDTO
import com.fleetmanagement.modules.vehiclecampaigns.api.CampaignsForVehicleByVehicleId
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class FVMVehicleCampaignsService(
    private val campaignsForVehicleAPI: CampaignsForVehicleByVehicleId,
) {
    fun getCampaignsForVehicle(vehicleId: UUID): List<FVMCampaign> {
        val campaigns = campaignsForVehicleAPI.findCampaignsForVehicle(vehicleId)
        return campaigns.map {
            it.toDTO()
        }
    }
}
