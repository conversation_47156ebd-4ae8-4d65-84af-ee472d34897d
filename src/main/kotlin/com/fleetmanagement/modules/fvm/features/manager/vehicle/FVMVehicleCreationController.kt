/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle

import com.fleetmanagement.modules.fvm.dto.VehicleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleDataMapper
import com.fleetmanagement.modules.vehicledata.api.CreateOrUpdateVehicle
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateUIVehicleDTO
import com.fleetmanagement.modules.vehicleregistration.api.WriteRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateVehicleRegistration
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferDto
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferUseCase
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBUser
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.http.converter.HttpMessageNotReadableException
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.MethodArgumentNotValidException
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI
import java.util.UUID

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMVehicleCreationController(
    private val creationService: CreateOrUpdateVehicle,
    private val writeRegistrationOrder: WriteRegistrationOrder,
    private val createPlannedVehicleTransferUseCase: CreatePlannedVehicleTransferUseCase,
) {
    private val logger = LoggerFactory.getLogger(FVMVehicleCreationController::class.java)

    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_WRITE)")
    @PostMapping("/vehicles")
    fun create(
        @Valid @RequestBody createVehicleRequest: CreateUIVehicleDTO,
    ): ResponseEntity<APIResponse<VehicleUIRepresentation>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser

        val vehicle =
            creationService.createOrUpdateVehicle(
                createVehicleRequest,
                currentUser.fullNameWithDepartment,
            )

        val registrationSucceeded = tryCreateRegistrationOrder(createVehicleRequest)
        val transferSucceeded = tryCreateVehicleTransfer(vehicle.id, createVehicleRequest)

        val errors = collectErrorsIfAny(registrationSucceeded, transferSucceeded)

        // Why 409? Primary resource (vehicle) was created, but some related operations failed,
        // creating an inconsistent or conflicting state that needs manual resolution.
        val status = if (errors.isNullOrEmpty()) HttpStatus.CREATED else HttpStatus.CONFLICT

        return ResponseEntity.status(status).body(
            APIResponse(
                data = VehicleUIRepresentation(vehicle = FVMVehicleDataMapper.INSTANCE.map(vehicle)),
                errors = errors,
            ),
        )
    }

    private fun tryCreateRegistrationOrder(request: CreateUIVehicleDTO): Boolean =
        runCatching {
            request.licencePlate?.let { licencePlate ->
                val response =
                    writeRegistrationOrder.createRegistrationOrders(
                        listOf(
                            CreateVehicleRegistration(
                                vin = request.vin,
                                licencePlate = licencePlate,
                                registrationType = 1,
                                registrationDate = request.registrationDate,
                            ),
                        ),
                    )
                response.errors.isNullOrEmpty()
            } ?: true
        }.onFailure {
            logger.error("Failed to create registration order for VIN ${request.vin}", it)
        }.getOrDefault(false)

    private fun tryCreateVehicleTransfer(
        vehicleId: UUID,
        request: CreateUIVehicleDTO,
    ): Boolean =
        runCatching {
            createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(
                CreatePlannedVehicleTransferDto(
                    vehicleId = vehicleId,
                    vehicleUsageId = request.vehicleUsageId,
                    vehicleResponsiblePerson = request.vehicleResponsiblePerson,
                    internalContactPerson = request.internalContactPerson,
                    internalOrderNumber = request.internalOrderNumber,
                    licensePlate = request.licencePlate,
                    usingCostCenter = request.usingCostCenter,
                ),
            )
            true
        }.onFailure {
            logger.error("Failed to create vehicle transfer for VIN ${request.vin}", it)
        }.getOrDefault(false)

    private fun collectErrorsIfAny(
        registrationSucceeded: Boolean,
        transferSucceeded: Boolean,
    ): List<ProblemDetail>? {
        if (registrationSucceeded && transferSucceeded) return null

        val failedProcesses =
            buildList {
                if (!registrationSucceeded) add("vehicle registration")
                if (!transferSucceeded) add("vehicle transfer")
            }

        return listOf(
            ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                type = URI.create(ErrorType.FVM_VEHICLE_CREATION_PARTIAL_SUCCESS.value)
                title = "Manual Action Required"
                detail = "Vehicle created successfully. Please create ${failedProcesses.joinToString(", ")} manually."
                setProperty("failedProcesses", failedProcesses)
            },
        )
    }

    @ExceptionHandler(MethodArgumentNotValidException::class)
    fun handleMethodArgumentNotValidException(ex: MethodArgumentNotValidException): ResponseEntity<APIResponse<Nothing?>> {
        val errors =
            ex.fieldErrors.map {
                ProblemDetail.forStatus(HttpStatus.UNPROCESSABLE_ENTITY).apply {
                    type = it.defaultMessage?.let { msg -> URI.create(msg) } ?: URI.create("error.validation")
                    setProperty("field", it.field)
                }
            }
        return ResponseEntity
            .status(HttpStatus.UNPROCESSABLE_ENTITY)
            .body(APIResponse.forErrors(*errors.toTypedArray()))
    }

    @ExceptionHandler(HttpMessageNotReadableException::class)
    fun handleHttpMessageNotReadableException(ex: HttpMessageNotReadableException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatus(HttpStatus.UNPROCESSABLE_ENTITY).apply {
                type = URI.create("error.validation.structure")
            }
        return ResponseEntity
            .status(HttpStatus.UNPROCESSABLE_ENTITY)
            .body(APIResponse.forErrors(problemDetail))
    }
}
