/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle

import com.fleetmanagement.modules.fvm.dto.VehicleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleDataMapper
import com.fleetmanagement.modules.vehicledata.api.VehicleImportInterface
import com.fleetmanagement.modules.vehicledata.api.dtos.ImportVehicleDto
import com.fleetmanagement.modules.vehicledata.api.dtos.InitialLoadVehicleDto
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBUser
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMVehicleImportController(
    private val importService: VehicleImportInterface,
) {
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_WRITE)")
    @PostMapping("/vehicles/import")
    fun import(
        @Valid @RequestBody importVehicleDto: ImportVehicleDto,
    ): ResponseEntity<APIResponse<VehicleUIRepresentation>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser
        val vehicle = importService.importVehicle(importVehicleDto, currentUser.fullNameWithDepartment)

        return ResponseEntity.status(HttpStatus.CREATED).body(
            APIResponse(
                VehicleUIRepresentation(
                    vehicle = FVMVehicleDataMapper.INSTANCE.map(vehicle),
                ),
            ),
        )
    }

    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_WRITE)")
    @PostMapping("/vehicles/initial-load")
    fun initialLoad(
        @Valid @RequestBody initialLoadVehicleDto: InitialLoadVehicleDto,
    ): ResponseEntity<APIResponse<VehicleUIRepresentation>> {
        val vehicle = importService.initialLoadVehicle(initialLoadVehicleDto.vin)
        return ResponseEntity.status(HttpStatus.CREATED).body(
            APIResponse(
                VehicleUIRepresentation(
                    vehicle = FVMVehicleDataMapper.INSTANCE.map(vehicle),
                ),
            ),
        )
    }
}
