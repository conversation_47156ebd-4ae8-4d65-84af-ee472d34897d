/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle

import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.integrations.aggrid.api.DataManagerService
import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleOptionTag
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleRowUpdate
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.ExportService
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.dto.ExportRequest
import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.DLZViewVehicleRow
import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.toVehicleUIRepresentationList
import com.fleetmanagement.modules.fvm.features.manager.vehicle.repository.DLZViewVehicleRowDAO
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.ValidationError
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.toProblemDetail
import com.fleetmanagement.modules.fvm.features.toProblemDetailType
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleOptionTags
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBUser
import jakarta.validation.Valid
import org.springframework.http.*
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.*
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.net.URI

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMVehicleManagerController(
    private val dataManagerService: DataManagerService,
    private val exportService: ExportService,
    private val exportStrategy: VehicleManagerExportStrategy,
    private val vehicleDLZUpdateService: VehicleDLZUpdateService,
    private val dLZViewVehicleRowDAO: DLZViewVehicleRowDAO,
    private val vehicleOptionTags: ReadVehicleOptionTags,
) {
    @PostMapping("/vehicles/export")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    @JsonView(VehicleJsonView.DLZPage::class)
    fun getVehiclesAsCsv(
        @RequestHeader("Authorization") authorization: String?,
        @RequestBody exportRequest: ExportRequest<VehicleUIRepresentation>,
    ): ResponseEntity<StreamingResponseBody> {
        exportStrategy.validateRequest(exportRequest)
        val streamingResponseBody = exportService.export(exportRequest, exportStrategy)
        return ResponseEntity
            .ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"${exportStrategy.fileNameWithoutExtension}.csv\"",
            ).header(HttpHeaders.CONTENT_TYPE, "text/csv")
            .body(streamingResponseBody)
    }

    @PostMapping("/vehicles/search", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    @JsonView(VehicleJsonView.DLZPage::class)
    fun search(
        @RequestBody searchRequest: SearchRequest,
    ): ResponseEntity<APIResponse<List<VehicleUIRepresentation>>> {
        val (results, rowCount) = dataManagerService.search(searchRequest, DLZViewVehicleRow::class.java)
        val rows = results.toVehicleUIRepresentationList()
        return ResponseEntity.ok(APIResponse(rows, rowCount = rowCount))
    }

    @PutMapping("/vehicles/update", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_WRITE)")
    fun updateVehicles(
        @RequestBody @Valid updateVehicleDLZRows: List<FVMVehicleRowUpdate>,
    ): ResponseEntity<APIResponse<List<VehicleUIRepresentation>>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser
        val (warnings, errors) =
            updateVehicleDLZRows.fold(
                Pair(emptyList<ProblemDetail>(), emptyList<ProblemDetail>()),
            ) { (warnings, errors), row ->
                try {
                    val warningList =
                        vehicleDLZUpdateService
                            .updateDLZRow(row, currentUser.fullNameWithDepartment)
                            .map(ValidationError::toProblemDetail)
                    Pair(warnings + warningList, errors)
                } catch (exception: VehicleDLZRowUpdateException) {
                    Pair(warnings, errors + exception.toProblemDetail())
                }
            }

        val updatedRows = dLZViewVehicleRowDAO.findAllByIdIn(updateVehicleDLZRows.mapNotNull { it.vehicle?.id })
        val resultData = updatedRows.toVehicleUIRepresentationList()
        return if (warnings.isEmpty() && errors.isEmpty()) {
            ResponseEntity.ok(
                APIResponse(
                    data = resultData,
                    rowCount = resultData.size.toLong(),
                ),
            )
        } else {
            ResponseEntity.status(HttpStatus.CONFLICT).body(
                APIResponse(
                    data = resultData,
                    rowCount = resultData.size.toLong(),
                    errors = errors,
                    warnings = warnings,
                ),
            )
        }
    }

    @GetMapping("/vehicles/options/tags")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    fun getAllVehicleOptionTags(): ResponseEntity<APIResponse<List<FVMVehicleOptionTag>>> {
        val tags = vehicleOptionTags.readAll().map { FVMVehicleOptionTag.from(it) }
        return ResponseEntity.ok(APIResponse(tags))
    }
}

fun VehicleDLZRowUpdateException.toProblemDetail(): ProblemDetail {
    val titleMessage = "Error while updating vehicle through DLZ"
    val message = this.message
    val propertyName = this.constraintViolation?.propertyName
    val constraintViolation =
        this.constraintViolation
            ?: return ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                title = titleMessage
                type = URI.create(ErrorType.FVM_VEHICLE_UPDATE_FAILED.value)
                detail = message
            }

    return ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
        title = titleMessage
        detail = message
        type = constraintViolation.type.toProblemDetailType()
        propertyName?.let { setProperty("properties", mapOf("propertyName" to it)) }
    }
}
