/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicle

import com.fleetmanagement.modules.fvm.dto.noncustomeradequate.FVMNonCustomerAdequateUpdate
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspectionUpdate
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleRowUpdate
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleUpdate
import com.fleetmanagement.modules.fvm.dto.vehiclesales.FVMVehicleSalesUpdate
import com.fleetmanagement.modules.fvm.dto.vehiclesales.toVehicleSalesUpdateDto
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.ValidationError
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.toPreDeliveryInspectionUpdateDto
import com.fleetmanagement.modules.noncustomeradequate.api.NonCustomerAdequateDataUpdate
import com.fleetmanagement.modules.noncustomeradequate.api.UpsertNonCustomerAdequateData
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.NonCustomerAdequateVehicleFutureDateException
import com.fleetmanagement.modules.predeliveryinspection.application.UnknownTireSetException
import com.fleetmanagement.modules.predeliveryinspection.application.port.PreDeliveryInspectionId
import com.fleetmanagement.modules.predeliveryinspection.application.port.UpdatePreDeliveryInspectionUseCase
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionFutureDateException
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionNotFoundException
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicle
import com.fleetmanagement.modules.vehicledata.api.VehicleUpdateDto
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateConstraintViolation
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import com.fleetmanagement.modules.vehiclesales.api.UpsertVehicleSale
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBUser
import org.slf4j.LoggerFactory
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Component
class VehicleDLZUpdateService(
    private val updateVehicleService: UpdateVehicle,
    private val updatePreDeliveryInspectionUseCase: UpdatePreDeliveryInspectionUseCase,
    private val upsertNonCustomerAdequateData: UpsertNonCustomerAdequateData,
    private val upsertVehicleSale: UpsertVehicleSale,
) {
    private val logger = LoggerFactory.getLogger(VehicleDLZUpdateService::class.java)

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun updateDLZRow(
        rowUpdate: FVMVehicleRowUpdate,
        currentUser: String,
    ): List<ValidationError> {
        // update vehicle
        updateVehicle(rowUpdate.vehicle)

        // update pre-delivery inspection
        val preDeliveryInspectionValidationErrors =
            rowUpdate.preDeliveryInspection?.let {
                updatePreDeliveryInspection(it)
            } ?: emptyList()

        // update nonCustomerAdequate data
        try {
            rowUpdate.nonCustomerAdequate?.let {
                updateNonCustomerAdequateData(rowUpdate.vehicle.id, it)
            }
        } catch (exception: NonCustomerAdequateVehicleFutureDateException) {
            throw VehicleDLZRowUpdateException(message = exception.message, cause = exception.cause)
        }

        // update vehicle sales
        rowUpdate.vehicleSales?.let {
            updateVehicleSales(rowUpdate.vehicle.id, it, currentUser)
        }

        return preDeliveryInspectionValidationErrors
    }

    private fun updateVehicleSales(
        vehicleId: UUID,
        vehicleSales: FVMVehicleSalesUpdate,
        currentUser: String,
    ) {
        try {
            val vehicleSaleUpdateDto = vehicleSales.toVehicleSalesUpdateDto(vehicleId)
            upsertVehicleSale.upsert(vehicleSaleUpdateDto, currentUser)
        } catch (exception: RuntimeException) {
            logger.error("Error updating vehicle sales data", exception)
            throw VehicleDLZRowUpdateException(
                message = "Error updating vehicle sales data",
                cause = exception.cause,
            )
        }
    }

    private fun updateNonCustomerAdequateData(
        vehicleId: UUID,
        it: FVMNonCustomerAdequateUpdate,
    ) {
        upsertNonCustomerAdequateData.upsertNonCustomerAdequate(it.toNonCustomerAdequateDataUpdateDto(vehicleId))
    }

    private fun updateVehicle(update: FVMVehicleUpdate) {
        val modifier = (SecurityContextHolder.getContext().authentication?.principal as? ALBUser)?.fullNameWithDepartment ?: "Unknown"

        try {
            updateVehicleService.updateVehicle(
                vehicleId = update.id,
                vehicleUpdateDto = update.toVehicleUpdateDto(),
                modifier = modifier,
            )
        } catch (exception: VehicleNotFoundException) {
            throw VehicleDLZRowUpdateException(message = exception.message, cause = exception.cause)
        } catch (exception: VehicleUpdateException) {
            throw VehicleDLZRowUpdateException(
                message = exception.message,
                cause = exception.cause,
                constraintViolation = exception.constraintViolation,
            )
        }
    }

    private fun updatePreDeliveryInspection(update: FVMPreDeliveryInspectionUpdate): List<ValidationError> =
        try {
            updatePreDeliveryInspectionUseCase.updatePreDeliveryInspection(
                id = PreDeliveryInspectionId(update.id),
                preDeliveryInspectionUpdate = update.toPreDeliveryInspectionUpdateDto(),
            )
        } catch (exception: UnknownTireSetException) {
            throw VehicleDLZRowUpdateException(message = exception.message, cause = exception.cause)
        } catch (exception: PreDeliveryInspectionNotFoundException) {
            throw VehicleDLZRowUpdateException(message = exception.message, cause = exception.cause)
        } catch (exception: PreDeliveryInspectionFutureDateException) {
            throw VehicleDLZRowUpdateException(message = exception.message, cause = exception.cause)
        }
}

private fun FVMNonCustomerAdequateUpdate.toNonCustomerAdequateDataUpdateDto(vehicleId: UUID) =
    NonCustomerAdequateDataUpdate(
        vehicleId = vehicleId,
        profitabilityAuditDone = this.profitabilityAuditDone,
        rebuildStarted = this.rebuildStarted,
        rebuildDone = this.rebuildDone,
        comment = this.comment,
        plannedRebuildCost = this.plannedRebuildCost,
        actualRebuildCost = this.actualRebuildCost,
        expectedRevenue = this.expectedRevenue,
        salesPrice = this.salesPrice,
        ncaStatus = this.ncaStatus,
    )

data class VehicleDLZRowUpdateException(
    override val message: String?,
    override val cause: Throwable? = null,
    val constraintViolation: VehicleUpdateConstraintViolation? = null,
) : RuntimeException()

fun FVMVehicleUpdate.toVehicleUpdateDto() =
    VehicleUpdateDto(
        referenceId = this.referenceId,
        purchaseOrderDate = this.order?.purchaseOrderDate,
        requestedDeliveryDate = this.order?.requestedDeliveryDate,
        deliveryType = this.order?.deliveryType,
        primaryStatus = this.order?.primaryStatus,
        preproductionVehicle = this.order?.preproductionVehicle,
        blockedForSale = this.order?.blockedForSale,
        scrapVehicle = this.fleet?.scrapVehicle,
        primaryFuelType = this.consumption?.primaryFuelType?.map { FuelType.valueOf(it) },
        secondaryFuelType = this.consumption?.secondaryFuelType?.map { FuelType.valueOf(it) },
        currentTires = this.currentTires?.map { TireSet.valueOf(it) },
        externalLeaseStart = this.externalLeaseStart,
        externalLeaseEnd = this.externalLeaseEnd,
        externalLeaseLessee = this.externalLeaseLessee,
        externalLeaseRate = this.externalLeaseRate,
        amountSeats = this.technical?.amountSeats,
        engineCapacity = this.technical?.engineCapacity,
        netPriceWithExtras = this.price?.netPriceWithExtras,
        soldDate = this.fleet?.soldDate,
        scrappedDate = this.fleet?.scrappedDate,
        stolenDate = this.fleet?.stolenDate,
        soldCupCarDate = this.fleet?.soldCupCarDate,
        approvedForScrappingDate = this.fleet?.approvedForScrappingDate,
        scrappedVehicleOfferedDate = this.fleet?.scrappedVehicleOfferedDate,
        vehicleSentToSalesDate = this.fleet?.vehicleSentToSalesDate,
        costEstimationOrderedDate = this.fleet?.costEstimationOrderedDate,
        isResidualValueMarket = this.fleet?.isResidualValueMarket,
        profitabilityAuditDate = this.fleet?.profitabilityAuditDate,
        comment = this.fleet?.comment,
        preparationDoneDate = this.delivery?.preparationDoneDate,
        isPreparationNecessary = this.delivery?.isPreparationNecessary,
        nextProcess = this.returnInfo?.nextProcess,
        keyReturned = this.returnInfo?.keyReturned,
        tireSetChangeOrderedDate = this.tireSetChange?.orderedDate,
        tireSetChangeCompletedDate = this.tireSetChange?.completedDate,
        tireSetChangeComment = this.tireSetChange?.comment,
        appraisalNetPrice = this.evaluation?.appraisalNetPrice,
        pcComplaintCheckComment = this.evaluation?.pcComplaintCheckComment,
        vehicleEvaluationComment = this.evaluation?.vehicleEvaluationComment,
        currentMileage = this.currentMileage?.mileage,
        currentMileageReadDate = this.currentMileage?.readDate,
        tuevAppointment = this.tuevAppointment,
        raceCar = this.fleet?.raceCar,
        classic = this.fleet?.classic,
    )
