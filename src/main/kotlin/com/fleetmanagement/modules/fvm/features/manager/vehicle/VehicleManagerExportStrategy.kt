package com.fleetmanagement.modules.fvm.features.manager.vehicle

import com.fleetmanagement.integrations.aggrid.api.DataManagerService
import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.fvm.dto.VehicleUIRepresentation
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.ExportStrategy
import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.DLZViewVehicleRow
import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.toVehicleUIRepresentationList
import org.springframework.stereotype.Service

@Service
class VehicleManagerExportStrategy(
    private val dataManagerService: DataManagerService,
) : ExportStrategy<VehicleUIRepresentation> {
    override fun getDataInChunks(searchRequest: SearchRequest): List<VehicleUIRepresentation> {
        val (results, _) = dataManagerService.search(searchRequest, DLZViewVehicleRow::class.java)
        return results.toVehicleUIRepresentationList()
    }
}
