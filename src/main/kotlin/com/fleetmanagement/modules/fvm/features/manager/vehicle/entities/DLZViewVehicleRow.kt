/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle.entities

import com.fleetmanagement.modules.fvm.dto.VehicleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.noncustomeradequate.toFVMNonCustomerAdequateData
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.toFVMPreDeliveryInspection
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.FVMVehicleCampaignsDataMapper
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleDataMapper
import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocationMapper
import com.fleetmanagement.modules.fvm.dto.vehicleregistration.FVMVehicleRegistrationMapper
import com.fleetmanagement.modules.fvm.dto.vehiclesales.toFVMVehicleSales
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.toFVMVehicleTransfer
import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded.*
import jakarta.persistence.Column
import jakarta.persistence.Embedded
import jakarta.persistence.Entity
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.util.UUID

@Entity
@Table(name = "vehicle_manager", schema = "fvm")
open class DLZViewVehicleRow(
    @Id
    @Column(name = "vehicledata_id")
    open var id: UUID? = null,
    @Embedded
    open var vehicle: DLZViewVehicleData? = null,
    @Embedded
    var vehicleTransfer: DLZViewVehicleTransferData? = null,
    @Embedded
    open var vehicleCampaigns: DLZViewVehicleCampaignsData? = null,
    @Embedded
    open var vehicleRegistration: DLZViewVehicleRegistrationData? = null,
    @Embedded
    open var vehicleLastKnownLocation: DLZViewVehicleLastKnownLocationData? = null,
    @Embedded
    open var preDeliveryInspection: DLZViewPreDeliveryInspectionData? = null,
    @Embedded
    open var nonCustomerAdequate: DLZViewNonCustomerAdequateData? = null,
    @Embedded
    open var vehicleSales: DLZViewVehicleSalesData? = null,
)

fun List<DLZViewVehicleRow>.toVehicleUIRepresentationList(): List<VehicleUIRepresentation> =
    this.map {
        VehicleUIRepresentation(
            vehicle = FVMVehicleDataMapper.INSTANCE.map(it),
            vehicleTransfer = it.vehicleTransfer?.toFVMVehicleTransfer(),
            vehicleCampaigns = FVMVehicleCampaignsDataMapper.INSTANCE.map(it),
            vehicleRegistration = if (it.vehicle?.vin != null) FVMVehicleRegistrationMapper.INSTANCE.map(it) else null,
            vehicleLastKnownLocation = FVMVehicleLocationMapper.INSTANCE.map(it),
            preDeliveryInspection = it.preDeliveryInspection?.toFVMPreDeliveryInspection(it.id),
            nonCustomerAdequate = it.nonCustomerAdequate?.toFVMNonCustomerAdequateData(),
            vehicleSales = it.vehicleSales?.toFVMVehicleSales(),
        )
    }
