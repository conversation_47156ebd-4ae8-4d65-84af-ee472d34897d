/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded

import jakarta.persistence.Embeddable
import jakarta.validation.constraints.Size

@Embeddable
open class DLZViewFakeDimensionData(
    @Size(max = 50)
    open var column1: String? = null,
    @Size(max = 50)
    open var column2: String? = null,
    @Size(max = 50)
    open var column3: String? = null,
    @Size(max = 50)
    open var column4: String? = null,
    @Size(max = 50)
    open var column5: String? = null,
    @Size(max = 50)
    open var column6: String? = null,
    @Size(max = 50)
    open var column7: String? = null,
    @Size(max = 50)
    open var column8: String? = null,
    @Size(max = 50)
    open var column9: String? = null,
    @Size(max = 50)
    open var column10: String? = null,
    @Size(max = 50)
    open var column11: String? = null,
    @Size(max = 50)
    open var column12: String? = null,
    @Size(max = 50)
    open var column13: String? = null,
    @Size(max = 50)
    open var column14: String? = null,
    @Size(max = 50)
    open var column15: String? = null,
    @Size(max = 50)
    open var column16: String? = null,
    @Size(max = 50)
    open var column17: String? = null,
    @Size(max = 50)
    open var column18: String? = null,
    @Size(max = 50)
    open var column19: String? = null,
    @Size(max = 50)
    open var column20: String? = null,
    @Size(max = 50)
    open var column21: String? = null,
    @Size(max = 50)
    open var column22: String? = null,
    @Size(max = 50)
    open var column23: String? = null,
    @Size(max = 50)
    open var column24: String? = null,
    @Size(max = 50)
    open var column25: String? = null,
)
