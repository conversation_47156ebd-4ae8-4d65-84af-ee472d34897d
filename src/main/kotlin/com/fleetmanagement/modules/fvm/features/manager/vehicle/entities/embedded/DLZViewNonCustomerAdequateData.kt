/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import java.math.BigDecimal
import java.time.OffsetDateTime

@Embeddable
open class DLZViewNonCustomerAdequateData(
    @Column(name = "noncustomeradequate_status")
    val ncaStatus: String?,
    @Column(name = "noncustomeradequate_profitability_audit_done")
    val profitabilityAuditDone: OffsetDateTime?,
    @Column(name = "noncustomeradequate_rebuild_started")
    val rebuildStarted: OffsetDateTime?,
    @Column(name = "noncustomeradequate_rebuild_done")
    val rebuildDone: OffsetDateTime?,
    @Column(name = "noncustomeradequate_comment")
    val comment: String?,
    @Column(name = "noncustomeradequate_planned_rebuild_cost")
    val plannedRebuildCost: BigDecimal?,
    @Column(name = "noncustomeradequate_actual_rebuild_cost")
    val actualRebuildCost: BigDecimal?,
    @Column(name = "noncustomeradequate_expected_revenue")
    val expectedRevenue: BigDecimal?,
    @Column(name = "noncustomeradequate_sales_price")
    val salesPrice: BigDecimal?,
)
