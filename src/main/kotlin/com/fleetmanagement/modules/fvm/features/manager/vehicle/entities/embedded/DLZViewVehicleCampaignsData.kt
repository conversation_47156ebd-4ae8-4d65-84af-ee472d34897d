/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable

@Embeddable
data class DLZViewVehicleCampaignsData(
    @Column(name = "vehiclecampaigns_campaignids")
    val campaignIds: String? = null,
)
