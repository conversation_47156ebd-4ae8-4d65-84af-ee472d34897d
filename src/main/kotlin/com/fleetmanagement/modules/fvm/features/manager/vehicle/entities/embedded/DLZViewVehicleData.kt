/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import jakarta.validation.constraints.Size
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime

@Embeddable
open class DLZViewVehicleData(
    @Size(max = 255)
    @Column(name = "vehicledata_vin")
    open var vin: String? = null,
    @Size(max = 255)
    @Column(name = "vehicledata_vguid")
    open var vguid: String? = null,
    @Size(max = 255)
    @Column(name = "vehicledata_equiid")
    open var equiId: String? = null,
    @Column(name = "vehicledata_equipmentnumber")
    open var equipmentNumber: Long? = null,
    @Column(name = "vehicledata_source")
    open var source: String? = null,
    @Column(name = "vehicledata_referenceid")
    open var referenceId: String? = null,
    @Column(name = "vehicledata_financialassettype")
    open var financialAssetType: String? = null,
    @Column(name = "vehicledata_currenttires")
    open var currentTires: String? = null,
    @Column(name = "vehicledata_externalleasestart")
    open var externalLeaseStart: LocalDate? = null,
    @Column(name = "vehicledata_externalleaseend")
    open var externalLeaseEnd: LocalDate? = null,
    @Column(name = "vehicledata_externalleaserate")
    open var externalLeaseRate: Float? = null,
    @Column(name = "vehicledata_externalleaselessee")
    open var externalLeaseLessee: String? = null,
    @Column(name = "vehicledata_numberofdamages")
    open var numberOfDamages: Int? = null,
    @Column(name = "vehicledata_repairfixcarid")
    open var repairfixCarId: String? = null,
    @Column(name = "vehicledata_tuevappointment")
    open var tuevAppointment: LocalDate? = null,
    @Column(name = "vehicledata_options", columnDefinition = "jsonb")
    var options: String? = null,
    @Column(name = "vehicledata_createdat")
    open var createdAt: LocalDate? = null,
    @Column(name = "vehicledata_status")
    open var status: String? = null,
    @Column(name = "vehicledata_version")
    open var version: Int,
    @Embedded
    open var model: DLZViewModelData? = null,
    @Embedded
    open var consumption: DLZViewConsumptionData? = null,
    @Embedded
    open var order: DLZViewOrderData? = null,
    @Embedded
    open var production: DLZViewProductionData? = null,
    @Embedded
    open var country: DLZViewCountryData? = null,
    @Embedded
    open var pmp: DLZViewPMPData? = null,
    @Embedded
    open var embargo: DLZViewEmbargoData? = null,
    @Embedded
    open var color: DLZViewColorData? = null,
    @Embedded
    open var price: DLZViewPriceData? = null,
    @Embedded
    open var technical: DLZViewTechnicalData? = null,
    @Embedded
    open var fleet: DLZViewFleetData? = null,
    @Embedded
    open var wltp: DLZViewWltpData? = null,
    @Embedded
    open var delivery: DLZViewDeliveryData? = null,
    @Embedded
    open var returnInfo: DLZViewReturnData? = null,
    @Embedded
    open var evaluation: DLZViewEvaluationData? = null,
    @Embedded
    open var tireSetChange: DLZViewTireSetChange? = null,
    @Embedded
    open var currentMileage: DLZViewVehicleCurrentMileage? = null,
    @Embedded
    var vtstamm: DLZVehicleVTStammData? = null,
)

@Embeddable
class DLZViewModelData(
    @Column(name = "vehicledata_modeldescription", length = 1024)
    var description: String? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_productid", length = 32)
    var productId: String? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_productcode", length = 32)
    var productCode: String? = null,
    @Size(max = 1024)
    @Column(name = "vehicledata_ordertype", length = 1024)
    var orderType: String? = null,
    @Column(name = "vehicledata_manufacturer")
    var manufacturer: String? = null,
    @Column(name = "vehicledata_vehicletype")
    var vehicleType: String? = null,
    @Column(name = "vehicledata_range")
    var range: String? = null,
    @Column(name = "vehicledata_modeldescriptiondevelopment")
    var modelDescriptionDevelopment: String? = null,
    @Column(name = "vehicledata_modelrangedevelopment")
    var rangeDevelopment: String? = null,
)

@Embeddable
class DLZViewConsumptionData(
    @Size(max = 32)
    @Column(name = "vehicledata_drivetype", length = 32)
    var driveType: String? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_typification", length = 32)
    var typification: String? = null,
    @Column(name = "vehicledata_primary_fuel_type")
    var primaryFuelType: String? = null,
    @Column(name = "vehicledata_secondary_fuel_type")
    var secondaryFuelType: String? = null,
)

@Embeddable
class DLZViewOrderData(
    @Size(max = 32)
    @Column(name = "vehicledata_department", length = 32)
    var department: String? = null,
    @Column(name = "vehicledata_tradingpartnernumber")
    var tradingPartnerNumber: String? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_purposeordertype", length = 32)
    var purposeOrderType: String? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_importershortname", length = 32)
    var importerShortName: String? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_commissionnumber", length = 32)
    var commissionNumber: String? = null,
    @Column(name = "vehicledata_invoicenumber")
    var invoiceNumber: String? = null,
    @Column(name = "vehicledata_invoicedate")
    var invoiceDate: LocalDate? = null,
    @Column(name = "vehicledata_purchaseorderdate")
    var purchaseOrderDate: LocalDate? = null,
    @Column(name = "vehicledata_requesteddeliverydate")
    var requestedDeliveryDate: LocalDate? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_deliverytype", length = 32)
    var deliveryType: String? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_primarystatus", length = 32)
    var primaryStatus: String? = null,
    @Size(max = 8)
    @Column(name = "vehicledata_leasingtype", length = 8)
    var leasingType: String? = null,
    @Column(name = "vehicledata_preproduction_vehicle")
    var preproductionVehicle: Boolean? = null,
    @Column(name = "vehicledata_blocked_for_sale")
    var blockedForSale: Boolean? = null,
)

@Embeddable
class DLZViewProductionData(
    @Size(max = 32)
    @Column(name = "vehicledata_productionnumber", length = 32)
    var number: String? = null,
    @Column(name = "vehicledata_productionenddate")
    var endDate: LocalDate? = null,
    @Column(name = "vehicledata_technicalmodelyear")
    var technicalModelYear: Int? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_factory", length = 32)
    var factory: String? = null,
    @Column(name = "vehicledata_factoryvw")
    var factoryVW: Int? = null,
    @Column(name = "vehicledata_quotemonth")
    var quoteMonth: Int? = null,
    @Column(name = "vehicledata_quoteyear")
    var quoteYear: Int? = null,
    @Column(name = "vehicledata_plannedproductionenddate")
    var plannedEndDate: LocalDate? = null,
    @Size(max = 4)
    @Column(name = "vehicledata_gearboxclass", length = 4)
    var gearBoxClass: String? = null,
)

@Embeddable
class DLZViewCountryData(
    @Size(max = 6)
    @Column(name = "vehicledata_countryinfobnrvalue", length = 6)
    var bnrValue: String? = null,
    @Size(max = 6)
    @Column(name = "vehicledata_countryinfocnrvalue", length = 6)
    var cnrValue: String? = null,
    @Size(max = 255)
    @Column(name = "vehicledata_countryinfocnrvaluecountrydescription")
    var cnrCountryDescription: String? = null,
)

@Embeddable
class DLZViewPMPData(
    @Column(name = "vehicledata_pmpdataodometer")
    var odometer: Int? = null,
    @Column(name = "vehicledata_pmpdatatimestamp")
    var timestamp: LocalDateTime? = null,
)

@Embeddable
class DLZViewEmbargoData(
    @Column(name = "vehicledata_inembargo")
    var inEmbargo: Boolean? = null,
)

@Embeddable
class DLZViewColorData(
    @Size(max = 32)
    @Column(name = "vehicledata_colorexterior", length = 32)
    var exterior: String? = null,
    @Column(name = "vehicledata_colorexteriordescription", length = Integer.MAX_VALUE)
    var exteriorDescription: String? = null,
    @Size(max = 32)
    @Column(name = "vehicledata_colorinterior", length = 32)
    var interior: String? = null,
    @Column(name = "vehicledata_colorinteriordescription", length = Integer.MAX_VALUE)
    var interiorDescription: String? = null,
)

@Embeddable
class DLZViewPriceData(
    @Column(name = "vehicledata_grosspricewithextras")
    var grossPriceWithExtras: BigDecimal? = null,
    @Column(name = "vehicledata_netpricewithextras")
    var netPriceWithExtras: BigDecimal? = null,
    @Column(name = "vehicledata_factorynetpriceeur")
    var factoryNetPriceEUR: BigDecimal? = null,
    @Column(name = "vehicledata_factorygrosspriceeur")
    var factoryGrossPriceEUR: BigDecimal? = null,
)

@Embeddable
class DLZViewTechnicalData(
    @Column(name = "vehicledata_amountseats")
    var amountSeats: Int? = null,
    @Column(name = "vehicledata_enginecapacity")
    var engineCapacity: Float? = null,
    @Column(name = "vehicledata_cargovolume")
    var cargoVolume: Int? = null,
    @Column(name = "vehicledata_vehiclewidthmirrorsextended")
    var vehicleWidthMirrorsExtended: Int? = null,
    @Column(name = "vehicledata_maximumchargingpowerdc")
    var maximumChargingPowerDc: Int? = null,
    @Column(name = "vehicledata_grossvehicleweight")
    var grossVehicleWeight: Int? = null,
    @Column(name = "vehicledata_curbweighteu")
    var curbWeightEu: Int? = null,
    @Column(name = "vehicledata_totalpowerkw")
    var totalPowerKw: Int? = null,
    @Column(name = "vehicledata_curbweightdin")
    var curbWeightDin: Int? = null,
    @Column(name = "vehicledata_maximumpayload")
    var maximumPayload: Int? = null,
    @Column(name = "vehicledata_chargingtimeac22kw")
    var chargingTimeAc22Kw: Float? = null,
    @Column(name = "vehicledata_chargingtimeac11kw0100")
    var chargingTimeAc11kw0100: Float? = null,
    @Column(name = "vehicledata_chargingtimeac96kw0100")
    var chargingTimeAc96kw0100: Float? = null,
    @Column(name = "vehicledata_netbatterycapacity")
    var netBatteryCapacity: Float? = null,
    @Column(name = "vehicledata_grossbatterycapacity")
    var grossBatteryCapacity: Float? = null,
    @Column(name = "vehicledata_acceleration0100Kmh")
    var acceleration0100Kmh: Float? = null,
    @Column(name = "vehicledata_acceleration0100kmhlaunchcontrol")
    var acceleration0100KmhLaunchControl: Float? = null,
    @Column(name = "vehicledata_topspeed")
    var topSpeed: Int? = null,
    @Column(name = "vehicledata_height")
    var height: Int? = null,
    @Column(name = "vehicledata_widthmirrorsfolded")
    var widthMirrorsFolded: Int? = null,
    @Column(name = "vehicledata_length")
    var length: Int? = null,
    @Column(name = "vehicledata_acceleration80120kmh")
    var acceleration80120Kmh: Float? = null,
    @Column(name = "vehicledata_maxroofloadwithporscherooftransportsystem")
    var maxRoofLoadWithPorscheRoofTransportSystem: Int? = null,
    @Column(name = "vehicledata_chargingtimedcmaxpower580")
    var chargingTimeDcMaxPower580: Float? = null,
    @Column(name = "vehicledata_powerkw")
    var powerKw: Int? = null,
)

@Embeddable
class DLZViewFleetData(
    @Column(name = "vehicledata_solddate")
    var soldDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_scrappeddate")
    var scrappedDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_stolendate")
    var stolenDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_soldcupcardate")
    var soldCupCarDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_approvedforscrappingdate")
    var approvedForScrappingDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_scrappedvehicleoffereddate")
    var scrappedVehicleOfferedDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_vehiclesenttosalesdate")
    var vehicleSentToSalesDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_costestimationordereddate")
    var costEstimationOrderedDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_isresidualvaluemarket")
    var isResidualValueMarket: Boolean? = null,
    @Column(name = "vehicledata_profitabilityauditdate")
    var profitabilityAuditDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_comment")
    var comment: String? = null,
    @Column(name = "vehicledata_scrap_vehicle")
    var scrapVehicle: Boolean? = null,
    @Column(name = "vehicledata_racecar")
    var raceCar: Boolean,
    @Column(name = "vehicledata_classic")
    var classic: Boolean,
)

@Embeddable
class DLZViewWltpData(
    @Column(name = "vehicledata_vehicleweight")
    var vehicleWeight: Int? = null,
    @Column(name = "vehicledata_electricrange")
    var electricRange: Int? = null,
    @Column(name = "vehicledata_co2combined")
    var co2Combined: Int? = null,
    @Column(name = "vehicledata_electricrangecity")
    var electricRangeCity: Int? = null,
)

@Embeddable
class DLZViewDeliveryData(
    @Column(name = "vehicledata_preparationdonedate")
    var preparationDoneDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_ispreparationnecessary")
    var isPreparationNecessary: Boolean? = null,
)

@Embeddable
class DLZViewReturnData(
    @Column(name = "vehicledata_nextprocess")
    var nextProcess: String? = null,
    @Column(name = "vehicledata_isusedcar")
    var isUsedCar: Boolean? = null,
    @Column(name = "vehicledata_keyreturned")
    var keyReturned: OffsetDateTime? = null,
    @Column(name = "vehicledata_factorycarpreparationordernumber")
    var factoryCarPreparationOrderNumber: String? = null,
)

@Embeddable
class DLZViewEvaluationData(
    @Column(name = "vehicledata_appraisalnetprice")
    var appraisalNetPrice: BigDecimal? = null,
    @Column(name = "vehicledata_vehicleevaluationcomment")
    var vehicleEvaluationComment: String? = null,
    @Column(name = "vehicledata_pccomplaintcheckcomment")
    var pcComplaintCheckComment: String? = null,
)

@Embeddable
class DLZViewTireSetChange(
    @Column(name = "vehicledata_tire_set_change_comment")
    var comment: String? = null,
    @Column(name = "vehicledata_tire_set_ordered_date")
    var orderedDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_tire_set_completed_date")
    var completedDate: OffsetDateTime? = null,
)

@Embeddable
class DLZViewVehicleCurrentMileage(
    @Column(name = "vehiclecurrentmileage_mileage")
    val mileage: Int?,
    @Column(name = "vehiclecurrentmileage_readdate")
    val readDate: OffsetDateTime?,
)

@Embeddable
data class DLZVehicleVTStammData(
    @Column(name = "vehicledata_subjecttoconfidentiality")
    val subjectToConfidentiality: Boolean?,
    @Column(name = "vehicledata_confidentialityclassification")
    val confidentialityClassification: String?,
    @Column(name = "vehicledata_subjecttoconfidentialitystartdate")
    val subjectToConfidentialityStartDate: LocalDate?,
    @Column(name = "vehicledata_subjecttoconfidentialityenddate")
    val subjectToConfidentialityEndDate: LocalDate?,
    @Column(name = "vehicledata_recordfactoryexit")
    val recordFactoryExit: Boolean?,
    @Column(name = "vehicledata_camouflagerequired")
    val camouflageRequired: Boolean?,
    @Column(name = "vehicledata_internaldesignation")
    val internalDesignation: String? = null,
    @Column(name = "vehicledata_typeofusevts")
    val typeOfUseVTS: String? = null,
    @Column(name = "vehicledata_statusvts")
    val statusVTS: String? = null,
)
