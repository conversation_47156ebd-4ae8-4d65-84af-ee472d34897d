/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.validation.constraints.Size
import java.time.ZonedDateTime

@Embeddable
open class DLZViewVehicleRegistrationData(
    @Size(max = 15)
    @Column(name = "vehicleregistration_licenceplate", length = 15)
    open var licencePlate: String? = null,
    @Column(name = "vehicleregistration_firstregistrationdate")
    open var firstRegistrationDate: ZonedDateTime? = null,
    @Column(name = "vehicleregistration_lastregistrationdate")
    open var lastRegistrationDate: ZonedDateTime? = null,
    @Column(name = "vehicleregistration_lastderegistrationdate")
    open var lastDeRegistrationDate: ZonedDateTime? = null,
    @Size(max = 4)
    @Column(name = "vehicleregistration_hsn", length = 4)
    open var hsn: String? = null,
    @Size(max = 32)
    @Column(name = "vehicleregistration_tsn", length = 32)
    open var tsn: String? = null,
    @Column(name = "vehicleregistration_sfme")
    open var sfme: Boolean? = null,
    @Column(name = "vehicleregistration_testvehicle")
    open var testVehicle: Boolean? = null,
    @Size(max = 15)
    @Column(name = "vehicleregistration_storagelocation", length = 15)
    open var storageLocation: String? = null,
    @Column(name = "vehicleregistration_registrationtype")
    open var registrationType: Int? = null,
    @Size(max = 35)
    @Column(name = "vehicleregistration_registrationstatus", length = 35)
    open var registrationStatus: String? = null,
    @Column(name = "vehicleregistration_registrationdate")
    open var registrationDate: ZonedDateTime? = null,
    @Column(name = "vehicleregistration_testnumber")
    open var testNumber: Long? = null,
)
