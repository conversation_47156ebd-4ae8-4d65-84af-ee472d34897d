/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import java.math.BigDecimal
import java.time.OffsetDateTime

@Embeddable
class DLZViewVehicleSalesData(
    @Column(name = "vehiclesale_reservedforb2c")
    var reservedForB2C: Boolean?,
    @Column(name = "vehiclesale_reservedforb2b")
    var reservedForB2B: Boolean?,
    @Column(name = "vehiclesale_comment")
    var comment: String?,
    @Column(name = "vehiclesale_contractsigned")
    var contractSigned: Boolean?,
    @Column(name = "vehiclesale_planneddeliverydate")
    var plannedDeliveryDate: OffsetDateTime?,
    @Column(name = "vehiclesale_customerdeliverydate")
    var customerDeliveryDate: OffsetDateTime?,
    @Column(name = "vehiclesale_sales_net_price_eur")
    var salesNetPriceEUR: BigDecimal?,
    @Column(name = "vehiclesale_sales_net_price_after_discount_eur")
    var salesNetPriceAfterDiscountEUR: BigDecimal?,
    @Column(name = "vehiclesale_winter_tires_net_price_after_discount_eur")
    var winterTiresNetPriceAfterDiscountEUR: BigDecimal?,
    @Column(name = "vehiclesale_winter_tires_net_price_eur")
    var winterTiresNetPriceEUR: BigDecimal?,
    @Column(name = "vehiclesale_winter_tires_id")
    var winterTiresId: Long?,
    @Column(name = "vehiclesale_customer_partner_number")
    var customerPartnerNumber: String?,
    @Column(name = "vehiclesale_invoice_recipient_number")
    var invoiceRecipientNumber: String?,
    @Column(name = "vehiclesale_sales_person_number")
    var salesPersonNumber: String?,
    @Column(name = "vehiclesale_transaction_id")
    var fvmTransactionId: String?,
    @Column(name = "vehiclesale_auction_id")
    var auctionId: String?,
    @Column(name = "vehiclesale_receipt_number")
    var receiptNumber: String?,
    @Column(name = "vehiclesale_invoice_date")
    var invoiceDate: OffsetDateTime?,
    @Column(name = "vehiclesale_invoice_number")
    var invoiceNumber: String?,
    @Column(name = "vehiclesale_final_invoice_number")
    var finalInvoiceNumber: String?,
    @Column(name = "vehiclesale_loading_completed_date")
    var loadingCompletedDate: OffsetDateTime?,
)
