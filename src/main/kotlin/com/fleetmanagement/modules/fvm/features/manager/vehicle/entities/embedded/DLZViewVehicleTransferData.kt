/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.embedded

import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.FVMVehicleTransferDataStatus
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.time.OffsetDateTime
import java.util.UUID

@Embeddable
class DLZViewVehicleTransferData(
    @Column(name = "vehicletransfer_vehicle_transfer_key") var vehicleTransferKey: Long,
    @Column(name = "vehicletransfer_version") var version: Int,
    @Column(name = "vehicletransfer_vehicle_id") var vehicleId: UUID,
    @Column(name = "vehicletransfer_vehicle_usage_id") var vehicleUsageId: UUID? = null,
    @Column(name = "vehicletransfer_return_date") var returnDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_planned_return_date")
    var plannedReturnDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_latest_return_date")
    var latestReturnDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_mileage_at_delivery") var mileageAtDelivery: Int? = null,
    @Column(name = "vehicletransfer_mileage_at_return") var mileageAtReturn: Int? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "vehicletransfer_status")
    var status: FVMVehicleTransferDataStatus? = null,
)
