/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicle.repository

import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.DLZViewVehicleRow
import org.springframework.data.repository.Repository
import java.util.*

/**
 * DLZViewVehicleRow DAO to filter [DLZViewVehicleRow]s by vehicleId, As this is not possible using generic DataManagerViewRepository.
 */
interface DLZViewVehicleRowDAO : Repository<DLZViewVehicleRow, UUID> {
    fun findAllByIdIn(ids: List<UUID>): List<DLZViewVehicleRow>
}
