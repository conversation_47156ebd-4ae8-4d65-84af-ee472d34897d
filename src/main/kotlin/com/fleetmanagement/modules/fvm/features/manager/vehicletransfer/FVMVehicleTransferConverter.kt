/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer

import com.fleetmanagement.modules.fvm.dto.VehicleTransferUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.noncustomeradequate.toFVMNonCustomerAdequate
import com.fleetmanagement.modules.fvm.dto.peopledata.PeopleMapper
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspectionDayInformation
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspectionUpdate
import com.fleetmanagement.modules.fvm.dto.vehiclecampaigns.FVMVehicleCampaignsDataMapper
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleDataMapper
import com.fleetmanagement.modules.fvm.dto.vehicleregistration.FVMVehicleRegistrationMapper
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransferUpdate
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.toFVMPreDeliveryInspection
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.toFVMVehicleLocation
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.toFVMVehicleTransfer
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.FVMVehicleTransferRow
import com.fleetmanagement.modules.predeliveryinspection.application.PreDeliveryInspectionSlotDto
import com.fleetmanagement.modules.predeliveryinspection.application.PreDeliveryInspectionUpdateDto
import com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferUpdateDto
import com.fleetmanagement.modules.vehicletransfer.domain.entities.ServiceCard
import com.fleetmanagement.modules.vehicletransfer.domain.entities.TireSet
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import java.net.URI
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.ServiceCard as FVMVehicleTransferServiceCard
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.TireSet as FVMVehicleTransferTireSet
import com.fleetmanagement.modules.predeliveryinspection.application.TireSet as PDIUpdateDtoTireSet

fun FVMPreDeliveryInspectionUpdate.toPreDeliveryInspectionUpdateDto() =
    PreDeliveryInspectionUpdateDto(
        tireSet = this.tireSet?.map { PDIUpdateDtoTireSet.of(it) },
        isRelevant = this.isRelevant,
        foiling = this.foiling,
        refuel = this.refuel,
        charge = this.charge,
        digitalLogbook = this.digitalLogbook,
        licencePlateMounting = this.licencePlateMounting,
        orderedDate = this.orderedDate,
        completedDate = completedDate,
        plannedDate = plannedDate,
        comment = this.comment,
    )

fun FVMVehicleTransferUpdate.toVehicleTransferUpdateDto() =
    VehicleTransferUpdateDto(
        maximumServiceLifeInMonths = this.maximumServiceLifeInMonths,
        vehicleUsageId = this.vehicleUsageId,
        vehicleResponsiblePerson = this.vehicleResponsiblePerson,
        internalContactPerson = this.internalContactPerson,
        usingCostCenter = this.usingCostCenter,
        internalOrderNumber = this.internalOrderNumber,
        plannedDeliveryDate = this.plannedDeliveryDate,
        plannedReturnDate = this.plannedReturnDate,
        remark = this.remark,
        deliveryDate = this.deliveryDate,
        mileageAtDelivery = this.mileageAtDelivery,
        returnDate = this.returnDate,
        mileageAtReturn = this.mileageAtReturn,
        desiredDeliveryDate = this.desiredDeliveryDate,
        provisionForDeliveryComment = this.provisionForDeliveryComment,
        deliveryComment = this.deliveryComment,
        deliveryLeipzig = this.deliveryLeipzig,
        leasingPrivilege = this.leasingPrivilege,
        tiresComment = tiresComment,
        desiredTireSet = this.desiredTireSet?.map { it.toTireSet() },
        returnComment = this.returnComment,
        serviceCards = this.serviceCards?.map { dtoList -> dtoList.map { it.toServiceCard() } },
        registrationNeeded = this.registrationNeeded,
        usageMhp = this.usageMhp,
        usageVdw = this.usageVdw,
        privateMonthlyKilometers = this.privateMonthlyKilometers,
    )

fun FVMVehicleTransferTireSet.toTireSet(): TireSet =
    when (this) {
        FVMVehicleTransferTireSet.SR -> TireSet.SR
        FVMVehicleTransferTireSet.WR -> TireSet.WR
    }

fun FVMVehicleTransferServiceCard.toServiceCard(): ServiceCard =
    when (this) {
        FVMVehicleTransferServiceCard.FUELING_CARD -> ServiceCard.FUELING_CARD
        FVMVehicleTransferServiceCard.CHARGING_CARD -> ServiceCard.CHARGING_CARD
        FVMVehicleTransferServiceCard.WASHING_CARD -> ServiceCard.WASHING_CARD
    }

// TODO FPT1-813 this has to change to some form of warning in APIResponse
fun ValidationError.toProblemDetail(): ProblemDetail {
    val validationError = this
    return when (validationError.type) {
        ValidationError.ValidationErrorType.PDI_ALREADY_ORDERED ->
            ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                title = "PDI already ordered"
                detail = validationError.detail
                type = URI.create(ErrorType.FVM_PRE_DELIVERY_INSPECTION_PDI_ALREADY_ORDERED.value)
                setProperty("properties", validationError.properties)
            }

        ValidationError.ValidationErrorType.USING_COST_CENTER_UPDATE_VIOLATION_FOR_USAGE_GROUP_PERSON ->
            ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                title = "UsingCostCenter"
                detail = validationError.detail
                type = URI.create(ErrorType.FVM_INVALID_USING_COST_CENTER_ERROR_FOR_USAGE_GROUP_PERSON.value)
                setProperty("properties", validationError.properties)
            }

        ValidationError.ValidationErrorType.PDI_LEAD_TIME_VIOLATION ->
            ProblemDetail
                .forStatus(HttpStatus.CONFLICT)
                .apply {
                    title = "PDI lead time exceeded"
                    detail = validationError.detail
                    type = URI.create(ErrorType.FVM_PRE_DELIVERY_INSPECTION_PDI_LEAD_TIME_VIOLATION.value)
                    setProperty("properties", validationError.properties)
                }

        ValidationError.ValidationErrorType.PDI_INVALID_PLANNED_DATE ->
            ProblemDetail
                .forStatus(HttpStatus.CONFLICT)
                .apply {
                    title = "PDI plannedDate is invalid"
                    detail = validationError.detail
                    type = URI.create(ErrorType.FVM_PRE_DELIVERY_INSPECTION_PDI_INVALID_PLANNED_DATE.value)
                    setProperty("properties", validationError.properties)
                }

        ValidationError.ValidationErrorType.DELIVERY_LEAD_TIME_VIOLATION_FOR_DESIRED_DELIVERY_DATE ->
            ProblemDetail
                .forStatus(HttpStatus.CONFLICT)
                .apply {
                    title = "Delivery lead time violation for desired delivery date"
                    detail = validationError.detail
                    type =
                        URI.create(ErrorType.FVM_VEHICLE_TRANSFER_DELIVERY_LEAD_TIME_VIOLATION_FOR_DESIRED_DELIVERY_DATE.value)
                    setProperty("properties", validationError.properties)
                }

        ValidationError.ValidationErrorType.DELIVERY_LEAD_TIME_VIOLATION_FOR_PLANNED_DELIVERY_DATE ->
            ProblemDetail
                .forStatus(HttpStatus.CONFLICT)
                .apply {
                    title = "Delivery lead time violation for planned delivery date"
                    detail = validationError.detail
                    type =
                        URI.create(ErrorType.FVM_VEHICLE_TRANSFER_DELIVERY_LEAD_TIME_VIOLATION_FOR_PLANNED_DELIVERY_DATE.value)
                    setProperty("properties", validationError.properties)
                }
        ValidationError.ValidationErrorType.VEHICLE_DELIVERY_POWER_OF_ATTORNEY_CREATION_FAILED ->
            ProblemDetail
                .forStatus(HttpStatus.CONFLICT)
                .apply {
                    title = "Power of attorney creation for delivery email failed"
                    detail = validationError.detail
                    type =
                        URI.create(ErrorType.FVM_VEHICLE_TRANSFER_DELIVERY_POWER_OF_ATTORNEY_CREATION_FAILED.value)
                }
        ValidationError.ValidationErrorType.VEHICLE_DELIVERY_RESPONSIBLE_PERSON_DETAILS_COULD_NOT_BE_RETRIEVED ->
            ProblemDetail
                .forStatus(HttpStatus.CONFLICT)
                .apply {
                    title = "Details for responsible person could not be retrieved for delivery email"
                    detail = validationError.detail
                    type =
                        URI.create(ErrorType.FVM_VEHICLE_TRANSFER_DELIVERY_RESPONSIBLE_PERSON_DETAILS_COULD_NOT_BE_RETRIEVED.value)
                }
        ValidationError.ValidationErrorType.VEHICLE_DELIVERY_EMAIL_SENDING_FAILED ->
            ProblemDetail
                .forStatus(HttpStatus.CONFLICT)
                .apply {
                    title = "Delivery email not sent"
                    detail = validationError.detail
                    type =
                        URI.create(ErrorType.FVM_VEHICLE_TRANSFER_DELIVERY_EMAIL_NOT_SENT.value)
                }
    }
}

fun FVMVehicleTransferRow.toVehicleUIRepresentation() =
    VehicleTransferUIRepresentation(
        vehicleTransfer = this.vehicleTransfer?.toFVMVehicleTransfer(),
        // FIXME: If MapStruct mappers need to be used, they should be relocated to the vehicle transfer module to ensure modularity.
        vehicle = this.vehicle?.let { FVMVehicleDataMapper.INSTANCE.map(it) },
        vehicleCampaigns = this.vehicleCampaigns?.let { FVMVehicleCampaignsDataMapper.INSTANCE.map(it) },
        vehicleRegistration =
            if (this.vehicle?.vin != null) {
                FVMVehicleRegistrationMapper.INSTANCE.map(this.vehicleRegistration)
            } else {
                null
            },
        peopleData =
            this.peopleData?.let { employee -> PeopleMapper.INSTANCE.map(employee) },
        preDeliveryInspection =
            this.preDeliveryInspection?.toFVMPreDeliveryInspection(this.vehicle?.id),
        vehicleLastKnownLocation = this.vehicleLastKnownLocation?.toFVMVehicleLocation(this.vehicle?.id),
        nonCustomerAdequate = this.nonCustomerAdequate?.toFVMNonCustomerAdequate(),
    )

fun List<FVMVehicleTransferRow>.toVehicleUIRepresentationList() = this.map { it.toVehicleUIRepresentation() }

fun PreDeliveryInspectionSlotDto.toFVMPreDeliveryInspectionSlot() =
    FVMPreDeliveryInspectionDayInformation(
        date = date,
        pdiSlotsReserved = pdiSlotsReserved,
    )
