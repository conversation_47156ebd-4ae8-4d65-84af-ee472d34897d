/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer

import com.fleetmanagement.modules.documentgeneration.api.GenerateKeyFlagException
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransferKeyFlagException
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateNotAllowedActiveTransferExistsException
import com.fleetmanagement.modules.vehicletransfer.domain.*
import org.springframework.core.annotation.Order
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import java.net.URI

@ControllerAdvice
@Order(5)
class FVMVehicleTransferExceptionHandler {
    @ExceptionHandler(PlannedVehicleTransferNotFoundException::class)
    fun handlePlannedVehicleTransferNotFoundException(
        exception: PlannedVehicleTransferNotFoundException,
    ): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.NOT_FOUND,
                    exception.message ?: "Could not find requested PlannedVehicleTransfer.",
                ).apply {
                    type = URI.create(ErrorType.FVM_VEHICLE_TRANSFER_NOT_FOUND.value)
                }
        return ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(VehicleTransferNotFoundException::class)
    fun handleVehicleTransferNotFoundException(exception: VehicleTransferNotFoundException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.NOT_FOUND,
                    exception.message ?: "Could not find requested VehicleTransfer.",
                ).apply {
                    type = URI.create(ErrorType.FVM_VEHICLE_TRANSFER_NOT_FOUND.value)
                }
        return ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(VehicleTransferNotFinishedException::class)
    fun handleVehicleTransferNotFinishedException(exception: VehicleTransferNotFinishedException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.CONFLICT,
                    exception.message ?: "VehicleTransfer is not [FINISHED].",
                ).apply {
                    type = URI.create(ErrorType.FVM_VEHICLE_TRANSFER_ILLEGAL_STATUS.value)
                }
        return ResponseEntity
            .status(HttpStatus.CONFLICT)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(VehicleTransferNotActiveException::class)
    fun handleVehicleTransferNotActiveException(exception: VehicleTransferNotActiveException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.CONFLICT,
                    exception.message ?: "VehicleTransfer is not [ACTIVE].",
                ).apply {
                    type = URI.create(ErrorType.FVM_VEHICLE_TRANSFER_ILLEGAL_STATUS.value)
                }
        return ResponseEntity
            .status(HttpStatus.CONFLICT)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(ActiveVehicleTransferAlreadyExistsException::class)
    fun handleActiveVehicleTransferAlreadyExistsException(
        exception: ActiveVehicleTransferAlreadyExistsException,
    ): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.CONFLICT,
                    exception.message ?: "Vehicle has already active transfer.",
                ).apply {
                    type = URI.create(ErrorType.FVM_VEHICLE_TRANSFER_ILLEGAL_STATUS.value)
                }
        return ResponseEntity
            .status(HttpStatus.CONFLICT)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(GenerateKeyFlagException::class)
    fun handleGenerateKeyFlagException(ex: GenerateKeyFlagException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    ex.message,
                ).apply {
                    type = URI.create(ErrorType.SYSTEM_ERROR_INTERNAL_SERVER_ERROR.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }

    @ExceptionHandler(FVMVehicleTransferKeyFlagException::class)
    fun handleFVMVehicleTransferKeyFlagException(ex: FVMVehicleTransferKeyFlagException): ResponseEntity<APIResponse<Nothing?>> {
        val errorDetails =
            ex.errors.map { error ->
                ProblemDetail.forStatus(HttpStatus.INTERNAL_SERVER_ERROR).apply {
                    type = URI.create(ErrorType.FVM_DATA_MISSING.value)
                    title = "Problem generating key flag PDF"
                    detail = "${error.vehicleId} : ${error.message}"
                    properties =
                        mapOf(
                            "vehicleId" to error.vehicleId,
                            "vin" to error.vin,
                        )
                }
            }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(*errorDetails.toTypedArray()))
    }

    @ExceptionHandler(VehicleUpdateNotAllowedActiveTransferExistsException::class)
    fun handleVehicleUpdateNotAllowedActiveTransferExistsException(
        exception: VehicleUpdateNotAllowedActiveTransferExistsException,
    ): ResponseEntity<APIResponse<Nothing?>> {
        val error =
            ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                title = "Error while trying to update vehicle."
                detail = exception.message
                type = URI.create(ErrorType.FVM_VEHICLE_UPDATE_NOT_ALLOWED_ACTIVE_VEHICLE_TRANSFER_EXISTS.value)
                setProperty("properties", mapOf("propertyName" to exception.propertyName))
            }
        return ResponseEntity
            .status(HttpStatus.CONFLICT)
            .body(APIResponse.forErrors(error))
    }
}
