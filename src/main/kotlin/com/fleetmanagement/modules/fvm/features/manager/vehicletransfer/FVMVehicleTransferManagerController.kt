/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer

import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.integrations.aggrid.api.DataManagerService
import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.documentgeneration.api.GenerateKeyFlag
import com.fleetmanagement.modules.fvm.dto.VehicleTransferJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleTransferUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspectionDayInformation
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMPlannedVehicleTransferNew
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMPlannedVehicleTransfersNew
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransferCancel
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransferDelete
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransferKeyFlagException
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransferKeyFlagRequest
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransferRowUpdate
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.toCreatePlannedVehicleTransferDto
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.ExportService
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.dto.ExportRequest
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.FVMVehicleTransferRow
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.repository.FVMVehicleTransferRowDAO
import com.fleetmanagement.modules.fvm.features.toProblemDetailType
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferDLZInvalidFleetPropertiesException
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferInvalidDataException
import com.fleetmanagement.modules.vehicletransfer.application.port.CancelVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferDto
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.DeletePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferKey
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferCreateException
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import jakarta.validation.Valid
import org.springframework.http.HttpHeaders
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestHeader
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.servlet.mvc.method.annotation.StreamingResponseBody
import java.net.URI
import java.time.LocalDate

/** DLZ create,read and update supporting controller. */
@RestController
@RequestMapping(value = ["/ui/vehicle-transfers"])
@AccessControlledController
class FVMVehicleTransferManagerController(
    private val vehicleTransferDLZUpdateService: VehicleTransferDLZUpdateService,
    private val deletePlannedVehicleTransferUseCase: DeletePlannedVehicleTransferUseCase,
    private val cancelVehicleTransferUseCase: CancelVehicleTransferUseCase,
    private val dataManagerService: DataManagerService,
    private val createPlannedVehicleTransferUseCase: CreatePlannedVehicleTransferUseCase,
    private val vehicleTransferDLZReadService: VehicleTransferDLZReadService,
    private val fvmVehicleTransferRowDAO: FVMVehicleTransferRowDAO,
    private val exportStrategy: VehicleTransferManagerExportStrategy,
    private val exportService: ExportService,
    private val generateKeyFlag: GenerateKeyFlag,
) {
    @PostMapping("/export")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_READ)")
    @JsonView(VehicleTransferJsonView.DLZPage::class)
    fun getVehicleTransfersAsCsv(
        @RequestHeader("Authorization") authorization: String?,
        @RequestBody exportRequest: ExportRequest<VehicleTransferUIRepresentation>,
    ): ResponseEntity<StreamingResponseBody> {
        exportStrategy.validateRequest(exportRequest)
        val streamingResponseBody = exportService.export(exportRequest, exportStrategy)
        return ResponseEntity
            .ok()
            .header(
                HttpHeaders.CONTENT_DISPOSITION,
                "attachment; filename=\"${exportStrategy.fileNameWithoutExtension}.csv\"",
            ).header(HttpHeaders.CONTENT_TYPE, "text/csv")
            .body(streamingResponseBody)
    }

    @PostMapping("/search", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_READ)")
    @JsonView(VehicleTransferJsonView.DLZPage::class)
    fun search(
        @RequestBody searchRequest: SearchRequest,
    ): ResponseEntity<APIResponse<List<VehicleTransferUIRepresentation>>> {
        val (results, rowCount) =
            dataManagerService.search(searchRequest, FVMVehicleTransferRow::class.java)
        val rows = results.toVehicleUIRepresentationList()
        return ResponseEntity.ok(APIResponse(rows, rowCount = rowCount))
    }

    @PutMapping("/update", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_WRITE)")
    fun updateVehicleTransferManagerRows(
        @RequestBody @Valid updateVehicleTransferDLZRows: List<FVMVehicleTransferRowUpdate>,
    ): ResponseEntity<APIResponse<List<VehicleTransferUIRepresentation>>> {
        val (warnings, errors) =
            updateVehicleTransferDLZRows.fold(
                Pair(emptyList<ProblemDetail>(), emptyList<ProblemDetail>()),
            ) { (warnings, errors), row ->
                try {
                    val warningList =
                        vehicleTransferDLZUpdateService
                            .updateDLZRow(row)
                            .map(ValidationError::toProblemDetail)
                    Pair(warnings + warningList, errors)
                } catch (exception: VehicleTransferDLZDeliveryException) {
                    val error =
                        ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                            title = "Error while delivering planned vehicle transfer."
                            detail = exception.message
                            type = URI.create(ErrorType.FVM_VEHICLE_TRANSFER_DELIVERY_ERROR.value)
                            setProperty("properties", mapOf("missingProperties" to exception.missingProperties))
                        }
                    Pair(warnings, errors + error)
                } catch (exception: VehicleTransferDLZReturnException) {
                    val error =
                        ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                            title = "Error while returning vehicle."
                            detail = exception.message
                            type = URI.create(ErrorType.FVM_VEHICLE_TRANSFER_RETURN_ERROR.value)
                            setProperty("properties", mapOf("missingProperties" to exception.missingProperties))
                        }
                    Pair(warnings, errors + error)
                } catch (exception: VehicleTransferDLZEmptyUsingCostCenterException) {
                    val error =
                        ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                            title = "Error while updating using cost center"
                            detail = exception.message
                            type = URI.create(ErrorType.FVM_INVALID_USING_COST_CENTER_ERROR.value)
                            setProperty("properties", mapOf("key" to exception.key))
                        }
                    Pair(warnings, errors + error)
                } catch (exception: VehicleTransferDLZRowUpdateException) {
                    val error = exception.toProblemDetail()
                    Pair(warnings, errors + error)
                } catch (exception: VehicleTransferDLZDeliveryLeasingPrivilegesException) {
                    val error =
                        ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                            title = "Error in vehicle delivery"
                            detail = exception.message
                            type = URI.create(ErrorType.FVM_VEHICLE_TRANSFER_DLZ_DELIVERY_LEASING_PRIVILEGES_EXCEPTION.value)
                        }
                    Pair(warnings, errors + error)
                }
            }
        val updatedRows =
            fvmVehicleTransferRowDAO.findAllByVehicleTransfer_VehicleTransferKeyIn(
                updateVehicleTransferDLZRows.mapNotNull { it.vehicleTransfer?.key },
            )
        val resultData = updatedRows.toVehicleUIRepresentationList()
        return if (warnings.isEmpty() && errors.isEmpty()) {
            ResponseEntity.ok(APIResponse(data = resultData, rowCount = resultData.size.toLong()))
        } else {
            return ResponseEntity
                .status(HttpStatus.CONFLICT)
                .body(APIResponse(data = resultData, rowCount = resultData.size.toLong(), errors = errors, warnings = warnings))
        }
    }

    @PostMapping("/delete")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_WRITE)")
    fun deletePlannedVehicleTransfer(
        @RequestBody deletePayload: FVMVehicleTransferDelete,
    ): ResponseEntity<APIResponse<List<VehicleTransferUIRepresentation>>> {
        deletePlannedVehicleTransferUseCase.deletePlannedVehicleTransfer(
            plannedVehicleTransferKey = VehicleTransferKey(deletePayload.key),
            version = deletePayload.version,
        )
        val updatedRow =
            fvmVehicleTransferRowDAO.findByVehicleTransfer_VehicleTransferKey(deletePayload.key)
        val resultData = updatedRow?.toVehicleUIRepresentation()
        return ResponseEntity.ok(APIResponse(data = resultData?.let { listOf(it) } ?: emptyList(), rowCount = 1))
    }

    @PostMapping("/cancel")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_WRITE)")
    fun cancelVehicleTransfer(
        @RequestBody cancelPayload: FVMVehicleTransferCancel,
    ): ResponseEntity<APIResponse<List<VehicleTransferUIRepresentation>>> {
        cancelVehicleTransferUseCase.cancelVehicleTransfer(
            vehicleTransferKey = VehicleTransferKey(cancelPayload.key),
            version = cancelPayload.version,
        )
        val updatedRow =
            fvmVehicleTransferRowDAO.findByVehicleTransfer_VehicleTransferKey(cancelPayload.key)
        val resultData = updatedRow?.toVehicleUIRepresentation()
        return ResponseEntity.ok(APIResponse(data = resultData?.let { listOf(it) } ?: emptyList(), rowCount = 1))
    }

    @PostMapping
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_WRITE)")
    fun createVehicleTransfer(
        @RequestBody payload: FVMPlannedVehicleTransferNew,
    ): ResponseEntity<APIResponse<VehicleTransferUIRepresentation>> {
        try {
            val transferKey =
                createPlannedVehicleTransferUseCase
                    .manualCreatePlannedVehicleTransfer(payload.toCreatePlannedVehicleTransferDto())

            return fvmVehicleTransferRowDAO
                .findByVehicleTransfer_VehicleTransferKey(transferKey.value)
                ?.let { ResponseEntity.ok(APIResponse(data = it.toVehicleUIRepresentation())) }
                ?: throw VehicleTransferCreateException(payload.vehicleId)
        } catch (exception: PlannedVehicleTransferDLZInvalidFleetPropertiesException) {
            val problem =
                ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                    title = "Error while creating vehicle transfer"
                    detail = exception.message
                    type = URI.create(ErrorType.FVM_MANUAL_CREATE_VEHICLE_TRANSFER_ERROR.value)
                    setProperty("properties", mapOf("invalidProperties" to exception.invalidProperties, "vin" to exception.vin))
                }
            return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(APIResponse(data = VehicleTransferUIRepresentation(), errors = listOf(problem)))
        } catch (exception: PlannedVehicleTransferInvalidDataException) {
            val problem =
                ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                    title = "Error while creating vehicle transfer"
                    detail = exception.message
                    type = URI.create(ErrorType.FVM_MANUAL_CREATE_VEHICLE_TRANSFER_ERROR.value)
                    setProperty("properties", mapOf("vin" to exception.vin))
                }
            return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(APIResponse(data = VehicleTransferUIRepresentation(), errors = listOf(problem)))
        }
    }

    @PostMapping("/create")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_WRITE)")
    fun createPlannedVehicleTransfer(
        @RequestBody createPayload: FVMPlannedVehicleTransfersNew,
    ): ResponseEntity<APIResponse<List<VehicleTransferUIRepresentation>>> {
        val problems = mutableListOf<ProblemDetail>()
        val newlyCreatedVehicleTransferKeys = mutableListOf<VehicleTransferKey>()

        createPayload.vehicleIds.map {
            try {
                val transferKey =
                    createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(
                        CreatePlannedVehicleTransferDto(vehicleId = it),
                    )
                newlyCreatedVehicleTransferKeys.add(transferKey)
            } catch (exception: PlannedVehicleTransferDLZInvalidFleetPropertiesException) {
                problems.add(
                    ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                        title = "Error while manual creation ot vehicle transfer"
                        detail = exception.message
                        type = URI.create(ErrorType.FVM_MANUAL_CREATE_VEHICLE_TRANSFER_ERROR.value)
                        setProperty("properties", mapOf("invalidProperties" to exception.invalidProperties, "vin" to exception.vin))
                    },
                )
            }
        }
        val createdRows =
            fvmVehicleTransferRowDAO.findAllByVehicleTransfer_VehicleTransferKeyIn(newlyCreatedVehicleTransferKeys.map { it.value })
        val resultData = createdRows.toVehicleUIRepresentationList()
        return if (problems.isEmpty()) {
            ResponseEntity.ok(APIResponse(data = resultData, rowCount = resultData.size.toLong()))
        } else {
            return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(APIResponse(data = resultData, rowCount = resultData.size.toLong(), errors = problems))
        }
    }

    @GetMapping("/pdi-slots/{fromDate}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_READ)")
    fun getPreDeliveryInspectionSlots(
        @PathVariable fromDate: LocalDate,
    ): ResponseEntity<APIResponse<List<FVMPreDeliveryInspectionDayInformation>>> {
        val readPreDeliveryInspectionSlots = vehicleTransferDLZReadService.readPreDeliveryInspectionSlots(fromDate)
        return ResponseEntity.ok(APIResponse(readPreDeliveryInspectionSlots))
    }

    @PostMapping("/key-flag")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_READ)")
    fun downloadKeyFlag(
        @RequestBody request: FVMVehicleTransferKeyFlagRequest,
    ): ResponseEntity<ByteArray> {
        val keyFlag = generateKeyFlag.generateKeyFlag(request.vehicleIds)

        if (keyFlag.errors.isNotEmpty()) {
            throw FVMVehicleTransferKeyFlagException(keyFlag.errors)
        }

        return ResponseEntity
            .status(HttpStatus.OK)
            .header("Content-Disposition", "attachment; filename=document.pdf")
            .contentType(MediaType.APPLICATION_PDF)
            .body(keyFlag.data?.byteArray)
    }
}

fun VehicleTransferDLZRowUpdateException.toProblemDetail(): ProblemDetail {
    val message = this.message
    val propertyName = constraintViolation?.propertyName
    val constraintViolation =
        this.constraintViolation
            ?: return ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                title = "Error while updating VehicleTransfer through DLZ."
                detail = message
                type = URI.create(ErrorType.FVM_VEHICLE_TRANSFER_UPDATE_FAILED.value)
            }

    return ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
        title = "Error while updating vehicle transfer through DLZ."
        detail = message
        type = constraintViolation.type.toProblemDetailType()
        propertyName?.let { setProperty("properties", mapOf("propertyName" to propertyName)) }
    }
}
