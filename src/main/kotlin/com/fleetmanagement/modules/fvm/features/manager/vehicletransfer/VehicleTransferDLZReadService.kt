package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer

import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspectionDayInformation
import com.fleetmanagement.modules.predeliveryinspection.application.port.ReadPreDeliveryInspectionSlotsUseCase
import org.springframework.stereotype.Service
import java.time.LocalDate

@Service
class VehicleTransferDLZReadService(
    private val readPreDeliveryInspectionSlotsUseCase: ReadPreDeliveryInspectionSlotsUseCase,
) {
    fun readPreDeliveryInspectionSlots(fromDate: LocalDate): List<FVMPreDeliveryInspectionDayInformation> {
        val preDeliveryInspectionSlots = readPreDeliveryInspectionSlotsUseCase.readPreDeliveryInspectionSlots(fromDate)
        return preDeliveryInspectionSlots
            .map { it.toFVMPreDeliveryInspectionSlot() }
            .sortedBy { it.date }
            .toList()
    }
}
