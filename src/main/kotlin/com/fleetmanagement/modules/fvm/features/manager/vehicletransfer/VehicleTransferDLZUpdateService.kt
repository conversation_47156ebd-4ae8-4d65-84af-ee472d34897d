/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer

import com.fleetmanagement.emhshared.domain.ConstraintViolation
import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMPreDeliveryInspectionUpdate
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleUpdate
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransferRowUpdate
import com.fleetmanagement.modules.fvm.dto.vehicletransfer.FVMVehicleTransferUpdate
import com.fleetmanagement.modules.fvm.features.manager.vehicle.VehicleDLZRowUpdateException
import com.fleetmanagement.modules.fvm.features.manager.vehicle.toVehicleUpdateDto
import com.fleetmanagement.modules.predeliveryinspection.application.UnknownTireSetException
import com.fleetmanagement.modules.predeliveryinspection.application.port.PreDeliveryInspectionId
import com.fleetmanagement.modules.predeliveryinspection.application.port.UpdatePreDeliveryInspectionUseCase
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionFutureDateException
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionNotFoundException
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicle
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import com.fleetmanagement.modules.vehicletransfer.application.port.UpdateVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferKey
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferDeliveryLeasingPrivilegsException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferDeliveryException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferInvalidUsingCostCenterException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferNotFoundException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferReturnException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferUpdateException
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBUser
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

/**
 * A service that will delegate VehicleTransfer-DLZ updates to their respective modules.
 */
@Component
class VehicleTransferDLZUpdateService(
    private val updateVehicleTransferUseCase: UpdateVehicleTransferUseCase,
    private val updatePreDeliveryInspectionUseCase: UpdatePreDeliveryInspectionUseCase,
    private val updateVehicleService: UpdateVehicle,
) {
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun updateDLZRow(rowUpdate: FVMVehicleTransferRowUpdate): List<ValidationError> {
        // update pre-delivery inspection
        val preDeliveryInspectionValidationErrors =
            rowUpdate.preDeliveryInspection?.let {
                updatePreDeliveryInspection(it)
            } ?: emptyList()

        // update vehicle (FPT1-1227 this now happens before VehicleTransfer to allow setting of nextProcess and doing return-process in one request
        rowUpdate.vehicle?.also {
            updateVehicle(it)
        }

        // update vehicle-transfer
        val vehicleTransferValidationErrors =
            rowUpdate.vehicleTransfer?.let {
                updateVehicleTransfer(it)
            } ?: emptyList()

        return vehicleTransferValidationErrors + preDeliveryInspectionValidationErrors
    }

    private fun updateVehicleTransfer(update: FVMVehicleTransferUpdate): List<ValidationError> =
        try {
            updateVehicleTransferUseCase.updateVehicleTransfer(
                key = VehicleTransferKey(update.key),
                version = update.version,
                vehicleTransferUpdateDto = update.toVehicleTransferUpdateDto(),
            )
        } catch (exception: VehicleTransferNotFoundException) {
            throw VehicleTransferDLZRowUpdateException.default(message = exception.message, cause = exception.cause)
        } catch (exception: VehicleTransferUpdateException) {
            throw VehicleTransferDLZRowUpdateException.forConstraintViolation(
                message = exception.message,
                cause = exception.cause,
                constraintViolation = exception.constraintViolation,
            )
        } catch (exception: DataIntegrityViolationException) {
            throw VehicleTransferDLZRowUpdateException.default(message = exception.message, cause = exception.cause)
        } catch (exception: VehicleTransferDeliveryException) {
            throw VehicleTransferDLZDeliveryException(
                message = exception.message,
                cause = exception.cause,
                missingProperties = exception.missingProperties,
            )
        } catch (exception: PlannedVehicleTransferDeliveryLeasingPrivilegsException) {
            throw VehicleTransferDLZDeliveryLeasingPrivilegesException(
                message = exception.message,
                cause = exception.cause,
            )
        } catch (exception: VehicleTransferReturnException) {
            throw VehicleTransferDLZReturnException(
                message = exception.message,
                cause = exception.cause,
                missingProperties = exception.missingProperties,
            )
        } catch (exception: VehicleTransferInvalidUsingCostCenterException) {
            throw VehicleTransferDLZEmptyUsingCostCenterException(
                message = exception.message,
                cause = exception.cause,
                key = exception.vehicleTransferKey,
            )
        }

    private fun updatePreDeliveryInspection(update: FVMPreDeliveryInspectionUpdate): List<ValidationError> =
        try {
            updatePreDeliveryInspectionUseCase.updatePreDeliveryInspection(
                id = PreDeliveryInspectionId(update.id),
                preDeliveryInspectionUpdate = update.toPreDeliveryInspectionUpdateDto(),
            )
        } catch (exception: UnknownTireSetException) {
            throw VehicleTransferDLZRowUpdateException.default(message = exception.message, cause = exception.cause)
        } catch (exception: PreDeliveryInspectionNotFoundException) {
            throw VehicleTransferDLZRowUpdateException.default(message = exception.message, cause = exception.cause)
        } catch (exception: PreDeliveryInspectionFutureDateException) {
            throw VehicleDLZRowUpdateException(message = exception.message, cause = exception.cause)
        }

    private fun updateVehicle(update: FVMVehicleUpdate) {
        val modifier = (SecurityContextHolder.getContext().authentication?.principal as? ALBUser)?.fullNameWithDepartment ?: "Unknown"

        try {
            updateVehicleService.updateVehicle(
                vehicleId = update.id,
                vehicleUpdateDto = update.toVehicleUpdateDto(),
                modifier = modifier,
            )
        } catch (exception: VehicleNotFoundException) {
            throw VehicleTransferDLZRowUpdateException.default(message = exception.message, cause = exception.cause)
        } catch (exception: VehicleUpdateException) {
            throw VehicleTransferDLZRowUpdateException.forConstraintViolation(
                message = exception.message,
                cause = exception.cause,
                constraintViolation = exception.constraintViolation,
            )
        }
    }
}

class VehicleTransferDLZRowUpdateException private constructor(
    override val message: String?,
    override val cause: Throwable? = null,
    val constraintViolation: ConstraintViolation? = null,
) : RuntimeException() {
    companion object {
        fun default(
            message: String? = null,
            cause: Throwable? = null,
        ) = VehicleTransferDLZRowUpdateException(
            message = message,
            cause = cause,
        )

        fun forConstraintViolation(
            constraintViolation: ConstraintViolation?,
            message: String?,
            cause: Throwable?,
        ) = VehicleTransferDLZRowUpdateException(
            constraintViolation = constraintViolation,
            message = message,
            cause = cause,
        )
    }
}

data class VehicleTransferUpdateConstraintViolation(
    val key: Long? = null,
    override val type: ViolationType,
    override val propertyName: String?,
) : ConstraintViolation

data class VehicleTransferDLZDeliveryLeasingPrivilegesException(
    override val message: String?,
    override val cause: Throwable? = null,
) : IllegalArgumentException()

data class VehicleTransferDLZDeliveryException(
    override val message: String?,
    val missingProperties: List<String>,
    override val cause: Throwable? = null,
) : IllegalArgumentException()

data class VehicleTransferDLZReturnException(
    override val message: String?,
    val missingProperties: List<String>,
    override val cause: Throwable? = null,
) : IllegalArgumentException()

data class VehicleTransferDLZEmptyUsingCostCenterException(
    override val message: String?,
    val key: Long,
    override val cause: Throwable? = null,
) : IllegalArgumentException()

/**
 * An issue during some domain validation. The update still happened, but you receive this as a warning and should propagate it to the client.
 */
data class ValidationError(
    val identifier: String,
    val detail: String,
    val type: ValidationErrorType,
    val properties: Map<String, String>,
) {
    enum class ValidationErrorType {
        PDI_LEAD_TIME_VIOLATION,
        PDI_ALREADY_ORDERED,
        PDI_INVALID_PLANNED_DATE,
        DELIVERY_LEAD_TIME_VIOLATION_FOR_DESIRED_DELIVERY_DATE,
        DELIVERY_LEAD_TIME_VIOLATION_FOR_PLANNED_DELIVERY_DATE,
        USING_COST_CENTER_UPDATE_VIOLATION_FOR_USAGE_GROUP_PERSON,
        VEHICLE_DELIVERY_POWER_OF_ATTORNEY_CREATION_FAILED,
        VEHICLE_DELIVERY_EMAIL_SENDING_FAILED,
        VEHICLE_DELIVERY_RESPONSIBLE_PERSON_DETAILS_COULD_NOT_BE_RETRIEVED,
    }
}
