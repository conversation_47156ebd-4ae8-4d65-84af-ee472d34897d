/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer

import com.fleetmanagement.integrations.aggrid.api.DataManagerService
import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.fvm.dto.VehicleTransferUIRepresentation
import com.fleetmanagement.modules.fvm.features.manager.shared.export.api.ExportStrategy
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.FVMVehicleTransferRow
import org.springframework.stereotype.Service

@Service
class VehicleTransferManagerExportStrategy(
    private val dataManagerService: DataManagerService,
) : ExportStrategy<VehicleTransferUIRepresentation> {
    override fun getDataInChunks(searchRequest: SearchRequest): List<VehicleTransferUIRepresentation> {
        val (results, _) = dataManagerService.search(searchRequest, FVMVehicleTransferRow::class.java)
        return results.toVehicleUIRepresentationList()
    }
}
