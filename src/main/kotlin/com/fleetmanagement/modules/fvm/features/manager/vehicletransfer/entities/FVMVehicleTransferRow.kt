/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities

import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded.*
import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "vehicle_transfer_manager", schema = "fvm")
class FVMVehicleTransferRow(
    @Id
    @Column(name = "vehicletransfer_id")
    var id: UUID,
    @Embedded
    var vehicleTransfer: FVMVehicleTransferData? = null,
    @Embedded
    var vehicle: FVMVehicleData? = null,
    @Embedded
    var vehicleCampaigns: FVMVehicleCampaignsData? = null,
    @Embedded
    var peopleData: FVMEmployeeData? = null,
    @Embedded
    var vehicleRegistration: FVMVehicleRegistrationData? = null,
    @Embedded
    var preDeliveryInspection: FVMPreDeliveryInspection? = null,
    @Embedded
    var vehicleLastKnownLocation: FVMVehicleLastKnownLocationData? = null,
    @Embedded
    var nonCustomerAdequate: FVMVTNonCustomerAdequateData? = null,
)
