/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable

@Embeddable
class FVMEmployeeData(
    @Column(name = "peopledata_employeenumber")
    var employeeNumber: String? = null,
    @Column(name = "peopledata_firstname")
    var firstName: String? = null,
    @Column(name = "peopledata_lastname")
    var lastName: String? = null,
    @Column(name = "peopledata_companyemail")
    var companyEmail: String? = null,
    @Column(name = "peopledata_privateemail")
    var privateEmail: String? = null,
    @Column(name = "peopledata_accountingarea")
    var accountingArea: String? = null,
    @Column(name = "peopledata_department")
    var department: String? = null,
    @Column(name = "peopledata_personnelclerk")
    var personnelClerk: String? = null,
    @Column(name = "peopledata_leasingauthorization")
    var leasingAuthorization: String? = null,
    @Column(name = "peopledata_loyaltyleasing")
    var loyaltyLeasing: String? = null,
    @Column(name = "peopledata_permanentdrivingpermit")
    var permanentDrivingPermit: String? = null,
    @Column(name = "peopledata_replacementvehicleauthorization")
    var replacementVehicleAuthorization: String? = null,
    @Column(name = "peopledata_secondaryleasingauthorization")
    var secondaryLeasingAuthorization: String? = null,
    @Column(name = "peopledata_secondaryleasingspecialconditionsauthorization")
    var secondaryLeasingSpecialConditionsAuthorization: String? = null,
    @Column(name = "peopledata_leasingprivilegeleasing")
    var leasingPrivilegeLeasing: String? = null,
    @Column(name = "peopledata_leasingprivilegecompanycarleasing")
    var leasingPrivilegeCompanyCarLeasing: String? = null,
    @Column(name = "peopledata_leasingprivilegeloyaltyleasing")
    var leasingPrivilegeLoyaltyLeasing: String? = null,
    @Column(name = "peopledata_leasingprivilegesecondaryleasing")
    var leasingPrivilegeSecondaryLeasing: String? = null,
    @Column(name = "peopledata_leasingprivilegesecondaryleasingspecialconditions")
    var leasingPrivilegeSecondaryLeasingSpecialConditions: String? = null,
    @Column(name = "peopledata_leasingprivilegereplacementvehicle")
    var leasingPrivilegeReplacementVehicle: String? = null,
    @Column(name = "peopledata_fuelcardauthorizationde")
    var fuelCardAuthorizationDE: String? = null,
    @Column(name = "peopledata_fuelcardauthorizationeu")
    var fuelCardAuthorizationEU: String? = null,
    @Column(name = "peopledata_chargingcardauthorizationde")
    var chargingCardAuthorizationDE: String? = null,
    @Column(name = "peopledata_chargingcardauthorizationeu")
    var chargingCardAuthorizationEU: String? = null,
)
