/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import java.time.OffsetDateTime
import java.util.*

@Embeddable
class FVMPreDeliveryInspection(
    @Column(name = "predeliveryinspection_id")
    val id: UUID,
    @Column(name = "predeliveryinspection_tire_set")
    val tireSet: String?,
    @Column(name = "predeliveryinspection_is_relevant")
    val isRelevant: Boolean?,
    @Column(name = "predeliveryinspection_foiling")
    val foiling: Boolean?,
    @Column(name = "predeliveryinspection_refuel")
    val refuel: Boolean?,
    @Column(name = "predeliveryinspection_charge")
    val charge: Boolean?,
    @Column(name = "predeliveryinspection_digital_logbook")
    val digitalLogbook: Boolean?,
    @Column(name = "predeliveryinspection_licence_plate_mounting")
    val licencePlateMounting: Boolean?,
    @Column(name = "predeliveryinspection_ordered_date")
    val orderedDate: OffsetDateTime?,
    @Column(name = "predeliveryinspection_completed_date")
    val completedDate: OffsetDateTime?,
    @Column(name = "predeliveryinspection_planned_date")
    val plannedDate: OffsetDateTime?,
    @Column(name = "predeliveryinspection_comment")
    val comment: String?,
)
