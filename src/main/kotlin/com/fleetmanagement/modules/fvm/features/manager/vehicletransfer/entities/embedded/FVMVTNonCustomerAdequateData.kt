/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable

@Embeddable
open class FVMVTNonCustomerAdequateData(
    @Column(name = "noncustomeradequate_status")
    val ncaStatus: String?,
)
