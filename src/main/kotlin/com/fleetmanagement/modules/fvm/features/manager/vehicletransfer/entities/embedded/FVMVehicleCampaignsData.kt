/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable

/*
    FIXME: Entity classes in the FVM layer are currently used for displaying data in DLZ views.
    To maintain consistency across the application, adhere to the naming convention used for vehicle entities.

    Suggested naming pattern:
    - DLZViewVehicleTransferVehicleCampaignsData
    - DLZViewVehicleTransferVehicleData

    Following this convention will improve clarity and standardize the naming scheme across the system.
 */

@Embeddable
data class FVMVehicleCampaignsData(
    @Column(name = "vehiclecampaigns_campaignids")
    val campaignIds: String? = null,
)
