/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.Embedded
import java.math.BigDecimal
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.util.UUID

@Embeddable
class FVMVehicleData(
    @Column(name = "vehicledata_id")
    var id: UUID,
    @Column(name = "vehicledata_vin")
    var vin: String? = null,
    @Column(name = "vehicledata_equiid")
    var equiId: String? = null,
    @Column(name = "vehicledata_vguid")
    var vguid: String? = null,
    @Column(name = "vehicledata_equipmentnumber")
    var equipmentNumber: Long? = null,
    @Column(name = "vehicledata_numberofdamages")
    var numberOfDamages: Int? = null,
    @Column(name = "vehicledata_repairfixcarid")
    var repairfixCarId: String? = null,
    @Column(name = "vehicledata_tuevappointment")
    var tuevAppointment: LocalDate? = null,
    @Column(name = "vehicledata_currenttires")
    var currentTires: String? = null,
    @Column(name = "vehicledata_status")
    var status: String? = null,
    @Column(name = "vehicledata_financialassettype")
    var financialAssetType: String? = null,
    @Column(name = "vehicledata_createdat")
    var createdAt: LocalDate? = null,
    @Column(name = "vehicledata_externalleasestart")
    var externalLeaseStart: LocalDate? = null,
    @Column(name = "vehicledata_externalleaseend")
    var externalLeaseEnd: LocalDate? = null,
    @Column(name = "vehicledata_externalleaserate")
    var externalLeaseRate: Float? = null,
    @Column(name = "vehicledata_externalleaselessee")
    var externalLeaseLessee: String? = null,
    @Column(name = "vehicledata_options", columnDefinition = "jsonb")
    var options: String? = null,
    @Column(name = "vehicledata_referenceid")
    var referenceId: String? = null,
    @Column(name = "vehicledata_source")
    var source: String? = null,
    @Column(name = "vehicledata_version")
    var version: Int,
    @Embedded
    var color: FVMColorData? = null,
    @Embedded
    var country: FVMCountryData? = null,
    @Embedded
    var embargo: FVMEmbargoData? = null,
    @Embedded
    var consumption: FVMConsumptionData? = null,
    @Embedded
    var model: FVMModelData? = null,
    @Embedded
    var order: FVMOrderData? = null,
    @Embedded
    var pmp: FVMPMPData? = null,
    @Embedded
    var price: FVMPriceData? = null,
    @Embedded
    var production: FVMProductionData? = null,
    @Embedded
    var technical: FVMTechnicalData? = null,
    @Embedded
    var fleet: FVMMFleetData? = null,
    @Embedded
    var delivery: FVMDeliveryData? = null,
    @Embedded
    var returnInfo: FVMVReturnInfo? = null,
    @Embedded
    var tireSetChange: FVMTireSetChange? = null,
    @Embedded
    var wltp: FVMWltpData? = null,
    @Embedded
    var currentMileage: FVMCurrentMileage? = null,
    @Embedded
    var confidentiality: FVMVehicleVTStammData? = null,
)

@Embeddable
class FVMColorData(
    @Column(name = "vehicledata_colorexterior")
    var exterior: String? = null,
    @Column(name = "vehicledata_colorexteriordescription")
    var exteriorDescription: String? = null,
    @Column(name = "vehicledata_colorinterior")
    var interior: String? = null,
    @Column(name = "vehicledata_colorinteriordescription")
    var interiorDescription: String? = null,
)

@Embeddable
class FVMCountryData(
    @Column(name = "vehicledata_countryinfobnrvalue")
    var bnrValue: String? = null,
    @Column(name = "vehicledata_countryinfocnrvalue")
    var cnrValue: String? = null,
    @Column(name = "vehicledata_countryinfocnrvaluecountrydescription")
    var cnrCountryDescription: String? = null,
)

@Embeddable
class FVMEmbargoData(
    @Column(name = "vehicledata_inembargo")
    var inEmbargo: Boolean? = null,
)

@Embeddable
class FVMConsumptionData(
    @Column(name = "vehicledata_drivetype")
    var driveType: String? = null,
    @Column(name = "vehicledata_typification")
    var typification: String? = null,
    @Column(name = "vehicledata_primary_fuel_type")
    var primaryFuelType: String? = null,
    @Column(name = "vehicledata_secondary_fuel_type")
    var secondaryFuelType: String? = null,
)

@Embeddable
class FVMModelData(
    @Column(name = "vehicledata_modeldescription")
    var description: String? = null,
    @Column(name = "vehicledata_productid")
    var productId: String? = null,
    @Column(name = "vehicledata_productcode")
    var productCode: String? = null,
    @Column(name = "vehicledata_ordertype")
    var orderType: String? = null,
    @Column(name = "vehicledata_manufacturer")
    var manufacturer: String? = null,
    @Column(name = "vehicledata_vehicletype")
    var vehicleType: String? = null,
    @Column(name = "vehicledata_range")
    var range: String? = null,
    @Column(name = "vehicledata_modeldescriptiondevelopment")
    var modelDescriptionDevelopment: String? = null,
    @Column(name = "vehicledata_modelrangedevelopment")
    var rangeDevelopment: String? = null,
)

@Embeddable
class FVMOrderData(
    @Column(name = "vehicledata_department")
    var department: String? = null,
    @Column(name = "vehicledata_tradingpartnernumber")
    var tradingPartnerNumber: String? = null,
    @Column(name = "vehicledata_purposeordertype")
    var purposeOrderType: String? = null,
    @Column(name = "vehicledata_importershortname")
    var importerShortName: String? = null,
    @Column(name = "vehicledata_commissionnumber")
    var commissionNumber: String? = null,
    @Column(name = "vehicledata_invoicenumber")
    var invoiceNumber: String? = null,
    @Column(name = "vehicledata_invoicedate")
    var invoiceDate: LocalDate? = null,
    @Column(name = "vehicledata_purchaseorderdate")
    var purchaseOrderDate: LocalDate? = null,
    @Column(name = "vehicledata_requesteddeliverydate")
    var requestedDeliveryDate: LocalDate? = null,
    @Column(name = "vehicledata_deliverytype")
    var deliveryType: String? = null,
    @Column(name = "vehicledata_primarystatus")
    var primaryStatus: String? = null,
    @Column(name = "vehicledata_leasingtype")
    var leasingType: String? = null,
    @Column(name = "vehicledata_preproductionvehicle")
    var preproductionVehicle: Boolean? = null,
    @Column(name = "vehicledata_blockedforsale")
    var blockedForSale: Boolean? = null,
)

@Embeddable
class FVMPMPData(
    @Column(name = "vehicledata_pmpdataodometer")
    var odometer: Int? = null,
    @Column(name = "vehicledata_pmpdatatimestamp")
    var timestamp: LocalDateTime? = null,
)

@Embeddable
class FVMPriceData(
    @Column(name = "vehicledata_grosspricewithextras")
    var grossPriceWithExtras: BigDecimal? = null,
    @Column(name = "vehicledata_netpricewithextras")
    var netPriceWithExtras: BigDecimal? = null,
    @Column(name = "vehicledata_factorynetpriceeur")
    var factoryNetPriceEUR: BigDecimal? = null,
    @Column(name = "vehicledata_factorygrosspriceeur")
    var factoryGrossPriceEUR: BigDecimal? = null,
)

@Embeddable
class FVMProductionData(
    @Column(name = "vehicledata_productionnumber")
    var number: String? = null,
    @Column(name = "vehicledata_productionenddate")
    var endDate: LocalDate? = null,
    @Column(name = "vehicledata_technicalmodelyear")
    var technicalModelYear: Int? = null,
    @Column(name = "vehicledata_factory")
    var factory: String? = null,
    @Column(name = "vehicledata_factoryvw")
    var factoryVW: Int? = null,
    @Column(name = "vehicledata_quotemonth")
    var quoteMonth: Int? = null,
    @Column(name = "vehicledata_quoteyear")
    var quoteYear: Int? = null,
    @Column(name = "vehicledata_plannedproductionenddate")
    var plannedEndDate: LocalDate? = null,
    @Column(name = "vehicledata_gearboxclass")
    var gearBoxClass: String? = null,
)

@Embeddable
class FVMTechnicalData(
    @Column(name = "vehicledata_amountseats")
    var amountSeats: Int? = null,
    @Column(name = "vehicledata_enginecapacity")
    var engineCapacity: Float? = null,
    @Column(name = "vehicledata_cargovolume")
    var cargoVolume: Int? = null,
    @Column(name = "vehicledata_vehiclewidthmirrorsextended")
    var vehicleWidthMirrorsExtended: Int? = null,
    @Column(name = "vehicledata_maximumchargingpowerdc")
    var maximumChargingPowerDc: Int? = null,
    @Column(name = "vehicledata_grossvehicleweight")
    var grossVehicleWeight: Int? = null,
    @Column(name = "vehicledata_curbweighteu")
    var curbWeightEu: Int? = null,
    @Column(name = "vehicledata_totalpowerkw")
    var totalPowerKw: Int? = null,
    @Column(name = "vehicledata_curbweightdin")
    var curbWeightDin: Int? = null,
    @Column(name = "vehicledata_maximumpayload")
    var maximumPayload: Int? = null,
    @Column(name = "vehicledata_chargingtimeac22kw")
    var chargingTimeAc22Kw: Float? = null,
    @Column(name = "vehicledata_chargingtimeac11kw0100")
    var chargingTimeAc11Kw0100: Float? = null,
    @Column(name = "vehicledata_chargingtimeac96kw0100")
    var chargingTimeAc96Kw0100: Float? = null,
    @Column(name = "vehicledata_netbatterycapacity")
    var netBatteryCapacity: Float? = null,
    @Column(name = "vehicledata_grossbatterycapacity")
    var grossBatteryCapacity: Float? = null,
    @Column(name = "vehicledata_acceleration0100Kmh")
    var acceleration0100Kmh: Float? = null,
    @Column(name = "vehicledata_acceleration0100kmhlaunchcontrol")
    var acceleration0100KmhLaunchControl: Float? = null,
    @Column(name = "vehicledata_topspeed")
    var topSpeed: Int? = null,
    @Column(name = "vehicledata_height")
    var height: Int? = null,
    @Column(name = "vehicledata_widthmirrorsfolded")
    var widthMirrorsFolded: Int? = null,
    @Column(name = "vehicledata_length")
    var length: Int? = null,
    @Column(name = "vehicledata_acceleration80120kmh")
    var acceleration80120Kmh: Float? = null,
    @Column(name = "vehicledata_maxroofloadwithporscherooftransportsystem")
    var maxRoofLoadWithPorscheRoofTransportSystem: Int? = null,
    @Column(name = "vehicledata_chargingtimedcmaxpower580")
    var chargingTimeDcMaxPower580: Float? = null,
    @Column(name = "vehicledata_powerkw")
    var powerKw: Int? = null,
)

@Embeddable
class FVMMFleetData(
    @Column(name = "vehicledata_solddate")
    var soldDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_scrappeddate")
    var scrappedDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_stolendate")
    var stolenDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_soldcupcardate")
    var soldCupCarDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_approvedforscrappingdate")
    var approvedForScrappingDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_scrappedvehicleoffereddate")
    var scrappedVehicleOfferedDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_vehiclesenttosalesdate")
    var vehicleSentToSalesDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_costestimationordereddate")
    var costEstimationOrderedDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_isresidualvaluemarket")
    var isResidualValueMarket: Boolean? = null,
    @Column(name = "vehicledata_profitabilityauditdate")
    var profitabilityAuditDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_comment")
    var comment: String? = null,
    @Column(name = "vehicledata_scrapvehicle")
    var scrapVehicle: Boolean? = null,
    @Column(name = "vehicledata_racecar")
    var raceCar: Boolean,
)

@Embeddable
class FVMDeliveryData(
    @Column(name = "vehicledata_preparationdonedate")
    var preparationDoneDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_ispreparationnecessary")
    var isPreparationNecessary: Boolean? = null,
)

@Embeddable
class FVMVReturnInfo(
    @Column(name = "vehicledata_nextprocess")
    var nextProcess: String? = null,
    @Column(name = "vehicledata_isusedcar")
    var isUsedCar: Boolean? = null,
    @Column(name = "vehicledata_keyreturned")
    var keyReturned: OffsetDateTime? = null,
    @Column(name = "vehicledata_factorycarpreparationordernumber")
    var factoryCarPreparationOrderNumber: String? = null,
)

@Embeddable
class FVMTireSetChange(
    @Column(name = "vehicledata_tire_set_change_comment")
    var comment: String? = null,
    @Column(name = "vehicledata_tire_set_ordered_date")
    var orderedDate: OffsetDateTime? = null,
    @Column(name = "vehicledata_tire_set_completed_date")
    var completedDate: OffsetDateTime? = null,
)

@Embeddable
class FVMWltpData(
    @Column(name = "vehicledata_vehicleweight")
    var vehicleWeight: Int? = null,
    @Column(name = "vehicledata_electricrange")
    var electricRange: Int? = null,
    @Column(name = "vehicledata_co2combined")
    var co2Combined: Int? = null,
    @Column(name = "vehicledata_electricrangecity")
    var electricRangeCity: Int? = null,
)

@Embeddable
class FVMCurrentMileage(
    @Column(name = "vehiclecurrentmileage_mileage")
    val mileage: Int?,
    @Column(name = "vehiclecurrentmileage_readdate")
    val readDate: OffsetDateTime?,
)

@Embeddable
data class FVMVehicleVTStammData(
    @Column(name = "vehicledata_subjecttoconfidentiality")
    val subjectToConfidentiality: Boolean?,
    @Column(name = "vehicledata_confidentialityclassification")
    val confidentialityClassification: String?,
    @Column(name = "vehicledata_subjecttoconfidentialitystartdate")
    val subjectToConfidentialityStartDate: LocalDate?,
    @Column(name = "vehicledata_subjecttoconfidentialityenddate")
    val subjectToConfidentialityEndDate: LocalDate?,
    @Column(name = "vehicledata_recordfactoryexit")
    val recordFactoryExit: Boolean?,
    @Column(name = "vehicledata_camouflagerequired")
    val camouflageRequired: Boolean?,
    @Column(name = "vehicledata_internaldesignation")
    val internalDesignation: String? = null,
    @Column(name = "vehicledata_typeofusevts")
    val typeOfUseVTS: String? = null,
    @Column(name = "vehicledata_statusvts")
    val statusVTS: String? = null,
)
