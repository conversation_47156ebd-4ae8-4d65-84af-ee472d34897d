package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import java.time.OffsetDateTime

@Embeddable
class FVMVehicleLastKnownLocationData(
    @Column(name = "vehiclelocation_compoundname")
    open var compoundName: String? = null,
    @Column(name = "vehiclelocation_parkinglot")
    open var parkingLot: String? = null,
    @Column(name = "vehiclelocation_building")
    open var building: String? = null,
    @Column(name = "vehiclelocation_eventtype")
    open var eventType: String? = null,
    @Column(name = "vehiclelocation_occurredon")
    open var occurredOn: OffsetDateTime? = null,
    @Column(name = "vehiclelocation_level")
    open var level: String? = null,
    @Column(name = "vehiclelocation_comment")
    open var comment: String? = null,
)
