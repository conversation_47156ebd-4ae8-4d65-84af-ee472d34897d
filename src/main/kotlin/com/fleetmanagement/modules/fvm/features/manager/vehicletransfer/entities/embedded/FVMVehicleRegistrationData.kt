/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded

import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import java.time.ZonedDateTime

@Embeddable
class FVMVehicleRegistrationData(
    @Column(name = "vehicleregistration_licenceplate", length = 15)
    var licencePlate: String? = null,
    @Column(name = "vehicleregistration_firstregistrationdate")
    var firstRegistrationDate: ZonedDateTime? = null,
    @Column(name = "vehicleregistration_lastregistrationdate")
    var lastRegistrationDate: ZonedDateTime? = null,
    @Column(name = "vehicleregistration_registrationtype")
    var registrationType: Int? = null,
    @Column(name = "vehicleregistration_registrationstatus")
    var registrationStatus: String? = null,
    @Column(name = "vehicleregistration_registrationdate")
    var registrationDate: ZonedDateTime? = null,
    @Column(name = "vehicleregistration_lastderegistrationdate")
    var lastDeRegistrationDate: ZonedDateTime? = null,
)
