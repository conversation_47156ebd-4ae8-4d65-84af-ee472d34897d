/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.embedded

import jakarta.persistence.AttributeConverter
import jakarta.persistence.Column
import jakarta.persistence.Convert
import jakarta.persistence.Converter
import jakarta.persistence.Embeddable
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import java.time.OffsetDateTime
import java.util.*

@Embeddable
class FVMVehicleTransferData(
    @Column(name = "vehicletransfer_vehicle_transfer_key") var vehicleTransferKey: Long,
    @Column(name = "vehicletransfer_version") var version: Int,
    @Column(name = "vehicletransfer_vehicle_id") var vehicleId: UUID,
    @Column(name = "vehicletransfer_maximum_service_life_in_months")
    var maximumServiceLifeInMonths: Int? = null,
    @Column(name = "vehicletransfer_vehicle_usage_id") var vehicleUsageId: UUID? = null,
    @Column(name = "vehicletransfer_internal_contact_person")
    var internalContactPerson: String? = null,
    @Column(name = "vehicletransfer_depreciation_relevant_cost_center_id")
    var depreciationRelevantCostCenterId: UUID? = null,
    @Column(name = "vehicletransfer_using_cost_center")
    var usingCostCenter: String? = null,
    @Column(name = "vehicletransfer_internal_order_number") var internalOrderNumber: String? = null,
    @Column(name = "vehicletransfer_usage_group_id") var usageGroupId: UUID? = null,
    @Column(name = "vehicletransfer_delivery_date") var deliveryDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_return_date") var returnDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_planned_delivery_date")
    var plannedDeliveryDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_planned_return_date")
    var plannedReturnDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_latest_return_date")
    var latestReturnDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_mileage_at_delivery") var mileageAtDelivery: Int? = null,
    @Column(name = "vehicletransfer_mileage_at_return") var mileageAtReturn: Int? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "vehicletransfer_status")
    var status: FVMVehicleTransferDataStatus,
    @Column(name = "vehicletransfer_remark") var remark: String? = null,
    @Column(name = "vehicletransfer_vehicle_responsible_person")
    var vehicleResponsiblePerson: String? = null,
    @Column(name = "vehicletransfer_utilization_area") var utilizationArea: String? = null,
    @Column(name = "vehicletransfer_delivery_leipzig") var deliveryLeipzig: Boolean? = null,
    @Column(name = "vehicletransfer_desired_delivery_date")
    var desiredDeliveryDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_provision_for_delivery_comment")
    var provisionForDeliveryComment: String? = null,
    @Column(name = "vehicletransfer_delivery_comment") var deliveryComment: String? = null,
    @Column(name = "vehicletransfer_tires_comment") var tiresComment: String? = null,
    @Column(name = "vehicletransfer_desired_tire_set")
    @Enumerated(EnumType.STRING)
    var desiredTireSet: FVMVehicleTransferDataTiresSet? = null,
    @Column(name = "vehicletransfer_predecessor_latest_return_date") var predecessorLatestReturnDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_leasing_privilege") var leasingPrivilege: String? = null,
    @Column(name = "vehicletransfer_leasing_privilege_validation_successful") var leasingPrivilegeValidationSuccessful: Boolean? = null,
    @Column(name = "vehicletransfer_desired_tire_set_changed_manually") var desiredTireSetChangedManually: Boolean? = null,
    @Column(name = "vehicletransfer_maintenance_order_number") var maintenanceOrderNumber: String? = null,
    @Column(name = "vehicletransfer_license_plate") var licensePlate: String? = null,
    @Column(name = "vehicletransfer_successor_order_date") var successorOrderDate: OffsetDateTime? = null,
    @Column(name = "vehicletransfer_return_comment") var returnComment: String? = null,
    @Column(name = "vehicletransfer_service_cards")
    @Convert(converter = FVMServiceCardListConverter::class)
    var serviceCards: List<FVMVehicleTransferDataServiceCard>? = null,
    @Column(name = "vehicletransfer_registration_needed") var registrationNeeded: Boolean? = null,
    /** MHP = MHP Mitarbeiterfahrzeugprogramm */
    @Column(name = "vehicletransfer_mhp") var usageMhp: Boolean = false,
    /** vDW = vorgezogene Dienstwägen */
    @Column(name = "vehicletransfer_vdw") var usageVdw: Boolean = false,
    /** expected monthly private kilometers */
    @Column(name = "vehicletransfer_private_monthly_kilometers") var privateMonthlyKilometers: Int? = null,
)

enum class FVMVehicleTransferDataStatus {
    PLANNED,
    ACTIVE,
    FINISHED,
}

enum class FVMVehicleTransferDataServiceCard {
    FUELING_CARD,
    CHARGING_CARD,
    WASHING_CARD,
}

enum class FVMVehicleTransferDataTiresSet(
    val description: String,
) {
    SR("summer wheels"),
    WR("winter wheels"),
}

@Converter
class FVMServiceCardListConverter : AttributeConverter<List<FVMVehicleTransferDataServiceCard>, String> {
    private val delimiter = ","

    override fun convertToDatabaseColumn(attribute: List<FVMVehicleTransferDataServiceCard>?): String? =
        attribute?.joinToString(delimiter) {
            it.name
        }

    override fun convertToEntityAttribute(dbData: String?): List<FVMVehicleTransferDataServiceCard> {
        if (dbData.isNullOrBlank()) return emptyList()
        return dbData.split(delimiter).map { FVMVehicleTransferDataServiceCard.valueOf(it) }
    }
}
