/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.repository

import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.entities.FVMVehicleTransferRow
import org.springframework.data.repository.Repository
import java.util.*

interface FVMVehicleTransferRowDAO : Repository<FVMVehicleTransferRow, UUID> {
    @Suppress("ktlint:standard:function-naming")
    fun findAllByVehicleTransfer_VehicleTransferKeyIn(vehicleTransferKeys: List<Long>): List<FVMVehicleTransferRow>

    @Suppress("ktlint:standard:function-naming")
    fun findByVehicleTransfer_VehicleTransferKey(vehicleTransferKey: Long): FVMVehicleTransferRow?
}
