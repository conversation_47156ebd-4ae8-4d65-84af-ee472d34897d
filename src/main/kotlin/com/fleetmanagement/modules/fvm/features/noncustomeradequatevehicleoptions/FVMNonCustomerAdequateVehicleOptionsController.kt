package com.fleetmanagement.modules.fvm.features.noncustomeradequatevehicleoptions

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType.FVM_NON_CUSTOMER_ADEQUATE_OPTIONS_WRITE_ERROR
import com.fleetmanagement.modules.fvm.dto.noncustomeradequatevehicleoptions.FVMCreateVehicleOptionTag
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleOptionTag
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleOptionTagException.VehicleOptionTagNotFoundException
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI

/**
 * Controller manging Non-customer adequate Option such as adding or deleting.
 */
@RestController
@RequestMapping("/ui/non-customer-adequate-vehicle-options")
@AccessControlledController
class FVMNonCustomerAdequateVehicleOptionsController(
    private var nonCustomerAdequateService: FVMNonCustomerAdequateVehicleOptionsService,
) {
    @GetMapping
    fun getAllNonCustomerAdequateOptions(): ResponseEntity<APIResponse<List<FVMVehicleOptionTag>>> =
        ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(
            APIResponse(
                data = nonCustomerAdequateService.getAllNonCustomerAdequateOptions(),
            ),
        )

    @PostMapping
    fun createNonCustomerAdequateOption(
        @Valid @RequestBody fvmCreateVehicleOptionTag: FVMCreateVehicleOptionTag,
    ): ResponseEntity<APIResponse<FVMVehicleOptionTag>> {
        validateInput(fvmCreateVehicleOptionTag.id)
        return ResponseEntity.status(HttpStatus.CREATED).contentType(MediaType.APPLICATION_JSON).body(
            APIResponse(
                data = nonCustomerAdequateService.createNonCustomerAdequateOption(fvmCreateVehicleOptionTag),
            ),
        )
    }

    @DeleteMapping("/{id}")
    fun deleteNonCustomerAdequateOption(
        @PathVariable("id") optionId: String,
    ): ResponseEntity<APIResponse<String>> {
        validateInput(optionId)
        nonCustomerAdequateService.deleteNonCustomerAdequateOption(optionId)
        return ResponseEntity.ok(APIResponse(optionId))
    }

    /**
     * Handles input validation for create option tag.
     *
     * @throws ex The exception instance of [IllegalArgumentException] when input validation fails.
     */
    private fun validateInput(optionId: String) {
        require(optionId.isNotEmpty()) {
            "option tag id cannot be null or empty"
        }
    }

    /**
     * Handles exceptions when there is a failure deleting the option tag.
     *
     * This method catches [VehicleOptionTagNotFoundException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [VehicleOptionTagNotFoundException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(VehicleOptionTagNotFoundException::class)
    fun handleVehicleOptionTagNotFoundException(ex: VehicleOptionTagNotFoundException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatus(HttpStatus.NOT_FOUND).apply {
                title = "Option tag not found"
                detail = ex.message
                type = URI.create(FVM_NON_CUSTOMER_ADEQUATE_OPTIONS_WRITE_ERROR.value)
                setProperty("optionId", ex.optionId)
            }
        return ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(APIResponse.forErrors(problemDetail))
    }

    /**
     * Handles exceptions when there is bad input received from non-customer adequate option tags.
     *
     * This method catches [IllegalArgumentException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [IllegalArgumentException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(IllegalArgumentException::class)
    private fun handleNonCustomerAdequateVehicleOptionTagBadRequest(ex: IllegalArgumentException): ResponseEntity<APIResponse<Nothing?>> {
        val problemDetail =
            ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                title = "option tag can not be created or deleted"
                detail = ex.message
                type = URI.create(FVM_NON_CUSTOMER_ADEQUATE_OPTIONS_WRITE_ERROR.value)
            }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(APIResponse.forErrors(problemDetail))
    }
}
