package com.fleetmanagement.modules.fvm.features.noncustomeradequatevehicleoptions

import com.fleetmanagement.modules.fvm.dto.noncustomeradequatevehicleoptions.FVMCreateVehicleOptionTag
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleOptionTag
import com.fleetmanagement.modules.noncustomeradequate.api.NonCustomerAdequateVehicleValidationScheduler
import com.fleetmanagement.modules.vehicledata.api.CreateVehicleOptionTag
import com.fleetmanagement.modules.vehicledata.api.DeleteVehicleOptionTag
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleOptionTags
import org.springframework.stereotype.Service

/**
 * Service to manage non-customer adequate options
 */
@Service
class FVMNonCustomerAdequateVehicleOptionsService(
    private var readVehicleOptionTags: ReadVehicleOptionTags,
    private val createVehicleOptionTag: CreateVehicleOptionTag,
    private val deleteVehicleOptionTag: DeleteVehicleOptionTag,
    private val nonCustomerAdequateVehicleValidationScheduler: NonCustomerAdequateVehicleValidationScheduler,
) {
    fun getAllNonCustomerAdequateOptions(): List<FVMVehicleOptionTag> =
        readVehicleOptionTags
            .readAllNonCustomerAdequateOptionTags()
            .map { FVMVehicleOptionTag.from(it) }

    fun createNonCustomerAdequateOption(fvmCreateVehicleOptionTag: FVMCreateVehicleOptionTag): FVMVehicleOptionTag {
        val createOption = createVehicleOptionTag.createNonCustomerAdequateOptionTag(fvmCreateVehicleOptionTag.id)
        nonCustomerAdequateVehicleValidationScheduler.scheduleValidationOnOptionTagAdded(
            optionTagId = fvmCreateVehicleOptionTag.id,
        )
        return FVMVehicleOptionTag.from(createOption)
    }

    fun deleteNonCustomerAdequateOption(optionId: String) {
        deleteVehicleOptionTag.deleteNonCustomerAdequateOptionTag(optionId)
        nonCustomerAdequateVehicleValidationScheduler.scheduleValidationOnOptionTagDeleted(
            optionTagId = optionId,
        )
    }
}
