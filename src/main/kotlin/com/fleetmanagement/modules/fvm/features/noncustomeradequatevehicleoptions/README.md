# FVM Non-customer Adequate vehicles Feature
This feature enables FVM UI to manage the non-customer adequate options data in FVM system.
Adding or deleting a non-customer adequate option would schedule validation process.
For more details refer to NCA module [documentation](../../../noncustomeradequate/README.md). 


## APIs
This feature provides REST APIs to handle following use cases
1. [View NCA Options](#view-nca-options)
2. [Add NCA Option](#add-nca-option)
3. [Delete NCA Option](#delete-nca-option)

### View NCA Options
FVM UI can get all the existing NCA Options using API resource `/ui/non-customer-adequate-vehicle-options`

```plantuml
@startuml
title "FVM NCA Option Management: View NCA Options"
actor "FVM UI" as UI
box "FVM Backend"
participant "FVM Module" as FVM
participant "Non-Customer Adequate \n Module" as NCA
participant "Vehicle Data \n Module" as VD
end box
database "FVM Database" as DB

group "View All NCA Options"
UI -> FVM: Get all NCA options \n (/ui/non-customer-adequate-vehicle-options)
FVM -> FVM: Authorize
FVM -> VD: List all options flagged as NCA
VD -> DB: Fetch all Option Tags (JPA)
DB -> VD: List<JPAVehicleOptionTag>
VD -> FVM: List<VehicleOptionTagDTO>
FVM -> UI: List<FVMVehicleOptionTag>
end
@enduml
```

### Add NCA Option
FVM UI can flag existing option as NCA option using API resource `/ui/non-customer-adequate-vehicle-options`.


```plantuml
title "FVM NCA Option Management: Add NCA Option"
actor "FVM UI" as UI
box "FVM Backend"
participant "FVM Module" as FVM
participant "Non-Customer Adequate \n Module" as NCA
participant "Vehicle Data \n Module" as VD
end box
database "FVM Database" as DB

group "Add NCA Option"
UI -> FVM: Add NCA option \n (/ui/non-customer-adequate-vehicle-options)
FVM -> FVM: Authorize
FVM -> VD: Flag option tag as NCA
VD -> DB: Flag option tag as NCA
DB -> VD: Updated JPAVehicleOptionTag
VD -> FVM: Updated VehicleOptionTagDTO 
FVM -> NCA: Schedule NCA validation job for \n all the existing vehicles which are having the \n added option tag
FVM -> UI: Updated FVMVehicleOptionTag
end
@enduml
```


### Delete NCA Option
FVM UI can delete the NCA flag on option tag using API resource `/ui/non-customer-adequate-vehicle-options`. <br>
This will not delete the option tag, instead only NCA flag is deleted


```plantuml
title "FVM NCA Option Management: Delete NCA Option"
actor "FVM UI" as UI
box "FVM Backend"
participant "FVM Module" as FVM
participant "Non-Customer Adequate \n Module" as NCA
participant "Vehicle Data \n Module" as VD
end box
database "FVM Database" as DB

group "Delete NCA Option"
UI -> FVM: Delete NCA flag on option \n (/ui/non-customer-adequate-vehicle-options)
FVM -> FVM: Authorize
FVM -> VD: Delete NCA flag on option tag
VD -> DB: Delete NCA flag on option tag
FVM -> NCA: Schedule NCA validation job \n to re-check all the NCA vehicles which are having the \n added option tag
FVM -> UI: Response 204
end
@enduml
```
