/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.passthrough

import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.fvm.dto.api.APIRequest
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.vehicleregistration.FVMVehicleRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateBrieflistOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateVehicleRegistration
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import jakarta.validation.Valid
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestPart
import org.springframework.web.bind.annotation.RestController
import org.springframework.web.multipart.MultipartFile
import java.util.UUID

@RestController
@RequestMapping(value = ["/ui/vehicleregistration"])
@AccessControlledController
class FVMVehicleRegistrationController(
    private val vehicleRegistrationService: FVMVehicleRegistrationService,
) {
    @PostMapping("/orders/search", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_READ)")
    fun search(
        @RequestBody agGridRequest: SearchRequest,
    ): ResponseEntity<APIResponse<List<FVMVehicleRegistrationOrder>>> = ResponseEntity.ok(vehicleRegistrationService.search(agGridRequest))

    @GetMapping("/orders")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_READ)")
    fun getOrders(): ResponseEntity<APIResponse<List<FVMVehicleRegistrationOrder>>> =
        ResponseEntity.ok(vehicleRegistrationService.getUncompletedOrders())

    @GetMapping("/orders/completed/{vehicleId}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_READ)")
    fun getOrderBy(
        @PathVariable("vehicleId") vehicleId: UUID,
    ): ResponseEntity<APIResponse<FVMVehicleRegistrationOrder?>> = ResponseEntity.ok(vehicleRegistrationService.getLatestOrderBy(vehicleId))

    @PutMapping("/orders")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_WRITE)")
    fun putOrders(
        @Valid @RequestBody body: APIRequest<List<FVMVehicleRegistrationOrder>>,
    ): ResponseEntity<APIResponse<List<FVMVehicleRegistrationOrder>>> = ResponseEntity.ok(vehicleRegistrationService.putOrders(body))

    @PostMapping("/orders/renew-test-numbers")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_WRITE)")
    fun renewTestNumbers(): ResponseEntity<APIResponse<List<FVMVehicleRegistrationOrder>>> =
        ResponseEntity.ok(vehicleRegistrationService.renewTestNumbers())

    @PostMapping("/orders", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_WRITE)")
    fun createRegistrationOrders(
        @RequestBody orderList: List<CreateVehicleRegistration>,
    ): ResponseEntity<APIResponse<List<FVMVehicleRegistrationOrder>>> =
        ResponseEntity.ok(vehicleRegistrationService.createRegistrationOrders(orderList))

    @PostMapping("/orders", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_WRITE)")
    fun createRegistrationOrders(
        @RequestPart("file") file: MultipartFile,
    ): ResponseEntity<APIResponse<List<FVMVehicleRegistrationOrder>>> =
        ResponseEntity.ok(vehicleRegistrationService.createRegistrationOrders(file))

    @PostMapping("/brieflist", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_WRITE)")
    fun createOrdersWithoutRegistration(
        @RequestBody orderList: List<CreateBrieflistOrder>,
    ): ResponseEntity<APIResponse<List<FVMVehicleRegistrationOrder>>> =
        ResponseEntity.ok(vehicleRegistrationService.createOrdersWithoutRegistration(orderList))

    @PostMapping("/brieflist", consumes = [MediaType.MULTIPART_FORM_DATA_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_WRITE)")
    fun createOrdersWithoutRegistration(
        @RequestPart("file") file: MultipartFile,
    ): ResponseEntity<APIResponse<List<FVMVehicleRegistrationOrder>>> =
        ResponseEntity.ok(vehicleRegistrationService.createOrdersWithoutRegistration(file))

    @DeleteMapping("/orders/{id}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_REGISTRATION_DELETE)")
    fun softDeleteOrder(
        @PathVariable id: Long,
    ): ResponseEntity<APIResponse<FVMVehicleRegistrationOrder>> = ResponseEntity.ok(vehicleRegistrationService.softDeleteOrder(id))
}
