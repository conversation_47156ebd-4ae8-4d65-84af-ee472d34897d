/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.passthrough

import com.fleetmanagement.integrations.aggrid.api.dto.SearchRequest
import com.fleetmanagement.modules.fvm.dto.api.APIRequest
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.vehicleregistration.FVMVehicleRegistrationMapper
import com.fleetmanagement.modules.fvm.dto.vehicleregistration.FVMVehicleRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.WriteRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateBrieflistOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateVehicleRegistration
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIRequest
import com.fleetmanagement.modules.vehicleregistration.features.RenewKBANumbersService
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.web.multipart.MultipartFile
import java.util.UUID

@Service
class FVMVehicleRegistrationService(
    private val readRegistrationOrder: ReadRegistrationOrder,
    private val writeRegistrationOrder: WriteRegistrationOrder,
    private val renewKBANumbersService: RenewKBANumbersService,
) {
    private val logger = LoggerFactory.getLogger(FVMVehicleRegistrationService::class.java)

    fun getUncompletedOrders(): APIResponse<List<FVMVehicleRegistrationOrder>> {
        val response = readRegistrationOrder.getUncompletedOrders()

        return APIResponse(
            data = FVMVehicleRegistrationMapper.INSTANCE.map(response.data),
        )
    }

    fun search(searchRequest: SearchRequest): APIResponse<List<FVMVehicleRegistrationOrder>> {
        val response = readRegistrationOrder.search(searchRequest)
        return APIResponse(
            data = FVMVehicleRegistrationMapper.INSTANCE.map(response.data),
            errors = response.errors,
            rowCount = response.rowCount,
        )
    }

    fun getLatestOrderBy(vehicleId: UUID): APIResponse<FVMVehicleRegistrationOrder?> =
        runCatching {
            readRegistrationOrder
                .getLatestOrderBy(vehicleId)
                .data
                ?.let { FVMVehicleRegistrationMapper.INSTANCE.map(it) }
        }.fold(
            onSuccess = { APIResponse(it) },
            onFailure = { e ->
                logger.error("Error while getting completed registration order for vehicle id $vehicleId", e)
                APIResponse(null)
            },
        )

    fun putOrders(body: APIRequest<List<FVMVehicleRegistrationOrder>>): APIResponse<List<FVMVehicleRegistrationOrder>> {
        val mappedRequest = body.data.map { it.toVehicleRegistrationOrder() }
        val response = writeRegistrationOrder.putOrders(VehicleRegistrationAPIRequest(mappedRequest))
        return APIResponse(
            data = FVMVehicleRegistrationMapper.INSTANCE.map(response.data),
            errors = response.errors,
        )
    }

    fun renewTestNumbers(): APIResponse<List<FVMVehicleRegistrationOrder>> {
        val response = renewKBANumbersService.renewTestNumbers()
        return APIResponse(
            data = FVMVehicleRegistrationMapper.INSTANCE.map(response.data),
            response.errors,
        )
    }

    fun createRegistrationOrders(orders: List<CreateVehicleRegistration>): APIResponse<List<FVMVehicleRegistrationOrder>> {
        val response = writeRegistrationOrder.createRegistrationOrders(orders)
        return APIResponse(
            data = FVMVehicleRegistrationMapper.INSTANCE.map(response.data),
            response.errors,
        )
    }

    fun createRegistrationOrders(file: MultipartFile): APIResponse<List<FVMVehicleRegistrationOrder>> {
        val response = writeRegistrationOrder.createRegistrationOrders(file)
        return APIResponse(
            data = FVMVehicleRegistrationMapper.INSTANCE.map(response.data),
            response.errors,
        )
    }

    fun createOrdersWithoutRegistration(orders: List<CreateBrieflistOrder>): APIResponse<List<FVMVehicleRegistrationOrder>> {
        val response = writeRegistrationOrder.createOrdersWithoutRegistration(orders)
        return APIResponse(
            data = FVMVehicleRegistrationMapper.INSTANCE.map(response.data),
            response.errors,
        )
    }

    fun createOrdersWithoutRegistration(file: MultipartFile): APIResponse<List<FVMVehicleRegistrationOrder>> {
        val response = writeRegistrationOrder.createOrdersWithoutRegistration(file)
        return APIResponse(
            data = FVMVehicleRegistrationMapper.INSTANCE.map(response.data),
            response.errors,
        )
    }

    fun softDeleteOrder(id: Long): APIResponse<FVMVehicleRegistrationOrder> {
        val response = writeRegistrationOrder.softDeleteOrder(id)
        return APIResponse(
            data = FVMVehicleRegistrationMapper.INSTANCE.map(response.data),
            response.errors,
        )
    }
}
