/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.peopledetails

import com.fleetmanagement.modules.fvm.dto.PeopleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.peopledata.PeopleMapper
import com.fleetmanagement.modules.fvm.dto.peopledata.PersonHistory
import com.fleetmanagement.modules.fvm.features.peopledetails.exceptions.PeopleDetailNotFoundException
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehiclepersonhistory.ReadVehiclePersonHistory
import com.fleetmanagement.modules.vehicleperson.api.readvehiclepersonhistory.ReadVehiclePersonHistoryException
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI

@RestController
@RequestMapping(value = ["/ui/people"])
@AccessControlledController
class FVMPeopleDetailsController(
    private val vehiclePersonDetailReadService: ReadVehiclePersonDetailByEmployeeNumber,
    private val vehiclePersonHistory: ReadVehiclePersonHistory,
) {
    private val logger = LoggerFactory.getLogger(FVMPeopleDetailsController::class.java)

    @GetMapping("/{employeeNumber}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_PEOPLE_READ)")
    fun peopleDetailBy(
        @PathVariable("employeeNumber") @Valid employeeNumber: String,
    ): ResponseEntity<APIResponse<PeopleUIRepresentation>> {
        val peopleData = vehiclePersonDetailReadService.readVehiclePersonDetailByEmployeeNumber(employeeNumber)

        if (peopleData != null) {
            return ResponseEntity.ok(APIResponse(data = PeopleUIRepresentation(peopleData = PeopleMapper.INSTANCE.map(peopleData))))
        }

        throw PeopleDetailNotFoundException(employeeNumber)
    }

    @GetMapping("/{employeeNumber}/person-history")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_PEOPLE_READ)")
    fun peopleHistoryBy(
        @PathVariable("employeeNumber") @Valid employeeNumber: String,
    ): ResponseEntity<APIResponse<List<PersonHistory>>> {
        val history = vehiclePersonHistory.readVehiclePersonHistory(employeeNumber)
        return ResponseEntity.ok(APIResponse(data = PeopleMapper.INSTANCE.map(history)))
    }

    @ExceptionHandler(ReadVehiclePersonHistoryException::class)
    fun handleReadVehiclePersonHistoryException(ex: ReadVehiclePersonHistoryException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("ReadVehiclePersonHistoryException occurred", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "Error while fetching person history",
                ).apply {
                    type = URI.create(ErrorType.FVM_PERSON_HISTORY_FETCH_ERROR.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }
}
