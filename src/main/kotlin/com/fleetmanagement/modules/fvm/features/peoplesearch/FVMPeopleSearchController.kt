/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.peoplesearch

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.peopledata.PeopleMapper
import com.fleetmanagement.modules.fvm.dto.peopledata.PeopleSearchResult
import com.fleetmanagement.modules.vehicleperson.api.peoplesearch.PeopleSearch
import com.fleetmanagement.modules.vehicleperson.api.peoplesearch.PeopleSearchException
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.net.URI

@RestController
@RequestMapping(value = ["/ui/people"])
@AccessControlledController
class FVMPeopleSearchController(
    private val peopleSearch: PeopleSearch,
) {
    private val logger = LoggerFactory.getLogger(FVMPeopleSearchController::class.java)

    @GetMapping
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_PEOPLE_READ)")
    fun peopleSearch(
        @RequestParam(value = "employeeNumber") employeeNumber: String?,
        @RequestParam(value = "firstName") firstName: String?,
        @RequestParam(value = "lastName") lastName: String?,
        @RequestParam(value = "companyEmail") companyEmail: String?,
    ): ResponseEntity<APIResponse<List<PeopleSearchResult>>> {
        val peopleSearchResult =
            peopleSearch
                .peopleSearch(
                    employeeNumber = employeeNumber,
                    firstName = firstName,
                    lastName = lastName,
                    companyEmail = companyEmail,
                ).map { PeopleMapper.INSTANCE.map(it) }

        return ResponseEntity.ok(APIResponse(data = peopleSearchResult))
    }

    @ExceptionHandler(PeopleSearchException::class)
    fun handlePeopleSearchException(ex: PeopleSearchException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("PeopleSearchException occurred", ex)
        val problemDetail =
            ProblemDetail
                .forStatusAndDetail(
                    HttpStatus.INTERNAL_SERVER_ERROR,
                    "Error while fetching people search results",
                ).apply {
                    type = URI.create(ErrorType.FVM_PEOPLE_SEARCH_ERROR.value)
                }
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(APIResponse.forErrors(problemDetail))
    }
}
