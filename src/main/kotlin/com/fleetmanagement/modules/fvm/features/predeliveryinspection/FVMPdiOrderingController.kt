/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.predeliveryinspection

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.predeliveryinspection.application.port.SendPdiOrderingEmailUseCase
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.*

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMPdiOrderingController(
    private val sendPdiOrderingEmailUseCase: SendPdiOrderingEmailUseCase,
) {
    @PostMapping("/predelivery-inspection/ordering-email")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_PRE_DELIVERY_INSPECTION_WRITE)")
    fun sendPdiOrderingEmails(
        @RequestBody vehicleIds: List<UUID>,
    ): ResponseEntity<APIResponse<Nothing?>> {
        if (vehicleIds.isEmpty()) return ResponseEntity(APIResponse(data = null), HttpStatus.OK)

        val validationError = sendPdiOrderingEmailUseCase.createAndSendPdiEmailsAsync(vehicleIds = vehicleIds)

        return ResponseEntity(
            APIResponse(data = null, warnings = validationError.map { it.toProblemDetail() }),
            if (validationError.isEmpty()) HttpStatus.OK else HttpStatus.CONFLICT,
        )
    }
}
