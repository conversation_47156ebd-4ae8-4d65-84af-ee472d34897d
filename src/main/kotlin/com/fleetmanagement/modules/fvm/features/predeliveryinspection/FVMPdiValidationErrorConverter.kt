/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.predeliveryinspection

import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.predeliveryinspection.application.PdiValidationError
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import java.net.URI

fun PdiValidationError.toProblemDetail(): ProblemDetail {
    val message = this.detail

    return when (this.type) {
        PdiValidationError.ValidationErrorType.PDI_MISSING_PLANNED_DATE ->
            ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                title =
                    "PreDeliveryInspection with id [${<EMAIL>}] " +
                    "is missing plannedDate for vehicle with VIN [${<EMAIL>[PdiValidationError.PROPERTY_VIN]}}]."
                detail = message
                type = URI.create(ErrorType.FVM_PRE_DELIVERY_INSPECTION_PDI_INVALID_PLANNED_DATE.value)
            }

        PdiValidationError.ValidationErrorType.PDI_EMAIL_GENERATION_FAILED ->
            ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                title =
                    "Email generation failed for vehicles with vin [${<EMAIL>[PdiValidationError.PROPERTY_VINS]}]."
                detail = message
                type = URI.create(ErrorType.FVM_PRE_DELIVERY_INSPECTION_PDI_EMAIL_GENERATION_FAILED.value)
            }
    }
}
