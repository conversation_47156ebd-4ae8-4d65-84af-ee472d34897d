/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.predeliveryinspection

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMWinterTiresTimeframe
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMWinterTiresTimeframeUpdate
import com.fleetmanagement.modules.fvm.features.predeliveryinspection.validator.FVMWinterTiresTimeframeValidator
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMWinterTiresTimeframeController(
    private val service: FVMWinterTiresTimeframeService,
    private val validator: FVMWinterTiresTimeframeValidator,
) {
    @GetMapping("/predelivery-inspection/winter-tires-timeframe")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_PRE_DELIVERY_INSPECTION_READ)")
    fun getSchedule(): ResponseEntity<APIResponse<List<FVMWinterTiresTimeframe>>> {
        val schedule = service.getSchedule()
        return ResponseEntity.ok(APIResponse(schedule))
    }

    @PutMapping("/predelivery-inspection/winter-tires-timeframe")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_PRE_DELIVERY_INSPECTION_WRITE)")
    fun updateWinterTiresTimeframes(
        @RequestBody updateWinterTiresTimeframe: FVMWinterTiresTimeframeUpdate,
    ): ResponseEntity<APIResponse<List<FVMWinterTiresTimeframe>>> {
        val validationError = updateWinterTiresTimeframe.data.mapNotNull { validator.validate(it) }
        if (validationError.isNotEmpty()) {
            return ResponseEntity(APIResponse(data = listOf(), errors = validationError), HttpStatus.CONFLICT)
        }
        val response =
            service.updateWinterTiresTimeFrames(
                updateWinterTiresTimeframe.data,
            )
        return when {
            !response.errors.isNullOrEmpty() -> ResponseEntity(response, HttpStatus.MULTI_STATUS)
            else -> ResponseEntity(response, HttpStatus.OK)
        }
    }
}
