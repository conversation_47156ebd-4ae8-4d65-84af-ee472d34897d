/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.predeliveryinspection

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.*
import com.fleetmanagement.modules.predeliveryinspection.application.port.ReadWinterTiresScheduleUseCase
import com.fleetmanagement.modules.predeliveryinspection.application.port.UpdateWinterTiresTimeframeUseCase
import com.fleetmanagement.modules.predeliveryinspection.domain.WinterTiresTimeframeNotFoundException
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.stereotype.Service
import java.net.URI

@Service
class FVMWinterTiresTimeframeService(
    private val readWinterTiresScheduleUseCase: ReadWinterTiresScheduleUseCase,
    private val updateWinterTiresTimeframeUseCase: UpdateWinterTiresTimeframeUseCase,
) {
    fun getSchedule(): List<FVMWinterTiresTimeframe> {
        val schedule = readWinterTiresScheduleUseCase.getAllWinterTiresSchedule()
        val fvmSchedule = schedule.map { it.toFVMWinterTiresTimeframe() }
        return fvmSchedule
    }

    fun updateWinterTiresTimeFrames(
        winterTiresTimeframesToUpdate: List<FVMWinterTiresTimeframe>,
    ): APIResponse<List<FVMWinterTiresTimeframe>> {
        val winterTiresTimeframeUpdatedList = mutableListOf<FVMWinterTiresTimeframe>()
        val errors =
            winterTiresTimeframesToUpdate.mapNotNull {
                try {
                    val winterTiresTimeframeDataUpdate =
                        updateWinterTiresTimeframeUseCase.update(it.toWinterTiresTimeframeDto())
                    winterTiresTimeframeUpdatedList.add(winterTiresTimeframeDataUpdate.toFVMWinterTiresTimeframe())
                    null
                } catch (exception: DataIntegrityViolationException) {
                    ProblemDetail.forStatusAndDetail(HttpStatus.CONFLICT, exception.message).apply {
                        type = URI.create(ErrorType.FVM_DATA_INTEGRITY_VIOLATION.value)
                        properties = mapOf("id" to it.id)
                    }
                } catch (exception: WinterTiresTimeframeNotFoundException) {
                    ProblemDetail
                        .forStatusAndDetail(HttpStatus.NOT_FOUND, exception.message)
                        .apply {
                            type = URI.create(ErrorType.FVM_WINTER_TIRES_TIMEFRAME_NOTFOUND.value)
                            properties = mapOf("id" to it.id)
                        }
                }
            }
        return if (errors.isEmpty()) {
            APIResponse(
                data = winterTiresTimeframeUpdatedList,
            )
        } else {
            APIResponse(data = winterTiresTimeframeUpdatedList, errors = errors)
        }
    }
}
