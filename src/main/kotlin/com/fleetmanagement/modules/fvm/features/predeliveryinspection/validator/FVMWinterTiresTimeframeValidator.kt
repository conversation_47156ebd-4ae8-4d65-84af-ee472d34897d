/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.fvm.features.predeliveryinspection.validator

import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.predeliveryinspection.FVMWinterTiresTimeframe
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.stereotype.Component
import java.net.URI

interface Validator {
    fun validate(winterTiresTimeframe: FVMWinterTiresTimeframe): ProblemDetail?
}

@Component
class FVMWinterTiresTimeframeValidator : Validator {
    override fun validate(winterTiresTimeframe: FVMWinterTiresTimeframe): ProblemDetail? {
        val fromDate = winterTiresTimeframe.fromDate
        val toDate = winterTiresTimeframe.toDate
        if (fromDate.isAfter(toDate)) {
            return ProblemDetail.forStatusAndDetail(HttpStatus.CONFLICT, "from date $fromDate should be before to date$toDate").apply {
                type = URI.create(ErrorType.FVM_DATA_INTEGRITY_VIOLATION.value)
                properties = mapOf("fromDate" to fromDate, "toDate" to toDate)
            }
        }
        return null
    }
}
