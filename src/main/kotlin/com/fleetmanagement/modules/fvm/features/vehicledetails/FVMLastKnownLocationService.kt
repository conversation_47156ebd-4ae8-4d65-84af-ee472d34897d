/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.vehicledetails

import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocation
import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocationMapper
import com.fleetmanagement.modules.vehiclelocation.api.LastKnownLocation
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.*

@Service
class FVMLastKnownLocationService(
    val lastKnownLocationService: LastKnownLocation,
) {
    private val mapper = FVMVehicleLocationMapper.INSTANCE

    private val logger = LoggerFactory.getLogger(FVMLastKnownLocationService::class.java)

    fun getLastKnownLocationOfVehicle(vehicleId: UUID): FVMVehicleLocation? =
        try {
            val lastKnownLocation = lastKnownLocationService.findLastKnownVehicleLocationBy(vehicleId)
            lastKnownLocation?.let { mapper.map(it) }
        } catch (e: Exception) {
            logger.error("Error while getting last known location for vehicle id $vehicleId", e)
            null
        }
}
