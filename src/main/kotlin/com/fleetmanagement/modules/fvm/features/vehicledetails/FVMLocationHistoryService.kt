package com.fleetmanagement.modules.fvm.features.vehicledetails

import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocation
import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocationMapper
import com.fleetmanagement.modules.vehiclelocation.api.LocationHistory
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.*

@Service
class FVMLocationHistoryService(
    private val locationHistory: LocationHistory,
) {
    private val mapper = FVMVehicleLocationMapper.INSTANCE

    private val logger = LoggerFactory.getLogger(FVMLocationHistoryService::class.java)

    fun getLocationHistoryById(id: UUID): List<FVMVehicleLocation>? {
        try {
            return locationHistory.getLocationHistoryBy(id).map { mapper.map(it) }
        } catch (e: Exception) {
            logger.error("Error while getting location history for vehicle id $id", e)
            throw LocationHistoryFetchException(id, e)
        }
    }
}

class LocationHistoryFetchException(
    id: UUID,
    e: Exception,
) : RuntimeException("Error while getting location history for vehicle id $id", e)
