package com.fleetmanagement.modules.fvm.features.vehicledetails

import com.fleetmanagement.modules.fvm.dto.mileagereading.FVMMileageReading
import com.fleetmanagement.modules.fvm.dto.mileagereading.FVMMileageReadingMapper
import com.fleetmanagement.modules.vehicledata.api.ReadMileageReadings
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class FVMMileageReadingService(
    private val readMileageReadings: ReadMileageReadings,
) {
    private val mapper = FVMMileageReadingMapper.INSTANCE
    private val logger = LoggerFactory.getLogger(FVMMileageReadingService::class.java)

    fun getMileageReadingsByVehicleId(vehicleId: UUID): List<FVMMileageReading> {
        try {
            return readMileageReadings.readMileageReadings(vehicleId).map { mapper.map(it) }
        } catch (e: Exception) {
            logger.error("Error while getting mileage readings for vehicle id $vehicleId", e)
            throw MileageReadingFetchException(vehicleId, e)
        }
    }
}

class MileageReadingFetchException(
    id: UUID,
    e: Exception,
) : RuntimeException("Error while getting mileage reading for vehicle id $id", e)
