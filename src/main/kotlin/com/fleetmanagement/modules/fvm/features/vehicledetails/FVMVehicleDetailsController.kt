/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.vehicledetails

import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.mileagereading.FVMMileageReading
import com.fleetmanagement.modules.fvm.dto.vehicledetails.FVMUsageDetail
import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocation
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI
import java.util.UUID

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMVehicleDetailsController(
    private val service: FVMVehicleDetailsService,
) {
    private val logger = LoggerFactory.getLogger(FVMVehicleDetailsController::class.java)

    @GetMapping("/vehicles/{id}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    @JsonView(VehicleJsonView.VehicleDetailsPage::class)
    fun vehicleDetails(
        @PathVariable("id") @Valid vehicleId: UUID,
    ): ResponseEntity<APIResponse<VehicleUIRepresentation>> {
        val vehicleDetails = service.getVehicleDetailsById(vehicleId)
        return ResponseEntity.ok(APIResponse(vehicleDetails))
    }

    @PostMapping("/vehicles/{id}/refresh")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    @JsonView(VehicleJsonView.VehicleDetailsPage::class)
    fun refreshBy(
        @PathVariable("id") @Valid vehicleId: UUID,
    ): ResponseEntity<APIResponse<VehicleUIRepresentation>> {
        val vehicleDetails = service.refreshVehicleDetailsById(vehicleId)
        return ResponseEntity.ok(APIResponse(vehicleDetails))
    }

    @JsonView(VehicleJsonView.VehicleDetailsPage::class)
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_TRANSFER_READ)")
    @GetMapping("/vehicles/{id}/usage")
    fun getVehicleTransfer(
        @PathVariable("id") @Valid vehicleId: UUID,
    ): ResponseEntity<APIResponse<FVMUsageDetail>> {
        val vehicleTransferDetails = service.getVehicleTransferDetailUsageById(vehicleId)
        return ResponseEntity.ok(APIResponse(vehicleTransferDetails))
    }

    @GetMapping("/vehicles/{id}/location-history")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    @JsonView(VehicleJsonView.VehicleDetailsPage::class)
    fun locationHistory(
        @PathVariable("id") @Valid vehicleId: UUID,
    ): ResponseEntity<APIResponse<List<FVMVehicleLocation>?>> {
        val locations = service.getLocationHistoryById(vehicleId)
        return ResponseEntity.ok(APIResponse(locations))
    }

    @GetMapping("/vehicles/{id}/mileage-readings")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    @JsonView(VehicleJsonView.VehicleDetailsPage::class)
    fun mileageReadings(
        @PathVariable("id") @Valid vehicleId: UUID,
    ): ResponseEntity<APIResponse<List<FVMMileageReading>?>> {
        val mileageReadings = service.getMileageReadingsByVehicleId(vehicleId)
        return ResponseEntity.ok(APIResponse(mileageReadings))
    }

    @ExceptionHandler(LocationHistoryFetchException::class)
    fun handleLocationHistoryFetchException(ex: LocationHistoryFetchException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("LocationHistoryFetchException occurred", ex)
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(
                APIResponse.forErrors(
                    ProblemDetail
                        .forStatusAndDetail(
                            HttpStatus.INTERNAL_SERVER_ERROR,
                            "Error while fetching location history",
                        ).apply {
                            type = URI.create(ErrorType.FVM_LOCATION_HISTORY_FETCH_ERROR.value)
                        },
                ),
            )
    }

    @ExceptionHandler(VehicleTransferDetailUsageException::class)
    fun handleVehicleTransferDetailUsageException(ex: VehicleTransferDetailUsageException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("VehicleTransferDetailUsageException occurred", ex)
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(
                APIResponse.forErrors(
                    ProblemDetail
                        .forStatusAndDetail(
                            HttpStatus.INTERNAL_SERVER_ERROR,
                            "Error while fetching vehicle transfer detail",
                        ).apply {
                            type = URI.create(ErrorType.FVM_TRANSFER_DETAIL_USAGE_FETCH_ERROR.value)
                        },
                ),
            )
    }

    @ExceptionHandler(MileageReadingFetchException::class)
    fun handleMileageReadFetchException(ex: MileageReadingFetchException): ResponseEntity<APIResponse<Nothing?>> {
        logger.error("MileageReadFetchException occurred", ex)
        return ResponseEntity
            .status(HttpStatus.INTERNAL_SERVER_ERROR)
            .body(
                APIResponse.forErrors(
                    ProblemDetail
                        .forStatusAndDetail(
                            HttpStatus.INTERNAL_SERVER_ERROR,
                            "Error while fetching mileage readings",
                        ).apply {
                            type = URI.create(ErrorType.FVM_MILEAGE_READING_FETCH_ERROR.value)
                        },
                ),
            )
    }
}
