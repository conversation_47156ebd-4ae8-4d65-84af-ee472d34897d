/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.vehicledetails

import com.fleetmanagement.modules.fvm.dto.VehicleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.mileagereading.FVMMileageReading
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleDataMapper
import com.fleetmanagement.modules.fvm.dto.vehicledetails.FVMUsageDetail
import com.fleetmanagement.modules.fvm.dto.vehicledetails.FVMVehicleTransferDetailUsageMapper
import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocation
import com.fleetmanagement.modules.fvm.dto.vehiclesales.FVMVehicleSales
import com.fleetmanagement.modules.fvm.dto.vehiclesales.toFVMVehicleSales
import com.fleetmanagement.modules.fvm.features.passthrough.FVMVehicleRegistrationService
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.RefreshVehicleByVGUID
import com.fleetmanagement.modules.vehicledata.api.dtos.CurrentMileageDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehiclesales.api.ReadVehicleInvoices
import com.fleetmanagement.modules.vehiclesales.api.ReadVehicleSaleByVehicleId
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleSalesNotFoundException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.web.server.ResponseStatusException
import java.net.URLDecoder
import java.nio.charset.StandardCharsets
import java.util.UUID

@Service
class FVMVehicleDetailsService(
    private val vehicleReadByIdService: ReadVehicleByVehicleId,
    private val vehicleRefreshByVguidService: RefreshVehicleByVGUID,
    private val registrationService: FVMVehicleRegistrationService,
    private val vehicleLastKnownLocationService: FVMLastKnownLocationService,
    private val locationHistoryService: FVMLocationHistoryService,
    private val vehiclesTransferUsageDetail: FVMVehiclesTransferUsageDetail,
    private val mileageReadingService: FVMMileageReadingService,
    private val vehicleSalesInvoiceService: ReadVehicleInvoices,
    private val readVehicleSaleByVehicleId: ReadVehicleSaleByVehicleId,
) {
    private val logger = LoggerFactory.getLogger(FVMVehicleDetailsService::class.java)

    fun getVehicleDetailsById(vehicleId: UUID): VehicleUIRepresentation {
        val vehicleDetails =
            vehicleReadByIdService.readVehicleById(vehicleId) ?: throw ResponseStatusException(
                HttpStatus.NOT_FOUND,
                "Vehicle with $vehicleId not found",
            )
        val currentMileage = getCurrentMileageByVehicleId(vehicleId)?.toCurrentMileageDTO()
        val vehicleDetailsWithCurrentMileage = vehicleDetails.copy(currentMileage = currentMileage)
        val fvmVehicleSales = getVehicleSalesData(vehicleId)

        return VehicleUIRepresentation(
            vehicle = FVMVehicleDataMapper.INSTANCE.map(vehicleDetailsWithCurrentMileage),
            vehicleSales = fvmVehicleSales,
            vehicleRegistration = registrationService.getLatestOrderBy(vehicleId).data,
            vehicleLastKnownLocation = vehicleLastKnownLocationService.getLastKnownLocationOfVehicle(vehicleId),
        )
    }

    private fun getVehicleSalesData(vehicleId: UUID): FVMVehicleSales? {
        var fvmVehicleSales: FVMVehicleSales?
        val vehicleCurrentSalesInvoice = vehicleSalesInvoiceService.readCurrentInvoiceBy(vehicleId)
        fvmVehicleSales = vehicleCurrentSalesInvoice?.toFVMVehicleSales()
        if (vehicleCurrentSalesInvoice == null) {
            try {
                val vehicleSale = readVehicleSaleByVehicleId.readVehicleSalesBy(vehicleId)
                fvmVehicleSales = vehicleSale.toFVMVehicleSales()
            } catch (ex: VehicleSalesNotFoundException) {
                logger.info("No vehicle sales data found for vehicle id $vehicleId: ${ex.message}")
            }
        }
        return fvmVehicleSales
    }

    fun refreshVehicleDetailsById(vehicleId: UUID): VehicleUIRepresentation {
        val vehicle =
            vehicleReadByIdService.readVehicleById(vehicleId) ?: throw ResponseStatusException(
                HttpStatus.NOT_FOUND,
                "Vehicle with $vehicleId not found",
            )
        var vehicleDetails: VehicleDTO? = null
        try {
            val decodedVguid = URLDecoder.decode(vehicle.vguid, StandardCharsets.UTF_8)
            vehicleDetails = vehicleRefreshByVguidService.refreshVehicleByVGUID(decodedVguid)
        } catch (ex: Exception) {
            logger.error("Error while refreshing vehicle with vguid: ${vehicle.vguid}")
        }
        return VehicleUIRepresentation(
            vehicle = FVMVehicleDataMapper.INSTANCE.map(vehicleDetails ?: vehicle),
            vehicleRegistration = vehicleDetails?.let { registrationService.getLatestOrderBy(it.id).data },
        )
    }

    fun getLocationHistoryById(vehicleId: UUID): List<FVMVehicleLocation>? = locationHistoryService.getLocationHistoryById(vehicleId)

    fun getVehicleTransferDetailUsageById(vehicleId: UUID): FVMUsageDetail {
        try {
            val vehicleTransfersDetailUsage = vehiclesTransferUsageDetail.getVehicleTransfersUsage(vehicleId)
            val transfers =
                FVMVehicleTransferDetailUsageMapper.INSTANCE.map(transfers = vehicleTransfersDetailUsage.transfers)
            val currentTransfer =
                FVMVehicleTransferDetailUsageMapper.INSTANCE.map(
                    currentTransfer = vehicleTransfersDetailUsage.currentTransfer,
                )
            return FVMUsageDetail(
                transfers = transfers,
                currentTransfer = currentTransfer,
            )
        } catch (e: Exception) {
            logger.error("Error while getting Vehicle Transfer Detail Usage for vehicle id $vehicleId", e)
            throw VehicleTransferDetailUsageException(vehicleId, e)
        }
    }

    fun getMileageReadingsByVehicleId(vehicleId: UUID): List<FVMMileageReading> =
        mileageReadingService.getMileageReadingsByVehicleId(vehicleId).sortedByDescending { it.readDate }

    fun getCurrentMileageByVehicleId(vehicleId: UUID): FVMMileageReading? =
        mileageReadingService.getMileageReadingsByVehicleId(vehicleId).maxByOrNull { it.readDate }
}

fun FVMMileageReading.toCurrentMileageDTO(): CurrentMileageDTO =
    CurrentMileageDTO(
        mileage = mileage,
        readDate = readDate,
    )
