package com.fleetmanagement.modules.fvm.features.vehicledetails

import com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup.ReadUsageGroupUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.vehicleusage.ReadVehicleUsageUseCase
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicletransfer.application.port.CurrentVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.entities.PlannedVehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.PlannedVehicleTransferStatus
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.*

@Component
@Transactional(readOnly = true)
class FVMVehiclesTransferUsageDetail(
    private val readPlannedVehicleTransfer: ReadPlannedVehicleTransferUseCase,
    private val readVehicleTransferFinder: ReadVehicleTransferUseCase,
    private val currentVehicleTransferUseCase: CurrentVehicleTransferUseCase,
    private val readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber,
    private val readUsageGroupUseCase: ReadUsageGroupUseCase,
    private val readVehicleUsageUseCase: ReadVehicleUsageUseCase,
) {
    fun getVehicleTransfersUsage(vehicleID: UUID): VehiclesTransferDetailUsage {
        val plannedVehicleTransfers =
            readPlannedVehicleTransfer.findPlannedVehicleTransfersByVehicleId(vehicleID).filter {
                it.status !=
                    PlannedVehicleTransferStatus.DELETED
            }
        val vehicleTransfers =
            readVehicleTransferFinder.findVehicleTransfersByVehicleId(vehicleID).filter {
                it.status !=
                    VehicleTransferStatus.CANCELLED
            }

        val transfers =
            plannedVehicleTransfers.map { it.toVehicleTransferDetailUsage() } +
                vehicleTransfers.map { it.toVehicleTransferDetailUsage() }

        val currentTransferKey =
            currentVehicleTransferUseCase.retrieveCurrentTransfer(plannedVehicleTransfers, vehicleTransfers)

        val currentTransfer = currentTransferKey?.let { transfers.single { it.key == currentTransferKey.value } }

        return VehiclesTransferDetailUsage(
            transfers = transfers,
            currentTransfer = currentTransfer,
        )
    }

    private fun PlannedVehicleTransfer.toVehicleTransferDetailUsage(): VehicleTransferUsageDetail {
        val vehiclePerson =
            try {
                this.vehicleResponsiblePerson?.value?.let {
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(it)
                }
            } catch (e: ReadVehiclePersonException) {
                null
            }
        return VehicleTransferUsageDetail(
            employeeNumber = this.vehicleResponsiblePerson?.value,
            key = this.key.value,
            status = this.status.name,
            plannedReturnedDate = this.plannedReturnDate,
            returnedDate = null,
            deliveryDate = null,
            mileageAtReturn = null,
            mileageAtDelivery = null,
            plannedDeliveryDate = this.plannedDeliveryDate,
            licensePlate = this.licensePlate,
            internalOrderNumber = this.internalOrderNumber,
            usingCostCenter = this.usingCostCenter?.value,
            vehicleUsage = this.vehicleUsage?.value?.let { readVehicleUsageUseCase.findVehicleUsageById(it)?.usage },
            usageGroup = this.usageGroup?.value?.let { readUsageGroupUseCase.findUsageGroupById(it)?.description },
            maintenanceOrderNumber = this.maintenanceOrderNumber,
            firstName = vehiclePerson?.firstName,
            lastName = vehiclePerson?.lastName,
            department = vehiclePerson?.department,
            companyMobileNumber = vehiclePerson?.companyMobileNumber,
            companyEmail = vehiclePerson?.companyEmail,
            privateEmail = vehiclePerson?.privateEmail,
            leasingPrivilegeLeasing = vehiclePerson?.leasingPrivilegeLeasing,
            accountingArea = vehiclePerson?.accountingArea,
            depreciationRelevantCostCenterId = this.depreciationRelevantCostCenterId?.value,
            usageMhp = this.usageMhp,
            usageVdw = this.usageVdw,
            privateMonthlyKilometers = this.privateMonthlyKilometers,
        )
    }

    private fun VehicleTransfer.toVehicleTransferDetailUsage(current: Boolean = false): VehicleTransferUsageDetail {
        val vehiclePerson =
            try {
                this.vehicleResponsiblePerson?.value?.let {
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(it)
                }
            } catch (e: ReadVehiclePersonException) {
                null
            }
        return VehicleTransferUsageDetail(
            employeeNumber = this.vehicleResponsiblePerson?.value,
            key = this.key.value,
            status = this.status.name,
            plannedReturnedDate = this.plannedReturnDate,
            returnedDate = this.returnDate,
            deliveryDate = this.deliveryDate,
            mileageAtReturn = this.mileageAtReturn,
            mileageAtDelivery = this.mileageAtDelivery,
            plannedDeliveryDate = this.plannedDeliveryDate,
            licensePlate = this.licensePlate,
            internalOrderNumber = this.internalOrderNumber,
            usingCostCenter = this.usingCostCenter?.value,
            vehicleUsage = this.vehicleUsage?.value?.let { readVehicleUsageUseCase.findVehicleUsageById(it)?.usage },
            usageGroup = this.usageGroup?.value?.let { readUsageGroupUseCase.findUsageGroupById(it)?.description },
            maintenanceOrderNumber = this.maintenanceOrderNumber,
            firstName = vehiclePerson?.firstName,
            lastName = vehiclePerson?.lastName,
            department = vehiclePerson?.department,
            companyMobileNumber = if (current) vehiclePerson?.companyMobileNumber else null,
            companyEmail = if (current) vehiclePerson?.companyEmail else null,
            privateEmail = if (current) vehiclePerson?.privateEmail else null,
            leasingPrivilegeLeasing = if (current) vehiclePerson?.leasingPrivilegeLeasing else null,
            accountingArea = vehiclePerson?.accountingArea,
            depreciationRelevantCostCenterId = this.depreciationRelevantCostCenterId?.value,
            usageMhp = this.usageMhp,
            usageVdw = this.usageVdw,
            privateMonthlyKilometers = this.privateMonthlyKilometers,
        )
    }
}

data class VehicleTransferUsageDetail(
    val employeeNumber: String?,
    val key: Long,
    val status: String,
    val plannedReturnedDate: OffsetDateTime?,
    val returnedDate: OffsetDateTime?,
    val deliveryDate: OffsetDateTime?,
    val mileageAtReturn: Int?,
    val mileageAtDelivery: Int?,
    val plannedDeliveryDate: OffsetDateTime?,
    val licensePlate: String?,
    val internalOrderNumber: String?,
    val usingCostCenter: String?,
    val firstName: String?,
    val lastName: String?,
    val department: String?,
    val vehicleUsage: String?,
    val usageGroup: String?,
    val companyMobileNumber: String?,
    val companyEmail: String?,
    val privateEmail: String?,
    val leasingPrivilegeLeasing: String?,
    val accountingArea: String?,
    val maintenanceOrderNumber: String?,
    val depreciationRelevantCostCenterId: UUID?,
    val usageMhp: Boolean?,
    val usageVdw: Boolean?,
    val privateMonthlyKilometers: Int?,
)

data class VehiclesTransferDetailUsage(
    val transfers: List<VehicleTransferUsageDetail>,
    val currentTransfer: VehicleTransferUsageDetail?,
)

class VehicleTransferDetailUsageException(
    id: UUID,
    e: Exception,
) : RuntimeException("Error while fetching vehicle transfers for vehicle id $id", e)
