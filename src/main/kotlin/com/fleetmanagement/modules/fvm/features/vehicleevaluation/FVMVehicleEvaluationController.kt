package com.fleetmanagement.modules.fvm.features.vehicleevaluation

import com.fleetmanagement.modules.fvm.dto.VehicleUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.vehicleevaluation.FVMVehicleEvaluationRequest
import com.fleetmanagement.modules.fvm.features.manager.vehicle.entities.toVehicleUIRepresentationList
import com.fleetmanagement.modules.fvm.features.manager.vehicle.repository.DLZViewVehicleRowDAO
import com.fleetmanagement.modules.vehicleevaluation.application.port.CommissionTUVAppraisal
import com.fleetmanagement.modules.vehicleevaluation.application.port.ErrorDetail
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI

@RestController
@RequestMapping(value = ["/ui/vehicles/evaluation"])
@AccessControlledController
class FVMVehicleEvaluationController(
    private val commissionTUVAppraisal: CommissionTUVAppraisal,
    private val dLZViewVehicleRowDAO: DLZViewVehicleRowDAO,
) {
    @PostMapping
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    fun evaluate(
        @RequestBody request: FVMVehicleEvaluationRequest,
    ): ResponseEntity<APIResponse<List<VehicleUIRepresentation>>> {
        val problems =
            commissionTUVAppraisal
                .commissionTUVAppraisal(request.vehicleIds)
                .let { toProblemDetails(it) }

        val updatedRows = dLZViewVehicleRowDAO.findAllByIdIn(request.vehicleIds)
        val resultData = updatedRows.toVehicleUIRepresentationList()

        return if (problems.isEmpty()) {
            ResponseEntity.ok(
                APIResponse(
                    data = resultData,
                    rowCount = resultData.size.toLong(),
                ),
            )
        } else {
            return ResponseEntity.status(HttpStatus.CONFLICT).body(
                APIResponse(
                    data = resultData,
                    rowCount = resultData.size.toLong(),
                    errors = problems,
                ),
            )
        }
    }
}

fun toProblemDetails(errors: List<ErrorDetail>): List<ProblemDetail> {
    val tuvEmailVins = mutableSetOf<String>()
    val logisticVins = mutableSetOf<String>()
    val pdfGenerationVins = mutableSetOf<String>()

    errors.forEach {
        when (it.type) {
            ErrorDetail.VehicleEvaluationError.TUV_EMAIL_ERROR ->
                it.vin?.let { vin -> tuvEmailVins.add(vin) }

            ErrorDetail.VehicleEvaluationError.LOGISTICS_EMAIL_ERROR ->
                it.vin?.let { vin -> logisticVins.add(vin) }

            ErrorDetail.VehicleEvaluationError.PDF_GENERATION_ERROR ->
                it.vin?.let { vin -> pdfGenerationVins.add(vin) }
        }
    }

    return listOfNotNull(
        createProblemDetail(tuvEmailVins, ErrorType.FVM_VEHICLE_EVALUATION_TUV_EMAIL_ERROR),
        createProblemDetail(logisticVins, ErrorType.FVM_VEHICLE_EVALUATION_LOGISTICS_EMAIL_ERROR),
        createProblemDetail(pdfGenerationVins, ErrorType.FVM_VEHICLE_EVALUATION_PDF_GENERATION_ERROR),
    )
}

fun createProblemDetail(
    vins: Set<String>,
    errorType: ErrorType,
): ProblemDetail? =
    if (vins.isNotEmpty()) {
        ProblemDetail.forStatus(HttpStatus.INTERNAL_SERVER_ERROR).apply {
            type = URI.create(errorType.value)
            setProperty("properties", mapOf("vins" to vins))
        }
    } else {
        null
    }
