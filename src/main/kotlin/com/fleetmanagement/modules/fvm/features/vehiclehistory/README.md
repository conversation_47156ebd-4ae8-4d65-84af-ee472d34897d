# Vehicle History Module

## Overview

The Vehicle History module is responsible for processing and displaying all updates made to a vehicle in the application. These updates are tracked and stored as history, providing a detailed log of changes over time. The history data is displayed to authorized users through a REST API, allowing them to view the specific changes made to a vehicle's attributes.

## Features

- **Process Changes:** The FVM module is responsible to call the contract with vehicle history to store the changes
- **Display Changes:** Retrieve and display the vehicle's history, showing all modifications in a structured format. It contains field, old value, new value and modified user
- **Security:** Endpoint access is restricted based on permissions to ensure only authorized users can view the history

## How Changes Are Captured

Changes are captured in various ways on the application
1. When user performs an update to the vehicle attributes on the UI
2. When the system tries to pull in changes from external system synchronously
3. When there are asynchronous changes via streamzilla

At this point, the changes are performed in two **different transactions** on the FVM layer. Here, we rely on the response that is provided by the module call and then process the history.

This history data is stored in JSON format, which can later be retrieved, parsed, and displayed through the `/vehicle-history` endpoint.

## Drawbacks of Current Approach

While this approach simplifies the integration between the FVM layer and the modules, it introduces some **drawbacks** related to data consistency and transaction management:

1. **Data Consistency Issues:**
   - The update operation and capturing history are done in separate transactions. If one transaction succeeds and the other fails (e.g., if the module call update succeeds but the history write fails), it can lead to inconsistencies in the vehicle's change history

2. **No Guaranteed Delivery for History Writes:**
   - Since the history is written in a separate transaction, there is no automatic retry mechanism for failed history writes. This can result in missing history records if an error occurs

3. **Race Conditions:**
   - If multiple updates happen in quick succession, there could be race conditions that lead to incorrect or incomplete history records

## Alternatives: Inbox/Outbox Pattern

To address the above issues, an **Inbox/Outbox pattern** could be employed, which would ensure better coordination between the update and history processing transactions. This pattern offers several benefits:

### Benefits of Inbox/Outbox Pattern:

1. **Transactional Consistency:**
   - Both the update and history logging are coordinated within the same transactional scope, reducing the chances of inconsistency

2. **Automatic Retries:**
   - Failed writes to the history can be retried automatically using AWS SQS in case of transient errors ensuring no data loss

3. **Better Synchronization:**
   - The Inbox/Outbox pattern ensures that the history is always consistent with the vehicle's current state, even in cases of system crashes or errors.

4. **Reliability:**
   - The overall reliability of the system is improved as the history is recorded in a more robust and fault-tolerant manner.

---

While the current approach offers simplicity and separation of concerns, these drawbacks should be focused on before we start using the history in production
