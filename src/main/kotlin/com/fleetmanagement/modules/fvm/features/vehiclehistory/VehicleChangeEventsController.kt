package com.fleetmanagement.modules.fvm.features.vehiclehistory

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.vehiclehistory.FVMVehicleDelta
import com.fleetmanagement.modules.fvm.dto.vehiclehistory.FVMVehicleDeltaMapper
import com.fleetmanagement.modules.vehiclehistory.api.VehicleChangeDeltas
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.slf4j.LoggerFactory
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/ui")
@AccessControlledController
class VehicleChangeEventsController(
    private val vehicleChangeDeltas: VehicleChangeDeltas,
) {
    private val logger = LoggerFactory.getLogger(VehicleChangeEventsController::class.java)

    @GetMapping("/vehicles/{vehicleId}/vehicle-history")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    fun getVehicleChanges(
        @PathVariable("vehicleId") vehicleId: String,
    ): ResponseEntity<APIResponse<List<FVMVehicleDelta>>> {
        logger.info("get all vehicle changes for a vehicle $vehicleId")
        val changeDeltas = FVMVehicleDeltaMapper.INSTANCE.map(vehicleChangeDeltas.getChangeDeltas(vehicleId))
        return ResponseEntity.ok(APIResponse(changeDeltas))
    }
}
