/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.vehiclelocation

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMCompounds
import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocation
import com.fleetmanagement.modules.fvm.features.vehiclelocation.validator.FVMVehicleLocationValidator
import com.fleetmanagement.modules.fvm.features.vehiclelocation.validator.ValidationError
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehiclelocation.api.exception.InvalidLocation
import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationException
import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationValidationException
import com.fleetmanagement.modules.vehiclelocation.features.location.services.LocationNotFoundException
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.*
import java.net.URI
import kotlin.jvm.optionals.getOrNull

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMVehicleLocationController(
    private val service: FVMVehicleLocationService,
    private val validator: FVMVehicleLocationValidator,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(FVMVehicleLocationController::class.java)
    }

    @GetMapping("/vehicles/locations")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    fun allLocations(): ResponseEntity<APIResponse<FVMCompounds>> {
        val compounds = service.getKnownCompounds()
        return ResponseEntity.ok(APIResponse(FVMCompounds(compounds)))
    }

    @PostMapping("/vehicles/locations/event")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_READ)")
    fun addLocationEvent(
        @RequestBody location: FVMVehicleLocation,
    ): ResponseEntity<APIResponse<Nothing?>> {
        val errors = validator.validate(location)
        if (errors.isNotEmpty()) {
            throw InvalidVehicleLocationException(errors)
        }
        val locationEvent = convertToVehicleLocation(location)
        try {
            service.addLocationEvent(locationEvent)
        } catch (exception: LocationNotFoundException) {
            logger.error(
                "Add location to Vehicle Location Service failed for vehicleId ${location.vehicleId} as location does not exist with error",
                exception,
            )
            val problemDetail =
                ProblemDetail
                    .forStatusAndDetail(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Location does not exist error when attempting to add location event",
                    ).apply {
                        type = URI.create(ErrorType.FVM_LOCATION_DOES_NOT_EXIST_ERROR.value)
                    }
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(APIResponse.forErrors(problemDetail))
        } catch (exception: VehicleLocationValidationException) {
            logger.error("Add new vehicle location failed for vehicleId ${location.vehicleId} with error", exception)
            val problemDetail =
                ProblemDetail
                    .forStatusAndDetail(
                        HttpStatus.BAD_REQUEST,
                        "Invalid data for vehicle location",
                    ).apply {
                        type = URI.create(ErrorType.FVM_VEHICLE_LOCATION_ERROR.value)
                    }
            return ResponseEntity
                .status(HttpStatus.BAD_REQUEST)
                .body(APIResponse.forErrors(problemDetail))
        } catch (exception: VehicleLocationException) {
            logger.error("Add location to Vehicle Location Service failed for vehicleId ${location.vehicleId} with error", exception)
            val problemDetail =
                ProblemDetail
                    .forStatusAndDetail(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Error while adding location event",
                    ).apply {
                        type = URI.create(ErrorType.FVM_LOCATION_SERVICE_UNREACHABLE_ERROR.value)
                    }
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(APIResponse.forErrors(problemDetail))
        } catch (exception: InvalidLocation) {
            logger.error("Add location to Vehicle Location Service failed for vehicleId ${location.vehicleId} with error", exception)
            val problemDetail =
                ProblemDetail
                    .forStatusAndDetail(
                        HttpStatus.INTERNAL_SERVER_ERROR,
                        "Error while adding location event because of multiple matching locations",
                    ).apply {
                        type = URI.create(ErrorType.FVM_MULTIPLE_LOCATIONS_FOR_INPUTS_ERROR.value)
                    }
            return ResponseEntity
                .status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(APIResponse.forErrors(problemDetail))
        }
        return ResponseEntity
            .status(HttpStatus.CREATED)
            .body(APIResponse(data = null))
    }

    private fun convertToVehicleLocation(location: FVMVehicleLocation): VehicleLocation =
        VehicleLocation(
            location.vehicleId,
            checkNotNull(location.eventType).get(),
            checkNotNull(location.compoundName).get(),
            location.building?.getOrNull(),
            location.level?.getOrNull(),
            location.parkingLot?.getOrNull(),
            location.source?.getOrNull(),
            checkNotNull(location.occurredOn).get(),
            location.comment?.getOrNull(),
        )

    @ExceptionHandler(InvalidVehicleLocationException::class)
    fun handleInvalidVehicleLocationException(ex: InvalidVehicleLocationException): ResponseEntity<APIResponse<Nothing?>> {
        val errorDetails =
            ex.errors.map { error ->
                ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                    type = URI.create("${ErrorType.FVM_INVALID_INPUT_ERROR.value}.${error.parameter}")
                    title = "Bad Request"
                    detail = error.detail
                }
            }
        return ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(APIResponse.forErrors(*errorDetails.toTypedArray()))
    }
}

class InvalidVehicleLocationException(
    val errors: List<ValidationError>,
) : RuntimeException("Invalid Vehicle Location")
