/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.vehiclelocation

import com.fleetmanagement.modules.vehiclelocation.api.FVMCompounds
import com.fleetmanagement.modules.vehiclelocation.api.VehicleLocationEvent
import com.fleetmanagement.modules.vehiclelocation.api.dtos.Compound
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import org.springframework.stereotype.Service

@Service
class FVMVehicleLocationService(
    private val fvmCompounds: FVMCompounds,
    private val vehicleLocationEvent: VehicleLocationEvent,
) {
    fun getKnownCompounds(): List<Compound> = fvmCompounds.getKnownCompounds()

    fun addLocationEvent(location: VehicleLocation) {
        vehicleLocationEvent.addLocationEvent(location)
    }
}
