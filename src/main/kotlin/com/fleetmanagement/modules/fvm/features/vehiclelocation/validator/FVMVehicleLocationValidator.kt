/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.vehiclelocation.validator

import com.fleetmanagement.modules.fvm.dto.vehiclelocation.FVMVehicleLocation
import org.springframework.stereotype.Service
import java.time.OffsetDateTime
import java.time.ZoneOffset

data class ValidationError(
    val parameter: String,
    val detail: String,
)

interface Validator {
    fun validate(location: FVMVehicleLocation): List<ValidationError>
}

@Service
class FVMVehicleLocationValidator : Validator {
    private fun MutableList<ValidationError>.addError(
        fieldName: String,
        detail: String,
    ) {
        this.add(ValidationError(fieldName, detail))
    }

    private fun <T> MutableList<ValidationError>.checkField(
        fieldName: String,
        value: T?,
        detail: String,
        validator: (T) -> Boolean = { false },
    ) {
        if (value == null || validator(value)) {
            this.addError(fieldName, detail)
        }
    }

    override fun validate(location: FVMVehicleLocation): List<ValidationError> {
        val errors = mutableListOf<ValidationError>()

        errors.checkField("vehicleId", location.vehicleId, "VehicleId is required")
        errors.checkField("compoundName", location.compoundName, "Compound name is required") { it.isEmpty }
        errors.checkField("eventType", location.eventType, "EventType is required") { it.isEmpty }
        errors.checkField("occurredOn", location.occurredOn, "OccurredOn is required") { it.isEmpty }

        location.occurredOn?.ifPresent {
            if (it.isAfter(OffsetDateTime.now(ZoneOffset.UTC))) {
                errors.addError("occurredOnNotFuture", "OccurredOn cannot be in the future")
            }
        }

        return errors
    }
}
