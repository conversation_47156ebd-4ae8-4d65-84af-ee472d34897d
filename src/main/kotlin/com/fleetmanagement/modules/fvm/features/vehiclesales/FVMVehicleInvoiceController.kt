/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.vehiclesales

import com.fasterxml.jackson.annotation.JsonView
import com.fleetmanagement.modules.fvm.dto.VehicleSalesJsonView
import com.fleetmanagement.modules.fvm.dto.VehicleSalesUIRepresentation
import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.vehiclesales.FVMVehicleInvoiceCreateDto
import com.fleetmanagement.modules.fvm.dto.vehiclesales.FVMVehicleInvoiceUpdateDto
import com.fleetmanagement.modules.vehiclesales.api.dto.CancelInvoiceResult
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceAlreadyExistsException
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceNotFoundException
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceUpdateException
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleSalesInvoiceReadException
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleSalesNotFoundException
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBUser
import jakarta.validation.Valid
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.MediaType
import org.springframework.http.ProblemDetail
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.ExceptionHandler
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.net.URI
import java.util.UUID

@AccessControlledController
@RestController
@RequestMapping("/ui")
class FVMVehicleInvoiceController(
    private val vehicleInvoiceService: FVMVehicleInvoiceService,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(FVMVehicleInvoiceController::class.java)
    }

    @PostMapping("/vehicles/invoices")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_SALES_WRITE)")
    fun createVehicleInvoice(
        @Valid @RequestBody request: FVMVehicleInvoiceCreateDto,
    ): ResponseEntity<APIResponse<VehicleInvoice?>> =
        vehicleInvoiceService.createInvoice(request).let { result ->
            when (result) {
                is CreateInvoiceResult.Success -> {
                    logger.info("Vehicle invoice created successfully for vehicleId: ${request.vehicleId}")
                    ResponseEntity.status(HttpStatus.CREATED).body(APIResponse(data = result.invoice))
                }

                is CreateInvoiceResult.ValidationFailure -> {
                    logger.error("Failed to create vehicle invoice: ${result.errors}")
                    ResponseEntity
                        .status(HttpStatus.BAD_REQUEST)
                        .body(APIResponse(data = null, errors = result.errors))
                }
            }
        }

    @GetMapping("/vehicles/invoices")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_SALES_READ)")
    @JsonView(VehicleSalesJsonView.InvoicePage::class)
    fun getVehicleInvoices(): ResponseEntity<APIResponse<List<VehicleSalesUIRepresentation>>> {
        val invoices = vehicleInvoiceService.readVehicleInvoices()
        return ResponseEntity.ok(APIResponse(data = invoices))
    }

    @DeleteMapping("/vehicles/invoices/{invoiceId}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_SALES_WRITE)")
    fun rejectVehicleInvoice(
        @PathVariable("invoiceId") invoiceId: UUID,
    ): ResponseEntity<APIResponse<UUID>> {
        val deletedInvoiceId = vehicleInvoiceService.rejectVehicleInvoice(invoiceId)
        return ResponseEntity.ok(APIResponse(data = deletedInvoiceId))
    }

    @PatchMapping("/vehicles/invoices/{invoiceId}/approve")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_SALES_WRITE)")
    fun approveVehicleInvoice(
        @PathVariable invoiceId: UUID,
    ): ResponseEntity<APIResponse<VehicleInvoice?>> {
        val updatedInvoice = vehicleInvoiceService.approveVehicleInvoice(invoiceId)
        return ResponseEntity.ok(APIResponse(data = updatedInvoice))
    }

    @PostMapping("/vehicles/invoices/{invoiceId}/cancel")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_SALES_WRITE)")
    fun cancelInvoice(
        @PathVariable invoiceId: UUID,
    ): ResponseEntity<APIResponse<CancelInvoiceResult>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser
        val cancelInvoiceResult = vehicleInvoiceService.cancelInvoice(invoiceId, currentUser.fullNameWithDepartment)
        return ResponseEntity.ok(APIResponse(data = cancelInvoiceResult))
    }

    @PutMapping("/vehicles/invoices/{invoiceId}", consumes = [MediaType.APPLICATION_JSON_VALUE])
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VEHICLE_SALES_WRITE)")
    fun updateVehicleInvoice(
        @PathVariable invoiceId: UUID,
        @RequestBody vehicleInvoiceUpdateDto: FVMVehicleInvoiceUpdateDto,
    ): ResponseEntity<APIResponse<VehicleInvoice>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser
        val updatedInvoice =
            vehicleInvoiceService.updateVehicleInvoice(
                invoiceId,
                vehicleInvoiceUpdateDto,
                currentUser.fullNameWithDepartment,
            )
        return ResponseEntity.ok(APIResponse(data = updatedInvoice))
    }

    /**
     * Handles exceptions when there is a failure in creating vehicle invoice.
     *
     * This method catches [VehicleInvoiceAlreadyExistsException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [VehicleInvoiceAlreadyExistsException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(VehicleInvoiceAlreadyExistsException::class)
    fun handleReadVehicleSalesException(ex: VehicleInvoiceAlreadyExistsException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.CONFLICT)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatus(HttpStatus.CONFLICT).apply {
                        title = "Failed to create vehicle invoice"
                        detail = ex.message
                        type = URI.create(ErrorType.FVM_SALES_VEHICLE_INVOICE_ALREADY_EXISTS.value)
                    },
                ),
            )

    /**
     * Handles exceptions when there is a failure in reading vehicle sales.
     *
     * This method catches [VehicleSalesNotFoundException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [VehicleSalesNotFoundException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(VehicleSalesNotFoundException::class)
    fun handleReadVehicleSalesException(ex: VehicleSalesNotFoundException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatus(HttpStatus.NOT_FOUND).apply {
                        title = "Failed to read vehicle sales"
                        detail = ex.message
                        type = URI.create(ErrorType.FVM_SALES_VEHICLE_NOT_FOUND.value)
                    },
                ),
            )

    /**
     * Handles exceptions when there is a failure in reading vehicle sales invoices.
     *
     * This method catches [VehicleSalesInvoiceReadException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [VehicleSalesInvoiceReadException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(VehicleSalesInvoiceReadException::class)
    fun handleReadVehicleInvoicesException(ex: VehicleSalesInvoiceReadException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatus(HttpStatus.NOT_FOUND).apply {
                        title = "Failed to read vehicle invoices"
                        detail = ex.message
                        type = URI.create(ErrorType.FVM_SALES_VEHICLE_INVOICE_READ_ERROR.value)
                    },
                ),
            )

    /**
     * Handles exceptions when a specific vehicle invoice is not found in system.
     *
     * This method catches [VehicleInvoiceNotFoundException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [VehicleInvoiceNotFoundException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(VehicleInvoiceNotFoundException::class)
    fun handleVehicleInvoiceNotFoundException(ex: VehicleInvoiceNotFoundException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.NOT_FOUND)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatus(HttpStatus.NOT_FOUND).apply {
                        title = "Vehicle invoice not found"
                        detail = ex.message
                        type = URI.create(ErrorType.FVM_SALES_VEHICLE_INVOICE_NOT_FOUND.value)
                    },
                ),
            )

    /**
     * Handles exceptions when a specific vehicle invoice update is failed.
     *
     * This method catches [VehicleInvoiceUpdateException] and processes it accordingly,
     * ensuring a meaningful response is returned to the client.
     *
     * @param ex The exception instance of [VehicleInvoiceUpdateException] that occurred.
     * @return A structured error response encapsulating the exception details.
     */
    @ExceptionHandler(VehicleInvoiceUpdateException::class)
    fun handleFVMVehicleInvoiceUpdateException(ex: VehicleInvoiceUpdateException): ResponseEntity<APIResponse<Nothing?>> =
        ResponseEntity
            .status(HttpStatus.BAD_REQUEST)
            .body(
                APIResponse.forErrors(
                    ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                        title = "Vehicle invoice update failed"
                        detail = ex.message
                        type = URI.create(ErrorType.FVM_SALES_VEHICLE_INVOICE_UPDATE_ERROR.value)
                        properties = mapOf("missingProperties" to ex.properties)
                    },
                ),
            )
}
