/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.vehiclesales

import com.fleetmanagement.modules.fvm.dto.VehicleSalesUIRepresentation
import com.fleetmanagement.modules.fvm.dto.peopledata.PeopleMapper
import com.fleetmanagement.modules.fvm.dto.vehicledata.FVMVehicleDataMapper
import com.fleetmanagement.modules.fvm.dto.vehiclesales.FVMVehicleInvoiceCreateDto
import com.fleetmanagement.modules.fvm.dto.vehiclesales.FVMVehicleInvoiceUpdateDto
import com.fleetmanagement.modules.fvm.dto.vehiclesales.toFVMVehicleSales
import com.fleetmanagement.modules.fvm.dto.vehiclesales.toVehicleInvoiceUpdateDto
import com.fleetmanagement.modules.fvm.dto.vehiclesales.toVehicleSalesCreateDto
import com.fleetmanagement.modules.fvm.features.vehiclesales.validator.FVMInvoiceRequestValidator
import com.fleetmanagement.modules.fvm.features.vehiclesales.validator.ValidationResult
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicleperson.api.readvehiclesalesperson.ReadVehicleSalesPerson
import com.fleetmanagement.modules.vehiclesales.api.CreateVehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.ReadVehicleInvoices
import com.fleetmanagement.modules.vehiclesales.api.UpdateVehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.domain.InvoiceStatus
import com.fleetmanagement.modules.vehiclesales.api.dto.CancelInvoiceResult
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceUpdateException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.stereotype.Service
import java.net.URI
import java.util.UUID

sealed interface CreateInvoiceResult {
    data class Success(
        val invoice: VehicleInvoice,
    ) : CreateInvoiceResult

    data class ValidationFailure(
        val errors: List<ProblemDetail>,
    ) : CreateInvoiceResult
}

@Service
class FVMVehicleInvoiceService(
    private val vehicleInvoiceCreateService: CreateVehicleInvoice,
    private val vehicleInvoiceReadService: ReadVehicleInvoices,
    private val vehicleSalesPersonReadService: ReadVehicleSalesPerson,
    private val vehicleDataReadService: ReadVehicleByVehicleId,
    private val validator: FVMInvoiceRequestValidator,
    private val updateVehicleInvoice: UpdateVehicleInvoice,
) {
    fun createInvoice(request: FVMVehicleInvoiceCreateDto): CreateInvoiceResult =
        when (val result = validator.validate(request)) {
            is ValidationResult.Success -> {
                logger.info("Creating vehicle invoice for vehicleId: ${request.vehicleId}")
                val createdInvoice = vehicleInvoiceCreateService.createInvoice(request.toVehicleSalesCreateDto())
                CreateInvoiceResult.Success(createdInvoice)
            }

            is ValidationResult.Failure -> {
                CreateInvoiceResult.ValidationFailure(
                    result.errors.map { error ->
                        ProblemDetail.forStatus(HttpStatus.BAD_REQUEST).apply {
                            title = "Validation Failed"
                            type = URI.create(error.code.errorType.value)
                            detail = error.message
                            properties = mapOf(error.field to error.fieldValue)
                        }
                    },
                )
            }
        }

    fun readVehicleInvoices(): List<VehicleSalesUIRepresentation> {
        val invoices = vehicleInvoiceReadService.readVehicleInvoices()
        val vehicleIds = invoices.map { it.vehicleId }.distinct()
        val personIds = invoices.map { it.salesPersonNumber }.distinct()

        val vehiclesById = vehicleDataReadService.readVehiclesByIds(vehicleIds).associateBy { it.id }
        val personsById =
            runCatching {
                vehicleSalesPersonReadService
                    .readVehicleSalesPersonsBy(personIds)
                    .associateBy { it.employeeNumber }
            }.getOrDefault(emptyMap())

        return invoices.map { invoice ->
            val vehicle = vehiclesById[invoice.vehicleId]
            val person = personsById[invoice.salesPersonNumber]

            VehicleSalesUIRepresentation(
                vehicle = vehicle?.let { FVMVehicleDataMapper.INSTANCE.map(it) },
                vehicleSales = invoice.toFVMVehicleSales(),
                peopleData = person?.let { PeopleMapper.INSTANCE.map(it) },
            )
        }
    }

    fun rejectVehicleInvoice(invoiceId: UUID): UUID {
        val invoice = vehicleInvoiceReadService.readVehicleInvoiceById(invoiceId)
        if (invoice.invoiceStatus != InvoiceStatus.WAITING_FOR_APPROVAL) {
            throw VehicleInvoiceUpdateException(
                message = "Invoice with status ${invoice.invoiceStatus} can not be rejected",
            )
        }
        return updateVehicleInvoice.deleteInvoiceBy(invoiceId)
    }

    fun approveVehicleInvoice(invoiceId: UUID): VehicleInvoice {
        val invoice = vehicleInvoiceReadService.readVehicleInvoiceById(invoiceId)
        if (invoice.invoiceStatus != InvoiceStatus.WAITING_FOR_APPROVAL) {
            throw VehicleInvoiceUpdateException(
                message = "Invoice with status ${invoice.invoiceStatus} can not be approved",
            )
        }
        return updateVehicleInvoice.approveInvoiceBy(invoiceId)
    }

    fun cancelInvoice(
        invoiceId: UUID,
        modifier: String,
    ): CancelInvoiceResult {
        val invoice = vehicleInvoiceReadService.readVehicleInvoiceById(invoiceId)
        if (invoice.invoiceStatus !in invoiceCancelableStatus) {
            throw VehicleInvoiceUpdateException(
                message = "Invoice with status ${invoice.invoiceStatus} can not be canceled",
            )
        }
        return updateVehicleInvoice.cancelInvoiceBy(invoiceId, modifier)
    }

    fun updateVehicleInvoice(
        invoiceId: UUID,
        vehicleInvoiceUpdateDto: FVMVehicleInvoiceUpdateDto,
        modifier: String,
    ): VehicleInvoice = updateVehicleInvoice.updateInvoiceBy(invoiceId, vehicleInvoiceUpdateDto.toVehicleInvoiceUpdateDto(), modifier)

    companion object {
        private val logger = LoggerFactory.getLogger(FVMVehicleInvoiceService::class.java)
        private val invoiceCancelableStatus =
            listOf(
                InvoiceStatus.INVOICE_REQUEST_APPROVED,
                InvoiceStatus.INVOICE_REQUEST_CREATED,
                InvoiceStatus.INVOICE_SENT_TO_CUSTOMER,
                InvoiceStatus.CUSTOMER_DELIVERY_DATE_CONFIRMED,
            )
    }
}
