/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.vehiclesales.validator

import com.fleetmanagement.modules.fvm.dto.api.ErrorType
import com.fleetmanagement.modules.fvm.dto.vehiclesales.FVMVehicleInvoiceCreateDto
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehiclesales.api.ReadVehicleSaleByVehicleId
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.server.ResponseStatusException

enum class InvoiceValidationErrorCode(
    val errorType: ErrorType,
) {
    VEHICLE_STATUS_INVALID(ErrorType.FVM_SALES_INVOICE_VEHICLE_STATUS_ERROR),
    RESERVED_FOR_B2C_MISSING(ErrorType.FVM_SALES_INVOICE_RESERVED_FOR_B2C_MISSING),
    CONTRACT_NOT_SIGNED(ErrorType.FVM_SALES_INVOICE_CONTRACT_NOT_SIGNED),
}

data class ValidationError(
    val field: String,
    val fieldValue: Any?,
    val message: String,
    val code: InvoiceValidationErrorCode,
)

sealed class ValidationResult {
    object Success : ValidationResult()

    data class Failure(
        val errors: List<ValidationError>,
    ) : ValidationResult()
}

@Component
class FVMInvoiceRequestValidator(
    private val readVehicle: ReadVehicleByVehicleId,
    private val readVehicleSale: ReadVehicleSaleByVehicleId,
) {
    private companion object {
        val ALLOWED_STATUSES = setOf(VehicleStatus.S400, VehicleStatus.S410, VehicleStatus.S420)
    }

    fun validate(request: FVMVehicleInvoiceCreateDto): ValidationResult {
        val errors = mutableListOf<ValidationError>()

        val vehicle =
            readVehicle.readVehicleById(request.vehicleId)
                ?: throw ResponseStatusException(HttpStatus.NOT_FOUND, "Vehicle with ID ${request.vehicleId} not found")

        val status = vehicle.status?.let { enumValueOf<VehicleStatus>(it) }
        if (status !in ALLOWED_STATUSES) {
            errors.add(
                ValidationError(
                    field = "status",
                    fieldValue = status?.name,
                    message = "Vehicle status is not eligible for invoicing",
                    code = InvoiceValidationErrorCode.VEHICLE_STATUS_INVALID,
                ),
            )
        }

        val sale = readVehicleSale.readVehicleSalesBy(request.vehicleId)
        if (sale.reservedForB2C != true) {
            errors.add(
                ValidationError(
                    field = "reservedForB2C",
                    fieldValue = sale.reservedForB2C,
                    message = "Vehicle sale must be reserved for B2C",
                    code = InvoiceValidationErrorCode.RESERVED_FOR_B2C_MISSING,
                ),
            )
        }
        if (sale.contractSigned != true) {
            errors.add(
                ValidationError(
                    field = "contractSigned",
                    fieldValue = sale.contractSigned,
                    message = "Vehicle sale contract must be signed",
                    code = InvoiceValidationErrorCode.CONTRACT_NOT_SIGNED,
                ),
            )
        }

        return if (errors.isEmpty()) ValidationResult.Success else ValidationResult.Failure(errors)
    }
}
