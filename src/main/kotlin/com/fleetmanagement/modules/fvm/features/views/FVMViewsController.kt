/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.views

import com.fleetmanagement.modules.fvm.dto.api.APIResponse
import com.fleetmanagement.modules.fvm.features.views.dto.ViewListUIRepresentation
import com.fleetmanagement.modules.fvm.features.views.dto.ViewUIRepresentation
import com.fleetmanagement.modules.fvm.features.views.entites.JPAView
import com.fleetmanagement.modules.fvm.features.views.entites.ViewMapper
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledController
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBUser
import jakarta.servlet.http.HttpServletRequest
import jakarta.validation.Valid
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.security.core.context.SecurityContextHolder
import org.springframework.web.bind.annotation.DeleteMapping
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PatchMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@RestController
@RequestMapping("/ui")
@AccessControlledController
class FVMViewsController(
    private val service: FVMViewsService,
) {
    @GetMapping("/views")
    fun views(
        @RequestParam key: String,
        request: HttpServletRequest,
    ): ResponseEntity<APIResponse<List<ViewListUIRepresentation>>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser
        return ResponseEntity.ok(
            APIResponse(
                ViewMapper.INSTANCE.mapListItemList(
                    service.readViewsByKeyForUser(
                        key = key,
                        user = currentUser,
                    ),
                ),
            ),
        )
    }

    @GetMapping("/views/{id}")
    fun view(
        @PathVariable("id") id: Long,
        request: HttpServletRequest,
    ): ResponseEntity<APIResponse<ViewUIRepresentation>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser

        val view = service.readViewByIdForUser(viewId = id, user = currentUser)
        return ResponseEntity.ok(APIResponse(ViewMapper.INSTANCE.map(view)))
    }

    @DeleteMapping("/views/{id}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VIEW_DELETE)")
    fun deleteView(
        @PathVariable("id") id: Long,
        request: HttpServletRequest,
    ): ResponseEntity<APIResponse<Long>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser
        service.deleteView(id, currentUser)
        return ResponseEntity.ok(APIResponse(id))
    }

    @PostMapping("/views")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VIEW_WRITE)")
    fun createView(
        @Valid @RequestBody viewDto: ViewUIRepresentation,
        request: HttpServletRequest,
    ): ResponseEntity<APIResponse<ViewUIRepresentation>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser

        val view: JPAView = ViewMapper.INSTANCE.map(viewDto)

        val storedView: JPAView = service.saveView(view, currentUser)

        return ResponseEntity
            .status(HttpStatus.CREATED)
            .body(
                APIResponse(
                    ViewMapper.INSTANCE.map(storedView),
                ),
            )
    }

    @PatchMapping("/views/{id}")
    @PreAuthorize("hasAuthority(@apiLabelConstant.API_VIEW_WRITE)")
    fun updateView(
        @PathVariable id: Long,
        @Valid @RequestBody viewDto: ViewUIRepresentation,
        request: HttpServletRequest,
    ): ResponseEntity<APIResponse<ViewUIRepresentation>> {
        val currentUser = SecurityContextHolder.getContext().authentication.principal as ALBUser

        val view: JPAView = ViewMapper.INSTANCE.map(viewDto)

        val storedView: JPAView = service.updateView(id, view, currentUser)

        return ResponseEntity
            .status(HttpStatus.OK)
            .body(
                APIResponse(
                    ViewMapper.INSTANCE.map(
                        storedView,
                    ),
                ),
            )
    }
}
