/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.views

import com.fleetmanagement.modules.fvm.features.views.entites.JPAView
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface FVMViewsRepository : JpaRepository<JPAView, Long> {
    @Query("SELECT e FROM JPAView e WHERE e.key = :key AND (e.userIdSha256 = :userIdSha256 OR e.isPublic = true) ORDER BY e.id ASC")
    fun findByKeyAndUserIdSha256OrIsPublicTrue(
        key: String,
        userIdSha256: String,
    ): List<JPAView>

    @Query("SELECT e FROM JPAView e WHERE e.id = :viewId AND (e.userIdSha256 = :userIdSha256 OR e.isPublic = true)")
    fun findByIdAndUserIdSha256OrIsPublicTrue(
        viewId: Long,
        userIdSha256: String,
    ): JPAView?

    fun findByKeyAndIsPublicTrueAndIsDefaultTrue(key: String): JPAView?
}
