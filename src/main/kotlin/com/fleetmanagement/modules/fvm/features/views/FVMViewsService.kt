/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.views

import com.fleetmanagement.modules.fvm.features.views.entites.JPAView
import com.fleetmanagement.modules.fvm.features.views.exceptions.PermissionDeniedException
import com.fleetmanagement.modules.fvm.features.views.exceptions.ViewNotFoundException
import com.fleetmanagement.security.features.tokenvalidation.alb.ALBUser
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.web.server.ResponseStatusException

@Service
class FVMViewsService(
    private val fvmViewsRepository: FVMViewsRepository,
) {
    fun readViewsByKeyForUser(
        key: String,
        user: ALBUser,
    ): List<JPAView> = fvmViewsRepository.findByKeyAndUserIdSha256OrIsPublicTrue(key, user.oidAsSHA256Hash())

    private fun readDefaultPublicViewByKey(key: String): JPAView? = fvmViewsRepository.findByKeyAndIsPublicTrueAndIsDefaultTrue(key)

    fun readViewByIdForUser(
        viewId: Long,
        user: ALBUser,
    ): JPAView =
        fvmViewsRepository
            .findById(viewId)
            .orElseThrow { ViewNotFoundException(viewId) }
            .also {
                if (it.userIdSha256 != user.oidAsSHA256Hash() && it.isPublic != true) {
                    throw PermissionDeniedException()
                }
            }

    @Transactional
    fun saveView(
        view: JPAView,
        user: ALBUser,
    ): JPAView {
        view.userIdSha256 = user.oidAsSHA256Hash()

        val defaultPublicView = readDefaultPublicViewByKey(view.key)

        if (view.isPublic == true && defaultPublicView == null) {
            view.isDefault = true
        }

        if (view.isPublic == true && view.isDefault == true && defaultPublicView != null) {
            defaultPublicView.isDefault = false

            fvmViewsRepository.save(defaultPublicView)
        }

        return fvmViewsRepository.save(view)
    }

    fun updateView(
        id: Long,
        view: JPAView,
        user: ALBUser,
    ): JPAView {
        val existingView = readViewByIdForUser(id, user)
        if (view.isDefault == true && existingView.isDefault != true) {
            readDefaultPublicViewByKey(view.key)?.let {
                it.isDefault = false
                fvmViewsRepository.save(it)
            }
        }
        existingView.update(view)
        return fvmViewsRepository.save(existingView)
    }

    fun deleteView(
        id: Long,
        user: ALBUser,
    ) {
        val view = readViewByIdForUser(id, user)
        if (view.isDefault == true) {
            throw ResponseStatusException(HttpStatus.PRECONDITION_FAILED, "Default view cannot be deleted")
        }
        fvmViewsRepository.deleteById(id)
    }
}
