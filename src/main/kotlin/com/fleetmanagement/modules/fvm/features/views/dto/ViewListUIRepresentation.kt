/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.views.dto

import java.util.*

data class ViewListUIRepresentation(
    val id: Long,
    val key: String,
    val name: String,
    val description: String?,
    val isPublic: Boolean? = false,
    val isDefault: Boolean? = false,
    val createdAt: Date? = null,
    val updatedAt: Date? = null,
)
