/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.views.dto

import com.fasterxml.jackson.annotation.JsonFilter
import com.fleetmanagement.modules.fvm.dto.accesscontrol.FieldLabel
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.security.features.accesscontrol.middleware.requests.AccessControlledResource
import com.fleetmanagement.security.features.accesscontrol.middleware.responses.AccessControlledField
import jakarta.validation.constraints.NotBlank
import java.util.*

@JsonFilter("fieldLevelAccessControl")
@AccessControlledResource(Resource.VIEW)
data class ViewUIRepresentation(
    val id: Long? = null,
    var key: String,
    @field:NotBlank(message = "View name can not be empty")
    var name: String,
    val description: String? = null,
    @AccessControlledField(FieldLabel.VIEW_IS_PUBLIC)
    val isPublic: Boolean? = null,
    @AccessControlledField(FieldLabel.VIEW_IS_DEFAULT)
    val isDefault: Boolean? = null,
    val filterState: String?,
    val columnState: String?,
    val createdAt: Date? = null,
    val updatedAt: Date? = null,
)
