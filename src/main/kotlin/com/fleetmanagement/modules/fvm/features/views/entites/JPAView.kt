/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.views.entites

import jakarta.persistence.*
import org.hibernate.annotations.ColumnTransformer
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.util.*

@Entity
@Table(name = "view", schema = "fvm")
open class JPAView(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "user_id_sha256")
    open var userIdSha256: String? = null,
    open var name: String? = null,
    open var description: String? = null,
    open var key: String,
    @Column(name = "is_public")
    open var isPublic: Boolean? = null,
    @Column(name = "is_default")
    open var isDefault: Boolean? = null,
    @ColumnTransformer(write = "?::jsonb")
    @Column(name = "filter_state", columnDefinition = "jsonb")
    open var filterState: String? = null,
    @ColumnTransformer(write = "?::jsonb")
    @Column(name = "column_state", columnDefinition = "jsonb")
    open var columnState: String? = null,
    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    open val createdAt: Date? = null,
    @UpdateTimestamp
    @Column(name = "updated_at")
    open var updatedAt: Date? = null,
) {
    fun update(view: JPAView) {
        name = view.name
        description = view.description
        key = view.key
        isPublic = view.isPublic
        isDefault = view.isDefault
        filterState = view.filterState ?: filterState
        columnState = view.columnState ?: columnState
    }
}
