/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.views.entites

import com.fleetmanagement.modules.fvm.dto.VehicleUIRepresentation
import com.fleetmanagement.modules.fvm.features.views.dto.ViewListUIRepresentation
import com.fleetmanagement.modules.fvm.features.views.dto.ViewUIRepresentation
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface ViewMapper {
    companion object {
        val INSTANCE: ViewMapper = Mappers.getMapper(ViewMapper::class.java)
    }

    @Mapping(target = "isDefault", expression = "java(view.isDefault() != null ? view.isDefault() : false)")
    @Mapping(target = "isPublic", expression = "java(view.isPublic() != null ? view.isPublic() : false)")
    fun map(view: JPAView): ViewUIRepresentation

    fun map(view: ViewUIRepresentation): JPAView

    fun mapList(view: List<JPAView>): List<VehicleUIRepresentation>

    @Mapping(target = "isDefault", expression = "java(view.isDefault() != null ? view.isDefault() : false)")
    @Mapping(target = "isPublic", expression = "java(view.isPublic() != null ? view.isPublic() : false)")
    fun mapListItem(view: JPAView): ViewListUIRepresentation

    fun mapListItemList(view: List<JPAView>): List<ViewListUIRepresentation>
}
