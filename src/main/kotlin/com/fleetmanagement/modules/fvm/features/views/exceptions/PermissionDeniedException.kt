/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.views.exceptions

import org.springframework.http.HttpStatus
import org.springframework.web.server.ResponseStatusException

class PermissionDeniedException :
    ResponseStatusException(
        HttpStatus.valueOf(403),
        "User is not allowed to access the view",
    )
