/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.fvm.features.views.exceptions

import org.springframework.http.HttpStatusCode
import org.springframework.web.server.ResponseStatusException

class ViewNotFoundException(
    val id: Long,
) : ResponseStatusException(
        HttpStatusCode.valueOf(404),
        "View with ID $id not found",
    )
