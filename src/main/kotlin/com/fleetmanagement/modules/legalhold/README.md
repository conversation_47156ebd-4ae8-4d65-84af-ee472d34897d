## Legalhold module

This module is responsible to create and release legalhold on vehicle and relevant entities

### Purpose

A legalhold is applied when there is a legal case running against a vehicle or list of vehicles in the court. We need to ensure that the data is not altered until the legal case is resolved. To ensure this activity, this module captures the current state of the vehicle, and it's associated entities from operational database and stores it in S3 bucket by applying legalhold on it. This will ensure that the data will not be deleted from the S3 bucket. Additional restrictions on the bucket using Infrastructure as code(IaC) are added to not modify the data by anyone.

The core functionality includes:

Data Snapshot and Storage: Upon receiving a legal hold request, calls are made to the specific service by this module and then the individual service captures the data and stores this snapshot in a designated S3 bucket of that service
Legal Hold Application: The data stored in S3 is subject to a legal hold, which prevents modification or deletion of the data until the legal case is closed. This mechanism ensures compliance with legal requirements by preserving the integrity and availability of the data throughout the duration of a legal proceeding

### Table of Contents

- [Overview](#overview)
- [Architecture](#architecture)
- [Configuration](#configuration)
- [Error Handling](#error-handling)
- [Logging](#logging)
- [Security](#security)
- [Troubleshooting](#troubleshooting)

### Overview

The Vehicle LegalHold Module is created for internal usage. All the endpoints created in this module are private and should not be exposed to the UI layer. The developer from the team login to the bastion-host in AWS and calls the endpoints to create/release legalhold of a vehicle from S3

** The following details are required to create a legalhold:
1. A Machine-2-Machine token is required to call private endpoints for every service
2. VehicleId + lockId + legalHoldId are used from the request and will generate a legalhold identifier for capturing the vehicle and the relevant entities.
   - vehicleId: Internal vehicleId of the vehicle in EMH
   - lockId: optional value in the request. If it is not provided, then the current timestamp (from Instant) is used
   - legalHoldId: court case reference number associated for the vehicles

### Architecture

The module interacts with external services via the following endpoints:

1. **/legal-hold**: This endpoint creates a legalhold for the list of vehicles provided in the request and respond with the key to release the legalhold later when required. This key is important to store somewhere by the user until the legal issues are resolved on that vehicle.

## Sequence Diagram

```mermaid
sequenceDiagram

  title Legal Hold Process on Vehicle and Related Entities

  actor Member as Development Team Member
  participant AWS_Bastion_Host as AWS Bastion Host
  participant LegalHold_Module as LegalHold Module
  participant Vehicle_Data as Vehicle Data
  participant Vehicle_Registration_Service as Vehicle Registration Service
  participant VehicleSales_Module as Vehicle Sales
  participant S3_Bucket as S3 Bucket

  Note over LegalHold_Module: Creating LegalHolds
  alt
    Member ->>+ AWS_Bastion_Host: Apply /legal-hold request
    AWS_Bastion_Host -->>+ LegalHold_Module: Forward request
  
    LegalHold_Module ->>+ LegalHold_Module: generate keys from the request to use <br/>as a prefix when copying data to S3
  
    loop Process Legal Hold for Each Service
      
      LegalHold_Module ->>+ Vehicle_Data: method call to copy vehicle data to S3
      Vehicle_Data ->> S3_Bucket: Copy data to S3 bucket and apply legalhold
      Vehicle_Data -->>- LegalHold_Module: Success or Failure
  
      LegalHold_Module ->>+ Vehicle_Registration_Service: API call to copy registration orders to S3 
      Vehicle_Registration_Service ->> S3_Bucket: Copy data to S3 bucket and apply legalhold
      Vehicle_Registration_Service -->>- LegalHold_Module: Success or Failure
  
      LegalHold_Module ->>+ VehicleSales_Module: method call to copy data to S3
      VehicleSales_Module ->> S3_Bucket: Copy data to S3 bucket and apply legalhold
      VehicleSales_Module -->>- LegalHold_Module: Success or Failure
  
    end
  
    alt Any Service Fails
      LegalHold_Module -->> AWS_Bastion_Host: Return failure status with error and processed legalhold Keys
      AWS_Bastion_Host ->> Member: Return failure status with error and processed legalhold Keys
    else All Services Succeed
      LegalHold_Module -->>- AWS_Bastion_Host: Return success response with legalhold Keys
      AWS_Bastion_Host ->>- Member: Return success status with legalhold Keys
    end
  
  Note over LegalHold_Module: Releasing LegalHolds
  else
    Member ->>+ AWS_Bastion_Host: Release /legal-hold request
    AWS_Bastion_Host -->>+ LegalHold_Module: Forward request
  
    LegalHold_Module ->> LegalHold_Module: Start LegalHold Release Process
  
    loop Release LegalHold from Each Service
      LegalHold_Module ->>+ Vehicle_Data: method call to release legalhold on vehicle
      Vehicle_Data ->> S3_Bucket: Release legalhold and mark for deletion
      Vehicle_Data -->>- LegalHold_Module: Success or Failure
  
      LegalHold_Module ->>+ Vehicle_Registration_Service: API call to release legalhold to S3
      Vehicle_Registration_Service ->> S3_Bucket: Release legalhold and mark for deletion
      Vehicle_Registration_Service -->>- LegalHold_Module: Success or Failure
  
      LegalHold_Module ->>+ VehicleSales_Module: method call to release legalhold on vehicle
      VehicleSales_Module ->> S3_Bucket: Release legalhold and mark for deletion
      VehicleSales_Module -->>- LegalHold_Module: Success or Failure
    end
  
    alt Any Service Fails
      LegalHold_Module -->> AWS_Bastion_Host: Return failure with error and list of legalhold keys
      AWS_Bastion_Host ->> Member: Return failure with error and list of legalhold keys
      Member ->> Member: Analyse and release legalhold manually for processed entities
    else All Services Succeed
      LegalHold_Module -->>- AWS_Bastion_Host: Return success response with keys
      AWS_Bastion_Host ->>- Member: Return success status with keys
    end
  end
```

### Configuration

The below configuration is required to efficiently call the external service APIs for creating a successful legalhold entry and the relevant entries. The relevant clientId and secret can be found in the confluence page [here](https://skyway.porsche.com/confluence/display/FP20/Azure+clients+for+Fleet+Vehicle+Service)

```sh
export VEHICLE_REGISTRATION_LEGALHOLD_API_CLIENT_ID=<<client-id>>
export VEHICLE_REGISTRATION_LEGALHOLD_API_CLIENT_SECRET=<<client-secret>>
export VEHICLE_REGISTRATION_LEGALHOLD_API_SCOPE=app://cfb99f74-19b0-4747-b96f-81989c59bb4a/.default
```

#### Environment Variables

Ensure that the following environment variables are configured for the module:

- `VR_BASE_URI`: Base URI of the vehicle registration service

### Error Handling

- In case the external system is not available the job fails with a server exception and will throw an error in MS Teams. The developer responsible to create a legalhold should wait for the response from the API call and ensure the api call is successful. If the call is failed, the developer need to analyse the failure using the distributed trace-id and then also remove the stale entries by performing a call to remove the legalhold of the stale entries.

### Logging

- There will be a unique ID created on every call to legalhold and the same will be forwarded to the downstream system to have a distributed tracing in case of failures. Log messages are streamlined with this identifier when multiple log groups are selected in AWS. This trace-id will be returned in the response headers

### Security

- **API Authentication**: The provided REST endpoints are authenticated using OAuth2 tokens as per Porsche service’s security model
- **Secure Storage**: The client secret is stored in AWS Parameter store as documented in the configuration section

### Troubleshooting

- **API Errors**: Check the logs for detailed error messages and verify that the external services are reachable and functioning as expected
- **Tracing**: The trace ID references all the logs from all services
