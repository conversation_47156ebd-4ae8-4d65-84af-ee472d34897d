/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.client

import com.fleetmanagement.modules.legalhold.client.configuration.vehicleregistration.VehicleRegistrationLegalHoldClientConfig
import com.fleetmanagement.modules.legalhold.dto.LegalHoldKeys
import com.fleetmanagement.modules.legalhold.dto.LegalHoldRequest
import com.fleetmanagement.modules.legalhold.dto.LegalHoldResponse
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.http.MediaType
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody

@FeignClient(
    name = "vehicle-registration-legal-hold-client",
    url = "\${vehicle-legalhold.clients.vehicle-registration.base-url}",
    configuration = [VehicleRegistrationLegalHoldClientConfig::class],
)
interface VehicleRegistrationLegalHoldClient {
    @PostMapping("/private/legal-hold/registration-orders", consumes = [MediaType.APPLICATION_JSON_VALUE])
    fun createLegalHolds(
        @RequestBody legalHoldRequest: LegalHoldRequest,
    ): ResponseEntity<LegalHoldResponse>

    @PostMapping(
        "/private/legal-hold/release/registration-orders",
        consumes = [MediaType.APPLICATION_JSON_VALUE],
    )
    fun releaseLegalHolds(request: LegalHoldKeys): ResponseEntity<LegalHoldResponse>
}
