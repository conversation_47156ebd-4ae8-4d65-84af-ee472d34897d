/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.client.configuration.vehicleregistration

import com.fleetmanagement.observability.requestfilters.MDCFilter.Companion.X_TRACE_ID
import com.fleetmanagement.oidc.client.OIDCConfigurationProperties
import com.fleetmanagement.oidc.client.OIDCTokenClient
import com.fleetmanagement.oidc.service.OIDCTokenService
import feign.RequestInterceptor
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.web.context.request.RequestContextHolder
import org.springframework.web.context.request.ServletRequestAttributes

@Configuration
class VehicleRegistrationLegalHoldClientConfigProperties {
    @Bean("vrLegalHoldConfiguration")
    @ConfigurationProperties(prefix = "vehicle-legalhold.clients.vehicle-registration")
    fun oidcConfigurationProperties(): OIDCConfigurationProperties = OIDCConfigurationProperties()
}

class VehicleRegistrationLegalHoldClientConfig {
    @Bean("vrLegalHoldTokenService")
    fun oidcTokenService(
        vrLegalHoldTokenClient: OIDCTokenClient,
        @Qualifier("vrLegalHoldConfiguration") vrLegalHoldConfiguration: OIDCConfigurationProperties,
    ): OIDCTokenService = OIDCTokenService(vrLegalHoldTokenClient, vrLegalHoldConfiguration)

    @Bean
    fun requestInterceptor(
        @Qualifier("vrLegalHoldTokenService") vrLegalHoldTokenService: OIDCTokenService,
    ): RequestInterceptor =
        RequestInterceptor { template ->
            val token = vrLegalHoldTokenService.getAccessToken()
            template.header("Authorization", "Bearer $token")
            template.header(X_TRACE_ID, getCurrentTraceId())
        }

    private fun getCurrentTraceId(): String? {
        val requestAttributes = RequestContextHolder.getRequestAttributes() as ServletRequestAttributes?
        return requestAttributes?.response?.getHeader(X_TRACE_ID)
    }
}
