/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.configuration

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.emh.shared.s3.client.adapter.config.StorageProperties
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.util.StdDateFormat
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.awspring.cloud.s3.S3Template
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import software.amazon.awssdk.services.s3.S3Client

class VehicleLegalHoldProperties {
    var storage: Storage = Storage()
    var vehicleData: Bucket = Bucket()
    var vehicleSales: Bucket = Bucket()

    class Storage {
        var sse: String = "aws:kms"
        lateinit var sseKmsKey: String
    }

    class Bucket {
        lateinit var bucket: String
    }
}

@Configuration
class LegalHoldConfiguration {
    @Bean
    @ConfigurationProperties("vehicle-legalhold")
    fun legalHoldStorageProperties(): VehicleLegalHoldProperties = VehicleLegalHoldProperties()

    @Bean
    @Qualifier("legalHoldStorageClient")
    fun legalHoldStorageClient(
        s3Client: S3Client,
        s3Template: S3Template,
        vehicleLegalHoldProperties: VehicleLegalHoldProperties,
    ): S3StorageClient {
        val storage = vehicleLegalHoldProperties.storage
        val storageProperties = StorageProperties(storage.sse, storage.sseKmsKey)
        return S3StorageClient(s3Client, s3Template, storageProperties)
    }

    @Bean
    @Qualifier("legalHoldObjectMapper")
    fun legalHoldObjectMapper(): ObjectMapper =
        jacksonObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setDateFormat(StdDateFormat())
            .registerModule(JavaTimeModule())
            .registerModule(Jdk8Module())
}
