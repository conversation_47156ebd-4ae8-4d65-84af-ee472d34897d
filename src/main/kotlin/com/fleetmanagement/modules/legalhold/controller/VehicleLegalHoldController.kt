/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.controller

import com.fleetmanagement.modules.legalhold.PrivateRestController
import com.fleetmanagement.modules.legalhold.dto.LegalHoldKeys
import com.fleetmanagement.modules.legalhold.dto.LegalHoldRequest
import com.fleetmanagement.modules.legalhold.dto.LegalHoldResponse
import com.fleetmanagement.modules.legalhold.service.LegalHoldHandlerService
import io.swagger.v3.oas.annotations.Operation
import io.swagger.v3.oas.annotations.media.Content
import io.swagger.v3.oas.annotations.media.Schema
import io.swagger.v3.oas.annotations.responses.ApiResponse
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody

@PrivateRestController
@Tag(
    name = "Vehicle LegalHold",
    description = "Operations for managing legal holds on vehicles and associated entities",
)
class VehicleLegalHoldController(
    private val legalHoldHandlerService: LegalHoldHandlerService,
) {
    @Operation(
        summary = "Create Legal Holds for Vehicles",
        description = "Creates legal holds for the provided vehicle. It returns the list of legalhold keys on successful creation",
        responses = [
            ApiResponse(
                responseCode = "202",
                description = "Legal holds created successfully",
                content = [
                    Content(
                        mediaType = "application/json",
                        schema = Schema(implementation = LegalHoldResponse::class),
                    ),
                ],
            ),
            ApiResponse(
                responseCode = "500",
                description = "Internal server error",
                content = [
                    Content(
                        mediaType = "application/json",
                        schema = Schema(implementation = LegalHoldResponse::class),
                    ),
                ],
            ),
        ],
    )
    @PostMapping("/legal-hold/vehicles")
    fun createLegalHolds(
        @RequestBody request: LegalHoldRequest,
    ): ResponseEntity<LegalHoldResponse> {
        val result = legalHoldHandlerService.createLegalHolds(request)
        return if (result.errors.isEmpty()) {
            ResponseEntity.accepted().body(result)
        } else {
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result)
        }
    }

    @Operation(
        summary = "Release Legal Holds for Vehicles",
        description = "Releases legal holds for the provided vehicles. It returns list of legalhold keys that has been released",
        responses = [
            ApiResponse(
                responseCode = "202",
                description = "Legal holds released successfully",
                content = [
                    Content(
                        mediaType = "application/json",
                        schema = Schema(implementation = LegalHoldResponse::class),
                    ),
                ],
            ),
            ApiResponse(
                responseCode = "500",
                description = "Internal server error",
                content = [
                    Content(
                        mediaType = "application/json",
                        schema = Schema(implementation = LegalHoldResponse::class),
                    ),
                ],
            ),
        ],
    )
    @PostMapping("/legal-hold/release/vehicles")
    fun releaseLegalHolds(
        @RequestBody request: LegalHoldKeys,
    ): ResponseEntity<LegalHoldResponse> {
        val result = legalHoldHandlerService.releaseLegalHolds(request)
        return if (result.errors.isEmpty()) {
            ResponseEntity.accepted().body(result)
        } else {
            ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(result)
        }
    }
}
