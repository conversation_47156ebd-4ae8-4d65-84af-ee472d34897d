/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.dto

import io.swagger.v3.oas.annotations.media.Schema
import java.time.Instant
import java.util.UUID

data class LegalHoldRequest(
    val vehicleIds: List<UUID>,
    val lockId: String = Instant.now().toEpochMilli().toString(),
    val legalHoldId: String,
) {
    @Schema(hidden = true)
    private val bucketKeys: MutableMap<UUID, String> = mutableMapOf()

    fun getBucketKey(vehicleId: UUID): String? = bucketKeys[vehicleId]

    fun bucketKeyValues(): List<String> = bucketKeys.values.toList()

    private fun generateBucketKeys() {
        vehicleIds.forEach { vehicleId ->
            bucketKeys[vehicleId] = "$legalHoldId-$vehicleId-$lockId"
        }
    }

    init {
        generateBucketKeys()
    }
}

data class LegalHoldKeys(
    val keys: List<String>,
)
