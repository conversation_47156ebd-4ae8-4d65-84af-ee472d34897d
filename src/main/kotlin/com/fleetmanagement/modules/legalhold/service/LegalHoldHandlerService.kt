/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.service

import com.fleetmanagement.modules.legalhold.dto.LegalHoldKeys
import com.fleetmanagement.modules.legalhold.dto.LegalHoldRequest
import com.fleetmanagement.modules.legalhold.dto.LegalHoldResponse
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.stereotype.Service

@Service
class LegalHoldHandlerService(
    private val legalHoldServices: List<LegalHoldService>,
) {
    private val logger = LoggerFactory.getLogger(LegalHoldHandlerService::class.java)

    fun createLegalHolds(request: LegalHoldRequest): LegalHoldResponse {
        val keys = mutableSetOf<String>()

        legalHoldServices.forEach { service ->
            try {
                logger.info("Starting creation of legalhold on ${service.serviceName}")
                val response = service.createLegalHolds(request)
                keys.addAll(response.keys)
                logger.info("Completed creation of legalhold completed on ${service.serviceName}")
            } catch (exception: Exception) {
                val problemDetail =
                    createProblemDetail("Service error while creating legal hold", exception, service.serviceName)
                return LegalHoldResponse(keys = keys.toList(), errors = listOf(problemDetail))
            }
        }
        return LegalHoldResponse(keys = keys.toList())
    }

    fun releaseLegalHolds(request: LegalHoldKeys): LegalHoldResponse {
        val keys = mutableSetOf<String>()
        val errors = mutableListOf<ProblemDetail>()

        legalHoldServices.forEach { service ->
            try {
                logger.info("Starting release of legal hold on ${service.serviceName}")
                val result = service.releaseLegalHolds(request)
                keys.addAll(result.keys)
                errors.addAll(result.errors)
                logger.info("Completed release of legal hold on ${service.serviceName}")
            } catch (exception: Exception) {
                val problemDetail =
                    createProblemDetail("Service error while releasing legal hold", exception, service.serviceName)
                errors.add(problemDetail)
            }
        }
        return LegalHoldResponse(keys.toList(), errors)
    }

    private fun createProblemDetail(
        title: String,
        exception: Exception,
        serviceName: String,
    ): ProblemDetail {
        logger.error("${exception.message} on $serviceName")
        return ProblemDetail.forStatus(HttpStatus.INTERNAL_SERVER_ERROR).apply {
            this.title = title
            detail = "${exception.message} on $serviceName"
        }
    }
}
