/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.service

import com.fleetmanagement.modules.legalhold.dto.LegalHoldKeys
import com.fleetmanagement.modules.legalhold.dto.LegalHoldRequest
import com.fleetmanagement.modules.legalhold.dto.LegalHoldResponse

interface LegalHoldService {
    val serviceName: String

    fun createLegalHolds(request: LegalHoldRequest): LegalHoldResponse

    fun releaseLegalHolds(request: LegalHoldKeys): LegalHoldResponse
}

class LegalHoldCreationException(
    message: String? = null,
    cause: Throwable? = null,
) : RuntimeException(message ?: cause?.message, cause)

class LegalHoldReleaseException(
    message: String? = null,
    cause: Throwable? = null,
) : RuntimeException(message ?: cause?.message, cause)
