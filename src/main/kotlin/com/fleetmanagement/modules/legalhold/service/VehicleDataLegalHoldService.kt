/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.service

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.legalhold.configuration.VehicleLegalHoldProperties
import com.fleetmanagement.modules.legalhold.dto.LegalHoldKeys
import com.fleetmanagement.modules.legalhold.dto.LegalHoldRequest
import com.fleetmanagement.modules.legalhold.dto.LegalHoldResponse
import com.fleetmanagement.modules.legalhold.validator.Validator
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
class VehicleDataLegalHoldService(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val storageProperties: VehicleLegalHoldProperties,
    @Qualifier("legalHoldStorageClient") private val legalHoldStorageClient: S3StorageClient,
    @Qualifier("legalHoldStorageValidator") private val validator: Validator,
    @Qualifier("legalHoldObjectMapper") private val objectMapper: ObjectMapper,
) : LegalHoldService {
    companion object {
        private val logger = LoggerFactory.getLogger(VehicleDataLegalHoldService::class.java)
    }

    override val serviceName: String = "VEHICLE_DATA_SERVICE"

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    override fun createLegalHolds(request: LegalHoldRequest): LegalHoldResponse {
        validator.validate(bucket = storageProperties.vehicleData.bucket)
        val vehicleIds = request.vehicleIds
        val keys = mutableListOf<String>()

        vehicleIds.forEach { vehicleId ->
            val key = checkNotNull(request.getBucketKey(vehicleId))
            val vehicle =
                jpaVehicleRepository.findById(vehicleId).orElseThrow {
                    throw IllegalArgumentException("Vehicle with ID $vehicleId not found")
                }
            try {
                logger.info("creating legalhold for key [$key] to S3")
                val resource = ByteArrayResource(objectMapper.writeValueAsBytes(vehicle))
                legalHoldStorageClient.uploadResource(key, resource, storageProperties.vehicleData.bucket, true)
                logger.info("creating legalhold for vehicle data successful")
                keys.add(key)
            } catch (exception: Exception) {
                logger.error(
                    "An unexpected error encountered while creating legalhold for key: $key",
                    exception,
                )
                throw LegalHoldCreationException("legalhold creation failed", exception.cause)
            }
        }
        return LegalHoldResponse(keys)
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    override fun releaseLegalHolds(request: LegalHoldKeys): LegalHoldResponse {
        val keys = mutableListOf<String>()
        val errors = mutableListOf<ProblemDetail>()

        request.keys.forEach { key ->
            try {
                logger.info("releasing legalhold for key [$key] from S3")
                legalHoldStorageClient.unlockAndMarkFilesDeleted(key, storageProperties.vehicleData.bucket)
                logger.info("releasing legalhold for vehicle-data successful")
                keys.add(key)
            } catch (exception: Exception) {
                logger.error("service error encountered while releasing legalhold for key: $key", exception)
                val problemDetail =
                    ProblemDetail.forStatus(HttpStatus.INTERNAL_SERVER_ERROR).apply {
                        title = "Legalhold release error for key: $key"
                        detail = exception.message
                    }
                errors.add(problemDetail)
            }
        }
        return LegalHoldResponse(keys, errors)
    }
}
