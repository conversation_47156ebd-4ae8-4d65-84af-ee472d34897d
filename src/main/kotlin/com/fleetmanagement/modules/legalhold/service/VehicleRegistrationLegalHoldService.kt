/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.service

import com.fleetmanagement.modules.legalhold.client.VehicleRegistrationLegalHoldClient
import com.fleetmanagement.modules.legalhold.dto.LegalHoldKeys
import com.fleetmanagement.modules.legalhold.dto.LegalHoldRequest
import com.fleetmanagement.modules.legalhold.dto.LegalHoldResponse
import org.springframework.stereotype.Service

@Service
class VehicleRegistrationLegalHoldService(
    private val vehicleRegistrationLegalHoldClient: VehicleRegistrationLegalHoldClient,
) : LegalHoldService {
    override val serviceName: String = "VEHICLE_REGISTRATION_SERVICE"

    override fun createLegalHolds(request: LegalHoldRequest): LegalHoldResponse {
        val response = vehicleRegistrationLegalHoldClient.createLegalHolds(request)
        return response.body ?: throw LegalHoldCreationException("Failed to create legal hold")
    }

    override fun releaseLegalHolds(request: LegalHoldKeys): LegalHoldResponse {
        val response = vehicleRegistrationLegalHoldClient.releaseLegalHolds(request)
        return response.body ?: throw LegalHoldReleaseException("Failed to release legal hold")
    }
}
