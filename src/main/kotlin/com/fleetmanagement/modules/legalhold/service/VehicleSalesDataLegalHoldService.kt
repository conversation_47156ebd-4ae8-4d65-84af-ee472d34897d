/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.service

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.legalhold.configuration.VehicleLegalHoldProperties
import com.fleetmanagement.modules.legalhold.dto.LegalHoldKeys
import com.fleetmanagement.modules.legalhold.dto.LegalHoldRequest
import com.fleetmanagement.modules.legalhold.dto.LegalHoldResponse
import com.fleetmanagement.modules.legalhold.validator.Validator
import com.fleetmanagement.modules.vehiclesales.api.ReadVehicleInvoices
import com.fleetmanagement.modules.vehiclesales.api.ReadVehicleSaleByVehicleId
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSalesDto
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.ByteArrayResource
import org.springframework.http.HttpStatus
import org.springframework.http.ProblemDetail
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
class VehicleSalesDataLegalHoldService(
    private val vehicleSaleReadService: ReadVehicleSaleByVehicleId,
    private val vehicleInvoiceReadService: ReadVehicleInvoices,
    private val storageProperties: VehicleLegalHoldProperties,
    @Qualifier("legalHoldStorageClient") private val legalHoldStorageClient: S3StorageClient,
    @Qualifier("legalHoldStorageValidator") private val validator: Validator,
    @Qualifier("legalHoldObjectMapper") private val objectMapper: ObjectMapper,
) : LegalHoldService {
    companion object {
        private val logger = LoggerFactory.getLogger(VehicleSalesDataLegalHoldService::class.java)
        private const val SERVICE_NAME = "VEHICLE_SALES_DATA_SERVICE"
    }

    override val serviceName: String = SERVICE_NAME

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    override fun createLegalHolds(request: LegalHoldRequest): LegalHoldResponse {
        validator.validate(bucket = storageProperties.vehicleSales.bucket)
        val vehicleIds = request.vehicleIds
        val keys = mutableListOf<String>()

        vehicleIds.forEach { vehicleId ->
            val key = checkNotNull(request.getBucketKey(vehicleId))
            val vehicleSalesData =
                runCatching { vehicleSaleReadService.readVehicleSalesBy(vehicleId) }.getOrNull() ?: return@forEach
            val invoice = vehicleInvoiceReadService.readCurrentInvoiceBy(vehicleId)
            val vehicleSales = VehicleSalesLegalHoldRecord(vehicleSales = vehicleSalesData, vehicleInvoice = invoice)
            try {
                logger.info("creating legalhold for key [$key] to S3")
                val resource = ByteArrayResource(objectMapper.writeValueAsBytes(vehicleSales))
                legalHoldStorageClient.uploadResource(key, resource, storageProperties.vehicleSales.bucket, true)
                logger.info("creating legalhold for vehicle sales data successful")
                keys.add(key)
            } catch (exception: Exception) {
                logger.error(
                    "An unexpected error encountered while creating legalhold for key: $key",
                    exception,
                )
                throw LegalHoldCreationException("legalhold creation failed", exception.cause)
            }
        }
        return LegalHoldResponse(keys)
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    override fun releaseLegalHolds(request: LegalHoldKeys): LegalHoldResponse {
        val keys = mutableListOf<String>()
        val errors = mutableListOf<ProblemDetail>()

        request.keys.forEach { key ->
            try {
                logger.info("releasing legalhold for key [$key] from S3")
                legalHoldStorageClient.unlockAndMarkFilesDeleted(key, storageProperties.vehicleSales.bucket)
                logger.info("releasing legalhold for vehicle sales data successful")
                keys.add(key)
            } catch (exception: Exception) {
                logger.error("service error encountered while releasing legalhold for key: $key", exception)
                val problemDetail =
                    ProblemDetail.forStatus(HttpStatus.INTERNAL_SERVER_ERROR).apply {
                        title = "Legalhold release error for key: $key"
                        detail = exception.message
                    }
                errors.add(problemDetail)
            }
        }
        return LegalHoldResponse(keys, errors)
    }
}

data class VehicleSalesLegalHoldRecord(
    val vehicleSales: VehicleSalesDto,
    val vehicleInvoice: VehicleInvoice?,
)
