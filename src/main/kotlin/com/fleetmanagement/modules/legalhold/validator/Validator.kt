/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.legalhold.validator

import com.emh.shared.s3.client.adapter.S3StorageClient
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Service

interface Validator {
    fun validate(bucket: String): Boolean
}

@Service
class LegalHoldStorageValidator(
    @Qualifier("legalHoldStorageClient") private val s3StorageClient: S3StorageClient,
) : Validator {
    override fun validate(bucket: String): <PERSON><PERSON><PERSON> {
        val bucketExists = s3StorageClient.bucketExists(bucket)
        if (!bucketExists) {
            throw LegalHoldStorageException("Bucket [$bucket] does not exist.")
        }
        return true
    }
}

class LegalHoldStorageException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException()
