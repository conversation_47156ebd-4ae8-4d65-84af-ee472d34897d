package com.fleetmanagement.modules.masterdata

import com.fleetmanagement.modules.masterdata.service.FleetVehicleMasterDataService
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@Component
@DisallowConcurrentExecution
class FleetVehicleMasterDataManualJob(
    private val fleetVehicleMasterDataService: FleetVehicleMasterDataService,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(FleetVehicleMasterDataManualJob::class.java)
    }

    override fun execute(context: JobExecutionContext) {
        try {
            log.info("Executing master data trigger for all FVM vehicles")
            fleetVehicleMasterDataService.createOutboxEventsForAllVehicles()
            log.info("Master data trigger for all FVM vehicles completed successfully")
        } catch (ex: Exception) {
            log.error("Job failed to produce fleet vehicle master data", ex)
        }
    }
}

@Configuration
class FleetVehicleMasterDataManualJobConfiguration {
    /**
     * Job to manually trigger the Fleet Vehicle Master Data job. On triggering this job,
     * all the vehicles from FVM system are fetched and added to outbox so that vehicle state
     * will be published to down stream systems .
     */
    @Bean("fleetVehicleMasterDataManualJobDetail")
    fun fleetVehicleMasterDataManualJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(FleetVehicleMasterDataManualJob::class.java)
            .withIdentity("fleetVehicleMasterDataManualJob")
            .withDescription("FleetVehicleMasterDataManualJob Detail")
            .storeDurably()
            .build()
}
