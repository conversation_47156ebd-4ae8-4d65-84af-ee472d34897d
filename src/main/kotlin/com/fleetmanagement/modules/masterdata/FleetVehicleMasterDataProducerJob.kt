/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata

import com.fleetmanagement.modules.masterdata.configuration.FleetVehicleMasterDataConfiguration
import com.fleetmanagement.modules.masterdata.service.FleetVehicleOutboxProcessor
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean

@ConditionalOnBean(FleetVehicleMasterDataConfiguration::class)
@DisallowConcurrentExecution
class FleetVehicleMasterDataProducerJob(
    private val fleetVehicleOutboxProcessor: FleetVehicleOutboxProcessor,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(FleetVehicleMasterDataProducerJob::class.java)
    }

    override fun execute(context: JobExecutionContext) {
        try {
            log.info("Executing fleet vehicle master data producer job...")
            fleetVehicleOutboxProcessor.process()
            log.info("Job completed successfully...")
        } catch (ex: Exception) {
            log.error("Job failed to produce fleet vehicle master data", ex)
        }
    }
}
