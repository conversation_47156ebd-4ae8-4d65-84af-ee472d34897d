# Masterdata Data Product

Masterdata is our EMH data product. It is published on creation or update of vehicle data. 
On each event the full vehicle "snapshot" is published to our Streamzilla topic.

The Streamzilla topic is `FRA_emhs_fleet_master_vehicle` (`FRA_emhs_fleet_master_vehicle_dev` and `FRA_emhs_fleet_master_vehicle_staging`)

## Message key
A Kafka message key is used to:
- Route messages to specific partitions.
- Determine ordering within a partition.
- If no key is provided, <PERSON><PERSON><PERSON> assigns partitions using round-robin.
- This key is also used when log compaction is enabled
  - <PERSON><PERSON><PERSON>’s log compaction feature ensures that only the latest message per key is retained.
  - <PERSON><PERSON><PERSON> will retain the most recent record for each unique key.

We define the message key using the most specific vehicle identifier available, to ensure that all events for the same vehicle land in the same partition, preserving event order.
The fallback order is:
- vguid – Vehicle Global Unique ID 
- vin – Vehicle Identification Number

The first non-null value among these is hashed and used as the key that looks something like:

```
e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855
```

FVM application always expects vin or vguid to be present in the message key. If both are missing, the producer will throw an error and the message will not be sent.

## Sample message header
```
{
vguid: GInV6MljOMI7Qo9vXCTfbWGpV7G}TEST,
vin: BP0ZZZ99ZMS217027,
equipmentNumber: 12345,
timestamp: 2025-05-07T07: 12: 00.109848964Z,
eventType: VEHICLE_TRANSFER_UPDATED,
X-Amzn-Trace-Id: Root=1-681b07c0-bea0c727ba2b1c03df28ae74;Parent=4fbe3d386d048434;Sampled=1,
traceparent: 00-681b07c0bea0c727ba2b1c03df28ae74-4fbe3d386d048434-01,
b3: 681b07c0bea0c727ba2b1c03df28ae74-4fbe3d386d048434-1,X-B3-TraceId: 681b07c0bea0c727ba2b1c03df28ae74,
X-B3-SpanId: 4fbe3d386d048434,
X-B3-Sampled:1  24e7c487db69e303ca1045c1883efa9d567e795741be91e2161dfff352d62a3e
}
```

## Sample message body

A sample message payload is generated automatically:

[View sample-kafka-message.json ›](../../../../../../test/resources/masterdata/sample_kafka_message.json)


## Schema Registration With Streamzilla Schema Registry

Auto Schema Registration automatically registers the schema of Kafka messages with the Schema Registry. This eliminates the need for manual schema registration and ensures consumers can deserialize data correctly.

### Schema Validation During Message Publishing
When a message is published, the producer uses the schema registry client to validate the message format. If the message doesn't conform to the schema, the producer will throw an error. This ensures that only correctly formatted messages are accepted.

In the KafkaProducer, the following happens when auto.register.schema is enabled:
**Serialization:** The producer serializes the message using the KafkaJsonSchemaSerializer.
**Schema Registry Client:** Before sending, the schema is registered (if it's a new schema), and validated using the Schema Registry client. The schema is checked to ensure that it aligns with the message data structure.
**Error Handling:** If the schema does not match the message structure, the producer will raise an exception, preventing the message from being published.

### Kafka Message Wire Format with Schema Registry
When using Confluent Schema Registry with Kafka, each serialized message includes a specific [wire format](https://docs.confluent.io/platform/current/schema-registry/fundamentals/serdes-develop/index.html#wire-format) that begins with a magic byte. This design allows consumers to identify and retrieve the correct schema for deserialization.

The structure of a Kafka message serialized with Confluent serializers (Avro, JSON Schema, or Protobuf) is as follows:
```text
[magic byte][schema ID][serialized data]
```
* **Magic Byte (1 byte):** A constant byte (0x00) indicating the use of Schema Registry serialization.
* **Schema ID (4 bytes):** A 32-bit integer (big-endian) representing the unique ID of the schema registered in Schema Registry.
* **Serialized Data:** The actual payload encoded according to the specified schema.

### How should consumers handle it
* Use the Appropriate Deserializer: **KafkaJsonSchemaDeserializer** (in case of master data)
* Configure the Schema Registry URL
* Avoid manual parsing
* Work around for StringDeserializer: consumers should manually process the magic byte and schema ID before deserializing the payload. 
  * Key considerations:
      * This bypasses Schema Registry client caching and requires manual parsing, so no schema validation
      * Requires manual error handling for schema evolution

### How to Enable Auto Schema Registration

1. **Enable the Feature Flag**
   In your Terraform `tfvars` file, set the feature flag:
   ```hcl
   KAFKA_SCHEMA_AUTO_REGISTER = true
   ```

This will set the auto.register.schema in application.yml to true.

### Key Properties for Schema Configuration in application.yml
1. **json.write.dates.iso8601** : Formats OffsetDateTime and Date fields in ISO 8601 format
2. **javax @NotNull annotation** : Mark all the required fields with @NotNull for the json schema to mark them as required, otherwise they are marked as nullable. There is currently no property that can be used in producer property for this.

## Additional Information
- LicensePlates are shown on the vehicle transfers only for ACTIVE status. It will be empty for all other statuses
- The current license plate will be empty if the vehicle is deregistered. The same will be reflected on the licensePlates array of the transfer.
- The chronological sequence for availability of vehicle identifiers
-- 1st vehicle in production -> only VGUID
-- 2nd vehicle in production and “linked” in EQUI -> VGUID + EQUID
-- 3rd vehicle produced -> VGUID + FIN
-- 4th vehicle produced and “linked” in EQUI -> VGUID VGUID + FIN + EQUID

- There are four event types existing
  - VEHICLE_CREATED 
  - VEHICLE_UPDATED 
  - VEHICLE_TRANSFER_CREATED 
  - VEHICLE_TRANSFER_UPDATED

For every update in one of the vehicle transfers there will once update message.

## Known Limitations

The following are known limitations in the current implementation. These will be addressed in future enhancements.

- **Clients might receive redundant messages:**  
  This module responds to vehicle and vehicle transfer related events. These events are triggered by their respective modules whenever any relevant field changes. However, the changed fields may not always be included in the messages sent to clients.  
  In such cases, even if clients calculate the delta, there may be no visible difference between consecutive messages, which could lead clients to assume the messages are redundant.

## Documentation Links

1. [Kafka Producer Configuration](https://kafka.apache.org/documentation/#producerconfigs)
2. [Schema Registry Documentation](https://docs.confluent.io/platform/current/schema-registry/index.html)
3. [Spring Kafka Reference Documentation](https://docs.spring.io/spring-kafka/reference/kafka/sending-messages.html)
4. [Json Schema Serializer Config Properties](https://github.com/confluentinc/schema-registry/blob/master/json-schema-serializer/src/main/java/io/confluent/kafka/serializers/json/KafkaJsonSchemaSerializerConfig.java)

