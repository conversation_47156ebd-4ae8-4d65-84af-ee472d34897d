# Kafka Producer: Data Contracts & Schema Evolution
This document outlines our strategy for managing Kafka message schemas. Our producers leverage Kotlin data classes and the Confluent KafkaJsonSchemaSerializer to automatically generate and register JSON Schemas with the Schema Registry.

### How Compatibility is Handled
We strictly enforce backward and aim for forward compatibility where feasible. This ensures seamless evolution as producers and consumers update independently. Our Schema Registry is configured for BACKWARD compatibility, but understanding FORWARD is key for robust systems.

**The io.confluent.kafka.serializers.json.KafkaJsonSchemaSerializer infers JSON Schema directly from your Kotlin data classes.**

1. @field:NotNull Annotation: Kotlin fields annotated with @field:NotNull translate to required fields in the generated JSON Schema.

2. Nullable Types / Absence of @field:NotNull: Fields declared as nullable (e.g., String?) or non-nullable without @field:NotNull will be considered optional in the generated JSON Schema.

### Rules for Compatible Schema Evolution (via Data Class Changes):

Adhere to these rules when modifying your Kotlin data classes to ensure both backward and forward compatibility:

#### 1. Adding New Fields:

* **ALLOWED**: Add new properties to your data class.

* **Critical**: New fields MUST be declared as nullable (e.g., val newField: String?) and NOT annotated with @field:NotNull. This makes them optional in the generated schema.

* **Impact (Backward)**: Older consumers ignore these new fields.

* **Impact (Forward)**: Older consumers, reading messages from newer producers, will simply ignore these new fields they don't expect. This is the primary way to achieve forward compatibility.

##### 2. Adding New Fields with Default Values:

* **ALLOWED** for Backward Compatibility, but limits Forward Compatibility if @field:NotNull is used.

* If a new field is logically required, provide a default in your data class constructor (e.g., val newField: Int = 0). This ensures the field is optional in the generated schema, providing backward compatibility.

* **Important for Forward Compatibility**: If you mark a new field with @field:NotNull (even with a default in Kotlin), the generated JSON Schema will mark it required. An older consumer expecting the old schema won't know about this new required field, potentially causing a validation failure if it tries to validate the message against its old schema.

* **Recommendation for strong forward compatibility**: Stick to nullable new fields without @field:NotNull.

##### 3. Making an Existing Field Optional:

* **ALLOWED** Change a non-nullable field (e.g., String) to a nullable type (e.g., String?) and/or remove its @field:NotNull annotation.

* **Impact (Backward & Forward)**: Old messages remain valid. New messages can omit the field, and both old and new consumers handle it gracefully.

##### 4. "Deprecating" Fields (Soft Removal):

* **DO NOT DELETE FIELDS FROM DATA CLASS IMMEDIATELY.**

* Action: Make the field nullable (e.g., val oldField: String?). Stop populating it in producer code. Add @Deprecated annotation and comments.

* Impact (Backward & Forward): The field stays in the schema for compatibility. Consumers handle its presence or absence.

### Deploying a Breaking Schema Change (If Necessary)
**WARNING:** A "breaking schema change" means new messages cannot be read by old consumers. In your setup, this occurs when a data class change violates backward or forward compatibility (e.g., removing a @field:NotNull field, changing a type). This is a high-impact operation and should be a last resort.

#### Strategy: New Topic & Phased Migration (Recommended)
This is the safest approach for fundamental schema changes.

**New Data Class/Schema:**
* Create a brand-new Kotlin data class (e.g., MyMessageV2) with the revised schema structure.

**New Kafka Topic:**
* Create a brand-new Kafka topic (e.g., my-data-topic-v2).

**Update & Deploy New Producers:**
* Modify producers to use MyMessageV2 and send to my-data-topic-v2. 
* The schema for MyMessageV2 will auto-register on the new topic. 
* Old producers continue sending to the original topic.

**Update & Deploy Consumers (Phased):**
* Dual-Read (Recommended): Update consumers to read from both my-data-topic (using original data class/deserializer) and my-data-topic-v2 (using MyMessageV2/deserializer). This allows seamless processing of historical and new data. 
* Cut-Over: (Higher Risk) Ensure all old messages are processed. Then, update consumers to solely read from my-data-topic-v2. Requires precise coordination.

**Decommission Old Topic & Producers:**
* Once fully migrated, decommission old producers and eventually the old Kafka topic.
