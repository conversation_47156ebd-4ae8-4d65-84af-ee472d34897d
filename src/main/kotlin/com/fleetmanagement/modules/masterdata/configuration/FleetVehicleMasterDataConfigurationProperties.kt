/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.configuration

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "fleet-master-data-publisher")
class FleetVehicleMasterDataConfigurationProperties(
    val scheduler: SchedulerConfiguration,
    val producer: ProducerProperties,
    val topic: String,
    val enabled: Boolean,
)

data class SchedulerConfiguration(
    val cron: String,
)

class ProducerProperties(
    val keySerializer: String,
    val valueSerializer: String,
    val schemaRegistryUrl: String,
    val schemaRegistryUser: String,
    val schemaRegistryPassword: String,
    val autoRegisterSchemas: Boolean,
)
