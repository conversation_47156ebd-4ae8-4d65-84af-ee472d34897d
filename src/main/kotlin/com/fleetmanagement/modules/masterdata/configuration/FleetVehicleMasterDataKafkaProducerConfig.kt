/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.configuration

import com.fleetmanagement.modules.masterdata.dto.ExternalFleetMasterVehicle
import org.apache.kafka.clients.producer.ProducerConfig
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.autoconfigure.kafka.KafkaProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.core.DefaultKafkaProducerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.core.ProducerFactory

@Configuration
@ConditionalOnProperty(name = ["fleet-master-data-publisher.enabled"], havingValue = "true", matchIfMissing = false)
class FleetVehicleMasterDataKafkaProducerConfig(
    private val kafkaProperties: KafkaProperties,
    private val customProperties: FleetVehicleMasterDataConfigurationProperties,
) {
    private fun fleetVehicleMasterDataKafkaProducerFactory(): ProducerFactory<String, ExternalFleetMasterVehicle> {
        val config = kafkaProperties.buildProducerProperties()

        config[ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG] = customProperties.producer.keySerializer
        config[ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG] = customProperties.producer.valueSerializer

        // Schema registry
        config["schema.registry.url"] = customProperties.producer.schemaRegistryUrl
        config["basic.auth.credentials.source"] = "USER_INFO"
        config["basic.auth.user.info"] =
            "${customProperties.producer.schemaRegistryUser}:${customProperties.producer.schemaRegistryPassword}"
        config["auto.register.schemas"] = customProperties.producer.autoRegisterSchemas

        // formats date in iso8601 format
        config["json.write.dates.iso8601"] = true

        return DefaultKafkaProducerFactory(config)
    }

    @Bean(name = ["fleetMasterDataKafkaTemplate"])
    fun fleetVehicleMasterDataKafkaTemplate(): KafkaTemplate<String, ExternalFleetMasterVehicle> =
        KafkaTemplate(fleetVehicleMasterDataKafkaProducerFactory())
}
