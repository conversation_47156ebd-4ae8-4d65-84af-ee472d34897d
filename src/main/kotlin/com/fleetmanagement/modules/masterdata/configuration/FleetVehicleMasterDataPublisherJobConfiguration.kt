/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.configuration

import com.fleetmanagement.jobmanagement.configuration.QuartzJobManager
import com.fleetmanagement.modules.masterdata.FleetVehicleMasterDataProducerJob
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobKey
import org.quartz.Scheduler
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.beans.factory.ObjectProvider
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.TimeZone

@Configuration
class FleetVehicleMasterDataPublisherJobConfiguration(
    private val config: FleetVehicleMasterDataConfigurationProperties,
) {
    @Bean("fleetVehicleMasterDataJobDetail")
    fun fleetVehicleMasterDataJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(FleetVehicleMasterDataProducerJob::class.java)
            .withIdentity("FleetVehicleMasterDataJobDetail")
            .withDescription("Fleet vehicle master data job")
            .storeDurably()
            .build()

    @Bean("fleetVehicleMasterDataTrigger")
    fun fleetVehicleMasterDataTrigger(fleetVehicleMasterDataJobDetail: JobDetail): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(fleetVehicleMasterDataJobDetail)
            .withIdentity("FleetVehicleMasterDataTrigger")
            .withDescription("Fleet vehicle master data trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(config.scheduler.cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()

    @Bean("fleetVehicleMasterDataJob")
    fun quartzJobManager(
        schedulerProvider: ObjectProvider<Scheduler>,
        configuration: FleetVehicleMasterDataConfigurationProperties,
    ): QuartzJobManager =
        QuartzJobManager(
            jobKey = JobKey("FleetVehicleMasterDataJob"),
            schedulerProvider = schedulerProvider,
            isJobEnabled = configuration.enabled,
        )
}
