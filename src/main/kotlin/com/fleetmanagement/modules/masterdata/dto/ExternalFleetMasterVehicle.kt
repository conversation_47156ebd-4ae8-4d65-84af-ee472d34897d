package com.fleetmanagement.modules.masterdata.dto

import javax.validation.constraints.NotNull

/**
 * # Kafka Message DTO: Schema Compatibility Guide
 *
 * This class defines a Kafka message. Its JSON Schema is auto-generated
 * and registered in Schema Registry. Follow these rules to avoid breaking
 * consumers.
 *
 * ## Safe Changes (Compatible):
 * - Add new fields as nullable (`val newField: Type?`) – no `@field:NotNull`.
 * - Make existing fields nullable.
 * - Deprecate fields: make nullable + mark with `@Deprecated`.
 *
 * ## Breaking Changes (Avoid):
 * - Removing or renaming fields.
 * - Changing a field's type (e.g., `String` → `Int`).
 * - Making a nullable field required.
 *
 * ## Notes:
 * - `@field:NotNull` → required in schema.
 * - Nullable types → optional in schema.
 *
 * ## Please update the README example json if you change the data product
 */
data class ExternalFleetMasterVehicle(
    @NotNull
    val vehicle: ExternalVehicle,
)
