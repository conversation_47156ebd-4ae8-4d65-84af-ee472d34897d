package com.fleetmanagement.modules.masterdata.dto

import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.dtos.ColorInfoDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.ConsumptionInfoDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.EvaluationDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.FleetInfoDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.ModelDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.OrderDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.PriceInfoDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.ProductionInfoDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.TechnicalInfoDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.WltpDataDTO
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSalesDto
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.time.ZoneOffset

/**
 * # Kafka Message DTO: Schema Compatibility Guide
 *
 * This class defines a Kafka message. Its JSON Schema is auto-generated
 * and registered in Schema Registry. Follow these rules to avoid breaking
 * consumers.
 *
 * ## Safe Changes (Compatible):
 * - Add new fields as nullable (`val newField: Type?`) – no `@field:NotNull`.
 * - Make existing fields nullable.
 * - Deprecate fields: make nullable + mark with `@Deprecated`.
 *
 * ## Breaking Changes (Avoid):
 * - Removing or renaming fields.
 * - Changing a field's type (e.g., `String` → `Int`).
 * - Making a nullable field required.
 *
 * ## Notes:
 * - `@field:NotNull` → required in schema.
 * - Nullable types → optional in schema.
 *
 * ### Please update the README example json if you change the data product
 */
data class ExternalVehicle(
    val vguid: String?,
    val vin: String?,
    val equiId: String?,
    val equipmentNumber: Long?,
    val status: String?,
    val production: ExternalVehicleProduction?,
    val model: ExternalVehicleModel?,
    val price: ExternalVehiclePrice?,
    val registration: ExternalVehicleRegistration?,
    val transfers: List<ExternalVehicleTransferWithStatus> = emptyList(),
    val consumption: ExternalConsumptionInfo?,
    val wltp: ExternalVehicleWltpInfo?,
    val currentMileage: ExternalCurrentMileage?,
    val order: ExternalOrderInfo?,
    val fleet: ExternalFleetInfo?,
    val evaluation: ExternalVehicleEvaluationData?,
    val sales: ExternalVehicleSalesData?,
    val technical: ExternalVehicleTechnicalData?,
    val color: ExternalVehicleColorData?,
    val options: Map<String, Any>?,
)

data class ExternalVehicleTechnicalData(
    val amountSeats: Int?,
    val acceleration0100KmhLaunchControl: Float?,
    val acceleration0100Kmh: Float?,
    val acceleration80120Kmh: Float?,
    val cargoVolume: Int?,
    val chargingTimeAc11Kw0100: Float?,
    val chargingTimeAc22Kw: Float?,
    val chargingTimeAc96Kw0100: Float?,
    val chargingTimeDcMaxPower580: Float?,
    val engineCapacity: Float?,
    val curbWeightDin: Int?,
    val curbWeightEu: Int?,
    val grossBatteryCapacity: Float?,
    val grossVehicleWeight: Int?,
    val height: Int?,
    val length: Int?,
    val maximumChargingPowerDc: Int?,
    val maximumPayload: Int?,
    val maxRoofLoadWithPorscheRoofTransportSystem: Int?,
    val netBatteryCapacity: Float?,
    val powerKw: Int?,
    val topSpeed: Int?,
    val totalPowerKw: Int?,
    val vehicleWidthMirrorsExtended: Int?,
    val widthMirrorsFolded: Int?,
) {
    companion object {
        fun from(technicalInfo: TechnicalInfoDTO?): ExternalVehicleTechnicalData? =
            technicalInfo?.let {
                ExternalVehicleTechnicalData(
                    amountSeats = technicalInfo.amountSeats,
                    acceleration0100KmhLaunchControl = technicalInfo.acceleration0100KmhLaunchControl,
                    acceleration0100Kmh = technicalInfo.acceleration0100Kmh,
                    acceleration80120Kmh = technicalInfo.acceleration80120Kmh,
                    cargoVolume = technicalInfo.cargoVolume,
                    chargingTimeAc11Kw0100 = technicalInfo.chargingTimeAc11kw0100,
                    chargingTimeAc22Kw = technicalInfo.chargingTimeAc22Kw,
                    chargingTimeAc96Kw0100 = technicalInfo.chargingTimeAc96kw0100,
                    chargingTimeDcMaxPower580 = technicalInfo.chargingTimeDcMaxPower580,
                    engineCapacity = technicalInfo.engineCapacity,
                    curbWeightDin = technicalInfo.curbWeightDin,
                    curbWeightEu = technicalInfo.curbWeightEu,
                    grossBatteryCapacity = technicalInfo.grossBatteryCapacity,
                    grossVehicleWeight = technicalInfo.grossVehicleWeight,
                    height = technicalInfo.height,
                    length = technicalInfo.length,
                    maximumChargingPowerDc = technicalInfo.maximumChargingPowerDc,
                    maximumPayload = technicalInfo.maximumPayload,
                    maxRoofLoadWithPorscheRoofTransportSystem = technicalInfo.maxRoofLoadWithPorscheRoofTransportSystem,
                    netBatteryCapacity = technicalInfo.netBatteryCapacity,
                    powerKw = technicalInfo.powerKw,
                    topSpeed = technicalInfo.topSpeed,
                    totalPowerKw = technicalInfo.totalPowerKw,
                    vehicleWidthMirrorsExtended = technicalInfo.vehicleWidthMirrorsExtended,
                    widthMirrorsFolded = technicalInfo.widthMirrorsFolded,
                )
            }
    }
}

data class ExternalVehicleProduction(
    val number: String?,
    val factory: String?,
    val zp8Date: OffsetDateTime?,
    val plannedEndDate: OffsetDateTime?,
    val technicalModelYear: Int?,
    val gearBoxClass: Char?,
) {
    companion object {
        fun from(production: ProductionInfoDTO?) =
            production?.let {
                ExternalVehicleProduction(
                    number = production.number,
                    factory = production.factory,
                    gearBoxClass = production.gearBoxClass,
                    zp8Date =
                        production
                            .endDate
                            ?.toInstant()
                            ?.atOffset(ZoneOffset.UTC),
                    plannedEndDate =
                        production.plannedEndDate
                            ?.toInstant()
                            ?.atOffset(ZoneOffset.UTC),
                    technicalModelYear = production.technicalModelYear,
                )
            }
    }
}

data class ExternalVehicleModel(
    val orderType: String?,
    val modelRange: String?,
    val manufacturer: String?,
    val vehicleType: String? = null,
    val description: String?,
) {
    companion object {
        fun from(model: ModelDTO?) =
            model?.let {
                ExternalVehicleModel(
                    orderType = model.orderType,
                    modelRange = model.range,
                    manufacturer = model.manufacturer,
                    description = model.description,
                    vehicleType = model.vehicleType?.name,
                )
            }
    }
}

data class ExternalVehiclePrice(
    val vehicleFactoryGrossPriceEUR: BigDecimal?,
    val vehicleFactoryNetPriceEUR: BigDecimal?,
    val grossPriceWithExtras: BigDecimal? = null,
) {
    companion object {
        fun from(price: PriceInfoDTO?) =
            price?.let {
                ExternalVehiclePrice(
                    vehicleFactoryGrossPriceEUR = price.factoryGrossPriceEUR,
                    vehicleFactoryNetPriceEUR = price.factoryNetPriceEUR,
                    grossPriceWithExtras = price.grossPriceWithExtras,
                )
            }
    }
}

data class ExternalVehicleWltpInfo(
    val co2Combined: Int?,
    val electricRangeCity: Int?,
    val electricRange: Int?,
) {
    companion object {
        fun from(wltp: WltpDataDTO?): ExternalVehicleWltpInfo? =
            wltp?.let {
                ExternalVehicleWltpInfo(
                    co2Combined = wltp.co2Combined,
                    electricRangeCity = wltp.electricRangeCity,
                    electricRange = wltp.electricRange,
                )
            }
    }
}

data class ExternalConsumptionInfo(
    val driveType: String?,
    val typification: String?,
    val primaryFuelType: FuelType?,
    val secondaryFuelType: FuelType?,
) {
    companion object {
        fun from(consumption: ConsumptionInfoDTO?): ExternalConsumptionInfo? =
            consumption?.let {
                ExternalConsumptionInfo(
                    driveType = it.driveType,
                    typification = it.typification,
                    primaryFuelType = it.primaryFuelType,
                    secondaryFuelType = it.secondaryFuelType,
                )
            }
    }
}

data class ExternalCurrentMileage(
    val mileage: Int?,
    val readDate: OffsetDateTime?,
) {
    companion object {
        fun from(currentMileage: MileageReadingDTO?): ExternalCurrentMileage? =
            currentMileage?.let {
                ExternalCurrentMileage(
                    mileage = it.mileage,
                    readDate = it.readDate,
                )
            }
    }
}

data class ExternalOrderInfo(
    val blockedForSale: Boolean?,
    val primaryStatus: String? = null,
    val tradingPartnerNumber: String? = null,
) {
    companion object {
        fun from(order: OrderDTO?): ExternalOrderInfo? =
            order?.let {
                ExternalOrderInfo(
                    blockedForSale = it.blockedForSale,
                    primaryStatus = it.primaryStatus,
                    tradingPartnerNumber = it.tradingPartnerNumber,
                )
            }
    }
}

data class ExternalFleetInfo(
    val scrapVehicle: Boolean?,
    val residualValueMarket: Boolean?,
    val raceCar: Boolean?,
    val classic: Boolean?,
) {
    companion object {
        fun from(fleet: FleetInfoDTO?): ExternalFleetInfo? =
            fleet?.let {
                ExternalFleetInfo(
                    scrapVehicle = it.scrapVehicle,
                    residualValueMarket = it.isResidualValueMarket,
                    raceCar = it.raceCar,
                    classic = it.isClassic,
                )
            }
    }
}

data class ExternalVehicleEvaluationData(
    val appraisalNetPrice: BigDecimal?,
) {
    companion object {
        fun from(evaluation: EvaluationDTO?): ExternalVehicleEvaluationData? =
            evaluation?.let {
                ExternalVehicleEvaluationData(
                    appraisalNetPrice = it.appraisalNetPrice,
                )
            }
    }
}

data class ExternalVehicleSalesData(
    val reservedForB2C: Boolean?,
    val contractSigned: Boolean?,
) {
    companion object {
        fun from(vehicleSalesData: VehicleSalesDto?) =
            vehicleSalesData?.let {
                ExternalVehicleSalesData(
                    reservedForB2C = it.reservedForB2C,
                    contractSigned = it.contractSigned,
                )
            }
    }
}

data class ExternalVehicleColorData(
    val exterior: String?,
    val exteriorDescription: String?,
    val interior: String?,
    val interiorDescription: String?,
) {
    companion object {
        fun from(color: ColorInfoDTO?) =
            color?.let {
                ExternalVehicleColorData(
                    exterior = it.exterior,
                    exteriorDescription = it.exteriorDescription,
                    interior = it.interior,
                    interiorDescription = it.interiorDescription,
                )
            }
    }
}
