package com.fleetmanagement.modules.masterdata.entities

import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "vehicle_event_hash", schema = "emh_fleet_data")
class FleetVehicleEventHash(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    val id: UUID? = null,
    @Column(name = "vehicle_id", nullable = false)
    val vehicleId: UUID,
    @Enumerated(EnumType.STRING)
    @Column(name = "event_type", nullable = false)
    val eventType: VehicleEventType,
    @Column(name = "created_at", nullable = false)
    @CreationTimestamp
    val createdAt: OffsetDateTime = OffsetDateTime.now(),
    @Column(name = "hash")
    var hash: String? = null,
)
