package com.fleetmanagement.modules.masterdata.entities

import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import org.springframework.data.repository.Repository
import java.util.UUID

interface FleetVehicleEventHashRepository : Repository<FleetVehicleEventHash, UUID> {
    fun findByVehicleIdAndEventType(
        vehicleId: UUID,
        eventType: VehicleEventType,
    ): FleetVehicleEventHash?

    fun save(fleetVehicleEventHash: FleetVehicleEventHash): FleetVehicleEventHash
}
