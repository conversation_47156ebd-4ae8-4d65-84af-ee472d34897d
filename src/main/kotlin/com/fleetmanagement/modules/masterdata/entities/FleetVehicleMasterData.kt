/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.entities

import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.Table
import java.time.OffsetDateTime
import java.util.UUID

@Entity
@Table(name = "vehicle_master_data", schema = "emh_fleet_data")
class FleetVehicleMasterData(
    @Column(name = "vehicle_id", nullable = false) val vehicleId: UUID,
    @Enumerated(EnumType.STRING) @Column(name = "event_type", nullable = false) val eventType: VehicleEventType,
    @Column(name = "created_at", nullable = false) val createdAt: OffsetDateTime = OffsetDateTime.now(),
    processed: Boolean = false,
    failed: Boolean = false,
) {
    @EmbeddedId
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: FleetVehicleMasterDataId = FleetVehicleMasterDataId()

    @Column(name = "processed", nullable = false)
    var processed: Boolean = processed
        private set

    var failed: Boolean = failed
        private set

    internal fun markAsProcessed() {
        this.processed = true
    }

    internal fun markAsFailed() {
        this.failed = true
    }
}

@Embeddable
data class FleetVehicleMasterDataId(
    @Basic val value: UUID = UUID.randomUUID(),
)
