/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.entities

import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import org.springframework.data.repository.Repository
import java.util.UUID

interface FleetVehicleMasterDataRepository : Repository<FleetVehicleMasterData, FleetVehicleMasterDataId> {
    fun existsByVehicleIdAndEventTypeAndProcessedFalse(
        vehicleId: UUID,
        eventType: VehicleEventType,
    ): Boolean

    fun save(fleetVehicleMasterData: FleetVehicleMasterData): FleetVehicleMasterData

    fun findAllByProcessedFalse(): List<FleetVehicleMasterData>
}
