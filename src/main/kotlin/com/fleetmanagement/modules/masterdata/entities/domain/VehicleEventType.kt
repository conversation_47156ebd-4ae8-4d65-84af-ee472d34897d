/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.entities.domain

enum class VehicleEventType(
    name: String,
) {
    VEHICLE_CREATED("VehicleCreated"),
    VEHICLE_UPDATED("VehicleUpdated"),
    VEHICLE_TRANSFER_CREATED("VehicleTransfersCreated"),
    VEHICLE_TRANSFER_UPDATED("VehicleTransfersUpdated"),
}
