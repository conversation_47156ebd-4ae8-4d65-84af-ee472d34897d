package com.fleetmanagement.modules.masterdata.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.datatype.jdk8.Jdk8Module
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fleetmanagement.modules.masterdata.dto.ExternalFleetMasterVehicle
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleEventHash
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleEventHashRepository
import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import jakarta.transaction.Transactional
import org.springframework.stereotype.Service
import java.security.MessageDigest
import java.util.Base64
import java.util.UUID

@Service
class FleetVehicleEventDeduplicator(
    private val repository: FleetVehicleEventHashRepository,
) {
    private val objectMapper: ObjectMapper =
        ObjectMapper()
            .registerModule(JavaTimeModule())
            .registerModule(Jdk8Module())
            .enable(SerializationFeature.ORDER_MAP_ENTRIES_BY_KEYS)

    fun shouldPublishEvent(
        vehicleId: UUID,
        eventType: VehicleEventType,
        payload: ExternalFleetMasterVehicle,
    ): Boolean {
        val existing = repository.findByVehicleIdAndEventType(vehicleId, eventType)
        if (existing == null) {
            return true
        }
        val newHash = computeHash(payload)
        return existing.hash != newHash
    }

    @Transactional
    fun replaceHash(
        vehicleId: UUID,
        eventType: VehicleEventType,
        payload: ExternalFleetMasterVehicle,
    ) {
        val newHash = computeHash(payload)
        val existing = repository.findByVehicleIdAndEventType(vehicleId, eventType)
        if (existing != null) {
            existing.hash = newHash
            repository.save(existing)
        } else {
            repository.save(FleetVehicleEventHash(vehicleId = vehicleId, eventType = eventType, hash = newHash))
        }
    }

    fun computeHash(obj: Any): String {
        val json = objectMapper.writeValueAsString(obj)
        val digest = MessageDigest.getInstance("SHA-256").digest(json.toByteArray())
        return Base64.getEncoder().encodeToString(digest)
    }
}
