/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.service

import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import com.fleetmanagement.modules.vehicledata.api.events.VehicleCreatedEvent
import com.fleetmanagement.modules.vehicledata.api.events.VehicleUpdatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferCreatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferEvent
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import java.util.UUID

data class VehicleEvent(
    val vehicleId: UUID,
    val eventType: VehicleEventType,
)

@Component
class FleetVehicleMasterDataEventListener(
    private val fleetVehicleOutboxCreatorService: ********************************,
) {
    @Async
    @EventListener
    fun listenVehicleCreatedEvent(event: VehicleCreatedEvent) {
        val vehicleEvent = VehicleEvent(vehicleId = event.vehicleId, eventType = VehicleEventType.VEHICLE_CREATED)
        fleetVehicleOutboxCreatorService.handleEvent(vehicleEvent)
    }

    @Async
    @EventListener
    fun listenVehicleUpdatedEvent(event: VehicleUpdatedEvent) {
        val vehicleEvent = VehicleEvent(vehicleId = event.vehicleId, eventType = VehicleEventType.VEHICLE_UPDATED)
        fleetVehicleOutboxCreatorService.handleEvent(vehicleEvent)
    }

    @Async
    @EventListener
    fun listenVehicleTransferCreatedEvent(event: PlannedVehicleTransferEvent) {
        val vehicleEvent =
            if (event is PlannedVehicleTransferCreatedEvent) {
                VehicleEvent(vehicleId = event.vehicleId, eventType = VehicleEventType.VEHICLE_TRANSFER_CREATED)
            } else {
                VehicleEvent(vehicleId = event.vehicleId, eventType = VehicleEventType.VEHICLE_TRANSFER_UPDATED)
            }
        fleetVehicleOutboxCreatorService.handleEvent(vehicleEvent)
    }

    @Async
    @EventListener
    fun listenVehicleTransferUpdatedEvent(event: VehicleTransferEvent) {
        val vehicleEvent = VehicleEvent(vehicleId = event.vehicleId, eventType = VehicleEventType.VEHICLE_TRANSFER_UPDATED)
        fleetVehicleOutboxCreatorService.handleEvent(vehicleEvent)
    }
}
