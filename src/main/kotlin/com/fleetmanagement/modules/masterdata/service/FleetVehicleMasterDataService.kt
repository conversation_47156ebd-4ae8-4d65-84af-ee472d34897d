package com.fleetmanagement.modules.masterdata.service

import com.fleetmanagement.modules.masterdata.entities.FleetVehicleMasterData
import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import com.fleetmanagement.modules.vehicledata.api.ReadAllVehicles
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service

@Service
class FleetVehicleMasterDataService(
    private val outboxCreatorService: ********************************,
    private val readAllVehicles: ReadAllVehicles,
) {
    /**
     * Fetches all vehicles using the [ReadAllVehicles] API with pagination and creates an outbox
     * event [FleetVehicleMasterData] entry for each vehicle using the event type [VehicleEventType.VEHICLE_UPDATED].
     */
    fun createOutboxEventsForAllVehicles() {
        var page = 0
        val pageSize = 100
        var hasNext = true
        while (hasNext) {
            val pageable = PageRequest.of(page, pageSize)
            val vehiclePage = readAllVehicles.readAllVehicles(pageable)
            val vehicleIdList = vehiclePage.content.map { it.id }
            outboxCreatorService.createOutboxEventsForVehicles(vehicleIdList)
            hasNext = vehiclePage.hasNext()
            page++
        }
    }
}
