/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.service

import com.fleetmanagement.modules.masterdata.entities.FleetVehicleMasterData
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleMasterDataRepository
import com.fleetmanagement.modules.masterdata.entities.domain.VehicleEventType
import jakarta.transaction.Transactional
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.util.*

@Service
class FleetVehicleOutboxCreatorService(
    private val repository: FleetVehicleMasterDataRepository,
) {
    private val logger: Logger = LoggerFactory.getLogger(FleetVehicleOutboxCreatorService::class.java)

    @Transactional
    fun handleEvent(vehicleEvent: VehicleEvent) {
        createOutboxEntryIfNotExists(vehicleEvent)
    }

    @Transactional
    fun createOutboxEventsForVehicles(vehicleIdList: List<UUID>) =
        vehicleIdList.forEach { vehicleId ->
            val vehicleEvent =
                VehicleEvent(
                    vehicleId = vehicleId,
                    eventType = VehicleEventType.VEHICLE_UPDATED,
                )
            createOutboxEntryIfNotExists(vehicleEvent)
        }

    private fun createOutboxEntryIfNotExists(vehicleEvent: VehicleEvent) {
        if (repository.existsByVehicleIdAndEventTypeAndProcessedFalse(vehicleEvent.vehicleId, vehicleEvent.eventType)) {
            logger.warn(
                "Unprocessed outbox entry already exists for vehicle ${vehicleEvent.vehicleId} and event ${vehicleEvent.eventType}, ignoring new update",
            )
            return
        }
        repository.save(FleetVehicleMasterData(vehicleId = vehicleEvent.vehicleId, eventType = vehicleEvent.eventType))
    }
}
