/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.masterdata.service

import com.fleetmanagement.modules.masterdata.configuration.FleetVehicleMasterDataConfiguration
import com.fleetmanagement.modules.masterdata.entities.FleetVehicleMasterDataRepository
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@ConditionalOnBean(FleetVehicleMasterDataConfiguration::class)
class FleetVehicleOutboxProcessor(
    private val repository: FleetVehicleMasterDataRepository,
    private val fleetVehicleEventPublisher: FleetVehicleEventPublisher,
) {
    private val log = LoggerFactory.getLogger(FleetVehicleOutboxProcessor::class.java)

    @Transactional
    fun process() {
        val vehiclesToPublish = repository.findAllByProcessedFalse().ifEmpty { return }
        vehiclesToPublish.forEach { vehicle ->
            log.info("Publishing fleet vehicle snapshot for vehicle ${vehicle.vehicleId}")
            fleetVehicleEventPublisher.publishKafkaEvent(vehicle)
            log.info("Successfully published fleet vehicle snapshot for vehicle ${vehicle.vehicleId}")
        }
    }
}
