package com.fleetmanagement.modules.masterdata.service

import org.apache.kafka.clients.producer.ProducerRecord
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(name = ["fleet-master-data-publisher.enabled"], havingValue = "true", matchIfMissing = false)
class StreamzillaKafkaProducer<T>(
    @Qualifier("fleetMasterDataKafkaTemplate")
    private val kafkaTemplate: KafkaTemplate<String, T>,
) {
    private val log = LoggerFactory.getLogger(StreamzillaKafkaProducer::class.java)

    fun send(
        topic: String,
        key: String,
        payload: T,
        headers: Map<String, String?> = emptyMap(),
    ) {
        val record =
            ProducerRecord(topic, key, payload).apply {
                log.debug("Sending message to Kafka: topic={}, key={} message: {}", topic, key, payload)
                headers.forEach { (key, value) -> this.headers().add(key, value?.toByteArray()) }
            }
        kafkaTemplate.send(record)
    }
}
