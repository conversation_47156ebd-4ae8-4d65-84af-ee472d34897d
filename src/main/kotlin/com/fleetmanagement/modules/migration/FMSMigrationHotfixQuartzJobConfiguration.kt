package com.fleetmanagement.modules.migration

import com.fleetmanagement.modules.migration.job.FMSMigrationJobService
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
@DisallowConcurrentExecution
class FMSMigrationHotfixJob(
    private val fmsMigrationJobService: FMSMigrationJobService,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(FMSMigrationHotfixJob::class.java)
    }

    override fun execute(p0: JobExecutionContext?) {
        log.info("FMSMigrationHotfixJob started")
        fmsMigrationJobService.executeHotfixes()
        log.info("FMSMigrationHotfixJob finished")
    }
}

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Configuration
class FMSMigrationHotfixJobConfig {
    @Bean("fmsMigrationHotfixJobDetail")
    fun fmsMigrationHotfixJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(FMSMigrationHotfixJob::class.java)
            .withIdentity("FMSMigrationHotfixJob")
            .withDescription("FMSMigrationHotfixJob Detail")
            .storeDurably()
            .build()
}
