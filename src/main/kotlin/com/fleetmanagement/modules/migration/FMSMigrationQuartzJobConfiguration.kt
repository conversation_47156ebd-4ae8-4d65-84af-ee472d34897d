package com.fleetmanagement.modules.migration

import com.fleetmanagement.modules.migration.job.FMSMigrationJobService
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
@DisallowConcurrentExecution
class FMSMigrationJob(
    private val fmsMigrationJobService: FMSMigrationJobService,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(FMSMigrationJob::class.java)
    }

    override fun execute(context: JobExecutionContext) {
        log.info("FMSMigrationJob started")
        val vinFilter = parseVinFilterParameter(context)
        fmsMigrationJobService.executeMigration(vinFilter = vinFilter)
        log.info("FMSMigrationJob finished")
    }

    private fun parseVinFilterParameter(context: JobExecutionContext) =
        context.trigger
            ?.jobDataMap
            ?.getString("vinFilter")
            ?.takeIf { it.isNotBlank() }
            ?.let { filterString ->
                filterString.split(",").map { it.trim() }.filter { it.isNotBlank() }
            }
}

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Configuration
class FMSMigrationJobConfig {
    @Bean("fmsMigrationJobDetail")
    fun fmsMigrationJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(FMSMigrationJob::class.java)
            .withIdentity("FmsMigrationJob")
            .withDescription("FmsMigrationJob Detail")
            .storeDurably()
            .build()
}
