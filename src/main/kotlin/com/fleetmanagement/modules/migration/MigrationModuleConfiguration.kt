package com.fleetmanagement.modules.migration

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.emh.shared.s3.client.adapter.config.StorageProperties
import io.awspring.cloud.s3.S3Template
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import software.amazon.awssdk.services.s3.S3Client

@Configuration
@ConditionalOnProperty(name = ["fms-migration-job.enabled"], havingValue = "true", matchIfMissing = false)
class MigrationModuleConfiguration {
    @Bean("fmsMigrationStorageProperties")
    @ConfigurationProperties("fms-migration-job.fms-source.storage")
    fun fmsMigrationStorageProperties(): FMSMigrationStorageProperties = FMSMigrationStorageProperties()

    @Bean
    @Qualifier("fmsMigrationStorageClient")
    fun fmsMigrationStorageClient(
        s3Client: S3Client,
        s3Template: S3Template,
        @Qualifier("fmsMigrationStorageProperties") props: FMSMigrationStorageProperties,
    ): S3StorageClient {
        val storageProps = StorageProperties(props.sse, props.sseKmsKey)
        return S3StorageClient(s3Client, s3Template, storageProps)
    }
}

class FMSMigrationStorageProperties {
    val sse: String = "aws:kms"
    var sseKmsKey: String = ""
    var bucket: String = ""
}
