# Migration Module

The migration module is the coordinator of the FMS to FVM migration.
It handles FMS-specific data handling (mappings, aggregations) and pushes the data into respective domain modules.

It is intended to be removed without hassle later.

## Trigger the migration

> **⚠️ Deprecation Notice**  
> Access to the jobs API will be removed in the future, see [FPT3-2913](https://skyway.porsche.com/jira/browse/FPT3-2913).

### Prerequisites

- Valid authentication credentials for the target environment
- Appropriate permissions to trigger migration jobs
- Knowledge of the target environment URL (`<env>`)

### Basic Migration Trigger

The migration can be triggered by sending an authenticated POST request to the `/jobs/trigger` endpoint:

```bash
curl -X POST 'https://<env>/api/vs/jobs/trigger?jobName=FmsMigrationJob' \
  -H 'Authorization: Bearer <your-token>' \
  -H 'Content-Type: application/json'
```

### Selective VIN Migration

You can specify specific VINs to be migrated by passing a `vinFilter` in the JSON body:

```bash
curl -X POST 'https://<env>/api/vs/jobs/trigger?jobName=FmsMigrationJob' \
  -H 'Authorization: Bearer <your-token>' \
  -H 'Content-Type: application/json' \
  -d '{
    "vinFilter": "WP0ZZZ99ZTS392124,WVWZZZ1JZXW000001,WAUZZZ8V0JA000001"
  }'
```

### Parameters

| Parameter      | Type   | Required | Description                                                                                             |
|---------------|--------|----------|---------------------------------------------------------------------------------------------------------|
| `jobName`     | String | Yes      | Must be `FmsMigrationJob`. This specifies the migration job to be triggered.                           |
| `vinFilter`   | String | No       | Comma-separated list of VINs to migrate as a String. If omitted, all available vehicles will be migrated. |

### Response

Example response when the job is triggered successfully:

```json
Job triggered successfully.
```

## Post-Migration Actions

After vehicles have been migrated, sandbox mode needs to be deactivated, and further jobs need to be executed **one time**. Below are the required actions:

1. **Vehicle Restriction Manual Job**: Restricts all registrations of a vehicle with an active transfer, as this mechanism is not triggered by the migration.
   - **Endpoint**: `/api/vs/jobs/trigger?jobName=vehicleRestrictionManualJob`
   - **Method**: POST

2. **Emit Data Product for All Vehicles**: Ensures all vehicles are published as a data product in their latest state, as data product messages are not emitted during migration.
   - **Endpoint**: `/api/vs/jobs/trigger?jobName=fleetVehicleMasterDataManualJob`
   - **Method**: POST

## Migration Flow

Detailed migration documentation can be found in the [Confluence Migration Runbook](https://skyway.porsche.com/confluence/spaces/FP20/pages/1913807300/Runbook+Full+FMS+FVM+Migration).

### Overview of Migration Steps

Below is a sequence diagram illustrating the role of the Migration Module and the steps to be executed as part of the migration:

```mermaid
sequenceDiagram
    actor User
    participant S3 bucket
    participant MigrationModule
    box rgba(255, 255, 50, .1) "vehicle-service"
        participant MigrationModule
        participant VehicleData
        participant VehicleTransfer
        participant PDI
    end
    box rgba(255, 255, 50, .1) "vehicle-registration"
        participant VehicleRegistration
    end
    box rgba(255, 255, 50, .1) "location-service"
        participant LocationService
    end
    box rgba(0, 0, 255, .1) "MS Bookings"
        participant MS Bookings
    end

    User ->> MigrationModule: Trigger job
    MigrationModule ->> S3 bucket: Read CSV files
    MigrationModule ->> MigrationModule: Process CSV files
    MigrationModule ->> VehicleData: Trigger PVH vehicles import by VINs
    MigrationModule ->> VehicleData: Create manual vehicles
    MigrationModule ->> VehicleData: Update all vehicles with additional data
    VehicleData ->> VehicleData: Trigger calculations (example: ModelRange)
    MigrationModule ->> PDI: Create PDI
    MigrationModule ->> VehicleRegistration: Upload registrations
    MigrationModule ->> VehicleTransfer: Create/Update vehicle transfers
    VehicleTransfer ->> VehicleTransfer: Trigger calculations (example: Leasingberechtigung)
    MigrationModule ->> LocationService: Create Events
    MigrationModule ->> MS Bookings: Create events for specific vehicle transfers
```

## Migration Module Technical strategy

```mermaid
flowchart LR
    subgraph CSV Files Reading
        A1["FMS-vehicle-data.csv"]
        A2["FMS-vehicle-other-data.csv"]
        A3["FMS-historic events.csv"]
    end

    subgraph Data PreProcessing
        B1["validation / cleaning / mapping"]
        B2["validation / cleaning / mapping"]
        B3["validation / cleaning / mapping"]
        B3_2["aggregate"]
    end
    C1[("VIN to FMSVehicleAggregate")]

    subgraph PostProcessing
        C2["Map to \n→CreateVehicleDTO"]
        C2_2["Map to \n→ UpdateVehicleDTO"]
        C2_3["Map to \n→ VehicleTransferDTO"]
    end

    subgraph PostProcessing
        D1["Call Create Vehicle API"]
        D2["Call Update Vehicle API"]
        D3["Call Create Vehicle Transfer API"]
    end

    A1 --> B1
    A2 --> B2
    A3 --> B3

    B1 --> C1
    B2 --> C1
    B3 --> B3_2
    B3_2 --> C1

    C1 --> C2
    C1 --> C2_2
    C1 --> C2_3
    C2 --> D1
    C2_2 --> D2
    C2_3 --> D3
```

> **Note**: Ensure all preconditions mentioned in the Confluence Runbook are met before initiating the migration.
