/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.migration.appointments

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
@ConditionalOnBean(MigrationModuleConfiguration::class)
class MSBookingAppointmentSchedulerJobConfiguration {
    @Bean("msBookingAppointmentSchedulerJobDetail")
    fun msBookingAppointmentSchedulerJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(MSBookingAppointmentSchedulerJob::class.java)
            .withIdentity("MSBookingAppointmentSchedulerJob")
            .withDescription("Job to schedule MS Booking appointments")
            .storeDurably()
            .build()
}

@ConditionalOnBean(MigrationModuleConfiguration::class)
@DisallowConcurrentExecution
class MSBookingAppointmentSchedulerJob(
    private val msBookingAppointmentsHandler: MsBookingAppointmentsHandler,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(MSBookingAppointmentSchedulerJob::class.java)
    }

    override fun execute(context: JobExecutionContext) {
        try {
            log.info("Executing MS Booking appointment scheduling Job...")
            val batchSize = context.trigger.jobDataMap.getIntFromString("batchSize")
            msBookingAppointmentsHandler.scheduleMSBookingAppointments(batchSize)
            log.info("Job completed successfully...")
        } catch (ex: Exception) {
            log.error("Job failed to schedule MS Booking appointments", ex)
        }
    }
}
