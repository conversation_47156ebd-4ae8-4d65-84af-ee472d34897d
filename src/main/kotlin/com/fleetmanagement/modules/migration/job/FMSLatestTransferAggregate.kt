package com.fleetmanagement.modules.migration.job

import java.time.OffsetDateTime

data class FMSLatestTransferAggregate(
    var internalOrderNumber: String? = null,
    var internalContactPerson: String? = null,
    var usingCostCenter: String? = null,
    var fmsVehicleUsageId: Int? = null,
    var deliveryLeipzig: Boolean? = null,
    var latestReturnDate: OffsetDateTime? = null,
    var maintenanceOrderNumber: String? = null,
    var plannedDeliveryAppointment: PlannedAppointment? = null,
    var plannedReturnAppointment: PlannedAppointment? = null,
    var vehicleResponsiblePerson: String? = null,
) {
    companion object {
        val FMS_STATUSES_SIGNIFYING_PLANNED_TRANSFER = listOf("YF10", "YF11")
    }

    val isPersonalVehicle: Boolean
        get() =
            vehicleUsageDescription in
                listOf(
                    "Leasing Tarif",
                    "Leasing MK",
                    "Rentner",
                    "Zweitleasing",
                    "Dienstwagen",
                    "Treueleasing",
                )

    fun calculateFVMPlannedReturnDate(): OffsetDateTime? = this.plannedReturnAppointment?.let { mapDateAccordingToUsageGroup(it) }

    fun calculateFVMPlannedDeliveryDate(): OffsetDateTime? = this.plannedDeliveryAppointment?.let { mapDateAccordingToUsageGroup(it) }

    private fun mapDateAccordingToUsageGroup(appointment: PlannedAppointment): OffsetDateTime? =
        if (isPersonalVehicle) {
            if (appointment.isDaySpecific()) {
                // we do not migrate these, they are not needed in FVM
                null
            } else {
                // we migrate these as-is with time
                appointment.from
            }
        } else {
            // we migrate non-personal vehicles with time as start of the day, because they only have day-specificity
            appointment.from
                .toLocalDate()
                ?.atStartOfDay()
                ?.atOffset(appointment.from.offset)
        }

    val vehicleUsageDescription: String?
        get() {
            val fmsUsageIdTofvmUsageDescriptionMap: Map<Int, String> =
                mapOf(
                    1 to "Leasing Tarif",
                    2 to "Leasing MK",
                    3 to "Rentner",
                    4 to "Zweitleasing",
                    5 to "Dienstwagen",
                    6 to "Pool Sonstige",
                    7 to "Messe",
                    8 to "Presse",
                    9 to "PWRS",
                    10 to "Pool Sonstige",
                    11 to "Event/Marketing/TravelClub",
                    12 to "Pool Sonstige",
                    13 to "Pool Sonstige",
                    14 to "Entwicklung",
                    15 to "Q-Absicherung",
                    16 to "Pool Sonstige",
                    17 to "Museum",
                    18 to "Pool PD",
                    19 to "Pool PLG",
                    20 to "Pool VI",
                    21 to "Pool VA",
                    22 to "Mobilitätspool Zuffenhausen",
                    23 to "Berufsausbildung",
                    24 to "Mobilitätspool Weissach",
                    25 to "Pool Sonstige",
                    26 to "Pool Sonstige",
                    27 to "Pool Sonstige",
                    28 to "Treueleasing",
                )
            return fmsUsageIdTofvmUsageDescriptionMap[this.fmsVehicleUsageId]
        }
}
