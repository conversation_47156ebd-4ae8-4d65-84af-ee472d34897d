package com.fleetmanagement.modules.migration.job

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.ports.MigrationReportFactory
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.postprocessors.ManualVehicleCreator
import com.fleetmanagement.modules.migration.postprocessors.MigrationHotfixUpdater
import com.fleetmanagement.modules.migration.postprocessors.PVHVehicleImporter
import com.fleetmanagement.modules.migration.postprocessors.VehicleDataUpdater
import com.fleetmanagement.modules.migration.postprocessors.VehicleLocationUpdater
import com.fleetmanagement.modules.migration.postprocessors.VehiclePDIUpserter
import com.fleetmanagement.modules.migration.postprocessors.VehicleRegistrationCreator
import com.fleetmanagement.modules.migration.postprocessors.VehicleSalesUpserter
import com.fleetmanagement.modules.migration.postprocessors.VehicleTransferUpserter
import org.slf4j.LoggerFactory
import org.slf4j.MDC
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import org.springframework.util.StopWatch

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class FMSMigrationJobService(
    @Autowired private val preprocessors: List<Preprocessor>,
    @Autowired private val manualVehicleCreator: ManualVehicleCreator,
    @Autowired private val pvhVehicleImporter: PVHVehicleImporter,
    @Autowired private val vehicleDataUpdater: VehicleDataUpdater,
    @Autowired private val vehicleTransferUpserter: VehicleTransferUpserter,
    @Autowired private val vehicleRegistrationCreator: VehicleRegistrationCreator,
    @Autowired private val vehiclePDIUpserter: VehiclePDIUpserter,
    @Autowired private val migrationReportFactory: MigrationReportFactory,
    @Autowired private val vehicleLocationUpdater: VehicleLocationUpdater,
    @Autowired private val hotfixUpdater: MigrationHotfixUpdater,
    @Autowired private val vehicleSalesUpserter: VehicleSalesUpserter,
) {
    companion object {
        private const val PRODUCT_GUID_MDC_KEY = "productGUID"
    }

    private val logger = LoggerFactory.getLogger(FMSMigrationJobService::class.java)

    fun executeMigration(
        fmsVehicleAggregateCache: FMSVehicleAggregateCache = FMSVehicleAggregateCache(),
        vinFilter: List<String>? = null,
    ) {
        preprocessExcelFiles(fmsVehicleAggregateCache)

        val allAggregates = fmsVehicleAggregateCache.getAllAggregates()

        val aggregates = applyVinFilter(vinFilter, allAggregates)

        logger.info("Migrating ${aggregates.size} vehicle aggregates.")
        migrateVehicleAggregates(aggregates, ::migrateVehicle)
    }

    private fun migrateVehicle(
        aggregate: FMSVehicleAggregate,
        stopwatch: StopWatch,
    ) {
        try {
            stopwatch.nextStep("Vehicle creation import")
            val existsAlreadyOrImportedFromPVH = pvhVehicleImporter.attemptPVHImport(aggregate)
            if (!existsAlreadyOrImportedFromPVH) {
                stopwatch.nextStep("Vehicle manual creation")
                // if vehicle was not found in PVH, import it manually
                manualVehicleCreator.createManualVehicle(aggregate)
            }
        } catch (exception: Exception) {
            logger.error("Could not migrate vehicle with productGuid ${aggregate.productGuid}", exception)
            aggregate.reportError(exception.message)
            // abort migration, since we couldn't create the vehicle
            return
        }

        // update vehicle
        stopwatch.nextStep("VehicleUpdate")
        catchErrorInAggregate(aggregate) { vehicleDataUpdater.updateVehicleData(aggregate) }

        // create registrations
        stopwatch.nextStep("RegistrationUpsertion")
        catchErrorInAggregate(aggregate) { vehicleRegistrationCreator.create(aggregate) }

        // update transfers
        stopwatch.nextStep("TransferUpsertion")
        catchErrorInAggregate(aggregate) { vehicleTransferUpserter.upsert(aggregate) }

        // update pdi
        stopwatch.nextStep("PDIUpsertion")
        catchErrorInAggregate(aggregate) { vehiclePDIUpserter.upsert(aggregate) }

        // update location
        stopwatch.nextStep("LocationUpdate")
        catchErrorInAggregate(aggregate) { vehicleLocationUpdater.updateVehicleLocation(aggregate) }

        // update vehicle sales
        stopwatch.nextStep("SalesUpsertion")
        catchErrorInAggregate(aggregate) { vehicleSalesUpserter.upsertSalesData(aggregate) }
    }

    fun executeHotfixes(fmsVehicleAggregateCache: FMSVehicleAggregateCache = FMSVehicleAggregateCache()) {
        preprocessExcelFiles(fmsVehicleAggregateCache)

        val aggregates = fmsVehicleAggregateCache.getAllAggregates()
        logger.info("Migration hotfix ${aggregates.size} vehicle aggregates.")
        val hotfix = { it: FMSVehicleAggregate, sw: StopWatch ->
            sw.nextStep("Applying hotfix")
            catchErrorInAggregate(it) { hotfixUpdater.applyHotfix(it) }
        }
        migrateVehicleAggregates(aggregates, hotfix)
    }

    private fun migrateVehicleAggregates(
        aggregates: Collection<FMSVehicleAggregate>,
        migrationFunc: (FMSVehicleAggregate, StopWatch) -> Unit,
    ) {
        val report = migrationReportFactory.startReport()
        aggregates.parallelStream().forEach {
            val stopwatch = StopWatch(it.productGuid)
            stopwatch.setKeepTaskList(true)
            try {
                MDC.put(PRODUCT_GUID_MDC_KEY, it.productGuid)
                migrationFunc(it, stopwatch)
            } finally {
                stopwatch.stop()
                stopwatch.taskInfo.forEach {
                    MDC.put("durationMs_${it.taskName}", it.timeMillis.toString())
                }
                logger.info("Migration complete for ${it.vin}/${it.productGuid}. Errors: ${it.errors}")
                report.appendResult(it)
                MDC.remove(PRODUCT_GUID_MDC_KEY)
            }
        }
        report.finalizeReport()
    }

    private fun preprocessExcelFiles(fmsVehicleAggregateCache: FMSVehicleAggregateCache) {
        // 0. Validate File
        val valid = preprocessors.all { it.validate() }
        if (!valid) {
            throw RuntimeException("Validations Failed")
        }

        // 1. Execute all preprocessors to load CSV files, map and aggregate the vehicles' data in FMSVehicleAggregates
        for (preprocessor in preprocessors) {
            preprocessor.process(fmsVehicleAggregateCache)
        }
    }

    fun catchErrorInAggregate(
        aggregate: FMSVehicleAggregate,
        migrationStep: () -> Unit,
    ) {
        try {
            migrationStep.invoke()
        } catch (exception: Exception) {
            logger.error("Error executing migration step.", exception)
            aggregate.reportError(exception.message)
        }
    }

    private fun applyVinFilter(
        vinFilter: List<String>?,
        allAggregates: Collection<FMSVehicleAggregate>,
    ): Collection<FMSVehicleAggregate> =
        if (!vinFilter.isNullOrEmpty()) {
            logger.info("Filtering vehicles by VIN. Filter size: ${vinFilter.size}")
            allAggregates.filter { it.vin in vinFilter }
        } else {
            allAggregates
        }

    private fun StopWatch.nextStep(stepName: String) {
        if (isRunning) {
            stop()
        }
        start(stepName)
    }
}
