package com.fleetmanagement.modules.migration.job

import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationArea
import java.time.OffsetDateTime

data class FMSRegistrationAggregate(
    var registrationType: Int,
    var registrationDate: OffsetDateTime,
    var licencePlate: String? = null,
    var registrationArea: RegistrationArea = RegistrationArea.GERMANY,
    var remark: String? = null,
)

data class FMSExtendedRegistrationAggregate(
    // fields to be applied only to latest registration
    var briefNumberOnlyForLatestRegistration: String? = null,
    var sfmeOnlyForLatestRegistration: Boolean? = null,
    // fields to be copied over to all registrations
    var tsnForAllRegistrations: String? = null,
    var hsnForAllRegistrations: String? = null,
)
