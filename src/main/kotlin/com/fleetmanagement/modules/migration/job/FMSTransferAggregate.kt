package com.fleetmanagement.modules.migration.job

import java.time.OffsetDateTime

data class FMSTransferAggregate(
    val fmsDeliveryIndex: String? = null, // nullable for PlannedVehicleTransfer
    val deliveryDate: OffsetDateTime? = null,
    val mileageAtDelivery: Int? = null,
    val returnDate: OffsetDateTime? = null,
    val mileageAtReturn: Int? = null,
    val vehicleResponsiblePerson: String? = null,
    val vehicleUsageGroupDescription: String? = null,
    val vehicleUsingCostCenter: String? = null,
) {
    // filled separately from ORDER_NUMBERS  table
    var maintenanceOrderNumber: String? = null
}
