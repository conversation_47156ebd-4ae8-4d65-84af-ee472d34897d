package com.fleetmanagement.modules.migration.job

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime

data class FMSVehicleAggregate(
    val productGuid: String,
) {
    var isClassic: Boolean = false

    // data set for create vehicle manually:
    var vin: String? = null

    var modelDescription: String? = null
    var manufacturer: String? = null
    var orderType: String? = null
    var vehicleType: VehicleType? = null
    var financialAssetType: FinancialAssetType? = null
    var blockedForSale: Boolean = false
    var scrappedDate: OffsetDateTime? = null
    var equipmentNumber: Long? = null
    var equipmentId: String? = null
    var zp8Date: OffsetDateTime? = null

    // vehicle location
    var compoundNameId: String? = null
    var parkingLot: String? = null
    var parkingLotDate: OffsetDateTime? = null

    // more fields:
    var currentMileage: Int? = null
    var scrapVehicle: Boolean = false
    var soldDate: OffsetDateTime? = null
    var approvedForScrappingDate: OffsetDateTime? = null
    var vehicleSentToSalesDate: OffsetDateTime? = null
    var preparationDoneDate: OffsetDateTime? = null
    var isSoldOutsideFMS: Boolean = false // if true, then last vehicle transfer's return date = soldCupCarDate
    val soldCupCarDate: OffsetDateTime?
        get() =
            if (isSoldOutsideFMS) {
                transfers.mapNotNull { it.returnDate }.maxOrNull()
            } else {
                null
            }
    var lastUnstolenDate: OffsetDateTime? = null
    var lastStolenDate: OffsetDateTime? = null
    val stolenDate: OffsetDateTime?
        get() =
            lastStolenDate?.let {
                if (lastUnstolenDate == null || lastUnstolenDate!!.isBefore(lastStolenDate)) {
                    lastStolenDate
                } else {
                    null
                }
            }

    var isResidualValueMarket: Boolean = false
    var maximumServiceLifeInMonths: Int? = null

    var pdiOrderedDate: OffsetDateTime? = null
    var pdiCompletedDate: OffsetDateTime? = null

    var netPriceNewVehicle: BigDecimal? = null
    var newVehicleInvoiceDate: LocalDate? = null

    var fmsPrimaryStatus: String? = null

    val nextProcess: NextProcess?
        get() =
            when {
                !scrapVehicle &&
                    !blockedForSale &&
                    fmsPrimaryStatus in listOf("YF35", "YF40", "YQ98", "YF30")
                -> NextProcess.SALES

                scrapVehicle && fmsPrimaryStatus in listOf("YF69", "YF70", "YXXX") -> NextProcess.SCRAPPED_CAR_RETURNED

                blockedForSale && !scrapVehicle && fmsPrimaryStatus == "YF30" -> NextProcess.PROFITABILITY_AUDIT_IN_PREPARATION

                // we set this, so that a vehicle transfer can be correctly closed. might not be needed though.
                fmsPrimaryStatus == "YF90" -> NextProcess.CHECK_IF_REUSAGE_IS_POSSIBLE

                else -> null
            }

    // sales invoice fields
    val sales: FMSVehicleSalesAggregate = FMSVehicleSalesAggregate()

    // transfer fields
    val transfers: MutableList<FMSTransferAggregate> = mutableListOf()
    var latestTransferAdditionalData: FMSLatestTransferAggregate = FMSLatestTransferAggregate()

    // registrations
    val registrations: MutableList<FMSRegistrationAggregate> = mutableListOf()
    var extendedRegistrationData: FMSExtendedRegistrationAggregate = FMSExtendedRegistrationAggregate()

    fun getTransfersWithOrderNumber(): List<FMSTransferAggregate> {
        val activeTransfer = transfers.firstOrNull { it.deliveryDate != null && it.returnDate == null }
        activeTransfer?.maintenanceOrderNumber = latestTransferAdditionalData.maintenanceOrderNumber
        return transfers
    }

    val errors: MutableList<String?> = mutableListOf()

    fun reportError(error: String?) {
        errors.add(error)
    }

    fun hasCompletedTransfer(): Boolean = transfers.any({ it.returnDate != null })

    val vehicleLocationCompoundName: String?
        get() {
            val fmsCompoundIdTofvmCompoundNameMap: Map<Int, String> =
                mapOf(
                    1 to "ZWZ",
                    2 to "ZWZ",
                    3 to "Compound Siegelsbach",
                    4 to "Compound Vaihingen",
                    5 to "Räderservice / DL",
                    8 to "Compound Weissach",
                    9 to "Werkscompound Kornwestheim",
                    10 to "Compound Illingen",
                    13 to "Abschleppservice / DL",
                    14 to "ZWZ",
                    16 to "Werkscompound Leipzig",
                    17 to "Hafen Bremerhaven",
                    20 to "ZWZ",
                    21 to "ZWZ - VA9 Nacharbeit",
                    22 to "Compound Bietigheim-Bissingen",
                    24 to "Hafen Emden",
                    25 to "Compound Renningen",
                )
            return if (this.compoundNameId.isNullOrBlank()) {
                null
            } else {
                fmsCompoundIdTofvmCompoundNameMap[this.compoundNameId!!.toInt()]
            }
        }
}
