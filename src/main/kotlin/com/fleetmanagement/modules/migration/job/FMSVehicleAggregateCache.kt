package com.fleetmanagement.modules.migration.job

class FMSVehicleAggregateCache {
    private val inMemoryCache = mutableMapOf<String, FMSVehicleAggregate>()
    private val inMemoryCacheByVin = mutableMapOf<String, String>()
    private val inMemoryCacheByEquiNumber = mutableMapOf<Long, String>()

    fun getAllAggregates(): Collection<FMSVehicleAggregate> = inMemoryCache.values

    fun getAggregate(productGuid: String): FMSVehicleAggregate {
        if (inMemoryCache[productGuid] == null) {
            inMemoryCache[productGuid] = FMSVehicleAggregate(productGuid)
        }
        return inMemoryCache[productGuid]!!
    }

    fun registerVin(
        productGuid: String,
        vin: String?,
    ) {
        vin?.let { inMemoryCacheByVin[it] = productGuid }
    }

    fun registerEquiNumber(
        productGuid: String,
        equiNumber: Long,
    ) {
        inMemoryCacheByEquiNumber[equiNumber] = productGuid
    }

    fun getAggregateByVin(vin: String): FMSVehicleAggregate? {
        val productGuid = inMemoryCacheByVin[vin]
        return inMemoryCache[productGuid]
    }

    fun getAggregateByEquiNumber(equiNumber: Long): FMSVehicleAggregate? {
        val productGuid =
            inMemoryCacheByEquiNumber[equiNumber]
        return inMemoryCache[productGuid]
    }
}
