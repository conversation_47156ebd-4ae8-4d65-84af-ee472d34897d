/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.migration.job

import java.math.BigDecimal
import java.time.OffsetDateTime

data class FMSVehicleSalesAggregate(
    var loadingCompletedDate: OffsetDateTime? = null,
    var salesNetPriceEUR: BigDecimal? = null,
    var salesDiscountPercentage: Float = 0.0f,
    var winterTiresId: Long? = null,
    var winterTiresNetPriceEUR: BigDecimal? = null,
    var winterTiresDiscountPercentage: Float = 0.0f,
    var customerPartnerNumber: String? = null,
    var salesPersonNumber: String? = null,
    var reservedForB2C: Boolean? = null,
    var contractSigned: Boolean? = null,
)
