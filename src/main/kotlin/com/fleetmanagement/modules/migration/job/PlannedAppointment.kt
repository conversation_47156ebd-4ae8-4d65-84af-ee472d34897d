package com.fleetmanagement.modules.migration.job

import java.time.OffsetDateTime

data class PlannedAppointment(
    val from: OffsetDateTime,
    val to: OffsetDateTime? = null,
    val responsiblePerson: String,
    val appointmentKind: Int,
) {
    private val dayAndTimeSpecificAppointmentKinds = setOf(1, 2, 3)

    fun isDayAndTimeSpecific(): Boolean = appointmentKind in dayAndTimeSpecificAppointmentKinds

    fun isDaySpecific(): Boolean = !isDayAndTimeSpecific()
}
