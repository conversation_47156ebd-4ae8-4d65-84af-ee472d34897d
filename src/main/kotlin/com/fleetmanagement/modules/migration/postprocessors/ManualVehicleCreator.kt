package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.migration.job.ports.VehicleMigrationFailedException
import com.fleetmanagement.modules.vehicledata.api.CreateOrUpdateVehicle
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateVehicleDTO
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class ManualVehicleCreator(
    @Autowired
    val createOrUpdateVehicle: CreateOrUpdateVehicle,
) {
    fun createManualVehicle(vehicle: FMSVehicleAggregate) {
        try {
            assertValidData(vehicle)
            createOrUpdateVehicle.createOrUpdateVehicle(
                CreateVehicleDTO(
                    vin = checkNotNull(vehicle.vin) { "VIN must not be null" },
                    source = VehicleSource.MANUAL,
                    modelDescription = checkNotNull(vehicle.modelDescription) { "modelDescription must not be null" },
                    manufacturer = vehicle.manufacturer ?: "UNKNOWN",
                    vehicleType = checkNotNull(vehicle.vehicleType) { "vehicleType must not be null" },
                    orderType = vehicle.orderType,
                    financialAssetType = vehicle.financialAssetType,
                    blockedForSale = vehicle.blockedForSale,
                    equipmentNumber = vehicle.equipmentNumber,
                ),
            )
        } catch (e: Throwable) {
            throw ManualVehicleCreationFailed(vehicle.productGuid, e)
        }
    }

    fun assertValidData(aggregate: FMSVehicleAggregate) {
        val message =
            listOf(
                "VIN" to aggregate.vin,
                "modelDescription" to aggregate.modelDescription,
                // TODO: Map Manufacturer correctly
//                "manufacturer" to aggregate.manufacturer,
                "vehicleType" to aggregate.vehicleType,
            ).filter { it.second == null }.joinToString(", ") { "${it.first} must not be null" }
        if (message.isNotBlank()) {
            throw IllegalArgumentException(message)
        }
    }

    class ManualVehicleCreationFailed(
        productGuid: String?,
        rootCause: Throwable,
    ) : VehicleMigrationFailedException(productGuid, "Manual vehicle creation failed: ${rootCause.message}", rootCause)
}
