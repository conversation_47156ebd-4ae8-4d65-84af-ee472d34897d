package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class MigrationHotfixUpdater(
    @Autowired val vehicleLocationUpdater: VehicleLocationUpdater,
) {
    fun applyHotfix(vehicleAggregate: FMSVehicleAggregate) {
        vehicleLocationUpdater.updateVehicleLocation(vehicleAggregate)
    }
}
