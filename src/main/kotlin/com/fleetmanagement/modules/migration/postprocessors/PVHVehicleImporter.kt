package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.vehicledata.api.VehicleAlreadyExists
import com.fleetmanagement.modules.vehicledata.api.VehicleImportInterface
import com.fleetmanagement.modules.vehicledata.api.exceptions.PVHVehicleNotFoundException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PVHVehicleImporter(
    @Autowired val vehicleImportInterface: VehicleImportInterface,
) {
    private val logger = LoggerFactory.getLogger(PVHVehicleImporter::class.java)

    fun attemptPVHImport(vehicleAggregate: FMSVehicleAggregate): <PERSON><PERSON>an {
        val vin = requireNotNull(vehicleAggregate.vin) { "VIN cannot be null for ${vehicleAggregate.productGuid}" }
        try {
            vehicleImportInterface.initialLoadVehicle(vin)
        } catch (e: PVHVehicleNotFoundException) {
            logger.info(
                "Could not find vehicle with VIN $vin in PVH. Assuming it must be imported manually. " +
                    "Message: ${e.message}",
            )
            return false
        } catch (e: VehicleAlreadyExists) {
            logger.info("Vehicle with VIN $vin is already imported. Message: ${e.message}")
            return true
        }
        return true
    }
}
