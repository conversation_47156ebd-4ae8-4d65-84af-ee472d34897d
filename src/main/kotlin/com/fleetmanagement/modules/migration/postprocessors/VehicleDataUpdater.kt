package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.vehicledata.api.AddCurrentMileageReading
import com.fleetmanagement.modules.vehicledata.api.MigrationVehicleUpdateDto
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicle
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import java.util.Optional

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class VehicleDataUpdater(
    private val updateVehicleAPI: UpdateVehicle,
    private val readVehicleByVIN: ReadVehicleByVIN,
    private val addCurrentMileageReading: AddCurrentMileageReading,
) {
    fun updateVehicleData(vehicle: FMSVehicleAggregate) {
        val vin = requireNotNull(vehicle.vin) { "VIN cannot be null for ${vehicle.productGuid}" }

        vehicle.autocorrectForFVMValidations()

        updateVehicleAPI.updateMigrationVehicle(
            vin = vin,
            vehicleUpdateDto =
                MigrationVehicleUpdateDto(
                    blockedForSale = vehicle.blockedForSale.let { Optional.of(it) },
                    scrapVehicle = Optional.ofNullable(vehicle.scrapVehicle),
                    assetType = vehicle.financialAssetType?.let { Optional.of(it) },
                    soldDate = vehicle.soldDate?.let { Optional.of(it) },
                    scrappedDate = vehicle.scrappedDate?.let { Optional.of(it) },
                    stolenDate = vehicle.stolenDate?.let { Optional.of(it) },
                    isClassic = vehicle.isClassic.let { Optional.of(it) },
                    approvedForScrappingDate = vehicle.approvedForScrappingDate?.let { Optional.of(it) },
                    vehicleSentToSalesDate = vehicle.vehicleSentToSalesDate?.let { Optional.of(it) },
                    isResidualValueMarket = Optional.ofNullable(vehicle.isResidualValueMarket),
                    isPreparationNecessary = null,
                    preparationDoneDate = vehicle.preparationDoneDate?.let { Optional.of(it) },
                    nextProcess = Optional.ofNullable(vehicle.nextProcess),
                    costEstimationOrderedDate = null,
                    netPriceWithExtras = vehicle.netPriceNewVehicle?.let { Optional.of(it) },
                    appraisalNetPrice = null,
                    soldCupCarDate = vehicle.soldCupCarDate?.let { Optional.of(it) },
                    scrappedVehicleOfferedDate = null,
                    profitabilityAuditDate = null,
                    factoryCarPreparationOrderNumber = null,
                    isUsedCar = Optional.of(vehicle.hasCompletedTransfer()),
                    maximumServiceLifeInMonths = vehicle.maximumServiceLifeInMonths?.let { Optional.of(it) },
                    equipmentId = Optional.ofNullable(vehicle.equipmentId),
                    equipmentNumber = Optional.ofNullable(vehicle.equipmentNumber),
                    zp8Date = vehicle.zp8Date?.let { Optional.of(it) },
                    newVehicleInvoiceDate = Optional.ofNullable(vehicle.newVehicleInvoiceDate),
                ),
        )

        addCurrentMileage(aggregate = vehicle)
    }

    fun addCurrentMileage(aggregate: FMSVehicleAggregate) {
        aggregate.currentMileage?.also { currentMileage ->
            val vin = checkNotNull(aggregate.vin) { "VIN cannot be null when adding mileage" }
            val vehicleId = readVehicleByVIN.readVehicleByVIN(vin).id
            addCurrentMileageReading.addCurrentMileageReading(
                vehicleId,
                currentMileage,
                OffsetDateTime.now(),
                MileageReadingSource.FMS,
            )
        }
    }

    /**
     * Corrects some data that are validated in FVM, but are inconsistent in FMS.
     */
    private fun FMSVehicleAggregate.autocorrectForFVMValidations() {
        // If scrapping related date is set, mark for scrapping
        if (scrappedDate != null || approvedForScrappingDate != null) {
            this.scrapVehicle = true
        }
    }
}
