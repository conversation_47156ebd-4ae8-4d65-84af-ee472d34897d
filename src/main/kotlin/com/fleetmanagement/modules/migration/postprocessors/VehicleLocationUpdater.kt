package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehiclelocation.api.VehicleLocationEvent
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationValidationException
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import java.util.UUID

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class VehicleLocationUpdater(
    private val readVehicleByVIN: ReadVehicleByVIN,
    private val vehicleLocationEvent: VehicleLocationEvent,
) {
    fun updateVehicleLocation(vehicle: FMSVehicleAggregate) {
        val compoundName = vehicle.vehicleLocationCompoundName?.takeIf { it.isNotBlank() } ?: return
        val vehicleId = getVehicleId(vehicle)

        val locationData =
            if (isZwzCompound(compoundName)) {
                parseZwzLocation(vehicle.parkingLot)
            } else {
                parseNonZwzLocation(vehicle.parkingLot)
            }

        try {
            createLocationEvent(vehicleId, compoundName, locationData, vehicle.parkingLotDate)
        } catch (e: VehicleLocationValidationException) {
            logger.error("Could not create location event for vehicle $vehicleId with parkingLot ${vehicle.parkingLot}.", e)
            vehicle.reportError(
                "Could not create location for vehicle $vehicleId: ${e.message} with parkingLot {vehicle.parkingLot}. " +
                    "Retry with Compound only.",
            )
            val fallbackLocationData =
                locationData.copy(
                    level = null,
                    parkingLot = null,
                    comment = "migriert, Parkplatz: ${vehicle.parkingLot}",
                )
            createLocationEvent(vehicleId, compoundName, fallbackLocationData, vehicle.parkingLotDate)
        }
    }

    private fun getVehicleId(vehicle: FMSVehicleAggregate): UUID {
        val vin = requireNotNull(vehicle.vin) { "VIN cannot be null for ${vehicle.productGuid}" }
        return readVehicleByVIN.readVehicleByVIN(vin).id
    }

    private fun isZwzCompound(compoundName: String): Boolean = compoundName.contains("ZWZ")

    private fun parseZwzLocation(rawParkingLot: String?): LocationData {
        val trimmed = rawParkingLot?.trim()
        if (trimmed.isNullOrEmpty()) {
            return LocationData(building = DEFAULT_ZWZ_BUILDING)
        }

        return when {
            FOUR_DIGITS_PATTERN.matches(trimmed) ->
                LocationData(
                    building = DEFAULT_ZWZ_BUILDING,
                    level = trimmed.substring(0, 1),
                    parkingLot = trimmed.substring(1),
                )

            UP_TO_THREE_DIGITS_PATTERN.matches(trimmed) ->
                LocationData(
                    building = DEFAULT_ZWZ_BUILDING,
                    level = "0",
                    parkingLot = trimmed,
                )

            SEPARATOR_PATTERN.matches(trimmed) -> {
                val (level, lot) = SEPARATOR_PATTERN.find(trimmed)!!.destructured
                LocationData(
                    building = DEFAULT_ZWZ_BUILDING,
                    level = level,
                    parkingLot = lot,
                )
            }

            else ->
                LocationData(
                    building = DEFAULT_ZWZ_BUILDING,
                    comment = "migriert, Parkplatz: $trimmed",
                )
        }
    }

    private fun parseNonZwzLocation(rawParkingLot: String?): LocationData =
        if (rawParkingLot.isNullOrBlank()) {
            LocationData()
        } else {
            LocationData(comment = "migriert, Parkplatz: $rawParkingLot")
        }

    private fun createLocationEvent(
        vehicleId: UUID,
        compoundName: String,
        locationData: LocationData,
        parkingLotDate: OffsetDateTime?,
    ) {
        vehicleLocationEvent.addLocationEvent(
            VehicleLocation(
                vehicleId = vehicleId,
                eventType = "IN",
                compoundName = compoundName,
                building = locationData.building,
                level = locationData.level,
                parkingLot = locationData.parkingLot(),
                source = "MANUAL",
                occurredOn = parkingLotDate ?: OffsetDateTime.now(),
                comment = locationData.comment,
            ),
        )
    }

    private data class LocationData(
        val building: String? = null,
        val level: String? = null,
        private val parkingLot: String? = null,
        val comment: String = "migriert",
    ) {
        fun parkingLot(): String? =
            parkingLot?.let {
                return (level ?: "0") + parkingLot.padStart(3, '0')
            }
    }

    companion object {
        private val FOUR_DIGITS_PATTERN = Regex("^\\d{4}$")
        private val UP_TO_THREE_DIGITS_PATTERN = Regex("^\\d{1,3}$")
        private val SEPARATOR_PATTERN = Regex("^(\\d+)[./\\\\;,](\\d+)$")
        private val logger = LoggerFactory.getLogger(VehicleLocationUpdater::class.java)

        private const val DEFAULT_ZWZ_BUILDING = "Parkhaus"
    }
}
