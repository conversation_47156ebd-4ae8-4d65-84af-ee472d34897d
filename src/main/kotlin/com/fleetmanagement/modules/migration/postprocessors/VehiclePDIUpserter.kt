package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.predeliveryinspection.application.PreDeliveryInspectionNewDto
import com.fleetmanagement.modules.predeliveryinspection.application.PreDeliveryInspectionUpdateDto
import com.fleetmanagement.modules.predeliveryinspection.application.port.CreatePreDeliveryInspectionUseCase
import com.fleetmanagement.modules.predeliveryinspection.application.port.PreDeliveryInspectionId
import com.fleetmanagement.modules.predeliveryinspection.application.port.ReadPreDeliveryInspectionUseCase
import com.fleetmanagement.modules.predeliveryinspection.application.port.UpdatePreDeliveryInspectionUseCase
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import org.springframework.stereotype.Component
import java.util.Optional
import java.util.UUID

@Component
class VehiclePDIUpserter(
    private val readVehicleByVIN: ReadVehicleByVIN,
    private val createPreDeliveryInspectionUseCase: CreatePreDeliveryInspectionUseCase,
    private val readPreDeliveryInspectionUseCase: ReadPreDeliveryInspectionUseCase,
    private val updatePreDeliveryInspectionUseCase: UpdatePreDeliveryInspectionUseCase,
) {
    fun upsert(vehicle: FMSVehicleAggregate) {
        val vin = requireNotNull(vehicle.vin) { "VIN cannot be null for ${vehicle.productGuid}" }
        val vehicleId = readVehicleByVIN.readVehicleByVIN(vin).id
        val pdiToUpdate = existingPDIForVehicle(vehicleId) ?: createNewPDIForVehicle(vehicleId)

        updatePreDeliveryInspectionUseCase.updatePreDeliveryInspection(
            PreDeliveryInspectionId(pdiToUpdate.id),
            PreDeliveryInspectionUpdateDto(
                tireSet = null,
                isRelevant = null,
                foiling = null,
                refuel = null,
                charge = null,
                digitalLogbook = null,
                licencePlateMounting = null,
                orderedDate = vehicle.pdiOrderedDate?.let { Optional.of(it) },
                completedDate = vehicle.pdiCompletedDate?.let { Optional.of(it) },
                plannedDate = vehicle.pdiOrderedDate?.let { Optional.of(it) },
                comment = null,
            ),
        )
    }

    private fun createNewPDIForVehicle(vehicleId: UUID) =
        createPreDeliveryInspectionUseCase.createPreDeliveryInspection(
            PreDeliveryInspectionNewDto(
                vehicleId = vehicleId,
                tireSet = null,
                isRelevant = null,
                foiling = null,
                refuel = null,
                charge = null,
                digitalLogbook = null,
                licencePlateMounting = null,
            ),
        )

    private fun existingPDIForVehicle(vehicleId: UUID) =
        readPreDeliveryInspectionUseCase.readPreDeliveryInspection(
            vehicleId,
        )
}
