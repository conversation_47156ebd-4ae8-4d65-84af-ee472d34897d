/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSExtendedRegistrationAggregate
import com.fleetmanagement.modules.migration.job.FMSRegistrationAggregate
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.migration.job.ports.VehicleMigrationFailedException
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.WriteRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateVehicleRegistration
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Service
import kotlin.jvm.optionals.getOrNull

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Service
class VehicleRegistrationCreator(
    private val readVehicleByVIN: ReadVehicleByVIN,
    private val readRegistrationOrder: ReadRegistrationOrder,
    private val writeRegistrationOrder: WriteRegistrationOrder,
) {
    private val logger = LoggerFactory.getLogger(VehicleRegistrationCreator::class.java)

    fun create(aggregate: FMSVehicleAggregate) {
        if (aggregate.registrations.isEmpty()) return
        val vin = checkNotNull(aggregate.vin) { "VIN cannot be null" }
        try {
            val createRegistrations =
                buildNewVehicleRegistrations(
                    vin = vin,
                    registrations = aggregate.registrations,
                    extendedData = aggregate.extendedRegistrationData,
                )
            writeRegistrationOrder.createRegistrationOrders(createRegistrations)
        } catch (e: Throwable) {
            throw VehicleRegistrationCreationFailed(aggregate.productGuid, e)
        }
    }

    private fun buildNewVehicleRegistrations(
        vin: String,
        registrations: MutableList<FMSRegistrationAggregate>,
        extendedData: FMSExtendedRegistrationAggregate,
    ): List<CreateVehicleRegistration> {
        // Step 1: Filter out existing registrations
        val newRegistrations = filterNewRegistrations(vin, registrations)

        // Step 2: Adjust FMS registrationType = 3 to FVM registration type based on presence of type 1
        val adjustedRegistrations = adjustTypeThreeRegistrations(newRegistrations)

        // Step 3: Create final objects
        val latestRegistration = adjustedRegistrations.maxByOrNull { it.registrationDate }
        return adjustedRegistrations.map { reg ->
            CreateVehicleRegistration(
                vin = vin,
                licencePlate = reg.licencePlate,
                registrationArea = reg.registrationArea,
                registrationType = reg.registrationType,
                registrationDate = reg.registrationDate.toZonedDateTime(),
                remark = reg.remark,
                tsn = extendedData.tsnForAllRegistrations,
                hsn = extendedData.hsnForAllRegistrations,
                briefNumber = if (reg == latestRegistration) extendedData.briefNumberOnlyForLatestRegistration else null,
                sfme = if (reg == latestRegistration) extendedData.sfmeOnlyForLatestRegistration else null,
            )
        }
    }

    private fun adjustTypeThreeRegistrations(registrations: List<FMSRegistrationAggregate>): List<FMSRegistrationAggregate> {
        val hasType1 = registrations.any { it.registrationType == 1 }
        return registrations.map { reg ->
            if (reg.registrationType == 3) {
                reg.copy(registrationType = if (hasType1) 2 else 1)
            } else {
                reg
            }
        }
    }

    private fun filterNewRegistrations(
        vin: String,
        incoming: MutableList<FMSRegistrationAggregate>,
    ): List<FMSRegistrationAggregate> {
        val vehicleId = readVehicleByVIN.readVehicleByVIN(vin).id
        val existing = readRegistrationOrder.getCompletedOrdersBy(vehicleId).data

        return incoming.filterNot { new ->
            val alreadyExists =
                existing.any { old ->
                    old.licencePlate?.getOrNull() == new.licencePlate &&
                        old.registrationType?.getOrNull() == new.registrationType &&
                        old.registrationDate?.getOrNull()?.toLocalDate() == new.registrationDate.toLocalDate()
                }
            if (alreadyExists) {
                logger.info(
                    "Duplicate registration skipped for VIN: $vin, plate: ${new.licencePlate}, date: ${new.registrationDate}, type: ${new.registrationType}",
                )
            }
            alreadyExists
        }
    }

    class VehicleRegistrationCreationFailed(
        productGuid: String?,
        rootCause: Throwable,
    ) : VehicleMigrationFailedException(
            productGuid,
            "Vehicle registration creation failed: ${rootCause.message}",
            rootCause,
        )
}
