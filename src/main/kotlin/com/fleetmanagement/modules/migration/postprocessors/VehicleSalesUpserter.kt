package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.migration.job.ports.VehicleMigrationFailedException
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehiclesales.api.CreateVehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.ReadVehicleInvoices
import com.fleetmanagement.modules.vehiclesales.api.UpsertVehicleSale
import com.fleetmanagement.modules.vehiclesales.api.domain.PaymentType
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoiceCreateDto
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSaleUpdateDto
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Service
import java.util.Optional
import java.util.UUID

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Service
class VehicleSalesUpserter(
    private val upsertVehicleSale: UpsertVehicleSale,
    private val readVehicleByVIN: ReadVehicleByVIN,
    private val createVehicleInvoiceService: CreateVehicleInvoice,
    private val readVehicleInvoiceService: ReadVehicleInvoices,
) {
    private final val logger: Logger = LoggerFactory.getLogger(VehicleSalesUpserter::class.java)
    private val statusToProcess = setOf(VehicleStatus.S400.name, VehicleStatus.S410.name, VehicleStatus.S420.name)

    fun upsertSalesData(aggregate: FMSVehicleAggregate) {
        try {
            val vin = checkNotNull(aggregate.vin) { "VIN cannot be null" }
            val existingVehicle = readVehicleByVIN.readVehicleByVIN(vin)
            upsertVehicleSale.upsertMigrationVehicleSalesData(
                VehicleSaleUpdateDto(
                    vehicleId = existingVehicle.id,
                    loadingCompletedDate = aggregate.sales.loadingCompletedDate?.let { Optional.of(it) },
                    reservedForB2C = aggregate.sales.reservedForB2C?.let { Optional.of(it) },
                    contractSigned = aggregate.sales.contractSigned?.let { Optional.of(it) },
                    comment = null,
                    plannedDeliveryDate = null,
                ),
            )
            if (aggregate.sales.reservedForB2C == true && aggregate.sales.contractSigned == true) {
                if (existingVehicle.status !in statusToProcess) {
                    logger.error("Invoice cannot be created for vehicle status: ${existingVehicle.status} with VIN $vin")
                    aggregate.reportError(
                        "Invoice cannot be created for vehicle status: ${existingVehicle.status} with VIN $vin",
                    )
                    return
                }
                if (readVehicleInvoiceService.readCurrentInvoiceBy(existingVehicle.id) != null) {
                    logger.warn("Vehicle with VIN $vin already has an invoice, skipping invoice creation.")
                    aggregate.reportError(
                        "Vehicle with VIN $vin already has an invoice, skipping invoice creation.",
                    )
                    return
                }
                createVehicleInvoice(existingVehicle.id, aggregate)
            }
        } catch (e: Throwable) {
            throw VehicleSalesUpsertFailed(aggregate.productGuid, e)
        }
    }

    private fun createVehicleInvoice(
        vehicleId: UUID,
        vehicle: FMSVehicleAggregate,
    ) {
        createVehicleInvoiceService.createInvoice(
            VehicleInvoiceCreateDto(
                vehicleId = vehicleId,
                salesDiscountPercentage = vehicle.sales.salesDiscountPercentage,
                salesNetPriceEUR =
                    checkNotNull(
                        vehicle.sales.salesNetPriceEUR,
                    ) { "Sales net price cannot be null for ${vehicle.productGuid}" },
                winterTiresDiscountPercentage = vehicle.sales.winterTiresDiscountPercentage,
                winterTiresNetPriceEUR = vehicle.sales.winterTiresNetPriceEUR,
                winterTiresId = vehicle.sales.winterTiresId,
                customerInvoiceRecipient = true,
                paymentType = PaymentType.CASH,
                customerPartnerNumber =
                    checkNotNull(
                        vehicle.sales.customerPartnerNumber,
                    ) { "Customer partner number cannot be null for ${vehicle.productGuid}" },
                invoiceRecipientNumber =
                    checkNotNull(vehicle.sales.customerPartnerNumber) {
                        "Customer partner number cannot be null for ${vehicle.productGuid}"
                    },
                salesPersonNumber =
                    checkNotNull(
                        vehicle.sales.salesPersonNumber,
                    ) { "Sales person number cannot be null for ${vehicle.productGuid}" },
            ),
        )
    }

    class VehicleSalesUpsertFailed(
        productGuid: String?,
        rootCause: Throwable,
    ) : VehicleMigrationFailedException(
            productGuid,
            "Vehicle sales data upsert failed: ${rootCause.message}",
            rootCause,
        )
}
