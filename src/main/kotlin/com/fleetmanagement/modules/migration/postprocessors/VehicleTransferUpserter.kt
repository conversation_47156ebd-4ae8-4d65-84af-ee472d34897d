/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.appointments.MsBookingAppointmentsHandler
import com.fleetmanagement.modules.migration.job.FMSLatestTransferAggregate
import com.fleetmanagement.modules.migration.job.FMSRegistrationAggregate
import com.fleetmanagement.modules.migration.job.FMSTransferAggregate
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.migration.job.PlannedAppointment
import com.fleetmanagement.modules.msbooking.application.AppointmentType
import com.fleetmanagement.modules.vehicletransfer.application.port.MigrateVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferMigrationDto
import com.fleetmanagement.modules.vehicletransfer.domain.service.VehicleTransferMigrationResult
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Service
import java.time.OffsetDateTime

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Service
class VehicleTransferUpserter(
    private val migrateVehicleTransferUseCase: MigrateVehicleTransferUseCase,
    private val appointmentHandler: MsBookingAppointmentsHandler,
) {
    fun upsert(aggregate: FMSVehicleAggregate) {
        if (aggregate.transfers.isEmpty() && !hasAdditionalPlannedTransfer(aggregate)) return
        val vin = checkNotNull(aggregate.vin) { "VIN cannot be null" }

        val latestTransfer = getLatestTransfer(aggregate)

        aggregate.getTransfersWithOrderNumber().filter { t -> t != latestTransfer }.forEach {
            migrateSingleTransfer(it.toMigrationDto(vin, aggregate.registrations), aggregate)
        }

        // latest transfer must be created last, because vehicleTransfer module will
        // remove any planned transfer for a vehicle before each transfer migration creation.
        // The reason is: this way it prevents stale and duplicate planned transfers when migration is executed multiple times,
        // since we have no fmsDeliveryIndex for those to identify it
        migrateLatestTransfer(aggregate, latestTransfer)
    }

    private fun migrateLatestTransfer(
        aggregate: FMSVehicleAggregate,
        transfer: FMSTransferAggregate,
    ) {
        val latestTransferDto =
            transfer.toMigrationDto(
                aggregate.vin!!,
                aggregate.registrations,
                aggregate.latestTransferAdditionalData,
            )

        val result = migrateSingleTransfer(latestTransferDto, aggregate)

        if (validateAppointments(aggregate, transfer)) {
            handleMSBookingAppointments(aggregate, result)
        }
    }

    private fun isActiveOrPlanned(transferInfo: FMSTransferAggregate): Boolean = transferInfo.returnDate == null

    private fun migrateSingleTransfer(
        transferMigrationDto: VehicleTransferMigrationDto,
        aggregate: FMSVehicleAggregate,
    ): VehicleTransferMigrationResult {
        val result = migrateVehicleTransferUseCase.migrateVehicleTransfer(transferMigrationDto)
        val errorsToAttach =
            (result.errors + result.warnings).map { "Transfer[${transferMigrationDto.deliveryIndex}]: $it" }
        aggregate.errors.addAll(errorsToAttach)
        return result
    }

    private fun handleMSBookingAppointments(
        aggregate: FMSVehicleAggregate,
        result: VehicleTransferMigrationResult,
    ) {
        fun createAppointment(
            appointment: PlannedAppointment,
            type: AppointmentType,
        ) = runCatching {
            appointmentHandler.upsertPendingMSBookingAppointment(
                appointment = appointment,
                vin = aggregate.vin!!,
                vehicleTransferKey = result.vehicleTransferKey!!,
                appointmentType = type,
            )
        }.onFailure { e ->
            aggregate.reportError(
                "Failed to create MS Booking appointment for personal vehicle: ${e.message}",
            )
        }

        val additionalData = aggregate.latestTransferAdditionalData
        if (additionalData.isPersonalVehicle) {
            additionalData.plannedDeliveryAppointment
                ?.takeIf { it.isDayAndTimeSpecific() }
                ?.let { createAppointment(it, AppointmentType.DELIVERY) }

            additionalData.plannedReturnAppointment
                ?.takeIf { it.isDayAndTimeSpecific() }
                ?.let { createAppointment(it, AppointmentType.RETURN) }
        }
    }

    private fun FMSTransferAggregate.toMigrationDto(
        vin: String,
        registrations: List<FMSRegistrationAggregate> = emptyList(),
        additionalInfo: FMSLatestTransferAggregate? = null,
    ): VehicleTransferMigrationDto =
        VehicleTransferMigrationDto(
            vin = vin,
            deliveryIndex = this.fmsDeliveryIndex,
            deliveryDate = this.deliveryDate,
            mileageAtDelivery = this.mileageAtDelivery,
            returnDate = this.returnDate,
            mileageAtReturn = this.mileageAtReturn,
            vehicleResponsiblePerson = this.vehicleResponsiblePerson,
            maintenanceOrderNumber = this.maintenanceOrderNumber,
            deliveryComment = null,
            returnComment = null,
            desiredDeliveryDate = null,
            vehicleUsage = this.vehicleUsageGroupDescription,
            internalContactPerson = additionalInfo?.internalContactPerson,
            internalOrderNumber = additionalInfo?.internalOrderNumber,
            usingCostCenter = additionalInfo?.usingCostCenter ?: this.vehicleUsingCostCenter,
            plannedDeliveryDate = additionalInfo?.calculateFVMPlannedDeliveryDate(),
            plannedReturnDate = additionalInfo?.calculateFVMPlannedReturnDate(),
            latestReturnDate = additionalInfo?.latestReturnDate,
            deliveryLeipzig = additionalInfo?.deliveryLeipzig,
            licensePlate = if (isActiveOrPlanned(this)) getLatestLicensePlate(registrations) else null,
        )

    private fun getLatestLicensePlate(registrations: List<FMSRegistrationAggregate>): String? =
        registrations.maxByOrNull { it.registrationDate }?.licencePlate

    private fun validateAppointments(
        aggregate: FMSVehicleAggregate,
        transfer: FMSTransferAggregate,
    ): Boolean {
        val deliveryDate = aggregate.latestTransferAdditionalData.plannedDeliveryAppointment
        val returnDate = aggregate.latestTransferAdditionalData.plannedReturnAppointment
        return if (
            deliveryDate?.let { it.responsiblePerson == transfer.vehicleResponsiblePerson } == false ||
            returnDate?.let { it.responsiblePerson == transfer.vehicleResponsiblePerson } == false
        ) {
            aggregate.reportError(
                "Planned appointment's responsible person did not match the latest vehicle transfer's responsible person!",
            )
            false
        } else {
            true
        }
    }

    private fun getLatestTransfer(aggregate: FMSVehicleAggregate): FMSTransferAggregate =
        if (hasAdditionalPlannedTransfer(aggregate)) {
            FMSTransferAggregate(
                null,
                vehicleResponsiblePerson = aggregate.latestTransferAdditionalData.vehicleResponsiblePerson,
                vehicleUsageGroupDescription = aggregate.latestTransferAdditionalData.vehicleUsageDescription,
            )
        } else {
            aggregate.transfers
                .withIndex()
                .maxByOrNull { it.value.returnDate ?: OffsetDateTime.MAX }!!
                .value
        }

    private fun hasAdditionalPlannedTransfer(aggregate: FMSVehicleAggregate): Boolean =
        aggregate.fmsPrimaryStatus in FMSLatestTransferAggregate.Companion.FMS_STATUSES_SIGNIFYING_PLANNED_TRANSFER
}
