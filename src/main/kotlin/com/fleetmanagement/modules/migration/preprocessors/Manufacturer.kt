package com.fleetmanagement.modules.migration.preprocessors

object Manufacturer {
    val lookupTable: Map<String, String> =
        mapOf(
            "AM" to "Aston Martin",
            "AP" to "Aprilia",
            "AR" to "Alfa Romeo",
            "AU" to "Audi",
            "BE" to "Bentley",
            "BG" to "Bugatti",
            "BM" to "BMW",
            "BT" to "BT Deutschland",
            "BU" to "BUICK (GM)",
            "BY" to "Bentley",
            "CA" to "Cadillac (GM)",
            "CH" to "Chevrolet (GM)",
            "CI" to "Citroen",
            "CL" to "Chrysler",
            "CP" to "CPC",
            "CR" to "CROWN",
            "DA" to "Daewoo / Korea",
            "DC" to "Daimler Chrysler",
            "DU" to "Ducati",
            "FA" to "First Automobil Works (China)",
            "FD" to "Fendt",
            "FE" to "Ferrari",
            "FI" to "Fiat",
            "FM" to "Famek",
            "FN" to "Fantuzzi",
            "FO" to "Ford",
            "FU" to "Fuji Heavy",
            "GM" to "General Motors",
            "GR" to "Gruse",
            "HA" to "Hartmann",
            "HD" to "Harley Davidson",
            "HK" to "HAKO",
            "HO" to "Honda",
            "HR" to "Hydrobull",
            "HU" to "Humbauer",
            "HY" to "Hyundai",
            "IC" to "Iran Khodro",
            "JB" to "Jin Bei",
            "JD" to "John Deere",
            "JE" to "Jeep",
            "JG" to "Jaguar",
            "JO" to "Johta",
            "JU" to "Jungheinrich",
            "KA" to "Kaiser und Kraft",
            "KB" to "Kässbohrer",
            "KR" to "Kramer",
            "KT" to "KTM",
            "KÖ" to "König Anhänger",
            "LA" to "Lancia",
            "LB" to "Lamborghini",
            "LE" to "Lexus",
            "LI" to "Linde",
            "LO" to "Lotus",
            "LR" to "Land Rover",
            "MA" to "Mazda",
            "MB" to "Mercedes Benz",
            "MC" to "McLaren",
            "ME" to "Melex",
            "MI" to "Mitsubishi",
            "MN" to "MAN",
            "MS" to "Maserati",
            "MU" to "Muenz Tiefladeanhänger",
            "NI" to "Nissan",
            "OL" to "Oldsmobile (GM)",
            "OP" to "Opel",
            "PC" to "Pontiac",
            "PE" to "Peugeot",
            "PF" to "Pfaff-Silberblau",
            "PI" to "PIAGGIO",
            "PO" to "Porsche",
            "RC" to "China First (RC)",
            "RE" to "Renault",
            "RO" to "Rover",
            "SA" to "Saturn",
            "SB" to "Saab",
            "SC" to "Schenk",
            "SE" to "Seat",
            "SH" to "Shanghai Volkswagen",
            "SK" to "Skoda",
            "SL" to "Schuler",
            "SM" to "Schwarzmueller",
            "SN" to "Scania",
            "SP" to "Speedywash",
            "SR" to "Subaru",
            "ST" to "Still",
            "SU" to "Suzuki",
            "SZ" to "Kundenprojektfahrzeug",
            "TA" to "Tatra",
            "TE" to "Techau",
            "TO" to "Toyota",
            "VO" to "Volvo",
            "VW" to "Volkswagen",
            "WA" to "WAZ",
            "WG" to "Wagner",
            "XX" to "Kundenprojektfahrzeug",
            "YH" to "Yamaha",
            "ZF" to "ZF Getriebe",
            "ZZ" to "Sonsige",
        )
}
