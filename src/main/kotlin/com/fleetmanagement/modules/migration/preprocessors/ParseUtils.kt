package com.fleetmanagement.modules.migration.preprocessors

import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.util.Calendar
import java.util.Calendar.HOUR_OF_DAY
import java.util.Calendar.MINUTE
import java.util.Calendar.SECOND
import java.util.Date

object ParseUtils {
    fun Date.toGermanyOffsetDateTime(): OffsetDateTime {
        val zone = ZoneId.of("Europe/Berlin")
        val localDate = this.toInstant().atZone(zone).toLocalDate()
        val localDateTime = localDate.atTime(12, 0) // noon to avoid DST issues
        return localDateTime.atZone(zone).toOffsetDateTime()
    }

    fun Date.toGermanyLocalDate(): LocalDate {
        val zone = ZoneId.of("Europe/Berlin")
        return toInstant().atZone(zone).toLocalDate()
    }

    fun combineDatesAsGermanyOffsetDateTime(
        date: Date,
        time: Date,
    ): OffsetDateTime? {
        val zone = ZoneId.of("Europe/Berlin")
        val localDate = date.toInstant().atZone(zone).toLocalDate()

        val calendar = Calendar.getInstance().apply { this.time = time }
        val localTime =
            LocalTime.of(
                calendar.get(HOUR_OF_DAY),
                calendar.get(MINUTE),
                calendar.get(SECOND),
            )

        return OffsetDateTime
            .of(localDate, localTime, zone.rules.getOffset(localDate.atStartOfDay(zone).toInstant()))
    }

    fun parseFMSBoolean(value: String?) = value?.lowercase()?.trim() == "x"
}
