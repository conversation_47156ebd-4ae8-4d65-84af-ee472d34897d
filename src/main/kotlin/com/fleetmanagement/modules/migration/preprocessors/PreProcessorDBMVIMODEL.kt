package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorDBMVIMODEL(
    val excelPreprocessor: ExcelPreprocessor,
) : FMSRowPreprocessor,
    Preprocessor {
    private final val fileName = "DBM_V_IMODEL.XLSX"

    companion object {
        private const val HEADER_PRODUCT_GUID = "PRODUCT_GUID"
        private const val HEADER_MCODECS = "MCODECS"
        private const val HEADER_PRODUCT_VCLASS = "VCLASS"
    }

    private val expectedHeaders = setOf(HEADER_PRODUCT_GUID, HEADER_MCODECS, HEADER_PRODUCT_VCLASS)

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val productGuid = row.getStringValue(HEADER_PRODUCT_GUID)!!
        val aggregate = cache.getAggregate(productGuid)

        withErrorHandling(fileName, aggregate) {
            val orderType = row.getStringValue(HEADER_MCODECS)
            if (orderType?.lowercase()?.trim() != "fremd") {
                aggregate.orderType = orderType
            }

            val vehicleType = row.getStringValue(HEADER_PRODUCT_VCLASS)
            aggregate.vehicleType =
                when (vehicleType?.uppercase()?.trim()) {
                    "PKW" -> VehicleType.PKW
                    "LKW" -> VehicleType.TRUCK
                    "ANHÄ" -> VehicleType.TRAILER
                    "TRAN" -> VehicleType.TRANSPORTER
                    "MOT" -> VehicleType.MOTORCYCLE
                    else -> {
                        aggregate.reportError("Unknown VCLASS/vehicleType $vehicleType")
                        null
                    }
                }
        }
    }
}
