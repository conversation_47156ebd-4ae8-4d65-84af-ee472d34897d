package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.toGermanyLocalDate
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.toGermanyOffsetDateTime
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.junit.jupiter.api.Order
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
/**
 * This will ensure that the VIN and EquiNumber are present in the cache [FMSVehicleAggregateCache] before we call the methods
 * getAggregateByVin or getAggregateByEquiNumber
 */
@Order(1)
class PreProcessorDLZM(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    private final val fileName = "DLZM.XLSX"

    companion object {
        private const val HEADER_PRODUCT_GUID = "Produkt-GUID"
        private const val HEADER_VIN = "FahrzeugidentNr"
        private const val HEADER_MODEL_DESCRIPTION = "Bestelltyp Text"
        private const val HEADER_EQUIPMENT = "Equipment"
        private const val HEADER_PRIMARY_STATUS = "Primärstatus"
        private const val HEADER_MILEAGE = "Zählerstand"
        private const val HEADER_SOLD_DATE = "Fakturiert"
        private const val HEADER_SCRAPPED_DATE = "Fahrzeug verschrottet"
        private const val HEADER_APPROVED_FOR_SCRAPPING_DATE = "Weiterverwendung Verschrottung"
        private const val HEADER_SENT_TO_SALES_DATE = "Weiterverwendung Verkauf"
        private const val HEADER_PREPARATION_DONE_DATE = "IST Ende Aufbereitung VKT"
        private const val HEADER_MAX_SERVICE_LIFE_MONTHS = "Laufzeit in Monaten"
        private const val HEADER_DELIVERY_LEIPZIG = "Auslieferung/Rücknahme Leipzig"
        private const val HEADER_VEHICLE_RESPONSIBLE_PERSON = "Interner Fahrzeugverantwortlicher"
        private const val HEADER_LATEST_RETURN_DATE = "FP30 Späteste Rückg."
        private const val HEADER_MODEL_RANGE = "Baureihe"
        private const val HEADER_ZP8_DATE = "ZP8 Fahrzeug produz."
        private const val HEADER_LOADING_COMPLETED_DATE = "Fahrzeug verladen/ausgeliefert"
        private const val HEADER_NEW_VEHICLE_INVOICE_DATE = "Neuwagenfaktura"
    }

    private val expectedHeaders =
        setOf(
            HEADER_PRODUCT_GUID,
            HEADER_VIN,
            HEADER_MODEL_DESCRIPTION,
            HEADER_EQUIPMENT,
            HEADER_PRIMARY_STATUS,
            HEADER_MILEAGE,
            HEADER_SOLD_DATE,
            HEADER_SCRAPPED_DATE,
            HEADER_APPROVED_FOR_SCRAPPING_DATE,
            HEADER_SENT_TO_SALES_DATE,
            HEADER_PREPARATION_DONE_DATE,
            HEADER_VEHICLE_RESPONSIBLE_PERSON,
            HEADER_MAX_SERVICE_LIFE_MONTHS,
            HEADER_DELIVERY_LEIPZIG,
            HEADER_LATEST_RETURN_DATE,
            HEADER_MODEL_RANGE,
            HEADER_ZP8_DATE,
            HEADER_LOADING_COMPLETED_DATE,
        )

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val productGuid = row.getStringValue(HEADER_PRODUCT_GUID)
        checkNotNull(productGuid) { "FMS data-export must contain PRODUCT_GUID column!" }
        val aggregate = cache.getAggregate(productGuid)

        withErrorHandling(fileName, aggregate) {
            aggregate.vin = row.getStringValue(HEADER_VIN)
            cache.registerVin(productGuid, aggregate.vin)

            aggregate.modelDescription = row.getStringValue(HEADER_MODEL_DESCRIPTION)

            val equipmentNumber = row.getStringValue(HEADER_EQUIPMENT)?.toLong()
            equipmentNumber?.let {
                aggregate.equipmentNumber = it
                cache.registerEquiNumber(productGuid, it)
            }

            aggregate.fmsPrimaryStatus = row.getStringValue(HEADER_PRIMARY_STATUS)

            val mileage = row.getIntValue(HEADER_MILEAGE)
            aggregate.currentMileage = mileage

            val soldDate = row.getDateValue(HEADER_SOLD_DATE)
            aggregate.soldDate = soldDate?.toGermanyOffsetDateTime()

            val scrappedDate = row.getDateValue(HEADER_SCRAPPED_DATE)
            aggregate.scrappedDate = scrappedDate?.toGermanyOffsetDateTime()

            val approvedForScrappingDate = row.getDateValue(HEADER_APPROVED_FOR_SCRAPPING_DATE)
            aggregate.approvedForScrappingDate = approvedForScrappingDate?.toGermanyOffsetDateTime()

            val sentToSalesDate = row.getDateValue(HEADER_SENT_TO_SALES_DATE)
            aggregate.vehicleSentToSalesDate = sentToSalesDate?.toGermanyOffsetDateTime()

            val preparationDoneDate = row.getDateValue(HEADER_PREPARATION_DONE_DATE)
            aggregate.preparationDoneDate = preparationDoneDate?.toGermanyOffsetDateTime()

            val maxServiceLife = row.getStringValue(HEADER_MAX_SERVICE_LIFE_MONTHS)
            aggregate.maximumServiceLifeInMonths = maxServiceLife?.toInt()

            val deliveryLeipzig = row.hasValue(HEADER_DELIVERY_LEIPZIG)
            aggregate.latestTransferAdditionalData.deliveryLeipzig = deliveryLeipzig

            val latestReturnDate = row.getDateValue(HEADER_LATEST_RETURN_DATE)
            aggregate.latestTransferAdditionalData.latestReturnDate = latestReturnDate?.toGermanyOffsetDateTime()

            val vehicleResponsiblePerson = row.getStringValue(HEADER_VEHICLE_RESPONSIBLE_PERSON)
            aggregate.latestTransferAdditionalData.vehicleResponsiblePerson = vehicleResponsiblePerson

            val modelRange = row.getStringValue(HEADER_MODEL_RANGE)
            aggregate.isClassic = modelRange?.lowercase()?.trim() == "classic"

            val loadingCompleted = row.getDateValue(HEADER_LOADING_COMPLETED_DATE)
            aggregate.sales.loadingCompletedDate = loadingCompleted?.toGermanyOffsetDateTime()

            val zp8Date = row.getDateValue(HEADER_ZP8_DATE)
            aggregate.zp8Date = zp8Date?.toGermanyOffsetDateTime()

            val newVehicleInvoiceDate = row.getDateValue(HEADER_NEW_VEHICLE_INVOICE_DATE)
            aggregate.newVehicleInvoiceDate = newVehicleInvoiceDate?.toGermanyLocalDate()
        }
    }
}
