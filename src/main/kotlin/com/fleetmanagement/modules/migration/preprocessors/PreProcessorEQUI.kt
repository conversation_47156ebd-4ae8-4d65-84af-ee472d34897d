package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorEQUI(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    private final val logger: Logger = LoggerFactory.getLogger(PreProcessorEQUI::class.java)
    private final val fileName = "EQUI.XLSX"

    companion object {
        private const val HEADER_EQUIPMENT_NUMBER = "EQUNR"
        private const val HEADER_EQUIPMENT_ID = "EQKTX"
    }

    private val expectedHeaders = setOf(HEADER_EQUIPMENT_NUMBER, HEADER_EQUIPMENT_NUMBER)

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val equipmentNumber = row.getStringValue(HEADER_EQUIPMENT_NUMBER)?.toLong()
        val aggregate =
            equipmentNumber?.let {
                cache.getAggregateByEquiNumber(equipmentNumber)
            }
        if (aggregate == null) {
            // if we don't have the vehicle, just ignore it
            logger.warn("No vehicle found for equipmentNumber $equipmentNumber. Cannot attach equiId.")
            return
        }
        withErrorHandling(fileName, aggregate) {
            val equiId = row.getStringValue(HEADER_EQUIPMENT_ID)
            aggregate.equipmentId = equiId
        }
    }
}
