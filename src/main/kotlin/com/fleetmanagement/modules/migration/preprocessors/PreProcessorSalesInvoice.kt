package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorSalesInvoice(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    private final val fileName = "SALES_INVOICE.XLSX"

    companion object {
        private val logger = LoggerFactory.getLogger(PreProcessorSalesInvoice::class.java)
        private const val HEADER_GUID = "Produkt-GUID"
        private const val HEADER_VEHICLE_SALES_PRICE = "Sales price (net) in €"
        private const val HEADER_VEHICLE_DISCOUNT = "Discount (vehicle)"
        private const val HEADER_WINTER_TIRES_ID = "Winter tires ID"
        private const val HEADER_WINTER_TIRES_PRICE = "Winter tires price (net) in €"
        private const val HEADER_WINTER_TIRES_DISCOUNT = "Discount (winter tires)"
        private const val HEADER_SALES_PERSON_NUMBER = "Sales person number"
        private const val HEADER_PARTNER_NUMBER = "Partner Number"
    }

    private val expectedHeaders =
        setOf(
            HEADER_GUID,
            HEADER_VEHICLE_SALES_PRICE,
            HEADER_VEHICLE_DISCOUNT,
            HEADER_WINTER_TIRES_ID,
            HEADER_WINTER_TIRES_PRICE,
            HEADER_WINTER_TIRES_DISCOUNT,
            HEADER_SALES_PERSON_NUMBER,
            HEADER_PARTNER_NUMBER,
        )

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val productGuid = row.getStringValue(HEADER_GUID)
        if (productGuid == null) {
            // if the product GUID is empty, just ignore the line
            logger.warn("No Product-GUID provided in row of SALES.")
            return
        }
        val aggregate = cache.getAggregate(productGuid)
        withErrorHandling(fileName, aggregate) {
            val vehicleSalesPrice = row.getBigDecimalValue(HEADER_VEHICLE_SALES_PRICE)
            aggregate.sales.salesNetPriceEUR = vehicleSalesPrice

            val vehicleDiscount = row.getFloatValue(HEADER_VEHICLE_DISCOUNT)
            aggregate.sales.salesDiscountPercentage = vehicleDiscount ?: 0.0f

            val winterTiresId =
                row.getLongValue(HEADER_WINTER_TIRES_ID)
            aggregate.sales.winterTiresId = winterTiresId

            val winterTiresPrice = row.getBigDecimalValue(HEADER_WINTER_TIRES_PRICE)
            aggregate.sales.winterTiresNetPriceEUR = winterTiresPrice

            val winterTiresDiscount = row.getFloatValue(HEADER_WINTER_TIRES_DISCOUNT)
            aggregate.sales.winterTiresDiscountPercentage = winterTiresDiscount ?: 0.0f

            val salesPersonNumber = row.getLongValue(HEADER_SALES_PERSON_NUMBER)?.toString()
            aggregate.sales.salesPersonNumber = salesPersonNumber

            val customerPartnerNumber = row.getLongValue(HEADER_PARTNER_NUMBER)?.toString()
            aggregate.sales.customerPartnerNumber = customerPartnerNumber

            aggregate.sales.reservedForB2C = true
            aggregate.sales.contractSigned = true
        }
    }
}
