package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.parseFMSBoolean
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorYDBMA3302ADD(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    val fileName = "YDBMA3302_ADD.XLSX"

    companion object {
        private const val HEADER_PRODUCT_GUID = "Produkt-GUID"
        private const val HEADER_FINANCIAL_ASSET_TYPE = "Vermögensart"
        private const val HEADER_BLOCKED_FOR_SALE = "Für Verkauf gesperrt"
        private const val HEADER_KVA_FLAG = "Ungerichtet Verkauf an HO"
        private const val HEADER_FINANCIAL_ASSET_REASON = "Vermögensart Grund"
        private const val HEADER_MANUFACTURER_SHORT = "Herstellerkürzel"
    }

    private val expectedHeaders =
        setOf(
            HEADER_PRODUCT_GUID,
            HEADER_FINANCIAL_ASSET_TYPE,
            HEADER_BLOCKED_FOR_SALE,
            HEADER_KVA_FLAG,
            HEADER_FINANCIAL_ASSET_REASON,
            HEADER_MANUFACTURER_SHORT,
        )

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    // this value indicates the 'reason for change of financial asset type is that the car is sold outside of FMS'
    // In most cases these are Cup Cars, so if this is set, we set the "soldCupCarDate" to the last vehicle transfer's return date
    private final val soldOutsideFMSFinancialAssetTypeChangeReason = "12"

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val productGuid = row.getStringValue(HEADER_PRODUCT_GUID)
        val aggregate =
            cache.getAggregate(checkNotNull(productGuid) { "FMS data-export must contain PRODUCT_GUID column!" })

        withErrorHandling(fileName, aggregate) {
            val financialAssetType = row.getStringValue(HEADER_FINANCIAL_ASSET_TYPE)
            if (financialAssetType == "FE") {
                aggregate.financialAssetType = FinancialAssetType.FE
            }

            val blockedForSale = row.getStringValue(HEADER_BLOCKED_FOR_SALE)
            aggregate.blockedForSale = parseFMSBoolean(blockedForSale)

            val isSoldOutsideFMS =
                row.getStringValue(HEADER_FINANCIAL_ASSET_REASON) == soldOutsideFMSFinancialAssetTypeChangeReason
            aggregate.isSoldOutsideFMS = isSoldOutsideFMS

            val manufacturerShort = row.getStringValue(HEADER_MANUFACTURER_SHORT)
            aggregate.manufacturer = Manufacturer.lookupTable[manufacturerShort]
            if (aggregate.manufacturer == null) {
                aggregate.reportError("No manufacturer found for $manufacturerShort")
            }
        }
    }
}
