package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.toGermanyOffsetDateTime
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.OffsetDateTime

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorYDBMA3302CLAI(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    private final val fileName = "YDBMA3302_CLAI.XLSX"

    companion object {
        private const val HEADER_PRODUCT_GUID = "PRODUCT_GUID"
        private const val HEADER_DAMAGE_CLASSIFICATION = "ZZ0010"
        private const val HEADER_DAMAGE_DATE = "ZZ0011"
        private val DAMAGE_CLASSIFICATION_STOLEN = listOf(9, 11)
        private const val DAMAGE_CLASSIFICATION_UNSTOLEN = 10
    }

    private val expectedHeaders = setOf(HEADER_PRODUCT_GUID, HEADER_DAMAGE_CLASSIFICATION, HEADER_DAMAGE_DATE)

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val productGuid = row.getStringValue(HEADER_PRODUCT_GUID)
        val aggregate =
            cache.getAggregate(checkNotNull(productGuid) { "FMS data-export must contain PRODUCT_GUID column!" })

        withErrorHandling(fileName, aggregate) {
            val damageClassification = row.getIntValue(HEADER_DAMAGE_CLASSIFICATION)
            if (damageClassification in DAMAGE_CLASSIFICATION_STOLEN) {
                row.getDateValue(HEADER_DAMAGE_DATE)?.let {
                    aggregate.lastStolenDate = latestDate(it.toGermanyOffsetDateTime(), aggregate.lastStolenDate)
                } ?: aggregate.reportError("Empty value for 'stolen FMS date' field ZZ0011, table YDBMA3302_CLAI")
            } else if (damageClassification == DAMAGE_CLASSIFICATION_UNSTOLEN) {
                row.getDateValue(HEADER_DAMAGE_DATE)?.let {
                    aggregate.lastUnstolenDate = latestDate(it.toGermanyOffsetDateTime(), aggregate.lastUnstolenDate)
                } ?: aggregate.reportError("Empty value for 'unstolen FMS date' field ZZ0011, table YDBMA3302_CLAI")
            }
        }
    }

    fun latestDate(
        date1: OffsetDateTime?,
        date2: OffsetDateTime?,
    ) = listOfNotNull(date1, date2).maxOrNull()
}
