package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSTransferAggregate
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.toGermanyOffsetDateTime
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorYDBMA3302DEL(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    private final val fileName = "YDBMA3302_DEL.XLSX"

    companion object {
        private const val HEADER_PRODUCT_GUID = "Produkt-GUID"
        private const val HEADER_DELIVERY_INDEX = "ID"
        private const val HEADER_DELIVERY_DATE = "Tatsächliches Auslieferungsdatum"
        private const val HEADER_MILEAGE_AT_DELIVERY = "Kilometerstand Auslieferung"
        private const val HEADER_RETURN_DATE = "Datum Fahrzeugrücknahme"
        private const val HEADER_MILEAGE_AT_RETURN = "Kilometerstand Fahrzeugrücknahme"
        private const val HEADER_VEHICLE_RESPONSIBLE_PERSON = "Interner Fahrzeugverantwortlicher"
        private const val HEADER_VEHICLE_USAGE_GROUP = "Verwendung"
        private const val HEADER_VEHICLE_USING_COST_CENTER = "Nutzende Kostenstelle"
    }

    private val expectedHeaders =
        setOf(
            HEADER_PRODUCT_GUID,
            HEADER_DELIVERY_INDEX,
            HEADER_DELIVERY_DATE,
            HEADER_MILEAGE_AT_DELIVERY,
            HEADER_RETURN_DATE,
            HEADER_MILEAGE_AT_RETURN,
            HEADER_VEHICLE_RESPONSIBLE_PERSON,
        )

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val productGuid = row.getStringValue(HEADER_PRODUCT_GUID)
        val aggregate =
            cache.getAggregate(checkNotNull(productGuid) { "FMS data-export must contain PRODUCT_GUID column!" })

        withErrorHandling(fileName, aggregate) {
            val deliveryIndex =
                checkNotNull(row.getStringValue(HEADER_DELIVERY_INDEX)) { "FMS delivery index cannot be null" }

            val deliveryDate = row.getDateValue(HEADER_DELIVERY_DATE)?.toGermanyOffsetDateTime()
            val mileageAtDelivery = row.getStringValue(HEADER_MILEAGE_AT_DELIVERY)?.toInt()
            val returnDate = row.getDateValue(HEADER_RETURN_DATE)?.toGermanyOffsetDateTime()
            val mileageAtReturn = row.getStringValue(HEADER_MILEAGE_AT_RETURN)?.toInt()?.takeIf { it > 0 }
            val vehicleResponsiblePerson = row.getStringValue(HEADER_VEHICLE_RESPONSIBLE_PERSON)
            val vehicleUsageGroupDescription = row.getStringValue(HEADER_VEHICLE_USAGE_GROUP)
            val vehicleUsingCostCenter = row.getStringValue(HEADER_VEHICLE_USING_COST_CENTER)

            aggregate.transfers.add(
                FMSTransferAggregate(
                    fmsDeliveryIndex = deliveryIndex,
                    deliveryDate = deliveryDate,
                    mileageAtDelivery = mileageAtDelivery,
                    returnDate = returnDate,
                    mileageAtReturn = mileageAtReturn,
                    vehicleResponsiblePerson = vehicleResponsiblePerson,
                    vehicleUsageGroupDescription = vehicleUsageGroupDescription,
                    vehicleUsingCostCenter = vehicleUsingCostCenter,
                ),
            )
        }
    }
}
