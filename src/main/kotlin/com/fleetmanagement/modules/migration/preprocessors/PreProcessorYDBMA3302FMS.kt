package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.parseFMSBoolean
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.toGermanyOffsetDateTime
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.math.BigDecimal

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorYDBMA3302FMS(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    private final val fileName = "YDBMA3302_FMS.XLSX"

    companion object {
        private const val HEADER_PRODUCT_GUID = "PRODUCT_GUID"
        private const val HEADER_NET_PRICE_NEW_VEHICLE = "ZZ0024"
        private const val HEADER_INTERNAL_ORDER_NUMBER = "ZZ0022"
        private const val HEADER_INTERNAL_CONTACT_PERSON = "ZZ0016"
        private const val HEADER_USING_COST_CENTER = "ZZ0019"
        private const val HEADER_VEHICLE_USAGE_ID = "ZZ0020"
        private const val HEADER_REGISTRATION_BRIEF_NUMBER = "ZZ0060"
        private const val HEADER_REGISTRATION_TSN = "ZZ0061"
        private const val HEADER_REGISTRATION_HSN = "ZZ0062"
        private const val HEADER_REGISTRATION_SFME = "ZZ0078"
        private const val HEADER_VEHICLE_COMPOUND_NAME_ID = "ZZ0028"
        private const val HEADER_VEHICLE_PARKING_LOT = "ZZ0029"
        private const val HEADER_VEHICLE_PARKING_LOT_DATE = "ZZ0030"

        private const val USING_COST_CENTER_PREFIX = "00H001"
    }

    private val expectedHeaders =
        setOf(
            HEADER_PRODUCT_GUID,
            HEADER_NET_PRICE_NEW_VEHICLE,
            HEADER_INTERNAL_ORDER_NUMBER,
            HEADER_INTERNAL_CONTACT_PERSON,
            HEADER_USING_COST_CENTER,
            HEADER_VEHICLE_USAGE_ID,
            HEADER_REGISTRATION_BRIEF_NUMBER,
            HEADER_REGISTRATION_TSN,
            HEADER_REGISTRATION_HSN,
            HEADER_REGISTRATION_SFME,
            HEADER_VEHICLE_COMPOUND_NAME_ID,
            HEADER_VEHICLE_PARKING_LOT,
            HEADER_VEHICLE_PARKING_LOT_DATE,
        )

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val productGuid = row.getStringValue(HEADER_PRODUCT_GUID)
        val aggregate =
            cache.getAggregate(checkNotNull(productGuid) { "FMS data-export must contain PRODUCT_GUID column!" })

        withErrorHandling(fileName, aggregate) {
            val nettPriceNewVehicle = row.getBigDecimalValue(HEADER_NET_PRICE_NEW_VEHICLE)
            aggregate.netPriceNewVehicle = nettPriceNewVehicle?.takeUnless { it.compareTo(BigDecimal.ZERO) == 0 }

            val internalOrderNumber = row.getStringValue(HEADER_INTERNAL_ORDER_NUMBER)
            aggregate.latestTransferAdditionalData.internalOrderNumber = internalOrderNumber

            val internalContactPerson = row.getStringValue(HEADER_INTERNAL_CONTACT_PERSON)
            aggregate.latestTransferAdditionalData.internalContactPerson = internalContactPerson

            val usingCostCenter = row.getStringValue(HEADER_USING_COST_CENTER)
            aggregate.latestTransferAdditionalData.usingCostCenter = usingCostCenter?.let { USING_COST_CENTER_PREFIX + it.takeLast(4) }

            val latestBriefNumber = row.getStringValue(HEADER_REGISTRATION_BRIEF_NUMBER)
            aggregate.extendedRegistrationData.briefNumberOnlyForLatestRegistration = latestBriefNumber

            val latestSfme = parseFMSBoolean(row.getStringValue(HEADER_REGISTRATION_SFME))
            aggregate.extendedRegistrationData.sfmeOnlyForLatestRegistration = latestSfme

            val tsn = row.getStringValue(HEADER_REGISTRATION_TSN)
            aggregate.extendedRegistrationData.tsnForAllRegistrations = tsn

            val hsn = row.getStringValue(HEADER_REGISTRATION_HSN)
            aggregate.extendedRegistrationData.hsnForAllRegistrations = hsn

            val vehicleUsageId = row.getStringValue(HEADER_VEHICLE_USAGE_ID)
            vehicleUsageId?.let {
                aggregate.latestTransferAdditionalData.fmsVehicleUsageId = it.toInt()
            }

            val compoundNameId = row.getStringValue(HEADER_VEHICLE_COMPOUND_NAME_ID)
            aggregate.compoundNameId = compoundNameId

            val parkingLot = row.getStringValue(HEADER_VEHICLE_PARKING_LOT)
            aggregate.parkingLot = parkingLot

            val parkingLotDate = row.getDateValue(HEADER_VEHICLE_PARKING_LOT_DATE)
            aggregate.parkingLotDate = parkingLotDate?.toGermanyOffsetDateTime()
        }
    }
}
