package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSRegistrationAggregate
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.toGermanyOffsetDateTime
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import com.fleetmanagement.modules.vehicledata.api.validators.validateLicensePlate
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationArea
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationArea.GERMANY
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationArea.OTHER
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorYDBMA3302REG1(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    private val fileName = "YDBMA3302_REG1.XLSX"

    companion object {
        // Excel column headers
        private const val HEADER_PRODUCT_GUID = "PRODUCT_GUID"
        private const val HEADER_REGISTRATION_TYPE = "ZZ0010"
        private const val HEADER_REGISTRATION_DATE = "ZZ0011"
        private const val HEADER_LICENCE_PLATE = "ZZ0012"
        private const val HEADER_REMARK = "ZZ0017"

        // Precompiled regex patterns for better performance for sanitization of license plates and remarks
        private val WHITESPACES_PATTERN = Regex("\\s+")
        private val HYPHEN_PATTERN = Regex("\\s*-\\s*")
        private val SANITIZE_REMARK_PATTERN = Regex("[\\s.-]+")

        // License plate suffix patterns
        private val E_SUFFIX_PATTERN = Regex("E")
        private val H_SUFFIX_PATTERN = Regex("H")
        private val E_KENNZEICHEN_PATTERN = Regex(".*E.*KEN.*")
        private val H_KENNZEICHEN_PATTERN = Regex(".*H.*KEN.*")
    }

    private val expectedHeaders =
        setOf(
            HEADER_PRODUCT_GUID,
            HEADER_REGISTRATION_TYPE,
            HEADER_REGISTRATION_DATE,
            HEADER_LICENCE_PLATE,
            HEADER_REMARK,
        )

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val productGuid = row.getStringValue(HEADER_PRODUCT_GUID)
        val aggregate =
            cache.getAggregate(checkNotNull(productGuid) { "FMS(REG1) data-export must contain PRODUCT_GUID column!" })

        withErrorHandling(fileName, aggregate) {
            val registrationType =
                checkNotNull(row.getIntValue(HEADER_REGISTRATION_TYPE)) { "FMS registration type cannot be null" }
            val registrationDate =
                checkNotNull(row.getDateValue(HEADER_REGISTRATION_DATE)) { "FMS registration date cannot be null" }

            val rawLicensePlate = row.getStringValue(HEADER_LICENCE_PLATE)
            val remark = row.getStringValue(HEADER_REMARK)

            val (processedLicensePlate, registrationArea) = processLicensePlate(rawLicensePlate, remark)

            aggregate.registrations.add(
                FMSRegistrationAggregate(
                    registrationType = registrationType,
                    registrationDate = registrationDate.toGermanyOffsetDateTime(),
                    licencePlate = processedLicensePlate,
                    registrationArea = registrationArea,
                    remark = remark,
                ),
            )
        }
    }

    /**
     * Processes a raw license plate by either sanitizing it for German plates or keeping it as-is for foreign plates.
     *
     * @param rawLicensePlate The raw license plate string from the Excel file
     * @param remark Additional remark that might contain suffix information (E or H)
     * @return Pair of processed license plate and registration area
     */
    private fun processLicensePlate(
        rawLicensePlate: String?,
        remark: String?,
    ): Pair<String?, RegistrationArea> {
        if (rawLicensePlate.isNullOrBlank()) {
            return null to GERMANY
        }

        val sanitizedLicensePlate = sanitizeLicensePlate(rawLicensePlate)
        val isValidGermanPlate = validateLicensePlate(sanitizedLicensePlate)

        return if (isValidGermanPlate) {
            val suffix = extractLicensePlateSuffix(remark)
            sanitizedLicensePlate + suffix to GERMANY
        } else {
            rawLicensePlate to OTHER
        }
    }

    /**
     * Sanitizes a license plate by normalizing whitespace and hyphens.
     * - Trims leading/trailing whitespace
     * - Replaces multiple whitespace characters with a single space
     * - Normalizes hyphen spacing (removes spaces around hyphens)
     */
    private fun sanitizeLicensePlate(licensePlate: String): String =
        licensePlate
            .trim()
            .replace(WHITESPACES_PATTERN, " ")
            .replace(HYPHEN_PATTERN, "-")

    /**
     * Extracts license plate suffix (E or H) from remark field.
     *
     * German license plates can have special suffixes:
     * - E: Electric vehicle
     * - H: Historic vehicle
     *
     * @param remark The remark field that might contain suffix information
     * @return "E", "H", or empty string if no suffix is found
     */
    internal fun extractLicensePlateSuffix(remark: String?): String {
        if (remark.isNullOrBlank()) return ""

        val sanitizedRemark = remark.uppercase().replace(SANITIZE_REMARK_PATTERN, "")

        return when {
            sanitizedRemark.matches(E_SUFFIX_PATTERN) -> "E"
            sanitizedRemark.matches(H_SUFFIX_PATTERN) -> "H"
            sanitizedRemark.matches(E_KENNZEICHEN_PATTERN) -> "E"
            sanitizedRemark.matches(H_KENNZEICHEN_PATTERN) -> "H"
            else -> ""
        }
    }
}
