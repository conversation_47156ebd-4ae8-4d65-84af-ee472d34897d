package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.parseFMSBoolean
import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.toGermanyOffsetDateTime
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorYDBMA4992ADD2(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    private final val fileName = "YDBMA4992_ADD2.XLSX"

    companion object {
        private const val HEADER_PRODUCT_GUID = "PRODUCT_GUID"
        private const val HEADER_IS_SCRAP_VEHICLE = "ZZ0011"
        private const val HEADER_IS_RESIDUAL_MARKET = "ZZ0049"
        private const val HEADER_PDI_ORDERED_DATE = "ZZ0035"
        private const val HEADER_PDI_COMPLETED_DATE = "ZZ0032"
        private const val HEADER_MAINTENANCE_ORDER_NUMBER = "ZZ0048"
    }

    private val expectedHeaders =
        setOf(
            HEADER_PRODUCT_GUID,
            HEADER_IS_SCRAP_VEHICLE,
            HEADER_IS_RESIDUAL_MARKET,
            HEADER_PDI_ORDERED_DATE,
            HEADER_PDI_COMPLETED_DATE,
            HEADER_MAINTENANCE_ORDER_NUMBER,
        )

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val productGuid = row.getStringValue(HEADER_PRODUCT_GUID)
        val aggregate =
            cache.getAggregate(checkNotNull(productGuid) { "FMS data-export must contain PRODUCT_GUID column!" })

        withErrorHandling(fileName, aggregate) {
            val isScrapVehicle = row.getStringValue(HEADER_IS_SCRAP_VEHICLE)
            aggregate.scrapVehicle = parseFMSBoolean(isScrapVehicle)

            val isResidualMarket = row.hasValue(HEADER_IS_RESIDUAL_MARKET)
            aggregate.isResidualValueMarket = isResidualMarket

            val pdiOrderedDate = row.getDateValue(HEADER_PDI_ORDERED_DATE)
            aggregate.pdiOrderedDate = pdiOrderedDate?.toGermanyOffsetDateTime()

            val pdiCompleted = row.getDateValue(HEADER_PDI_COMPLETED_DATE)
            aggregate.pdiCompletedDate = pdiCompleted?.toGermanyOffsetDateTime()

            val maintenanceOrderNumber = row.getStringValue(HEADER_MAINTENANCE_ORDER_NUMBER)
            aggregate.latestTransferAdditionalData.maintenanceOrderNumber = maintenanceOrderNumber
        }
    }
}
