/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.job.PlannedAppointment
import com.fleetmanagement.modules.migration.job.ports.Preprocessor
import com.fleetmanagement.modules.migration.preprocessors.RowProcessingParsingErrorHandler.withErrorHandling
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRow
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.FMSRowPreprocessor
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class PreProcessorYDBMA4992TERM(
    val excelPreprocessor: ExcelPreprocessor,
) : Preprocessor,
    FMSRowPreprocessor {
    private final val fileName = "YDBMA4992_TERM.XLSX"
    private final val logger: Logger = LoggerFactory.getLogger(PreProcessorYDBMA4992TERM::class.java)

    companion object {
        private const val HEADER_VIN = "Fahrzeugidentifikationsnummer"
        private const val HEADER_RESPONSIBLE_PERSON = "Interner Fahrzeugverantwortlicher"
        private const val HEADER_RETURN_OR_DELIVERY = "Ressourcenart"
        private const val HEADER_APPOINTMENT_DATE = "Termindatum"
        private const val HEADER_APPOINTMENT_TIME_FROM = "Uhrzeit von"
        private const val HEADER_APPOINTMENT_TIME_TO = "Uhrzeit bis"
        private const val HEADER_APPOINTMENT_KIND = "Terminart"
    }

    private val expectedHeaders =
        setOf(
            HEADER_VIN,
            HEADER_RESPONSIBLE_PERSON,
            HEADER_RETURN_OR_DELIVERY,
            HEADER_APPOINTMENT_DATE,
            HEADER_APPOINTMENT_TIME_FROM,
            HEADER_APPOINTMENT_TIME_TO,
            HEADER_APPOINTMENT_KIND,
        )

    override fun validate(): Boolean = excelPreprocessor.validate(fileName, expectedHeaders)

    override fun process(cache: FMSVehicleAggregateCache) {
        excelPreprocessor.processExcelFile(fileName, this, cache)
    }

    override fun processRow(
        row: FMSRow,
        cache: FMSVehicleAggregateCache,
    ) {
        val vin = row.getStringValue(HEADER_VIN)
        checkNotNull(vin) { "FMS data-export must contain vin column for MS-Bookings!" }
        val aggregate = cache.getAggregateByVin(vin)
        if (aggregate == null) {
            logger.warn("YDBMA4992_TERM.XLSX: Vehicle not found with VIN $vin. Cannot attach transfers.")
            return
        }

        withErrorHandling(fileName, aggregate) {
            val responsiblePerson = row.getStringValue(HEADER_RESPONSIBLE_PERSON)
            val appointmentType = row.getStringValue(HEADER_RETURN_OR_DELIVERY)
            val appointmentFrom = row.getOffsetDateTimeValue(HEADER_APPOINTMENT_DATE, HEADER_APPOINTMENT_TIME_FROM)
            val appointmentTo = row.getOffsetDateTimeValue(HEADER_APPOINTMENT_DATE, HEADER_APPOINTMENT_TIME_TO)
            val appointmentKind = row.getIntValue(HEADER_APPOINTMENT_KIND)
            val plannedAppointment =
                PlannedAppointment(
                    from = checkNotNull(appointmentFrom) { "Planned appointment is missing 'from'-date." },
                    to = appointmentTo,
                    responsiblePerson = checkNotNull(responsiblePerson) { "Planned appointment is missing responsiblePerson" },
                    appointmentKind = checkNotNull(appointmentKind) { "Planned appointment is missing Terminart" },
                )
            when (appointmentType) {
                "A" -> setDeliveryDate(aggregate, plannedAppointment)
                "R" -> setReturnDate(aggregate, plannedAppointment)
                else ->
                    aggregate.reportError(
                        "Unknown appointment type: $appointmentType. Expected 'A' for delivery or 'R' for return.",
                    )
            }
        }
    }

    private fun setReturnDate(
        aggregate: FMSVehicleAggregate,
        plannedAppointment: PlannedAppointment,
    ) {
        val existingReturnDate = aggregate.latestTransferAdditionalData.plannedReturnAppointment
        aggregate.latestTransferAdditionalData.plannedReturnAppointment = getNewer(plannedAppointment, existingReturnDate, aggregate)
    }

    private fun setDeliveryDate(
        aggregate: FMSVehicleAggregate,
        plannedAppointment: PlannedAppointment,
    ) {
        val existingDeliveryDate = aggregate.latestTransferAdditionalData.plannedDeliveryAppointment
        aggregate.latestTransferAdditionalData.plannedDeliveryAppointment = getNewer(plannedAppointment, existingDeliveryDate, aggregate)
    }

    private fun getNewer(
        newDate: PlannedAppointment,
        existing: PlannedAppointment?,
        aggregate: FMSVehicleAggregate,
    ): PlannedAppointment {
        if (existing != null) {
            aggregate.reportError("Multiple appointment of same kind, using latest.")
        }
        return listOfNotNull(newDate, existing).maxBy { it.from }
    }
}
