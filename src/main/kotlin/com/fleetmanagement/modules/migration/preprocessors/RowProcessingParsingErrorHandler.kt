package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import org.slf4j.Logger
import org.slf4j.LoggerFactory

object RowProcessingParsingErrorHandler {
    val logger: Logger = LoggerFactory.getLogger(RowProcessingParsingErrorHandler::class.java)

    fun withErrorHandling(
        tableName: String,
        aggregate: FMSVehicleAggregate,
        parsingRunnable: () -> Unit,
    ) {
        try {
            parsingRunnable()
        } catch (t: Throwable) {
            logger.error("Failed parsing $tableName for ${aggregate.productGuid}", t)
            aggregate.reportError("Failed parsing $tableName: ${t.message}}")
        }
    }
}
