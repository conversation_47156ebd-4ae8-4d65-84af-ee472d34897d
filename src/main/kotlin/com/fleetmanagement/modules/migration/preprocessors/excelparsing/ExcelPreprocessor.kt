package com.fleetmanagement.modules.migration.preprocessors.excelparsing

import com.emh.shared.s3.client.adapter.S3StorageClientException
import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Workbook
import org.apache.poi.ss.usermodel.WorkbookFactory
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.core.io.Resource
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class ExcelPreprocessor(
    val fileProvider: FileProvider,
    @Value("fms-export") val workingDir: String = "fms-export",
) {
    private val logger = LoggerFactory.getLogger(ExcelPreprocessor::class.java)

    fun processExcelFile(
        file: String,
        rowProcessor: FMSRowPreprocessor,
        cache: FMSVehicleAggregateCache,
    ) {
        val fileToProcess = provideFile("$workingDir/$file").inputStream
        WorkbookFactory.create(fileToProcess).use<Workbook, Unit> { workbook ->
            val sheet = workbook.getSheetAt(0) // first sheet
            val headerIndexMap =
                sheet.getRow(0).mapIndexed { index, cell -> cell.stringCellValue.trim() to index }.toMap()

            for (row in sheet.drop<Row>(1)) {
                rowProcessor.processRow(FMSRow(row, headerIndexMap), cache)
            }
        }
    }

    private fun provideFile(fileName: String): Resource = fileProvider.provideFile(fileName)

    private fun readHeaders(resource: Resource): Set<String> {
        val fileToProcess = resource.inputStream
        WorkbookFactory.create(fileToProcess).use { workbook ->
            val sheet = workbook.getSheetAt(0)
            val headerRow = sheet.getRow(0)
            return headerRow.map { cell -> cell.stringCellValue.trim() }.toSet()
        }
    }

    fun validate(
        fileName: String,
        expectedHeaders: Set<String>,
    ): Boolean {
        val resource =
            try {
                provideFile("$workingDir/$fileName")
            } catch (ex: S3StorageClientException) {
                logger.error("Expected file does not exist: $ex")
                return false
            }

        // Header validation
        val headersInFile = readHeaders(resource)
        val missingHeaders = expectedHeaders - headersInFile
        if (missingHeaders.isNotEmpty()) {
            logger.error("Missing required headers in the file: $fileName: $missingHeaders")
            return false
        }
        return true
    }
}
