package com.fleetmanagement.modules.migration.preprocessors.excelparsing

import com.fleetmanagement.modules.migration.preprocessors.ParseUtils.combineDatesAsGermanyOffsetDateTime
import org.apache.poi.ss.usermodel.Cell
import org.apache.poi.ss.usermodel.CellType
import org.apache.poi.ss.usermodel.Row
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy.RETURN_BLANK_AS_NULL
import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.Date

data class FMSRow(
    val row: Row,
    val headerIndexMap: Map<String, Int>,
) {
    fun getStringValue(headerName: String): String? =
        withExceptionMapping(headerName) {
            val cell = getCell(headerName)
            when (cell?.cellType) {
                CellType.NUMERIC -> cell.numericCellValue.toString().takeIf { it.isNotBlank() }
                CellType.STRING -> cell.stringCellValue.takeIf { it.isNotBlank() }
                else -> null
            }
        }

    fun getIntValue(headerName: String): Int? =
        withExceptionMapping(headerName) {
            val cell = getCell(headerName)
            when (cell?.cellType) {
                CellType.NUMERIC -> cell.numericCellValue.toInt()
                CellType.STRING -> cell.stringCellValue?.toIntOrNull()
                else -> null
            }
        }

    fun getLongValue(headerName: String): Long? =
        withExceptionMapping(headerName) {
            val cell = getCell(headerName)
            when (cell?.cellType) {
                CellType.NUMERIC -> cell.numericCellValue.toLong()
                CellType.STRING -> cell.stringCellValue?.toLongOrNull()
                else -> null
            }
        }

    fun getFloatValue(headerName: String): Float? =
        withExceptionMapping(headerName) {
            val cell = getCell(headerName)
            when (cell?.cellType) {
                CellType.NUMERIC -> cell.numericCellValue.toFloat()
                CellType.STRING -> cell.stringCellValue?.toFloatOrNull()
                else -> null
            }
        }

    fun getDateValue(headerName: String): Date? = withExceptionMapping(headerName) { getCell(headerName)?.dateCellValue }

    fun hasValue(headerName: String): Boolean =
        withExceptionMapping(headerName) {
            val cellValue = getCell(headerName)
            when (cellValue?.cellType) {
                CellType.STRING -> !cellValue.stringCellValue.isNullOrEmpty()
                else -> cellValue != null
            }
        }

    fun getBigDecimalValue(headerName: String): BigDecimal? = getCell(headerName)?.numericCellValue?.toBigDecimal()

    fun getOffsetDateTimeValue(
        dateHeader: String,
        timeHeader: String,
    ): OffsetDateTime? {
        val date = getDateValue(dateHeader) ?: return null
        val time = getDateValue(timeHeader) ?: date

        return combineDatesAsGermanyOffsetDateTime(date, time)
    }

    private fun getCell(headerName: String): Cell? {
        val headerIndex = headerIndexMap[headerName]
        if (headerIndex == null) {
            throw FMSExcelParsingException("Header $headerName does not exist!")
        }
        return row.getCell(headerIndex, RETURN_BLANK_AS_NULL)
    }

    private fun <T> withExceptionMapping(
        headerName: String,
        function: () -> T,
    ) = try {
        function()
    } catch (throwable: Throwable) {
        throw FMSExcelParsingException("Could not read cell $headerName; ${throwable.message}", throwable)
    }
}

class FMSExcelParsingException(
    message: String,
    throwable: Throwable? = null,
) : IllegalArgumentException(message, throwable)
