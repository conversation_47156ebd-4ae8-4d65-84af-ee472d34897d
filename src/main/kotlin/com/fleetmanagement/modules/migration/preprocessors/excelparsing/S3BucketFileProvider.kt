package com.fleetmanagement.modules.migration.preprocessors.excelparsing

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.fleetmanagement.modules.migration.FMSMigrationStorageProperties
import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class S3BucketFileProvider(
    @Qualifier("fmsMigrationStorageClient") val s3StorageClient: S3StorageClient,
    @Qualifier("fmsMigrationStorageProperties") val props: FMSMigrationStorageProperties,
) : FileProvider {
    override fun provideFile(fileName: String) = s3StorageClient.downloadFile(fileName, props.bucket)
}
