package com.fleetmanagement.modules.migration.report

import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.migration.job.ports.MigrationReport
import java.io.BufferedWriter
import java.nio.file.Files
import java.nio.file.Files.createTempFile
import java.nio.file.Path
import kotlin.io.path.absolutePathString

class CSVMigrationReport(
    val reportName: String,
    val uploadReport: (filename: String) -> Unit,
) : MigrationReport {
    val tmpFile: Path = createTempFile(reportName, ".csv")

    private val fileWriter: BufferedWriter = Files.newBufferedWriter(tmpFile)

    init {
        val csvHeaders = "vin;product_guid;error\n"
        this.fileWriter.write(csvHeaders)
    }

    val vehicleBuffer: MutableList<FMSVehicleAggregate> = mutableListOf()

    @Synchronized
    override fun appendResult(vehicle: FMSVehicleAggregate) {
        vehicleBuffer.add(vehicle)
        if (vehicleBuffer.size >= 5) {
            flushVehiclesToFile()
        }
    }

    @Synchronized
    override fun finalizeReport() {
        flushVehiclesToFile()
        uploadReport(tmpFile.absolutePathString())
    }

    private fun flushVehiclesToFile() {
        vehicleBuffer.forEach { vehicle ->
            vehicle.errors
                .forEach { error ->
                    fileWriter.append("${vehicle.vin};${vehicle.productGuid};${escapeCsv(error)}\n")
                }
            if (vehicle.errors.isEmpty()) {
                fileWriter.append("${vehicle.vin};${vehicle.productGuid};success\n")
            }
        }
        fileWriter.flush()
        vehicleBuffer.clear()
    }

    private fun escapeCsv(input: String?): String =
        if (input != null && (input.contains("\"") || input.contains(";"))) {
            "\"${input.replace("\"", "\"\"")}\""
        } else {
            input ?: ""
        }
}
