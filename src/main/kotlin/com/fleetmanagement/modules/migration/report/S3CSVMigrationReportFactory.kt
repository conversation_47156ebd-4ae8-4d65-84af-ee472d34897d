package com.fleetmanagement.modules.migration.report

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.fleetmanagement.modules.migration.FMSMigrationStorageProperties
import com.fleetmanagement.modules.migration.MigrationModuleConfiguration
import com.fleetmanagement.modules.migration.job.ports.MigrationReport
import com.fleetmanagement.modules.migration.job.ports.MigrationReportFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.core.io.FileSystemResource
import org.springframework.stereotype.Component

@ConditionalOnBean(MigrationModuleConfiguration::class)
@Component
class S3CSVMigrationReportFactory(
    @Qualifier("fmsMigrationStorageClient") val s3StorageClient: S3StorageClient,
    @Qualifier("fmsMigrationStorageProperties") val props: FMSMigrationStorageProperties,
) : MigrationReportFactory {
    override fun startReport(reportName: String): MigrationReport =
        CSVMigrationReport(
            reportName = reportName,
            uploadReport = { filename ->
                s3StorageClient.uploadResource(
                    key = "reports/$reportName.csv",
                    resource = FileSystemResource(filename),
                    bucket = props.bucket,
                    applyLegalHold = false,
                    contentType = "application/csv",
                )
            },
        )
}
