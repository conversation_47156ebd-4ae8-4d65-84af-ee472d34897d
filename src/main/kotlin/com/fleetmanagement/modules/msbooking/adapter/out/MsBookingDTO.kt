package com.fleetmanagement.modules.msbooking.adapter.out

data class MsBookingResponse(
    val value: List<MsBookingDTO>,
)

data class MsBookingDTO(
    val id: String,
    val selfServiceAppointmentId: String,
    val additionalInformation: String?,
    val isLocationOnline: Boolean,
    val joinWebUrl: String?,
    val customerName: String?,
    val customerEmailAddress: String?,
    val customerPhone: String?,
    val customerTimeZone: String?,
    val customerNotes: String?,
    val serviceId: String,
    val serviceName: String,
    val duration: String,
    val preBuffer: String,
    val postBuffer: String,
    val priceType: String,
    val price: Double,
    val serviceNotes: String?,
    val optOutOfCustomerEmail: Boolean,
    val staffMemberIds: List<String>,
    val smsNotificationsEnabled: Boolean,
    val anonymousJoinWebUrl: String?,
    val maximumAttendeesCount: Int,
    val filledAttendeesCount: Int,
    val createdDateTime: String,
    val lastUpdatedDateTime: String,
    val isCustomerAllowedToManageBooking: Boolean,
    val appointmentLabel: String?,
    val startDateTime: DateTimeDTO,
    val endDateTime: DateTimeDTO,
    val serviceLocation: LocationDTO,
    val reminders: List<ReminderDTO>?,
    val customers: List<CustomerDTO>?,
)

data class DateTimeDTO(
    val dateTime: String,
    val timeZone: String,
)

data class LocationDTO(
    val displayName: String?,
    val locationEmailAddress: String?,
    val locationUri: String?,
    val locationType: String,
    val uniqueId: String?,
    val uniqueIdType: String?,
    val address: AddressDTO,
    val coordinates: CoordinatesDTO,
)

data class AddressDTO(
    val street: String?,
    val city: String?,
    val state: String?,
    val countryOrRegion: String?,
    val postalCode: String?,
)

data class CoordinatesDTO(
    val altitude: Double,
    val latitude: Double,
    val longitude: Double,
    val accuracy: Double,
    val altitudeAccuracy: Double,
)

data class ReminderDTO(
    val offset: String,
    val recipients: String,
    val message: String,
)

data class CustomerDTO(
    val customerId: String?,
    val name: String?,
    val emailAddress: String?,
    val phone: String?,
    val timeZone: String?,
    val notes: String?,
    val location: LocationDTO,
    val customQuestionAnswers: List<CustomQuestionAnswerDTO>?,
)

data class CustomQuestionAnswerDTO(
    val questionId: String,
    val question: String,
    val answer: String?,
)

data class CreateAppointmentRequestDTO(
    val customerEmailAddress: String,
    val serviceId: String,
    val serviceName: String,
    val startDateTime: DateTimeDTO,
    val endDateTime: DateTimeDTO,
    val serviceNotes: String,
)
