package com.fleetmanagement.modules.msbooking.adapter.out

import com.fleetmanagement.modules.msbooking.application.AppointmentUpdateRequest
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange
import org.springframework.web.service.annotation.PatchExchange
import org.springframework.web.service.annotation.PostExchange

/**
 * seems like graph version 1 of appointments is very unbaked
 * the api do not support the $filter or the $top(paginate)
 * it seems version2 is going to be release (now still beta) in june 2025
 */
interface MsBookingWebClient {
    @GetExchange(
        url = "/solutions/bookingBusinesses/{bookings_id}/appointments",
        accept = ["application/json"],
    )
    fun getAppointments(
        @PathVariable("bookings_id") bookingsId: String,
    ): MsBookingResponse

    @GetExchange(
        url = "/solutions/bookingBusinesses/{bookings_id}/calendarView",
        accept = ["application/json"],
    )
    fun getCalendarView(
        @PathVariable("bookings_id") bookingsId: String,
        @RequestParam(value = "start", required = false) start: String?,
        @RequestParam(value = "end", required = false) end: String?,
    ): MsBookingResponse

    // todo: start using it once the filter works( refer to readme)
    @GetExchange(
        url = "/solutions/bookingBusinesses/{bookings_id}/appointments",
        accept = ["application/json"],
    )
    fun getFilteredAppointments(
        @PathVariable("bookings_id") bookingsId: String,
        @RequestParam(value = "\$filter", required = true) filter: String,
    ): MsBookingResponse

    @PatchExchange(
        url = "/solutions/bookingBusinesses/{bookings_id}/appointments/{appointment_id}",
        accept = ["application/json"],
        contentType = "application/json",
    )
    fun updateAppointment(
        @PathVariable("bookings_id") bookingsId: String,
        @PathVariable("appointment_id") appointmentId: String,
        @RequestBody updateRequest: AppointmentUpdateRequest,
    ): ResponseEntity<Void>

    @PostExchange(
        url = "/solutions/bookingBusinesses/{bookings_id}/appointments",
        accept = ["application/json"],
        contentType = "application/json",
    )
    fun createAppointment(
        @PathVariable("bookings_id") bookingsId: String,
        @RequestBody createAppointmentRequestDTO: CreateAppointmentRequestDTO,
    ): MsBookingDTO
}
