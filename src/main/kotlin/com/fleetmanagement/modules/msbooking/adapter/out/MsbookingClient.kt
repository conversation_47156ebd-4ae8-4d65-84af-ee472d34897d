package com.fleetmanagement.modules.msbooking.adapter.out

import com.fleetmanagement.modules.msbooking.application.AppointmentDto
import com.fleetmanagement.modules.msbooking.application.AppointmentKeys
import com.fleetmanagement.modules.msbooking.application.AppointmentType
import com.fleetmanagement.modules.msbooking.application.AppointmentUpdateRequest
import com.fleetmanagement.modules.msbooking.application.port.CreateAppointment
import com.fleetmanagement.modules.msbooking.application.port.ReadAppointments
import com.fleetmanagement.modules.msbooking.application.port.UpdateAppointment
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.Instant
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.time.format.DateTimeFormatter

@Component
@ConditionalOnBean(MsBookingConfiguration::class)
class MsbookingClient(
    @Value("\${msbooking.booking-id}") private val bookingId: String,
    @Value("\${msbooking.service-id}") private val serviceId: String,
    private val msBookingWebClient: MsBookingWebClient,
) : ReadAppointments,
    UpdateAppointment,
    CreateAppointment {
    override fun readCalendar(
        startTime: String?,
        endTime: String?,
    ): List<AppointmentDto> {
        val msBookingResponse =
            msBookingWebClient.getCalendarView(
                bookingsId = bookingId,
                start = startTime,
                end = endTime,
            )
        return msBookingResponse.value.mapNotNull { toAppointmentDto(it) }
    }

    private fun toAppointmentDto(msBookingDto: MsBookingDTO): AppointmentDto? {
        try {
            val appointmentType = findAppointmentType(msBookingDto.serviceName)
            return AppointmentDto(
                appointmentId = msBookingDto.id,
                startTime = convertToUtcOffsetDateTime(msBookingDto.startDateTime),
                endTime = convertToUtcOffsetDateTime(msBookingDto.endDateTime),
                customerEmailAddress = msBookingDto.customerEmailAddress,
                customerName = msBookingDto.customerName,
                keys = readKeysFromServiceNote(serviceNote = msBookingDto.serviceNotes, appointmentType = appointmentType),
                appointmentType = appointmentType,
                lastUpdatedDateTime = convertFromStringToTime(msBookingDto.lastUpdatedDateTime).atOffset(ZoneOffset.UTC),
            )
        } catch (e: IllegalServiceNameException) {
            log.warn("seems like the serviceName: ${msBookingDto.serviceName} is illegal appointment id ${msBookingDto.id}")
            return null
        } catch (e: IllegalServiceNoteException) {
            log.warn("seems like the ServiceNote: ${msBookingDto.serviceNotes} is illegal appointment id ${msBookingDto.id}")
            return null
        } catch (e: IllegalServiceNoteForExchangeException) {
            log.warn(
                "seems like the ServiceNote: ${msBookingDto.serviceNotes}  " +
                    "for serviceName: ${msBookingDto.serviceName} is illegal appointment id ${msBookingDto.id}",
            )
            return null
        } catch (e: NumberFormatException) {
            log.warn("seems like the ServiceNote: ${msBookingDto.serviceNotes}  is not a number")
            return null
        }
    }

    private fun convertToUtcOffsetDateTime(startDateTime: DateTimeDTO): OffsetDateTime {
        val zoneId = ZoneId.of(startDateTime.timeZone)

        val localDateTime = convertFromStringToTime(startDateTime.dateTime)

        val zonedDateTime = localDateTime.atZone(zoneId)

        return zonedDateTime.withZoneSameInstant(ZoneOffset.UTC).toOffsetDateTime()
    }

    private fun convertFromStringToTime(timeAsString: String): LocalDateTime {
        val instant = Instant.parse(timeAsString)
        val dateTime = LocalDateTime.ofInstant(instant, ZoneOffset.UTC)
        return dateTime
    }

    private fun findAppointmentType(serviceName: String): AppointmentType {
        val normalizedServiceName = normlize(serviceName)

        return when {
            normalizedServiceName.contains(AppointmentType.DELIVERY.normalizedDescription, ignoreCase = true) -> AppointmentType.DELIVERY
            normalizedServiceName.contains(AppointmentType.RETURN.normalizedDescription, ignoreCase = true) -> AppointmentType.RETURN
            normalizedServiceName.contains(AppointmentType.EXCHANGE.normalizedDescription, ignoreCase = true) -> AppointmentType.EXCHANGE
            else -> throw IllegalServiceNameException(serviceName)
        }
    }

    private fun readKeysFromServiceNote(
        serviceNote: String?,
        appointmentType: AppointmentType,
    ): AppointmentKeys {
        if (serviceNote.isNullOrBlank()) throw IllegalServiceNoteException(serviceNote)
        return when (appointmentType) {
            AppointmentType.DELIVERY -> {
                AppointmentKeys(deliveryKey = VehicleTransferKey(serviceNote.toLong()))
            }
            AppointmentType.RETURN -> {
                AppointmentKeys(returnKey = VehicleTransferKey(serviceNote.toLong()))
            }
            // The user only supplies the delivery key, and the job will extract the return key automatically
            AppointmentType.EXCHANGE -> {
                AppointmentKeys(deliveryKey = VehicleTransferKey(serviceNote.toLong()))
            }
        }
    }

    private fun normlize(duetchString: String): String =
        duetchString
            .replace("ü", "ue")
            .replace("Ü", "ue")
            .lowercase()
            .trim()

    override fun updateMsBookingId(
        appointmentId: String,
        updateRequest: AppointmentUpdateRequest,
    ) {
        try {
            msBookingWebClient
                .updateAppointment(
                    bookingsId = bookingId,
                    appointmentId = appointmentId,
                    updateRequest = updateRequest,
                )
        } catch (e: MsbookingClientException) {
            log.warn(e.message, e)
        }
    }

    override fun createAppointment(
        startDateTime: OffsetDateTime,
        endDateTime: OffsetDateTime,
        serviceNotes: String,
        customerEmail: String,
        serviceName: AppointmentType,
    ): String {
        val createAppointmentRequestDTO =
            CreateAppointmentRequestDTO(
                customerEmailAddress = customerEmail,
                serviceId = serviceId,
                serviceName = serviceName.description,
                serviceNotes = serviceNotes,
                startDateTime =
                    DateTimeDTO(
                        dateTime = startDateTime.withOffsetSameInstant(ZoneOffset.UTC).format(FORMATER),
                        timeZone = TIME_ZONE,
                    ),
                endDateTime =
                    DateTimeDTO(
                        dateTime = endDateTime.withOffsetSameInstant(ZoneOffset.UTC).format(FORMATER),
                        timeZone = TIME_ZONE,
                    ),
            )
        val booking =
            msBookingWebClient.createAppointment(
                bookingsId = bookingId,
                createAppointmentRequestDTO = createAppointmentRequestDTO,
            )
        return booking.id
    }

    companion object {
        private val log = LoggerFactory.getLogger(MsbookingClient::class.java)
        private val FORMATER = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")
        private const val TIME_ZONE = "UTC"
    }
}
