package com.fleetmanagement.modules.msbooking.adapter.out

class MsbookingClientException(
    override val message: String?,
    override val cause: Throwable?,
) : RuntimeException()

class IllegalServiceNameException(
    serviceName: String,
) : IllegalArgumentException(
        "appointment has a illegal service name: $serviceName",
        null,
    )

class IllegalServiceNoteException(
    serviceNote: String?,
) : IllegalArgumentException(
        "appointment has a illegal service note: $serviceNote",
        null,
    )

class IllegalServiceNoteForExchangeException(
    serviceName: String,
) : IllegalArgumentException(
        "appointment type exchange is has a service name $serviceName which is not following the contract",
        null,
    )
