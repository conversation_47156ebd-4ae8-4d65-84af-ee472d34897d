package com.fleetmanagement.modules.msbooking.application

import com.fasterxml.jackson.annotation.JsonProperty
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import java.time.OffsetDateTime

data class AppointmentDto(
    val appointmentId: String,
    val startTime: OffsetDateTime,
    val endTime: OffsetDateTime,
    val customerEmailAddress: String?,
    val customerName: String?,
    val keys: AppointmentKeys,
    val appointmentType: AppointmentType,
    val lastUpdatedDateTime: OffsetDateTime,
)

data class AppointmentKeys(
    val deliveryKey: VehicleTransferKey? = null,
    val returnKey: VehicleTransferKey? = null,
) {
    init {
        if (deliveryKey == null && returnKey == null) {
            throw IllegalArgumentException("At least one of deliveryKey or returnKey must be provided.")
        }
    }
}

enum class AppointmentType(
    val description: String,
    val normalizedDescription: String,
) {
    DELIVERY("Auslieferung", "auslieferung"),
    RETURN("Rücknahme", "ruecknahme"),
    EXCHANGE("Tauschtermin", "tauschtermin"),
}

data class AppointmentUpdateRequest(
    @JsonProperty("@odata.type") val odataType: String,
    val customerName: String,
    val customerEmailAddress: String,
    val customers: List<CustomerUpdateDTO>,
)

data class CustomerUpdateDTO(
    @JsonProperty("@odata.type") val odataType: String,
    val name: String,
    val emailAddress: String,
    val timeZone: String,
    val customQuestionAnswers: List<UpdateCustomQuestionAnswerDTO>,
)
