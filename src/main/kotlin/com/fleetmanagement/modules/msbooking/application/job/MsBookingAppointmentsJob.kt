package com.fleetmanagement.modules.msbooking.application.job

import com.fleetmanagement.modules.msbooking.adapter.out.MsBookingConfiguration
import com.fleetmanagement.modules.msbooking.application.MsBookingAppointmentsSync
import com.fleetmanagement.modules.msbooking.application.MsbookingCancelAppointment
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.TimeZone

@Component
@DisallowConcurrentExecution
@ConditionalOnBean(MsBookingConfiguration::class)
class MsBookingAppointmentsJob(
    private val msBookingAppointmentsSync: MsBookingAppointmentsSync,
    private val msbookingCancelAppointment: MsbookingCancelAppointment,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(MsBookingAppointmentsJob::class.java)
    }

    override fun execute(context: JobExecutionContext?) {
        log.info("Starting scheduled MsBookingAppointmentsJob Job.")
        msBookingAppointmentsSync.syncAppointments()
        log.info("Finished scheduled MsBookingAppointmentsJob Job.")
        log.info("Starting scheduled msbookingCancelAppointment Job.")
        msbookingCancelAppointment.handleCancelAppointment()
        log.info("Finished scheduled msbookingCancelAppointment Job.")
    }
}

@Configuration
@ConditionalOnBean(MsBookingConfiguration::class)
class MsBookingAppointmentsJobConfig {
    @Bean("msBookingAppointmentsJobDetail")
    fun msBookingAppointmentsJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(MsBookingAppointmentsJob::class.java)
            .withIdentity("msBookingAppointmentsJob")
            .withDescription("MsBookingAppointments Job")
            .storeDurably()
            .build()

    @Bean("msBookingAppointmentsJobTrigger")
    fun msBookingAppointmentsJobTrigger(
        @Qualifier("msBookingAppointmentsJobDetail") msBookingAppointmentsJobDetail: JobDetail,
        @Value("\${msbooking.scheduler.msbooking-appointments-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(msBookingAppointmentsJobDetail)
            .withIdentity("MsBookingAppointmentsJobTrigger")
            .withDescription("msBookingAppointments Job Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
