package com.fleetmanagement.modules.msbooking.application.port

import com.fleetmanagement.modules.msbooking.application.AppointmentType
import java.time.OffsetDateTime

/**
 * Functional interface to create an appointment in the MS Booking calendar.
 *
 * @param startDateTime The start time of the appointment in UTC.
 * @param endDateTime The end time of the appointment in UTC.
 * @param serviceNotes The vehicle transfer key.
 * @param customerEmail The email address of the customer (must have access to the calendar).
 * @param serviceName The type of appointment being created ( Delivery or Return).
 * @return A string representing the appointment ID or confirmation.
 */
fun interface CreateAppointment {
    fun createAppointment(
        startDateTime: OffsetDateTime,
        endDateTime: OffsetDateTime,
        serviceNotes: String,
        customerEmail: String,
        serviceName: AppointmentType,
    ): String
}
