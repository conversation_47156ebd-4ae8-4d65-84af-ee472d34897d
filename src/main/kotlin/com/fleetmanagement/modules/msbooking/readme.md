Msbookings

Overview:
1. The ms booking run as cron job
2. collect all meeting from "NOW" on
3. filter to meeting that was updated in the last day
4. extract the keys from the service note
5. extract the type (delivery return exchange ) of appointment
6. take update plandelivery\return date
7. if the meeting is new it will fill the missing details if they are available, any missing will be empty string


FYI:
* The [graph api](https://learn.microsoft.com/en-us/graph/api/bookingbusiness-list-appointments?view=graph-rest-1.0&tabs=http#:%7E:text=anonymousJoinWebUrl%22%3A%20null%2C%0A%20%20%20%20%20%20%20%20%20%20%20%20%22staffMemberIds%22%3A%20%5B%5D%2C%0A%20%20%20%20%20%20%20%20%20%20%20%20%22-,startDateTime,-%22%3A%20%7B%0A%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%20%22dateTime%22%3A%20%222018%2D04)  
to current date do not support $filter or $top (pagination) (more option [here](https://learn.microsoft.com/en-us/graph/filter-query-parameter?tabs=http))
and therefore the system fetch all existing appointments and filter in app base on the ``lastUpdatedDateTime`` property.
Once microsoft

* Because of the mention above to fetch all appointments we do not use the appointment api ( see link above) but the [calendar API](https://learn.microsoft.com/en-us/graph/api/user-list-calendarview?view=graph-rest-1.0&tabs=http)
as  this API make it possble to query base on time and therefore we can reduce from "all appointments" to future appointment \
The assumption is that appointments in the past already happend and if there are being updated\canceled it is not relevant

* when a using an Azure client with client_creds flow the user has "too much power" and was not apporoved to use in prod env
therefore Serkan has created a technical user, and we authenticate to azure with a password flow are deprecated in spring 5+ and therefore we could not use more spring security
as a result the authentication is "customise" and not the built-in spring web client oauth2 flow.

* There is a feature flag allowing to disable msbooking. the authentication to msbooking is being doin on bootstrap and therefore if you wish to
disable the behaviour, turn the flag off

Assumptions:

* If the user set an appointment to a planned vehicle transfer, it has a vehicle responsible person

* a key the user insert in msbooking service note have to valid and correspond to the service name i.e:
Delviery -> planned vehicle transfer key
Return -> vehicle transfer key
Exchange -> must be of the following form
``Auslieferung: <VT-KEY of planned VT>,
  Rücknahme: <VT-KEY active VT>``


