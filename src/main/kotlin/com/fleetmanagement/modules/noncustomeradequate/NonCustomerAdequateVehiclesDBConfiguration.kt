package com.fleetmanagement.modules.noncustomeradequate

import liquibase.integration.spring.SpringLiquibase
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import javax.sql.DataSource

@Configuration
class NonCustomerAdequateVehiclesDBConfiguration {
    @Bean
    fun nonCustomerAdequateVehiclesLiquibase(datasource: DataSource): SpringLiquibase {
        val liquibase = SpringLiquibase()
        liquibase.changeLog = "classpath:/db/changelog/db.changelog-noncustomeradequate.yml"
        liquibase.liquibaseSchema = "noncustomeradequate"
        liquibase.dataSource = datasource
        return liquibase
    }
}
