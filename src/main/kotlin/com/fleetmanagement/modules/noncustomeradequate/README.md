# Non-Customer Adequate Vehicles
This module handles the process of identifying non-customer adequate vehicles.

## Features Overview
This module comprises the following features:

1. [Schedule NCA validation when an option tag is added](#schedule-nca-validation-when-an-option-tag-is-added)
2. [Schedule NCA validation when an option tag is deleted](#schedule-nca-validation-when-an-option-tag-is-deleted)
3. [Cron Job - to execute scheduled validations](#cron-job---to-execute-scheduled-validations)
4. [Perform NCA validation when vehicle options are changed from PVH](#perform-nca-validation-when-vehicle-options-are-changed-from-pvh)
5. [Perform NCA validation when the vehicle's rebuilt status is changed from the UI](#perform-nca-validation-when-the-vehicles-rebuilt-status-is-changed-from-the-ui)
6. [Perform NCA validation when the NCA status is changed from UI](#perform-nca-validation-when-the-nca-status-is-changed-from-ui)

## Implementation Details

### Schedule NCA Validation When an Option Tag is Added
An API is exposed to schedule NCA validation when an option tag is added from the UI. This API will add a schedule entry <br>
to the database with a status of 'PENDING'. If there are other pending schedules with the same option tag, they will be aborted.

When this schedule is picked up by the Cron Job, the service will fetch all existing vehicles that have the specified NCA <br>
option and validate whether they are NCA vehicles or not.

### Schedule NCA Validation When an Option Tag is Deleted
An API is exposed to schedule NCA validation when an option tag is deleted from the UI. This API will add a schedule entry <br>
to the database with a status of 'PENDING'. If there are other pending schedules with the same option tag, they will be aborted.

When this schedule is picked up by the Cron Job, the service will fetch all existing vehicles that have the specified NCA <br>
option and validate whether they are still NCA vehicles or regular vehicles.

### Cron Job - To Execute Scheduled Validations
A cron job is configured to run every minute, which checks the pending schedules and processes them according to the NCA <br>
option action (option added/deleted).

This ensures that only one schedule is processed at a given time.

### Perform NCA Validation When Vehicle Options are Changed from PVH
When vehicle options are changed by the PVH system, the vehicle will be validated against NCA options and identified as <br>
either an NCA or regular vehicle.

### Perform NCA Validation When the Vehicle's Rebuilt Status is Changed from the UI
When the rebuilt status of an NCA vehicle is updated from the UI, the vehicle will no longer be considered an NCA vehicle. <br>
The vehicle will be marked as 'REBUILT'.

When the rebuilt status is deleted from the UI, the vehicle will again be validated against NCA options and identified as <br>
either an NCA or regular vehicle.

### Perform NCA Validation When the NCA Status is Changed from UI
When the NCA status of a vehicle is updated from the UI, then the NCA status will be set on vehicle and automatic validation <br>
never triggered for the same vehicle.

If the status is set to 'NCA', the vehicle will be blocked for sale. <br>
If the status is set to 'REBUILT' or null then the vehicle will be unblocked for sale. <br>

Once the status is set manually, the vehicle will not be automatically validated against NCA options or rebuilt logic.
