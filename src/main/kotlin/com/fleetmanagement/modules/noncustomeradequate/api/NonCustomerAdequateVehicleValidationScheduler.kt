package com.fleetmanagement.modules.noncustomeradequate.api

interface NonCustomerAdequateVehicleValidationScheduler {
    /**
     * This method schedules a job to verify all the existing vehicles which contain given option tag
     * and update non-customer adequate relevant data.
     */
    fun scheduleValidationOnOptionTagAdded(optionTagId: String)

    /**
     * This method schedules a job to verify all the existing non-customer adequate vehicles which contain given option tag
     * and update non-customer adequate relevant data.
     */
    fun scheduleValidationOnOptionTagDeleted(optionTagId: String)
}
