/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.noncustomeradequate.api

import java.math.BigDecimal
import java.time.OffsetDateTime
import java.util.Optional
import java.util.UUID

interface UpsertNonCustomerAdequateData {
    /**
     * Will update or create [com.fleetmanagement.modules.noncustomeradequate.repository.entities.JPANonCustomerAdequateVehicleEntity]
     * identified by given vehicle id.
     *
     * No version-check will be performed using this function, so only use it for DLZ based updates right now.
     *
     */
    fun upsertNonCustomerAdequate(update: NonCustomerAdequateDataUpdate)
}

data class NonCustomerAdequateDataUpdate(
    val vehicleId: UUID,
    val profitabilityAuditDone: Optional<OffsetDateTime>?,
    val rebuildStarted: Optional<OffsetDateTime>?,
    val rebuildDone: Optional<OffsetDateTime>?,
    val comment: Optional<String>?,
    val plannedRebuildCost: Optional<BigDecimal>?,
    val actualRebuildCost: Optional<BigDecimal>?,
    val expectedRevenue: Optional<BigDecimal>?,
    val salesPrice: Optional<BigDecimal>?,
    val ncaStatus: Optional<String>?,
)
