package com.fleetmanagement.modules.noncustomeradequate.features.job

import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.TimeZone

@Component
@DisallowConcurrentExecution
class NonCustomerAdequateVehicleEvaluationJob(
    private val jobService: NonCustomerAdequateVehicleEvaluationJobService,
) : Job {
    override fun execute(context: JobExecutionContext?) {
        jobService.executeNextEvaluation()
    }
}

@Configuration
class NonCustomerAdequateVehicleEvaluationJobConfig {
    @Bean("nonCustomerAdequateVehicleEvaluationJobDetail")
    fun nonCustomerAdequateVehicleEvaluationJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(NonCustomerAdequateVehicleEvaluationJob::class.java)
            .withIdentity("nonCustomerAdequateVehicleEvaluationJob")
            .withDescription("NonCustomerAdequateVehicleEvaluation Job")
            .storeDurably()
            .build()

    @Bean("nonCustomerAdequateVehicleEvaluationJobTrigger")
    fun nonCustomerAdequateVehicleEvaluationJobTrigger(
        @Qualifier("nonCustomerAdequateVehicleEvaluationJobDetail") nonCustomerAdequateVehicleEvaluationJobDetail: JobDetail,
        @Value("\${non-customer-adequate-vehicles.scheduler.evaluation-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(nonCustomerAdequateVehicleEvaluationJobDetail)
            .withIdentity("NonCustomerAdequateVehicleEvaluationJobTrigger")
            .withDescription("NonCustomerAdequateVehicleEvaluationJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
