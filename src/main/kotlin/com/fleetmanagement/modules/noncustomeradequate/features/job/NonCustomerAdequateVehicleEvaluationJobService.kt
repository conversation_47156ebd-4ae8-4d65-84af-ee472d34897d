package com.fleetmanagement.modules.noncustomeradequate.features.job

import com.fleetmanagement.modules.noncustomeradequate.features.validation.NonCustomerAdequateVehicleValidationService
import com.fleetmanagement.modules.noncustomeradequate.repository.JPANonCustomerAdequateEvaluationJobRepository
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JPANonCustomerAdequateEvaluationJobEntity
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JobStatus
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.OptionTagAction
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleOptionTags
import com.fleetmanagement.modules.vehicledata.api.ReadVehiclesByOptionTagId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class NonCustomerAdequateVehicleEvaluationJobService(
    private val jobRepository: JPANonCustomerAdequateEvaluationJobRepository,
    private val readVehiclesByOptionTagId: ReadVehiclesByOptionTagId,
    private val validationService: NonCustomerAdequateVehicleValidationService,
    private val readVehicleOptionTags: ReadVehicleOptionTags,
) {
    private val log = LoggerFactory.getLogger(NonCustomerAdequateVehicleEvaluationJobService::class.java)

    /**
     * This method will check if there is any new Non-Customer adequate option tag added or delete.
     * When one is found then this will fetch vehicles based on the option tag, and process them according
     * to the action (ADD or DELETE).
     */
    fun executeNextEvaluation() {
        val jobToExecute = findJobToExecute() ?: return

        log.info(
            "Starting NCA evaluation for option tag '${jobToExecute.optionTagId}', action: '${jobToExecute.action}'",
        )

        val nonCustomerAdequateOptionTags =
            readVehicleOptionTags.readAllNonCustomerAdequateOptionTags().map { it.optionId }
        var vehiclesPage: Page<VehicleDTO>
        var pageNumber = 0

        do {
            vehiclesPage = fetchVehicles(jobToExecute.optionTagId, pageNumber)

            if (vehiclesPage.isEmpty) {
                log.info("No vehicles found for option tag '${jobToExecute.optionTagId}', marking job as completed.")
                jobToExecute.updateStatus(JobStatus.COMPLETED)
                return
            }

            processVehicles(jobToExecute.action, vehiclesPage.content, nonCustomerAdequateOptionTags)

            ++pageNumber
        } while (vehiclesPage.content.size == PAGE_SIZE)

        // if loop ended naturally, it means we hit the last page
        jobToExecute.updateStatus(JobStatus.COMPLETED)
        log.info("NCA evaluation completed for option tag '${jobToExecute.optionTagId}', action: '${jobToExecute.action}'")
    }

    private fun processVehicles(
        actionType: OptionTagAction,
        vehicles: List<VehicleDTO>,
        nonCustomerAdequateOptionTags: List<String>,
    ) {
        when (actionType) {
            OptionTagAction.ADD -> {
                vehicles.forEach { validationService.setNonCustomerAdequateStatus(it) }
            }

            OptionTagAction.DELETE -> {
                vehicles.forEach {
                    validationService.clearNonCustomerAdequateStatus(
                        it,
                        nonCustomerAdequateOptionTags,
                    )
                }
            }
        }
    }

    private fun findJobToExecute(): JPANonCustomerAdequateEvaluationJobEntity? =
        jobRepository.findFirstByStatusOrderByCreatedAtAsc(JobStatus.PENDING)

    private fun fetchVehicles(
        optionTagId: String,
        pageNumber: Int,
    ): Page<VehicleDTO> {
        val pageRequest = PageRequest.of(pageNumber, PAGE_SIZE)
        return readVehiclesByOptionTagId.readAllVehiclesByOptionTagId(
            optionTagId = optionTagId,
            pageable = pageRequest,
        )
    }

    companion object {
        const val PAGE_SIZE = 1000
    }
}
