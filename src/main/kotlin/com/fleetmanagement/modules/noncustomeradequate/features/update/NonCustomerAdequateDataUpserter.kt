package com.fleetmanagement.modules.noncustomeradequate.features.update

import com.fleetmanagement.modules.noncustomeradequate.api.NonCustomerAdequateDataUpdate
import com.fleetmanagement.modules.noncustomeradequate.api.UpsertNonCustomerAdequateData
import com.fleetmanagement.modules.noncustomeradequate.features.validation.NonCustomerAdequateVehicleValidationService
import com.fleetmanagement.modules.noncustomeradequate.repository.JPANonCustomerAdequateVehicleRepository
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JPANonCustomerAdequateVehicleEntity
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import kotlin.jvm.optionals.getOrNull

@Component
class NonCustomerAdequateDataUpserter(
    private val repository: JPANonCustomerAdequateVehicleRepository,
    private val validationService: NonCustomerAdequateVehicleValidationService,
) : UpsertNonCustomerAdequateData {
    @Transactional
    override fun upsertNonCustomerAdequate(update: NonCustomerAdequateDataUpdate) {
        val existingEntry = repository.findByVehicleId(update.vehicleId)

        val entry =
            existingEntry ?: repository.save(
                JPANonCustomerAdequateVehicleEntity(update.vehicleId),
            )

        val shouldHandleRebuild =
            update.rebuildDone != null &&
                (existingEntry == null || existingEntry.rebuildDone != update.rebuildDone.getOrNull())

        val shouldHandleStatusUpdate =
            update.ncaStatus != null &&
                (existingEntry == null || existingEntry.status != update.ncaStatus.getOrNull())

        entry.updatePrivileged(
            profitabilityAuditDone = update.profitabilityAuditDone,
            rebuildStarted = update.rebuildStarted,
            rebuildDone = update.rebuildDone,
            comment = update.comment,
            plannedRebuildCost = update.plannedRebuildCost,
            actualRebuildCost = update.actualRebuildCost,
            expectedRevenue = update.expectedRevenue,
            salesPrice = update.salesPrice,
            status = update.ncaStatus,
        )

        when {
            shouldHandleStatusUpdate -> {
                validationService.handleManualStatusUpdate(entry)
            }

            shouldHandleRebuild -> {
                validationService.handleRebuildDoneDateUpdate(entry)
            }
        }
    }
}
