package com.fleetmanagement.modules.noncustomeradequate.features.validation

import com.fleetmanagement.modules.noncustomeradequate.api.NonCustomerAdequateVehicleValidationScheduler
import com.fleetmanagement.modules.noncustomeradequate.repository.JPANonCustomerAdequateEvaluationJobRepository
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JPANonCustomerAdequateEvaluationJobEntity
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JobStatus
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.OptionTagAction
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional
class NonCustomerAdequateVehicleValidationSchedulerService(
    private val jobRepository: JPANonCustomerAdequateEvaluationJobRepository,
) : NonCustomerAdequateVehicleValidationScheduler {
    override fun scheduleValidationOnOptionTagAdded(optionTagId: String) {
        abortExistingJobsForGivenOptionTagId(optionTagId)

        val newJob =
            JPANonCustomerAdequateEvaluationJobEntity(
                optionTagId = optionTagId,
                action = OptionTagAction.ADD,
            )

        jobRepository.save(newJob)
    }

    override fun scheduleValidationOnOptionTagDeleted(optionTagId: String) {
        abortExistingJobsForGivenOptionTagId(optionTagId)

        val newJob =
            JPANonCustomerAdequateEvaluationJobEntity(
                optionTagId = optionTagId,
                action = OptionTagAction.DELETE,
            )

        jobRepository.save(newJob)
    }

    private fun abortExistingJobsForGivenOptionTagId(optionTagId: String) =
        jobRepository
            .findAllByOptionTagIdAndStatusIn(
                optionTagId = optionTagId,
                statusList = listOf(JobStatus.PENDING),
            ).map { it.updateStatus(JobStatus.ABORTED) }
}
