package com.fleetmanagement.modules.noncustomeradequate.features.validation

import com.fasterxml.jackson.databind.JsonNode
import com.fleetmanagement.modules.noncustomeradequate.features.validation.NonCustomerAdequateStatus.NON_CUSTOMER_ADEQUATE_VEHICLE
import com.fleetmanagement.modules.noncustomeradequate.features.validation.NonCustomerAdequateStatus.NON_CUSTOMER_ADEQUATE_VEHICLE_RESOLVED
import com.fleetmanagement.modules.noncustomeradequate.repository.JPANonCustomerAdequateVehicleRepository
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JPANonCustomerAdequateVehicleEntity
import com.fleetmanagement.modules.vehicledata.api.BlockedForSaleSource
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleOptionTags
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicleBlockedForSale
import com.fleetmanagement.modules.vehicledata.api.dtos.OrderDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.api.events.VehicleOptionsUpdatedEvent
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener
import java.util.UUID

@Service
@Transactional
class NonCustomerAdequateVehicleValidationService(
    private val repository: JPANonCustomerAdequateVehicleRepository,
    private val updateVehicleBlockedForSale: UpdateVehicleBlockedForSale,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val readVehicleOptionTags: ReadVehicleOptionTags,
) {
    private val log = LoggerFactory.getLogger(NonCustomerAdequateVehicleValidationService::class.java)

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun onVehicleOptionsUpdated(event: VehicleOptionsUpdatedEvent) {
        try {
            val vehicle = getVehicle(event.vehicleId)
            evaluateVehicleAdequacyStatus(vehicle)
        } catch (e: Exception) {
            log.error("Failed to process vehicle options update for vehicle ${event.vehicleId}", e)
        }
    }

    private fun evaluateVehicleAdequacyStatus(vehicle: VehicleDTO) {
        if (isStatusSetManually(vehicle.id)) {
            log.info("Vehicle ${vehicle.id} status was set manually, skipping automatic validation")
            return
        }

        val nonCustomerAdequateOptionTags =
            readVehicleOptionTags.readAllNonCustomerAdequateOptionTags().map { it.optionId }

        when {
            vehicleHasMatchingOptions(vehicle, nonCustomerAdequateOptionTags) -> {
                log.info("Vehicle ${vehicle.id} identified as non-customer adequate due to option tags")
                setNonCustomerAdequateStatus(vehicle)
            }

            vehicleIsNonCustomerAdequateAlready(vehicle) -> {
                log.info("Vehicle ${vehicle.id} no longer non-customer adequate, removing status")
                clearNonCustomerAdequateStatus(vehicle, nonCustomerAdequateOptionTags)
            }
        }
    }

    fun handleRebuildDoneDateUpdate(entity: JPANonCustomerAdequateVehicleEntity) {
        val vehicle = getVehicle(entity.vehicleId)

        if (entity.statusUpdatedManually) {
            log.info("Vehicle ${vehicle.id} status was set manually to ${entity.status}, skipping vehicle rebuild handling")
            return
        }

        if (entity.rebuildDone != null) {
            entity.updateStatus(NON_CUSTOMER_ADEQUATE_VEHICLE_RESOLVED)
            log.info("Vehicle ${vehicle.id} rebuild completed, status updated to RESOLVED")

            if (!VehicleSaleBlockingRules.shouldSkipUnblockingForSaleOnRebuilt(vehicle)) {
                unblockVehicleForSale(vehicle.id)
            }
        } else { // rebuild date is cleared, so re-evaluate the vehicle status
            entity.updateStatus(null)
            evaluateVehicleAdequacyStatus(vehicle)
        }
    }

    private fun markVehicleAsNonCustomerAdequate(vehicleId: UUID) {
        val entity =
            repository.findByVehicleId(vehicleId)
                ?: repository.save(JPANonCustomerAdequateVehicleEntity(vehicleId = vehicleId))

        entity.updateStatus(NON_CUSTOMER_ADEQUATE_VEHICLE)
        log.info("Vehicle $vehicleId marked as non-customer adequate")
    }

    private fun resetNonCustomerAdequateStatus(vehicleId: UUID) {
        val entity = repository.findByVehicleId(vehicleId)
        entity?.let {
            it.updateStatus(null)
            log.info("Vehicle $vehicleId status has been reset")
        }
    }

    /**
     * Clears non-customer adequate status for a vehicle if it no longer has matching option tags.
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun clearNonCustomerAdequateStatus(
        vehicle: VehicleDTO,
        nonCustomerAdequateOptionTags: List<String>,
    ) {
        // Skip if status was set manually
        if (isStatusSetManually(vehicle.id)) {
            log.info("Vehicle ${vehicle.id} status was set manually, skipping automatic validation")
            return
        }

        if (vehicleHasMatchingOptions(vehicle, nonCustomerAdequateOptionTags)) {
            log.info("Vehicle ${vehicle.id} still has non-customer adequate option tags, status will not be cleared")
            return
        }

        resetNonCustomerAdequateStatus(vehicle.id)
        if (!VehicleSaleBlockingRules.shouldSkipUnblockingForSale(vehicle)) {
            unblockVehicleForSale(vehicle.id)
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun setNonCustomerAdequateStatus(vehicle: VehicleDTO) {
        // Skip if status was set manually
        if (isStatusSetManually(vehicle.id)) {
            log.info("Vehicle ${vehicle.id} status was set manually, skipping automatic validation")
            return
        }

        if (vehicleIsRebuiltAlready(vehicle.id)) {
            log.info("Vehicle ${vehicle.id} is already rebuilt, skipping non-customer adequate status update")
            return
        }

        if (vehicleIsNonCustomerAdequateAlready(vehicle)) {
            log.info("Vehicle ${vehicle.id} is already non-customer adequate, skipping status update")
            return
        }

        markVehicleAsNonCustomerAdequate(vehicle.id)
        if (!VehicleSaleBlockingRules.shouldSkipBlockingForSale(vehicle)) {
            blockVehicleForSale(vehicle.id)
        }
    }

    private fun getVehicle(vehicleId: UUID): VehicleDTO =
        readVehicleByVehicleId.readVehicleById(vehicleId)
            ?: throw IllegalStateException("Vehicle is expected to exist for id: $vehicleId")

    private fun vehicleHasMatchingOptions(
        vehicle: VehicleDTO,
        nonCustomerAdequateOptionTags: List<String>,
    ): Boolean {
        val vehicleOptions = vehicle.options ?: return false
        val vehicleOptionTags = VehicleOptionExtractor.extractOptionTags(vehicleOptions)
        return nonCustomerAdequateOptionTags.any(vehicleOptionTags::contains)
    }

    /**
     * Determines if the vehicle should have its non-customer adequate status removed.
     * If a vehicle is already in RESOLVED (already rebuilt) status, then it should be untouched.
     */
    private fun vehicleIsNonCustomerAdequateAlready(vehicle: VehicleDTO): Boolean {
        val entity = repository.findByVehicleId(vehicle.id)
        return entity?.status == NON_CUSTOMER_ADEQUATE_VEHICLE
    }

    /**
     * Determines if the vehicle should have its non-customer adequate status removed.
     * If a vehicle is already in RESOLVED (already rebuilt) status, then it should be untouched.
     */
    private fun vehicleIsRebuiltAlready(vehicleId: UUID): Boolean {
        val entity = repository.findByVehicleId(vehicleId)
        return entity?.status == NON_CUSTOMER_ADEQUATE_VEHICLE_RESOLVED
    }

    private fun blockVehicleForSale(
        vehicleId: UUID,
        source: String = BlockedForSaleSource.NON_CUSTOMER_ADEQUATE_CHECK,
    ) {
        updateVehicleBlockedForSale.updateVehicleBlockedForSale(
            vehicleId = vehicleId,
            blockedForSale = true,
            source = source,
        )
        log.info("Vehicle $vehicleId blocked for sale due to non-customer adequate status")
    }

    private fun unblockVehicleForSale(
        vehicleId: UUID,
        source: String = BlockedForSaleSource.NON_CUSTOMER_ADEQUATE_CHECK,
    ) {
        updateVehicleBlockedForSale.updateVehicleBlockedForSale(
            vehicleId = vehicleId,
            blockedForSale = false,
            source = source,
        )
        log.info("Vehicle $vehicleId unblocked for sale - no longer non-customer adequate")
    }

    /**
     * Checks if the vehicle's non-customer adequate status was set manually.
     * If it was, the automatic validation will be skipped.
     */
    private fun isStatusSetManually(vehicleId: UUID): Boolean {
        val entity = repository.findByVehicleId(vehicleId)
        return entity?.statusUpdatedManually == true
    }

    /**
     * Handles manual status updates by updating blocked for sale status WITHOUT preconditions.
     * This is called when a user manually sets the non-customer adequate status.
     * Unlike automatic updates, this bypasses all business rule checks per requirement.
     */
    fun handleManualStatusUpdate(entity: JPANonCustomerAdequateVehicleEntity) {
        val vehicleId = entity.vehicleId
        when (entity.status) {
            NON_CUSTOMER_ADEQUATE_VEHICLE -> {
                // Since its a manual update, block vehicle for sale without any preconditions
                blockVehicleForSale(vehicleId = vehicleId, source = BlockedForSaleSource.MANUAL_UPDATE)
                log.info("Vehicle $vehicleId manually set to NCA - blocked for sale without preconditions")
            }

            else -> {
                unblockVehicleForSale(vehicleId = vehicleId, source = BlockedForSaleSource.MANUAL_UPDATE)
                log.info("Vehicle $vehicleId manually cleared NCA status - unblocked for sale without preconditions")
            }
        }
    }
}

object NonCustomerAdequateStatus {
    const val NON_CUSTOMER_ADEQUATE_VEHICLE = "NCA"
    const val NON_CUSTOMER_ADEQUATE_VEHICLE_RESOLVED = "REBUILT"
}

/**
 * Centralized business rules for vehicle sale blocking/unblocking decisions.
 *
 * This object contains the core business logic that determines when vehicles should
 * or should not have their blocked-for-sale status updated, even when they are
 * identified as non-customer adequate or when that status changes.
 */
object VehicleSaleBlockingRules {
    /**
     * Determines if blocking a vehicle for sale should be skipped even though
     * it has been identified as non-customer adequate.
     *
     * The vehicle should not be blocked for sale if any of the following conditions are true:
     * - The vehicle is already sold (status "SX98")
     * - The vehicle is already blocked for sale (regardless of reason)
     * - The vehicle was manually unblocked for sale by an admin user
     */
    fun shouldSkipBlockingForSale(vehicle: VehicleDTO): Boolean =
        isSold(vehicle.status) ||
            vehicle.order?.let { order ->
                isBlockedForSale(order) || isUnblockedManually(order)
            } ?: false

    /**
     * Determines if unblocking a vehicle for sale should be skipped even though
     * it is no longer identified as non-customer adequate.
     *
     * The vehicle should not be unblocked for sale if any of the following conditions are true:
     * - The vehicle is already unblocked for sale
     * - The vehicle was manually blocked for sale by an admin user
     * - The vehicle is blocked for sale due to consignee/importer data issues
     */
    fun shouldSkipUnblockingForSale(vehicle: VehicleDTO): Boolean =
        vehicle.order?.let { order ->
            isUnblockedForSale(order) ||
                isBlockedManually(order) ||
                isBlockedByConsignee(order)
        } ?: false

    /**
     * Determines if unblocking a vehicle for sale should be skipped even though
     * it is Rebuilt and no longer a non-customer adequate.
     *
     * The vehicle should not be unblocked for sale if any of the following conditions are true:
     * - The vehicle is already unblocked for sale
     * - The vehicle was manually blocked for sale by an admin user
     */
    fun shouldSkipUnblockingForSaleOnRebuilt(vehicle: VehicleDTO): Boolean =
        vehicle.order?.let { order ->
            isUnblockedForSale(order) ||
                isBlockedManually(order)
        } ?: false

    private fun isSold(status: String?): Boolean = status == "SX98"

    private fun isBlockedForSale(order: OrderDTO): Boolean = order.blockedForSale == true

    private fun isUnblockedForSale(order: OrderDTO): Boolean = order.blockedForSale == false

    fun isBlockedManually(order: OrderDTO): Boolean =
        order.blockedForSale == true && order.blockedForSaleSource == BlockedForSaleSource.MANUAL_UPDATE

    private fun isUnblockedManually(order: OrderDTO): Boolean =
        order.blockedForSale == false && order.blockedForSaleSource == BlockedForSaleSource.MANUAL_UPDATE

    private fun isBlockedByConsignee(order: OrderDTO): Boolean =
        order.blockedForSale == true && order.blockedForSaleSource == BlockedForSaleSource.CONSIGNEE_DATA
}

object VehicleOptionExtractor {
    /**
     * Extracts option tag IDs from the provided JSON node representing vehicle options.
     *
     * @param optionsJson The JSON node containing vehicle options.
     * @return A set of option tag IDs extracted from the JSON.
     */
    fun extractOptionTags(optionsJson: JsonNode?): Set<String> {
        val optionsNode = optionsJson?.get("current")?.get("individualOptions") ?: return emptySet()

        return optionsNode
            .mapNotNull { option ->
                option
                    ?.get("id")
                    ?.takeIf { it.isTextual && it.asText().isNotBlank() }
                    ?.asText()
            }.toSet()
    }
}
