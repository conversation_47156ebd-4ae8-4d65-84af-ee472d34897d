package com.fleetmanagement.modules.noncustomeradequate.repository

import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JPANonCustomerAdequateEvaluationJobEntity
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JobStatus
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface JPANonCustomerAdequateEvaluationJobRepository : JpaRepository<JPANonCustomerAdequateEvaluationJobEntity, Long> {
    fun findAllByOptionTagIdAndStatusIn(
        optionTagId: String,
        statusList: List<JobStatus>,
    ): List<JPANonCustomerAdequateEvaluationJobEntity>

    fun findFirstByStatusOrderByCreatedAtAsc(status: JobStatus): JPANonCustomerAdequateEvaluationJobEntity?
}
