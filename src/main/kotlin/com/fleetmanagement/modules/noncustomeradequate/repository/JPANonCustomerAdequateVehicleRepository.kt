package com.fleetmanagement.modules.noncustomeradequate.repository

import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JPANonCustomerAdequateVehicleEntity
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface JPANonCustomerAdequateVehicleRepository : JpaRepository<JPANonCustomerAdequateVehicleEntity, Long> {
    fun findByVehicleId(vehicleId: UUID): JPANonCustomerAdequateVehicleEntity?

    fun existsByVehicleId(vehicleId: UUID): <PERSON><PERSON><PERSON>
}
