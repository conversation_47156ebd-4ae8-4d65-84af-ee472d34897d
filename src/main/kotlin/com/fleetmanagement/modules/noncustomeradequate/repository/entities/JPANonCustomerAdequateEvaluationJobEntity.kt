package com.fleetmanagement.modules.noncustomeradequate.repository.entities

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.time.OffsetDateTime

@Entity
@Table(name = "evaluation_job", schema = "noncustomeradequate")
class JPANonCustomerAdequateEvaluationJobEntity(
    @Column(name = "nca_option_tag_id")
    val optionTagId: String,
    @Column(name = "action")
    @Enumerated(EnumType.STRING)
    var action: OptionTagAction,
) {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long? = null

    @Column(name = "job_status")
    @Enumerated(EnumType.STRING)
    var status: JobStatus = JobStatus.PENDING
        private set

    @Column(name = "created_at")
    @CreationTimestamp
    val createdAt: OffsetDateTime = OffsetDateTime.now()

    @Column(name = "updated_at")
    @UpdateTimestamp
    val updatedAt: OffsetDateTime = OffsetDateTime.now()

    internal fun updateStatus(status: JobStatus) {
        this.status = status
    }
}

enum class JobStatus {
    PENDING, // NCA evaluation for given option tag will be picked in future job
    ABORTED, // NCA evaluation for given option tag is aborted because same option tag ID is either added or deleted from NCA list
    COMPLETED, // NCA evaluation for given option tag is successfully completed
}

enum class OptionTagAction {
    ADD, // Given Option Tag is added as non-customer adequate vehicle option
    DELETE, // Given Option Tag is deleted as non-customer adequate vehicle option
}
