package com.fleetmanagement.modules.noncustomeradequate.repository.entities

import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateConstraintViolation
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.Optional
import java.util.UUID

@Entity
@Table(name = "vehicle", schema = "noncustomeradequate")
class JPANonCustomerAdequateVehicleEntity(
    @Column(name = "vehicle_id", unique = true, nullable = false)
    val vehicleId: UUID,
) {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id", unique = true, nullable = false)
    val id: Long? = null

    @Column(name = "status")
    var status: String? = null
        private set

    @Column(name = "profitability_audit_done")
    var profitabilityAuditDone: OffsetDateTime? = null
        private set

    @Column(name = "rebuild_started")
    var rebuildStarted: OffsetDateTime? = null
        private set

    @Column(name = "rebuild_done")
    var rebuildDone: OffsetDateTime? = null
        private set

    @Column(name = "comment")
    var comment: String? = null
        private set

    @Column(name = "planned_rebuild_cost")
    var plannedRebuildCost: BigDecimal? = null
        private set

    @Column(name = "actual_rebuild_cost")
    var actualRebuildCost: BigDecimal? = null
        private set

    @Column(name = "expected_revenue")
    var expectedRevenue: BigDecimal? = null
        private set

    @Column(name = "sales_price")
    var salesPrice: BigDecimal? = null
        private set

    @Column(name = "created_at")
    @CreationTimestamp
    val createdAt: OffsetDateTime = OffsetDateTime.now()

    @Column(name = "updated_at")
    @UpdateTimestamp
    val updatedAt: OffsetDateTime = OffsetDateTime.now()

    @Column(name = "status_updated_manually", nullable = false)
    var statusUpdatedManually: Boolean = false
        private set

    internal fun updateStatus(status: String?) {
        this.status = status
    }

    internal fun updateStatusManually(status: String?) {
        this.status = status
        this.statusUpdatedManually = true
    }

    internal fun updatePrivileged(
        profitabilityAuditDone: Optional<OffsetDateTime>?,
        rebuildStarted: Optional<OffsetDateTime>?,
        rebuildDone: Optional<OffsetDateTime>?,
        comment: Optional<String>?,
        plannedRebuildCost: Optional<BigDecimal>?,
        actualRebuildCost: Optional<BigDecimal>?,
        expectedRevenue: Optional<BigDecimal>?,
        salesPrice: Optional<BigDecimal>?,
        status: Optional<String>?,
    ) {
        profitabilityAuditDone
            ?.ifPresentOrElse(
                {
                    requireDateNotInFuture(it, JPANonCustomerAdequateVehicleEntity::profitabilityAuditDone.name)
                    this.profitabilityAuditDone = it
                },
                { this.profitabilityAuditDone = null },
            )

        rebuildStarted
            ?.ifPresentOrElse(
                {
                    requireDateNotInFuture(it, JPANonCustomerAdequateVehicleEntity::rebuildStarted.name)
                    this.rebuildStarted = it
                },
                { this.rebuildStarted = null },
            )

        rebuildDone
            ?.ifPresentOrElse(
                {
                    requireDateNotInFuture(it, JPANonCustomerAdequateVehicleEntity::rebuildDone.name)
                    this.rebuildDone = it
                },
                { this.rebuildDone = null },
            )

        comment
            ?.ifPresentOrElse({ this.comment = it }, { this.comment = null })

        plannedRebuildCost
            ?.ifPresentOrElse({ this.plannedRebuildCost = it }, { this.plannedRebuildCost = null })

        actualRebuildCost
            ?.ifPresentOrElse({ this.actualRebuildCost = it }, { this.actualRebuildCost = null })

        expectedRevenue
            ?.ifPresentOrElse({ this.expectedRevenue = it }, { this.expectedRevenue = null })

        salesPrice
            ?.ifPresentOrElse({ this.salesPrice = it }, { this.salesPrice = null })

        status
            ?.ifPresentOrElse({ this.updateStatus(it) }, { this.updateStatus(null) })
            ?.also { this.statusUpdatedManually = true }
    }

    private fun requireDateNotInFuture(
        date: OffsetDateTime,
        propertyName: String,
    ) {
        if (date.toLocalDate().isAfter(LocalDate.now())) {
            throw NonCustomerAdequateVehicleFutureDateException(
                vehicleId = this.vehicleId,
                date = date,
                constraintViolation =
                    VehicleUpdateConstraintViolation(
                        vehicleId = this.vehicleId,
                        type = ViolationType.FUTURE_DATE_NOT_ALLOWED,
                        propertyName = propertyName,
                    ),
            )
        }
    }
}

class NonCustomerAdequateVehicleFutureDateException(
    val vehicleId: UUID,
    val date: OffsetDateTime,
    val constraintViolation: VehicleUpdateConstraintViolation,
) : IllegalArgumentException(
        "Vehicle with id [$vehicleId] has a date for [${constraintViolation.propertyName}] not allowed in future: $date.",
        null,
    )
