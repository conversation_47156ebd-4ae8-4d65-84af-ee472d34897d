package com.fleetmanagement.modules.operations.vehicleregistration

import com.fleetmanagement.modules.vehicleregistration.api.WriteRegistrationOrder
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import org.quartz.*
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.util.*

@Component
@DisallowConcurrentExecution
class VehicleRestrictionManualJob(
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase,
    private val writeRegistrationOrder: WriteRegistrationOrder,
) : Job {
    /**
     * This job will restrict creating and updating registrations for all the vehicles
     * that are currently in an active transfer with a specific reason code.
     */
    companion object {
        private val log = LoggerFactory.getLogger(VehicleRestrictionManualJob::class.java)
        const val ACTIVE_VEHICLE_TRANSFER_MANUAL_RESTRICTION = "ACTIVE_VEHICLE_TRANSFER_MANUAL_RESTRICTION"
    }

    override fun execute(context: JobExecutionContext) {
        try {
            log.info("Executing registration restriction job for all active vehicle transfers...")
            restrictActiveTransfers()
            log.info("Job to restrict vehicles completed successfully...")
        } catch (ex: Exception) {
            log.error("Job failed to restrict vehicles", ex)
        }
    }

    private fun restrictActiveTransfers() {
        readVehicleTransferUseCase.findAllActiveTransfers().forEach { transfer ->
            try {
                writeRegistrationOrder.restrictVehicle(transfer.vehicleId, ACTIVE_VEHICLE_TRANSFER_MANUAL_RESTRICTION)
                log.info("Restricted vehicle $transfer.vehicleId with reason: $ACTIVE_VEHICLE_TRANSFER_MANUAL_RESTRICTION")
            } catch (ex: Exception) {
                log.error("Failed to restrict vehicle ${transfer.vehicleId} due to: ${ex.message}", ex)
            }
        }
    }
}

@Configuration
class VehicleRestrictionManualJobConfiguration {
    @Bean("vehicleRestrictionManualJobDetail")
    fun vehicleRestrictionManualJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(VehicleRestrictionManualJob::class.java)
            .withIdentity("vehicleRestrictionManualJob")
            .withDescription("vehicleRestrictionManualJobDetail Detail")
            .storeDurably()
            .build()
}
