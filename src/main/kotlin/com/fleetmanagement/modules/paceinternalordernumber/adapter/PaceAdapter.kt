/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.paceinternalordernumber.adapter

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.paceinternalordernumber.application.port.RequestFactoryCarPreparationOrderNumberUseCase
import com.fleetmanagement.modules.paceinternalordernumber.application.port.RequestFactoryCarPreparationOrderNumberUseCaseException
import com.fleetmanagement.modules.paceinternalordernumber.application.port.RequestMaintenanceOrderNumberException
import com.fleetmanagement.modules.paceinternalordernumber.application.port.RequestMaintenanceOrderNumberUseCase
import com.fleetmanagement.modules.paceinternalordernumber.generated.adapter.out.rest.model.OrderTypeDto
import com.fleetmanagement.modules.paceinternalordernumber.generated.adapter.out.rest.model.RequestInternalOrderNumberDto
import com.fleetmanagement.modules.paceinternalordernumber.generated.adapter.out.rest.model.SettlTypeDto
import com.fleetmanagement.modules.vehicledata.repository.entities.ModelRange.BOXSTER
import com.fleetmanagement.modules.vehicledata.repository.entities.ModelRange.CAYENNE
import com.fleetmanagement.modules.vehicledata.repository.entities.ModelRange.CUSTOMER_SPORTS_VEHICLE
import com.fleetmanagement.modules.vehicledata.repository.entities.ModelRange.MACAN
import com.fleetmanagement.modules.vehicledata.repository.entities.ModelRange.NINE_ELEVEN
import com.fleetmanagement.modules.vehicledata.repository.entities.ModelRange.PANAMERA
import com.fleetmanagement.modules.vehicledata.repository.entities.ModelRange.SUPER_SPORTS_CAR
import com.fleetmanagement.modules.vehicledata.repository.entities.ModelRange.TAYCAN
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.time.LocalDate
import java.util.StringJoiner
import com.fleetmanagement.modules.paceinternalordernumber.application.port.RequestFactoryCarPreparationOrderNumberUseCase.OrderNumber as FactoryCarPreparationOrderNumber
import com.fleetmanagement.modules.paceinternalordernumber.application.port.RequestMaintenanceOrderNumberUseCase.OrderNumber as MaintenanceOrderNumber

@Component
class PaceAdapter(
    private val paceClient: PaceClient,
) : RequestMaintenanceOrderNumberUseCase,
    RequestFactoryCarPreparationOrderNumberUseCase {
    override fun requestMaintenanceOrderNumber(
        employeeNumber: EmployeeNumber,
        depreciationRelevantCostCenter: String,
        usingCostCenter: String,
        licensePlate: String?,
        vin: String,
    ): MaintenanceOrderNumber {
        val requestPayload =
            buildRequest(
                employeeNumber = employeeNumber.value,
                depreciationRelevantCostCenter = sanitizeCostCenter(depreciationRelevantCostCenter),
                usingCostCenter = sanitizeCostCenter(usingCostCenter),
                vin = vin,
                orderTypeDto = getOrderTypeForMaintenanceOrderNumber(depreciationRelevantCostCenter),
                orderName = buildMaintenanceOrderName(licensePlate = licensePlate, vin = vin),
                settlementTypeDto = SettlTypeDto.PER,
            )

        log.info("Requesting new maintenance order number from PACE for vin $vin...")

        return try {
            paceClient
                .requestOrderNumber(requestPayload)
                .n0YCOINTORDERREPLICATIONFMSResponse
                .EV_OBJNR
                ?.let { MaintenanceOrderNumber(it) }
                ?: throw RequestMaintenanceOrderNumberException(
                    "No order number was returned on order number request.",
                    null,
                )
        } catch (exception: PaceException) {
            throw RequestMaintenanceOrderNumberException(
                message = "Error while requesting a new maintenance order number.",
                cause = exception,
            )
        }
    }

    override fun requestFactoryCarPreparationOrderNumber(
        employeeNumber: EmployeeNumber,
        licensePlate: String?,
        vin: String,
        modelRange: String,
    ): FactoryCarPreparationOrderNumber {
        val requestPayload =
            buildRequest(
                employeeNumber = employeeNumber.value,
                depreciationRelevantCostCenter = null,
                usingCostCenter = FIXED_RESPCCTR_FACTORY_CAR_PREPARATION,
                vin = vin,
                orderTypeDto = getOrderTypeForFactoryCarPreparationOrderNumber(modelRange),
                orderName = buildFactoryCarPreparationOrderName(licensePlate = licensePlate, vin = vin),
                settlementTypeDto = null,
            )

        log.info("Requesting new factory car preparation order number from PACE for vin $vin...")

        return try {
            paceClient
                .requestOrderNumber(requestPayload)
                .n0YCOINTORDERREPLICATIONFMSResponse
                .EV_OBJNR
                ?.let { FactoryCarPreparationOrderNumber(it) }
                ?: throw RequestFactoryCarPreparationOrderNumberUseCaseException(
                    "No order number was returned on order number request.",
                    null,
                )
        } catch (exception: PaceException) {
            throw RequestFactoryCarPreparationOrderNumberUseCaseException(
                message = "Error while requesting a new factory car preparation order number.",
                cause = exception,
            )
        }
    }

    private fun buildRequest(
        employeeNumber: String,
        depreciationRelevantCostCenter: String?,
        usingCostCenter: String,
        vin: String,
        orderTypeDto: OrderTypeDto,
        orderName: String,
        settlementTypeDto: SettlTypeDto?,
    ) = RequestInternalOrderNumberDto(
        ORDER_TYPE = orderTypeDto,
        ORDER_NAME = orderName,
        CO_AREA = CO_AREA,
        COMP_CODE = COMP_CODE,
        RESPCCTR = usingCostCenter,
        S_ORD_ITEM = S_ORDER_ITEM,
        PERSON_RESP = employeeNumber,
        ESTIMATED_COSTS = COSTS_0,
        APPLICATION_DATE = LocalDate.now().toString(),
        DATE_WORK_BEGINS = EMPTY_DATE,
        DATE_WORK_ENDS = EMPTY_DATE,
        PROCESSING_GROUP = PROCESSING_GROUP,
        PLN_RELEASE = EMPTY_DATE,
        PLN_COMPLETION = EMPTY_DATE,
        PLN_CLOSE = EMPTY_DATE,
        SETTL_TYPE = settlementTypeDto,
        SOURCE = null,
        PERCENTAGE = PERCENTAGE_0,
        COSTCENTER = depreciationRelevantCostCenter,
        VIN = vin,
    )

    /**
     * The costCenter values maintained in the UI do not match those expected by PACE.
     * see FPT1-874
     */
    private fun sanitizeCostCenter(costCenter: String): String =
        costCenter
            .replace(H001_PREFIX, "")
            .padStart(10, '0')

    private fun buildMaintenanceOrderName(
        licensePlate: String?,
        vin: String,
    ): String {
        val stringJoiner =
            StringJoiner(" ")
                .add(MAINTENANCE_ORDER_NAME_PREFIX)
                .add(vin)
        licensePlate?.also { stringJoiner.add(it) }
        return stringJoiner.toString()
    }

    private fun buildFactoryCarPreparationOrderName(
        licensePlate: String?,
        vin: String,
    ): String {
        val stringJoiner =
            StringJoiner(" ")
                .add(FACTORY_CAR_PREPARATION_ORDER_NAME_PREFIX)
                .add(vin)
        licensePlate?.also { stringJoiner.add(it) }
        return stringJoiner.toString()
    }

    /**
     * Calculates OrderType based on given depreciationRelevantCostCenterId.
     * Mapping was provided in FPT1-874
     */
    private fun getOrderTypeForMaintenanceOrderNumber(depreciationRelevantCostCenter: String): OrderTypeDto =
        when (sanitizeCostCenter(depreciationRelevantCostCenter)) {
            "**********" -> OrderTypeDto.Y73V
            "**********" -> OrderTypeDto.Y73Y
            "**********" -> OrderTypeDto.Y73W
            "**********" -> OrderTypeDto.Y73X
            "**********" -> OrderTypeDto.Y73X
            "**********" -> OrderTypeDto.Y73X
            "**********" -> OrderTypeDto.Y73L
            else -> throw RequestMaintenanceOrderNumberException(
                message = "Could not find OrderType mapping for given depreciationRelevantCostCenter $depreciationRelevantCostCenter.",
                cause = null,
            )
        }

    /**
     * Calculates OrderType based on given modelRange.
     * Mapping was provided in FPT1-924
     */
    private fun getOrderTypeForFactoryCarPreparationOrderNumber(modelRange: String): OrderTypeDto =
        when (modelRange) {
            BOXSTER.description -> OrderTypeDto.Y49B
            NINE_ELEVEN.description -> OrderTypeDto.Y49C
            MACAN.description -> OrderTypeDto.Y49M
            PANAMERA.description -> OrderTypeDto.Y49P
            CUSTOMER_SPORTS_VEHICLE.description -> OrderTypeDto.Y49S
            TAYCAN.description -> OrderTypeDto.Y49T
            CAYENNE.description -> OrderTypeDto.Y49Y
            SUPER_SPORTS_CAR.description -> OrderTypeDto.Y49Z
            else -> throw RequestFactoryCarPreparationOrderNumberUseCaseException(
                message = "Could not find OrderType mapping for given modelRange $modelRange.",
                cause = null,
            )
        }

    companion object {
        private val log = LoggerFactory.getLogger(PaceAdapter::class.java)

        private const val MAINTENANCE_ORDER_NAME_PREFIX = "Rep./Inst."
        private const val FACTORY_CAR_PREPARATION_ORDER_NAME_PREFIX = "Aufb.zum"
        private const val CO_AREA = "0001"
        private const val EMPTY_DATE = "0000-00-00"
        private const val S_ORDER_ITEM = "000000"
        private const val PROCESSING_GROUP = "00"
        private const val PERCENTAGE_0 = "0.00"
        private const val COSTS_0 = "0.00"
        private const val COMP_CODE = "0001"

        private const val FIXED_RESPCCTR_FACTORY_CAR_PREPARATION = "0000005609"

        // custom prefix used in UI for costCenter description
        private const val H001_PREFIX = "H001"
    }
}
