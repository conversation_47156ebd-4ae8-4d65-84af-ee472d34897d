/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.paceinternalordernumber.adapter

import com.fleetmanagement.modules.paceinternalordernumber.generated.adapter.out.rest.model.*
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.service.annotation.GetExchange

interface PaceClient {
    @GetExchange(
        url = "/http/FMSInternalOrders/3541",
        accept = ["application/json"],
    )
    fun requestOrderNumber(
        @RequestBody body: RequestInternalOrderNumberDto,
    ): InternalOrderNumberDto
}
