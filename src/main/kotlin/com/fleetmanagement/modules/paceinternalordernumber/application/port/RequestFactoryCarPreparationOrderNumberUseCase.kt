/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.paceinternalordernumber.application.port

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber

fun interface RequestFactoryCarPreparationOrderNumberUseCase {
    fun requestFactoryCarPreparationOrderNumber(
        employeeNumber: EmployeeNumber,
        licensePlate: String?,
        vin: String,
        modelRange: String,
    ): OrderNumber

    @JvmInline value class OrderNumber(
        val value: String,
    )
}

class RequestFactoryCarPreparationOrderNumberUseCaseException(
    message: String?,
    cause: Throwable?,
) : RuntimeException(message, cause)
