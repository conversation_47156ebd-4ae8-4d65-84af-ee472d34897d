/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.paceinternalordernumber.application.port

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber

fun interface RequestMaintenanceOrderNumberUseCase {
    fun requestMaintenanceOrderNumber(
        employeeNumber: EmployeeNumber,
        depreciationRelevantCostCenter: String,
        usingCostCenter: String,
        licensePlate: String?,
        vin: String,
    ): OrderNumber

    @JvmInline value class OrderNumber(
        val value: String,
    )
}

class RequestMaintenanceOrderNumberException(
    message: String?,
    cause: Throwable?,
) : RuntimeException(message, cause)
