/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.application

data class PdiValidationError(
    val identifier: String,
    val detail: String,
    val type: ValidationErrorType,
    val properties: Map<String, String> = emptyMap(),
) {
    enum class ValidationErrorType {
        PDI_MISSING_PLANNED_DATE,
        PDI_EMAIL_GENERATION_FAILED,
    }

    companion object {
        const val PROPERTY_VINS = "vins"
        const val PROPERTY_VIN = "vin"
    }
}
