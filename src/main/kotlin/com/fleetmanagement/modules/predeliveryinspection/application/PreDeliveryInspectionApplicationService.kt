/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.application

import com.fleetmanagement.emhshared.WORKING_DAYS
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.ValidationError
import com.fleetmanagement.modules.predeliveryinspection.application.port.CreatePreDeliveryInspectionUseCase
import com.fleetmanagement.modules.predeliveryinspection.application.port.PreDeliveryInspectionId
import com.fleetmanagement.modules.predeliveryinspection.application.port.ReadPreDeliveryInspectionSlotsUseCase
import com.fleetmanagement.modules.predeliveryinspection.application.port.UpdatePreDeliveryInspectionUseCase
import com.fleetmanagement.modules.predeliveryinspection.domain.service.PreDeliveryInspectionCreateService
import com.fleetmanagement.modules.predeliveryinspection.domain.service.PreDeliveryInspectionUpdateService
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.OffsetDateTime

@Component
@Transactional
class PreDeliveryInspectionApplicationService(
    private val preDeliveryInspectionCreateService: PreDeliveryInspectionCreateService,
    private val preDeliveryInspectionUpdateService: PreDeliveryInspectionUpdateService,
    private val preDeliveryInspectionFinder: PreDeliveryInspectionFinder,
) : CreatePreDeliveryInspectionUseCase,
    UpdatePreDeliveryInspectionUseCase,
    ReadPreDeliveryInspectionSlotsUseCase {
    override fun createPreDeliveryInspection(preDeliveryInspectionNew: PreDeliveryInspectionNewDto): PreDeliveryInspectionDto {
        val preDeliveryInspectionToBeCreated = preDeliveryInspectionNew.toNewPreDeliveryInspection()

        val createdPreDeliveryInspection =
            preDeliveryInspectionCreateService.createPreDeliveryInspection(preDeliveryInspectionToBeCreated)

        return createdPreDeliveryInspection.toPreDeliveryInspectionDto()
    }

    override fun updatePreDeliveryInspection(
        id: PreDeliveryInspectionId,
        // FPT1-794 FIXME we are missing version control here (and in domain)
        preDeliveryInspectionUpdate: PreDeliveryInspectionUpdateDto,
    ): List<ValidationError> {
        val existingPreDeliveryInspection =
            preDeliveryInspectionFinder.getPreDeliveryInspection(
                com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionId(
                    id.value,
                ),
            )

        return preDeliveryInspectionUpdateService
            .updatePreDeliveryInspection(
                preDeliveryInspectionUpdate = preDeliveryInspectionUpdate.toPreDeliveryUpdate(),
                preDeliveryInspectionToBeUpdated = existingPreDeliveryInspection,
            ).map { it.toValidationError() }
    }

    override fun updatePreDeliveryInspectionOrderedDate(
        id: PreDeliveryInspectionId,
        orderedDate: OffsetDateTime,
    ) {
        val existingPreDeliveryInspection =
            preDeliveryInspectionFinder.getPreDeliveryInspection(
                com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionId(
                    id.value,
                ),
            )
        preDeliveryInspectionUpdateService.updateOrderedDate(
            preDeliveryInspection = existingPreDeliveryInspection,
            orderedDate = orderedDate,
        )
    }

    override fun readPreDeliveryInspectionSlots(fromDate: LocalDate): List<PreDeliveryInspectionSlotDto> {
        val preDeliveryInspections =
            preDeliveryInspectionFinder.findPreDeliveryInspectionPlannedDateBetween(
                fromDate,
                fromDate.plusDays(DAYS_FOR_PDI_SLOTS),
            )

        val datesInPeriod = fromDate.datesUntil(fromDate.plusDays(DAYS_FOR_PDI_SLOTS + 1))

        val slotsByDate =
            preDeliveryInspections
                .asSequence()
                .mapNotNull { it.plannedDate?.toLocalDate() }
                .groupBy { it }
                .mapValues { (_, dates) -> dates.size }

        return datesInPeriod
            .filter { it.dayOfWeek in WORKING_DAYS }
            .map { date ->
                PreDeliveryInspectionSlotDto(
                    date = date,
                    pdiSlotsReserved = slotsByDate.getOrDefault(date, 0),
                )
            }.toList()
    }

    companion object {
        const val DAYS_FOR_PDI_SLOTS: Long = 30
    }
}
