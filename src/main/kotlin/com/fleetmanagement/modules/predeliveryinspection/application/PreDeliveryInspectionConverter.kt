/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.application

import com.fleetmanagement.modules.consigneedatasheet.domain.ConsigneeData
import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.ValidationError
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection
import com.fleetmanagement.modules.predeliveryinspection.domain.TireSet
import com.fleetmanagement.modules.predeliveryinspection.domain.service.PreDeliveryInspectionUpdate
import com.fleetmanagement.modules.predeliveryinspection.domain.service.PreDeliveryInspectionUpdateWarning
import java.util.*
import com.fleetmanagement.modules.consigneedatasheet.domain.TireSet as ConsigneeTireSet
import com.fleetmanagement.modules.predeliveryinspection.application.TireSet as TireSetDto

fun PreDeliveryInspection.toPreDeliveryInspectionDto(): PreDeliveryInspectionDto =
    PreDeliveryInspectionDto(
        id = this.id.value,
        vehicleId = this.vehicleId,
        tireSet = this.tireSet?.toTireSetDto(),
        isRelevant = this.isRelevant,
        foiling = this.foiling,
        refuel = this.refuel,
        charge = this.charge,
        digitalLogbook = this.digitalLogbook,
        licencePlateMounting = this.licencePlateMounting,
        orderedDate = this.orderedDate,
        completedDate = this.completedDate,
        comment = this.comment,
        plannedDate = this.plannedDate,
    )

fun PreDeliveryInspectionUpdateDto.toPreDeliveryUpdate() =
    PreDeliveryInspectionUpdate(
        tireSet = this.tireSet?.map { it.toTireSet() },
        refuel = this.refuel,
        charge = this.charge,
        digitalLogbook = this.digitalLogbook,
        licencePlateMounting = this.licencePlateMounting,
        foiling = this.foiling,
        isRelevant = this.isRelevant,
        orderedDate = this.orderedDate,
        completedDate = this.completedDate,
        comment = this.comment,
        plannedDate = this.plannedDate,
    )

fun PreDeliveryInspectionNewDto.toNewPreDeliveryInspection(): PreDeliveryInspection =
    PreDeliveryInspection(
        vehicleId = this.vehicleId,
        tireSet = this.tireSet?.toTireSet(),
        isRelevant = this.isRelevant,
        foiling = this.foiling,
        refuel = this.refuel,
        charge = this.charge,
        digitalLogbook = this.digitalLogbook,
        licencePlateMounting = this.licencePlateMounting,
    )

fun ConsigneeData.toPreDeliveryInspection(vehicleId: UUID): PreDeliveryInspection =
    PreDeliveryInspection(
        vehicleId = vehicleId,
        tireSet = this.preDeliveryInspection.currentTireSet?.toTireSet(),
        isRelevant = this.preDeliveryInspection.isRelevant,
        foiling = this.preDeliveryInspection.foiling,
        refuel = this.preDeliveryInspection.refuel,
        charge = this.preDeliveryInspection.recharge,
        digitalLogbook = this.preDeliveryInspection.digitalLogbook,
        licencePlateMounting = this.preDeliveryInspection.licensePlateMounting,
    )

fun ConsigneeTireSet.toTireSet(): TireSet =
    when (this) {
        ConsigneeTireSet.SR -> TireSet.SR
        ConsigneeTireSet.WR -> TireSet.WR
    }

private fun TireSetDto.toTireSet(): TireSet =
    when (this) {
        TireSetDto.SR -> TireSet.SR
        TireSetDto.WR -> TireSet.WR
    }

private fun TireSet.toTireSetDto(): TireSetDto =
    when (this) {
        TireSet.SR -> TireSetDto.SR
        TireSet.WR -> TireSetDto.WR
    }

fun PreDeliveryInspectionUpdateWarning.toValidationError(): ValidationError =
    when (this.type) {
        PreDeliveryInspectionUpdateWarning.WarningType.PDI_ALREADY_ORDERED ->
            ValidationError(
                identifier = this.id.value.toString(),
                type = ValidationError.ValidationErrorType.PDI_ALREADY_ORDERED,
                detail = "PreDeliveryInspection has already been ordered.",
                properties = this.properties,
            )

        PreDeliveryInspectionUpdateWarning.WarningType.PDI_LEAD_TIME_VIOLATION ->
            ValidationError(
                identifier = this.id.value.toString(),
                type = ValidationError.ValidationErrorType.PDI_LEAD_TIME_VIOLATION,
                detail = "Update for PreDeliveryInspection with id [${this.id.value}] violates configured pdiLeadTime.",
                properties = this.properties,
            )

        PreDeliveryInspectionUpdateWarning.WarningType.PDI_INVALID_PLANNED_DATE ->
            ValidationError(
                identifier = this.id.value.toString(),
                type = ValidationError.ValidationErrorType.PDI_INVALID_PLANNED_DATE,
                detail = "Update for PreDeliveryInspection with id [${this.id.value}] contains invalid plannedDate.",
                properties = this.properties,
            )
    }
