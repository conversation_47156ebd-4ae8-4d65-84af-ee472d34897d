/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.application

import java.time.OffsetDateTime
import java.util.*

data class PreDeliveryInspectionDto(
    val id: UUID,
    val vehicleId: UUID,
    val tireSet: TireSet?,
    val isRelevant: Boolean?,
    val foiling: Boolean?,
    val refuel: Boolean?,
    val charge: Boolean?,
    val digitalLogbook: Boolean?,
    val licencePlateMounting: Boolean?,
    val orderedDate: OffsetDateTime?,
    val completedDate: OffsetDateTime?,
    val plannedDate: OffsetDateTime?,
    val comment: String?,
)

data class PreDeliveryInspectionNewDto(
    val vehicleId: UUID,
    val tireSet: TireSet?,
    val isRelevant: Boolean?,
    val foiling: Boolean?,
    val refuel: Boolean?,
    val charge: Boolean?,
    val digitalLogbook: Boolean?,
    val licencePlateMounting: Boolean?,
)

/**
 * Optionals are used to mirror tri-state.
 * Not-provided properties are deserialized to null and will be ignored during update.
 * Explicitly provided nulls will be deserialized to Optional.empty() and result in updating the property to null.
 * Explicitly provided values will be wrapped in an Optional an updated accordingly.
 */
data class PreDeliveryInspectionUpdateDto(
    val tireSet: Optional<TireSet>?,
    val isRelevant: Optional<Boolean>?,
    val foiling: Optional<Boolean>?,
    val refuel: Optional<Boolean>?,
    val charge: Optional<Boolean>?,
    val digitalLogbook: Optional<Boolean>?,
    val licencePlateMounting: Optional<Boolean>?,
    val orderedDate: Optional<OffsetDateTime>?,
    val completedDate: Optional<OffsetDateTime>?,
    val plannedDate: Optional<OffsetDateTime>?,
    val comment: Optional<String>?,
)

enum class TireSet {
    SR,
    WR,
    ;

    companion object {
        fun of(name: String): TireSet =
            try {
                entries.single { name.equals(it.name, true) }
            } catch (exception: NoSuchElementException) {
                throw UnknownTireSetException(tireSet = name, cause = exception)
            }
    }
}

class UnknownTireSetException(
    tireSet: String,
    override val cause: Throwable,
) : RuntimeException(
        "TireSet with name [$tireSet] is not known by the system. Accepted values [${
            TireSet.entries.joinToString { it.name }
        }]",
    )
