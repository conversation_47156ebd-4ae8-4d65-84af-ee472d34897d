/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.application

import com.fleetmanagement.modules.predeliveryinspection.application.port.ReadPreDeliveryInspectionUseCase
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionId
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionNotFoundException
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionRepository
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import java.util.*

@Component
@Transactional(readOnly = true)
class PreDeliveryInspectionFinder(
    private val preDeliveryInspectionRepository: PreDeliveryInspectionRepository,
) : ReadPreDeliveryInspectionUseCase {
    fun getPreDeliveryInspection(id: PreDeliveryInspectionId): PreDeliveryInspection =
        preDeliveryInspectionRepository.findById(id)
            ?: throw PreDeliveryInspectionNotFoundException(id.value)

    fun findPreDeliveryInspectionByVehicleId(vehicleId: UUID): PreDeliveryInspection? =
        preDeliveryInspectionRepository.findByVehicleId(vehicleId)

    fun findPreDeliveryInspectionByVehicleIds(vehicleIds: List<UUID>): List<PreDeliveryInspection> =
        preDeliveryInspectionRepository.findByVehicleIdIn(vehicleIds)

    override fun readPreDeliveryInspection(vehicleId: UUID): PreDeliveryInspectionDto? =
        preDeliveryInspectionRepository.findByVehicleId(vehicleId)?.toPreDeliveryInspectionDto()

    /**
     * will return PDIs having orderedDate as null and plannedDate less than equal to provided date
     */
    fun findPreDeliveryInspectionForPDIOrderingEmail(date: OffsetDateTime): List<PreDeliveryInspection> {
        val startDate = date.toLocalDate().atStartOfDay()

        /**
         * set time precision to 6 digits to avoid postgresql rounding issues
         */
        val endDate = date.toLocalDate().atTime(23, 59, 59, 999_999_000)
        val specification =
            Specification
                .where(
                    specificationPlannedDateBetween(
                        startDate.atOffset(ZoneOffset.UTC),
                        endDate.atOffset(
                            ZoneOffset.UTC,
                        ),
                    ),
                ).and(specificationOrderedDateIsNull())
        return preDeliveryInspectionRepository.findAll(specification)
    }

    fun findPreDeliveryInspectionForPDIOrderingEmailThatHasBeenSent(date: OffsetDateTime): List<PreDeliveryInspection> {
        val startDate = date.toLocalDate().atStartOfDay()
        val endDate = date.toLocalDate().atTime(LocalTime.MAX)
        val specification =
            Specification
                .where(
                    specificationPlannedDateBetween(
                        startDate.atOffset(ZoneOffset.UTC),
                        endDate.atOffset(
                            ZoneOffset.UTC,
                        ),
                    ),
                ).and(specificationOrderedDateIsNotNull())
        return preDeliveryInspectionRepository.findAll(specification)
    }

    /**
     * will return pre delivery inspections with planned date between startDate and endDate
     */
    fun findPreDeliveryInspectionPlannedDateBetween(
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<PreDeliveryInspection> {
        val startDateForSearching = startDate.atStartOfDay(ZoneOffset.UTC).toOffsetDateTime()
        val endDateForSearching = endDate.atTime(LocalTime.MAX).atZone(ZoneOffset.UTC).toOffsetDateTime()
        return preDeliveryInspectionRepository.findByPlannedDateBetween(startDateForSearching, endDateForSearching)
    }

    companion object {
        private const val PLANNED_DATE = "plannedDate"
        private const val ORDERED_DATE = "orderedDate"

        private fun specificationPlannedDateBetween(
            startDate: OffsetDateTime,
            endDate: OffsetDateTime,
        ) = Specification<PreDeliveryInspection> { root, _, criteriaBuilder ->
            criteriaBuilder.between(root.get(PLANNED_DATE), startDate, endDate)
        }

        private fun specificationOrderedDateIsNull() =
            Specification<PreDeliveryInspection> { root, _, criteriaBuilder ->
                criteriaBuilder.isNull(root.get<OffsetDateTime>(ORDERED_DATE))
            }

        private fun specificationOrderedDateIsNotNull() =
            Specification<PreDeliveryInspection> { root, _, criteriaBuilder ->
                criteriaBuilder.isNotNull(root.get<OffsetDateTime>(ORDERED_DATE))
            }
    }
}
