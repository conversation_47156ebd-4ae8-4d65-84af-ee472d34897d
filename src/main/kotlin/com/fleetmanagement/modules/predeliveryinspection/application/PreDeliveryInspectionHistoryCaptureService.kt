package com.fleetmanagement.modules.predeliveryinspection.application

import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionEvent
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionId
import com.fleetmanagement.modules.vehiclehistory.api.VehicleChanges
import com.fleetmanagement.modules.vehiclehistory.api.dto.PreDeliveryInspectionHistoryDto
import com.fleetmanagement.modules.vehiclehistory.api.dto.VehicleChangeEvent
import com.fleetmanagement.modules.vehiclehistory.api.dto.VehicleChangeEventPayload
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener
import java.time.OffsetDateTime

@Component
class PreDeliveryInspectionHistoryCaptureService(
    private val preDeliveryInspectionFinder: PreDeliveryInspectionFinder,
    private val vehicleChanges: VehicleChanges,
) {
    // ensures that handleEvents() always runs within a fresh and independent transaction,
    // regardless of the caller's transaction state.
    // ensures that event processing only occurs after the primary transaction successfully commits,
    // preventing issues when processing uncommitted or rolled-back data.
    // allows the event handling process to occur in a separate thread, minimizing response time for
    // the primary transaction.
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handlePreDeliveryInspectionEvents(event: PreDeliveryInspectionEvent) {
        handlePreDeliveryInspectionChange(
            event.preDeliveryInspectionId,
            event::class.simpleName ?: "PreDeliveryInspectionEvent",
        )
    }

    private fun handlePreDeliveryInspectionChange(
        id: PreDeliveryInspectionId,
        eventType: String,
    ) {
        val preDeliveryInspection = preDeliveryInspectionFinder.getPreDeliveryInspection(id)
        val preDeliveryInspectionDto = toPreDeliveryInspectionDto(preDeliveryInspection)
        captureHistoryRecord(
            preDeliveryInspectionDto,
            eventType,
            preDeliveryInspection.lastModified,
        )
    }

    fun toPreDeliveryInspectionDto(preDeliveryInspection: PreDeliveryInspection): PreDeliveryInspectionHistoryDto =
        PreDeliveryInspectionHistoryDto(
            id = preDeliveryInspection.id.value,
            vehicleId = preDeliveryInspection.vehicleId,
            tireSet = preDeliveryInspection.tireSet?.name,
            isRelevant = preDeliveryInspection.isRelevant,
            foiling = preDeliveryInspection.foiling,
            refuel = preDeliveryInspection.refuel,
            charge = preDeliveryInspection.charge,
            digitalLogbook = preDeliveryInspection.digitalLogbook,
            licencePlateMounting = preDeliveryInspection.licencePlateMounting,
            orderedDate = preDeliveryInspection.orderedDate,
            completedDate = preDeliveryInspection.completedDate,
            plannedDate = preDeliveryInspection.plannedDate,
            comment = preDeliveryInspection.comment,
            createdBy = preDeliveryInspection.createdBy,
            created = preDeliveryInspection.created,
            lastModifiedBy = preDeliveryInspection.lastModifiedBy,
        )

    private fun captureHistoryRecord(
        changedTransferEntry: PreDeliveryInspectionHistoryDto,
        eventType: String,
        lastModified: OffsetDateTime,
    ) {
        vehicleChanges.addVehicleChange(
            VehicleChangeEvent(
                vehicleId = changedTransferEntry.vehicleId,
                modifiedAt = lastModified,
                modifiedBy = eventType,
                payload = VehicleChangeEventPayload(preDeliveryInspection = changedTransferEntry),
            ),
        )
    }
}
