package com.fleetmanagement.modules.predeliveryinspection.application

import com.fleetmanagement.modules.consigneedatasheet.application.ConsigneeDataFinder
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection
import com.fleetmanagement.modules.predeliveryinspection.domain.service.PreDeliveryInspectionCreateService
import com.fleetmanagement.modules.vehicledata.api.events.AutomaticVehicleCreatedEvent
import com.fleetmanagement.modules.vehicledata.api.events.ManualVehicleCreatedEvent
import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class VehicleCreatedEventHandler(
    private val preDeliveryInspectionCreateService: PreDeliveryInspectionCreateService,
    private val consigneeDataFinder: ConsigneeDataFinder,
) {
    @EventListener
    @Transactional
    fun handleAutomaticVehicleCreatedEvent(event: AutomaticVehicleCreatedEvent) {
        val consigneeDataMatched =
            event.consigneeNumber?.let { consignee ->
                consigneeDataFinder.findConsigneeDataByConsignee(consignee)
            } ?: emptyList()

        when {
            consigneeDataMatched.isNotEmpty() -> {
                val preDeliveryInspectionToBeCreated =
                    consigneeDataMatched.first().toPreDeliveryInspection(event.vehicleId)
                preDeliveryInspectionCreateService.createPreDeliveryInspection(preDeliveryInspectionToBeCreated)
            }
            else -> {
                log.debug(
                    "Creating empty PreDeliveryInspection: consignee was " +
                        if (event.consigneeNumber == null) "null" else "not found",
                )
                preDeliveryInspectionCreateService.createPreDeliveryInspection(
                    PreDeliveryInspection(
                        vehicleId = event.vehicleId,
                        tireSet = null,
                        isRelevant = null,
                        foiling = null,
                        refuel = null,
                        charge = null,
                        digitalLogbook = null,
                        licencePlateMounting = null,
                    ),
                )
            }
        }
    }

    @Async
    @Transactional
    @EventListener
    fun handleManualVehicleCreatedEvent(event: ManualVehicleCreatedEvent) {
        preDeliveryInspectionCreateService.createPreDeliveryInspection(
            PreDeliveryInspection(
                vehicleId = event.vehicleId,
                tireSet = null,
                isRelevant = null,
                foiling = null,
                refuel = null,
                charge = null,
                digitalLogbook = null,
                licencePlateMounting = null,
            ),
        )
    }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleCreatedEventHandler::class.java)
    }
}
