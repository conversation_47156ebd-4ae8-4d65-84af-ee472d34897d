package com.fleetmanagement.modules.predeliveryinspection.application

import com.fleetmanagement.modules.predeliveryinspection.application.port.*
import com.fleetmanagement.modules.predeliveryinspection.domain.WinterTiresTimeframe
import com.fleetmanagement.modules.predeliveryinspection.domain.WinterTiresTimeframeId
import com.fleetmanagement.modules.predeliveryinspection.domain.WinterTiresTimeframeUpdatedEvent
import org.springframework.context.ApplicationEventPublisher
import org.springframework.dao.DataIntegrityViolationException
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.*

@Component
@Transactional
class WinterTiresTimeframApplication(
    private val winterTiresTimeframeFinder: WinterTiresTimeframeFinder,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : ReadWinterTiresScheduleUseCase,
    UpdateWinterTiresTimeframeUseCase {
    override fun getAllWinterTiresSchedule(): List<WinterTiresTimeframeDto> =
        winterTiresTimeframeFinder.getWinterTiresSchedule().map {
            it.toWinterTiresScheduleDto()
        }

    private fun WinterTiresTimeframe.toWinterTiresScheduleDto(): WinterTiresTimeframeDto =
        WinterTiresTimeframeDto(
            id = this.id.value,
            fromDate = this.fromDate,
            toDate = this.toDate,
            version = this.version,
        )

    override fun update(winterTiresTimeframeToUpdate: WinterTiresTimeframeDto): WinterTiresTimeframeDto {
        val timeframe = winterTiresTimeframeFinder.getWinterTiresTimeframe(WinterTiresTimeframeId(winterTiresTimeframeToUpdate.id))
        if (winterTiresTimeframeToUpdate.version != timeframe.version) {
            throw DataIntegrityViolationException(
                "Updating of WinterTiresTimeframe with id [${winterTiresTimeframeToUpdate.id}] failed. Invalid version supplied.",
            )
        }
        timeframe.update(fromDate = winterTiresTimeframeToUpdate.fromDate, toDate = winterTiresTimeframeToUpdate.toDate)
        applicationEventPublisher.publishEvent(
            WinterTiresTimeframeUpdatedEvent(
                winterTiresTimeframeId = timeframe.id,
                occurredOn = OffsetDateTime.now(),
                id = UUID.randomUUID(),
            ),
        )
        return timeframe.toWinterTiresScheduleDto()
    }
}
