/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.application

import com.fleetmanagement.modules.predeliveryinspection.domain.WinterTiresTimeframe
import com.fleetmanagement.modules.predeliveryinspection.domain.WinterTiresTimeframeId
import com.fleetmanagement.modules.predeliveryinspection.domain.WinterTiresTimeframeNotFoundException
import com.fleetmanagement.modules.predeliveryinspection.domain.WinterTiresTimeframeRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional
class WinterTiresTimeframeFinder(
    private val winterTiresTimeframeRepository: WinterTiresTimeframeRepository,
) {
    fun getWinterTiresSchedule(): List<WinterTiresTimeframe> = winterTiresTimeframeRepository.findAll()

    fun getWinterTiresTimeframe(id: WinterTiresTimeframeId): WinterTiresTimeframe =
        winterTiresTimeframeRepository.findById(id)
            ?: throw WinterTiresTimeframeNotFoundException(id.value)
}
