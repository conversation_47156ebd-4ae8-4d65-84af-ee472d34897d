/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.predeliveryinspection.application.job

import com.fleetmanagement.modules.predeliveryinspection.application.PDIOrderingEmailService
import org.quartz.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.*

@Component
@DisallowConcurrentExecution
class PDIOrderingEmailJob(
    private val pdiOrderingEmailService: PDIOrderingEmailService,
) : Job {
    override fun execute(p0: JobExecutionContext?) {
        log.info("Starting scheduled PDIOrderingEmail Job.")
        pdiOrderingEmailService.createAndSendPDIOrderingEmail()
        log.info("Finished scheduled PDIOrderingEmail Job.")
    }

    companion object {
        private val log = LoggerFactory.getLogger(PDIOrderingEmailJob::class.java)
    }
}

@Configuration
class PDIOrderingEmailJobConfig {
    @Bean("pdiOrderingEmailJobDetail")
    fun pdiOrderingEmailJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(PDIOrderingEmailJob::class.java)
            .withIdentity("pdiOrderingEmailJob")
            .withDescription("PDIOrderingEmail Job")
            .storeDurably()
            .build()

    @Bean("pdiOrderingEmailJobTrigger")
    fun pdiOrderingEmailJobTrigger(
        @Qualifier("pdiOrderingEmailJobDetail") pdiOrderingEmailJobDetail: JobDetail,
        @Value("\${pre-delivery-inspection.pdi-ordering.scheduler.pdi-ordering-email-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(pdiOrderingEmailJobDetail)
            .withIdentity("PdiOrderingEmailJobTrigger")
            .withDescription("PdiOrderingEmailJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
