/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.application.port

import java.time.LocalDate
import java.util.*

fun interface ReadWinterTiresScheduleUseCase {
    fun getAllWinterTiresSchedule(): List<WinterTiresTimeframeDto>
}

data class WinterTiresTimeframeDto(
    val id: UUID,
    val fromDate: LocalDate,
    val toDate: LocalDate,
    val version: Int,
)
