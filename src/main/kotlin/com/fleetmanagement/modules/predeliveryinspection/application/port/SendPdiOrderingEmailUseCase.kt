/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.application.port

import com.fleetmanagement.modules.predeliveryinspection.application.PdiValidationError
import java.util.UUID

fun interface SendPdiOrderingEmailUseCase {
    fun createAndSendPdiEmailsAsync(vehicleIds: List<UUID>): List<PdiValidationError>
}
