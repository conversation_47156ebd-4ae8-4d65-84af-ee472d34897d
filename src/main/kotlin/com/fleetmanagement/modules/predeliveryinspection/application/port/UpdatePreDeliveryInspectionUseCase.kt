/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.application.port

import com.fleetmanagement.modules.fvm.features.manager.vehicletransfer.ValidationError
import com.fleetmanagement.modules.predeliveryinspection.application.PreDeliveryInspectionUpdateDto
import java.time.OffsetDateTime
import java.util.*

interface UpdatePreDeliveryInspectionUseCase {
    fun updatePreDeliveryInspection(
        id: PreDeliveryInspectionId,
        preDeliveryInspectionUpdate: PreDeliveryInspectionUpdateDto,
    ): List<ValidationError>

    /**
     * Will update orderDate of given [com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection]
     */
    fun updatePreDeliveryInspectionOrderedDate(
        id: PreDeliveryInspectionId,
        orderedDate: OffsetDateTime,
    )
}

@JvmInline
value class PreDeliveryInspectionId(
    val value: UUID,
)

fun com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionId.toPreDeliveryInspectionId(): PreDeliveryInspectionId =
    PreDeliveryInspectionId(this.value)
