/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.domain

import com.fasterxml.jackson.annotation.JsonIgnore
import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateConstraintViolation
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.LastModifiedBy
import java.io.Serializable
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "pre_delivery_inspection", schema = "predelivery")
class PreDeliveryInspection(
    @Column(name = "vehicle_id") val vehicleId: UUID,
    tireSet: TireSet?,
    isRelevant: Boolean?,
    foiling: Boolean?,
    refuel: Boolean?,
    charge: Boolean?,
    digitalLogbook: Boolean?,
    licencePlateMounting: Boolean?,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: PreDeliveryInspectionId = PreDeliveryInspectionId()

    @Column(name = "tire_set")
    @Enumerated(EnumType.STRING)
    var tireSet: TireSet? = tireSet
        private set

    // FPT1-1190 defaults to false
    @Column(name = "is_relevant")
    var isRelevant: Boolean = isRelevant ?: false
        private set

    // FPT1-1190 defaults to false
    @Column(name = "foiling")
    var foiling: Boolean = foiling ?: false
        private set

    // FPT1-1190 defaults to false
    @Column(name = "refuel")
    var refuel: Boolean = refuel ?: false
        private set

    // FPT1-1190 defaults to false
    @Column(name = "charge")
    var charge: Boolean = charge ?: false
        private set

    // FPT1-1190 defaults to false
    @Column(name = "digital_logbook")
    var digitalLogbook: Boolean = digitalLogbook ?: false
        private set

    // FPT1-1190 defaults to false
    @Column(name = "licence_plate_mounting")
    var licencePlateMounting: Boolean = licencePlateMounting ?: false
        private set

    @Column(name = "ordered_date")
    var orderedDate: OffsetDateTime? = null
        private set

    @Column(name = "completed_date")
    var completedDate: OffsetDateTime? = null
        private set

    @Column(name = "comment")
    var comment: String? = null
        private set

    @Column(name = "planned_date")
    var plannedDate: OffsetDateTime? = null
        private set

    @CreatedBy
    @Column(name = "created_by")
    var createdBy: String = ""

    @CreationTimestamp
    @Column(name = "created_date")
    var created: OffsetDateTime = OffsetDateTime.now()

    @LastModifiedBy
    @Column(name = "last_modified_by")
    var lastModifiedBy: String = ""

    @UpdateTimestamp
    @Column(name = "last_modified_date")
    var lastModified: OffsetDateTime = OffsetDateTime.now()

    val preDeliveryInspectionCreatedEvent: PreDeliveryInspectionCreatedEvent
        @JsonIgnore
        get() =
            PreDeliveryInspectionCreatedEvent(
                preDeliveryInspectionId = this.id,
                occurredOn = OffsetDateTime.now(),
            )

    internal fun update(
        tireSet: Optional<TireSet>?,
        isRelevant: Optional<Boolean>?,
        foiling: Optional<Boolean>?,
        refuel: Optional<Boolean>?,
        charge: Optional<Boolean>?,
        digitalLogbook: Optional<Boolean>?,
        licencePlateMounting: Optional<Boolean>?,
        orderedDate: Optional<OffsetDateTime>?,
        completedDate: Optional<OffsetDateTime>?,
        plannedDate: Optional<OffsetDateTime>?,
        comment: Optional<String>?,
    ): PreDeliveryInspectionEvent {
        tireSet?.also { optional ->
            optional.ifPresentOrElse({ this.tireSet = it }, { this.tireSet = null })
        }
        isRelevant?.also { optional ->
            optional.ifPresent { this.isRelevant = it }
        }

        foiling?.also { optional ->
            optional.ifPresent { this.foiling = it }
        }

        refuel?.also { optional ->
            optional.ifPresent { this.refuel = it }
        }

        charge?.also { optional ->
            optional.ifPresent { this.charge = it }
        }

        digitalLogbook?.also { optional ->
            optional.ifPresent { this.digitalLogbook = it }
        }
        licencePlateMounting?.also { optional ->
            optional.ifPresent { this.licencePlateMounting = it }
        }
        plannedDate?.also { optional ->
            optional.ifPresentOrElse({ this.plannedDate = it }, { this.plannedDate = null })
        }
        orderedDate?.also { optional ->
            optional.ifPresentOrElse(
                {
                    requireDateNotInFuture(it, PreDeliveryInspection::orderedDate.name)
                    this.orderedDate = it
                },
                { this.orderedDate = null },
            )
        }
        completedDate?.also { optional ->
            optional.ifPresentOrElse(
                {
                    requireDateNotInFuture(it, PreDeliveryInspection::completedDate.name)
                    this.completedDate = it
                },
                { this.completedDate = null },
            )
        }
        comment?.also { optional ->
            optional.ifPresentOrElse({ this.comment = it }, { this.comment = null })
        }
        return PreDeliveryInspectionUpdatedEvent(
            preDeliveryInspectionId = this.id,
            occurredOn = OffsetDateTime.now(),
        )
    }

    internal fun updateOrderedDate(orderedDate: OffsetDateTime): PreDeliveryInspectionUpdatedEvent {
        requireDateNotInFuture(orderedDate, PreDeliveryInspection::orderedDate.name)
        this.orderedDate = orderedDate
        return PreDeliveryInspectionUpdatedEvent(
            preDeliveryInspectionId = this.id,
            occurredOn = OffsetDateTime.now(),
        )
    }

    private fun requireDateNotInFuture(
        date: OffsetDateTime,
        propertyName: String,
    ) {
        if (date.toLocalDate().isAfter(LocalDate.now())) {
            throw PreDeliveryInspectionFutureDateException(
                date = date,
                constraintViolation =
                    VehicleUpdateConstraintViolation(
                        vehicleId = this.vehicleId,
                        type = ViolationType.FUTURE_DATE_NOT_ALLOWED,
                        propertyName = propertyName,
                    ),
            )
        }
    }
}

@Embeddable
data class PreDeliveryInspectionId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable

/** Naming agreed with Luisa 2025-01-20 -> TireSet */
enum class TireSet(
    val description: String,
) {
    SR("summer wheels"),
    WR("winter wheels"),
}

interface PreDeliveryInspectionEvent : Serializable {
    val occurredOn: OffsetDateTime
    val id: UUID
    val preDeliveryInspectionId: PreDeliveryInspectionId
}

data class PreDeliveryInspectionCreatedEvent(
    override val preDeliveryInspectionId: PreDeliveryInspectionId,
    override val occurredOn: OffsetDateTime,
    override val id: UUID = UUID.randomUUID(),
) : PreDeliveryInspectionEvent

data class PreDeliveryInspectionUpdatedEvent(
    override val preDeliveryInspectionId: PreDeliveryInspectionId,
    override val occurredOn: OffsetDateTime = OffsetDateTime.now(),
    override val id: UUID = UUID.randomUUID(),
) : PreDeliveryInspectionEvent

data class PlannedPdiDateChangedEvent(
    val preDeliveryInspectionId: PreDeliveryInspectionId,
    val occurredOn: OffsetDateTime = OffsetDateTime.now(),
    val id: UUID = UUID.randomUUID(),
    val vehicleId: UUID,
    val plannedDate: OffsetDateTime? = null,
)
