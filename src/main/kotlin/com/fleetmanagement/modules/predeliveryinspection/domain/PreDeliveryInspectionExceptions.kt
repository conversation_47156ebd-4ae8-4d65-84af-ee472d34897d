/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.domain

import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateConstraintViolation
import java.time.OffsetDateTime
import java.util.UUID

class PreDeliveryInspectionNotFoundException(
    id: UUID,
) : NoSuchElementException("Pre delivery with key [$id] could not be found")

class PreDeliveryInspectionFutureDateException(
    val date: OffsetDateTime,
    val constraintViolation: VehicleUpdateConstraintViolation,
) : IllegalArgumentException(
        "Vehicle with id [$constraintViolation.vehicleId] has a date for [$constraintViolation.propertyName] not allowed in future: $date.",
        null,
    )
