/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.domain

import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.repository.Repository
import java.time.OffsetDateTime
import java.util.UUID

@org.springframework.stereotype.Repository
interface PreDeliveryInspectionRepository :
    Repository<PreDeliveryInspection, PreDeliveryInspectionId>,
    JpaSpecificationExecutor<PreDeliveryInspection> {
    fun save(preDeliveryInspection: PreDeliveryInspection): PreDeliveryInspection

    fun findById(id: PreDeliveryInspectionId): PreDeliveryInspection?

    fun findByVehicleId(vehicleId: UUID): PreDeliveryInspection?

    fun findByVehicleIdIn(vehicleId: List<UUID>): List<PreDeliveryInspection>

    fun findByPlannedDateBetween(
        startDate: OffsetDateTime,
        endDate: OffsetDateTime,
    ): List<PreDeliveryInspection>
}
