/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.domain

import jakarta.persistence.*
import jakarta.persistence.Entity
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.annotation.CreatedBy
import org.springframework.data.annotation.LastModifiedBy
import java.io.Serializable
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "winter_tires_timeframe", schema = "predelivery")
class WinterTiresTimeframe(
    fromDate: LocalDate,
    toDate: LocalDate,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: WinterTiresTimeframeId = WinterTiresTimeframeId()

    @Column(name = "from_date", nullable = false)
    var fromDate: LocalDate = fromDate
        private set

    @Column(name = "to_date", nullable = false)
    var toDate: LocalDate = toDate
        private set

    @Version
    val version: Int = 0

    @CreatedBy
    @Column(name = "created_by")
    var createdBy: String = ""

    @CreationTimestamp
    @Column(name = "created_date")
    var created: OffsetDateTime = OffsetDateTime.now()

    @LastModifiedBy
    @Column(name = "last_modified_by")
    var lastModifiedBy: String = ""

    @UpdateTimestamp
    @Column(name = "last_modified_date")
    var lastModified: OffsetDateTime = OffsetDateTime.now()

    internal fun update(
        fromDate: LocalDate,
        toDate: LocalDate,
    ) {
        this.fromDate = fromDate
        this.toDate = toDate
    }
}

@Embeddable
data class WinterTiresTimeframeId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable

class WinterTiresTimeframeUpdatedEvent(
    val winterTiresTimeframeId: WinterTiresTimeframeId,
    val occurredOn: OffsetDateTime,
    val id: UUID = UUID.randomUUID(),
)
