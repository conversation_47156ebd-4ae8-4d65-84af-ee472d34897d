/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.domain

import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.repository.Repository

interface WinterTiresTimeframeRepository :
    Repository<WinterTiresTimeframe, WinterTiresTimeframeId>,
    JpaSpecificationExecutor<WinterTiresTimeframe> {
    fun save(winterTiresTimeframe: WinterTiresTimeframe): WinterTiresTimeframe

    fun findById(id: WinterTiresTimeframeId): WinterTiresTimeframe?

    fun saveAndFlush(winterTiresTimeframe: WinterTiresTimeframe): WinterTiresTimeframe

    fun findAll(): List<WinterTiresTimeframe>
}
