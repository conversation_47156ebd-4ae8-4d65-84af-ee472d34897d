/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.domain.service

import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionRepository
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(propagation = Propagation.MANDATORY)
class PreDeliveryInspectionCreateService(
    private val preDeliveryInspectionRepository: PreDeliveryInspectionRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    fun createPreDeliveryInspection(preDeliveryInspectionToBeCreated: PreDeliveryInspection): PreDeliveryInspection {
        val preDeliveryInspection = preDeliveryInspectionRepository.save(preDeliveryInspectionToBeCreated)
        log.debug("Create pre-delivery inspection: {}", preDeliveryInspection)

        applicationEventPublisher.publishEvent(preDeliveryInspection.preDeliveryInspectionCreatedEvent)

        return preDeliveryInspection
    }

    companion object {
        private val log = LoggerFactory.getLogger(PreDeliveryInspectionCreateService::class.java)
    }
}
