/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.predeliveryinspection.domain.service

import com.fleetmanagement.emhshared.formatAsDotFormattedString
import com.fleetmanagement.emhshared.workingDaysBetween
import com.fleetmanagement.modules.predeliveryinspection.domain.PlannedPdiDateChangedEvent
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionId
import com.fleetmanagement.modules.predeliveryinspection.domain.TireSet
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.*
import kotlin.jvm.optionals.getOrNull

@Service
@Transactional(propagation = Propagation.MANDATORY)
class PreDeliveryInspectionUpdateService(
    private val applicationEventPublisher: ApplicationEventPublisher,
    // PDI lead time in working days
    @Value("\${pre-delivery-inspection.pdi-lead-time}") val pdiLeadTime: Int,
) {
    fun updatePreDeliveryInspection(
        preDeliveryInspectionUpdate: PreDeliveryInspectionUpdate,
        preDeliveryInspectionToBeUpdated: PreDeliveryInspection,
    ): List<PreDeliveryInspectionUpdateWarning> {
        // collect constraint violations, that should still update but be returned as warnings to the caller
        val warnings =
            listOfNotNull(
                checkForPdiLeadTimeViolation(
                    id = preDeliveryInspectionToBeUpdated.id,
                    preDeliveryInspectionUpdate = preDeliveryInspectionUpdate,
                ),
                checkIfPdiAlreadyOrdered(
                    preDeliveryInspectionToBeUpdated = preDeliveryInspectionToBeUpdated,
                    preDeliveryInspectionUpdate = preDeliveryInspectionUpdate,
                ),
            )

        val oldPlannedDate = preDeliveryInspectionToBeUpdated.plannedDate

        preDeliveryInspectionToBeUpdated
            .update(
                tireSet = preDeliveryInspectionUpdate.tireSet,
                isRelevant = preDeliveryInspectionUpdate.isRelevant,
                foiling = preDeliveryInspectionUpdate.foiling,
                refuel = preDeliveryInspectionUpdate.refuel,
                charge = preDeliveryInspectionUpdate.charge,
                digitalLogbook = preDeliveryInspectionUpdate.digitalLogbook,
                licencePlateMounting = preDeliveryInspectionUpdate.licencePlateMounting,
                orderedDate = preDeliveryInspectionUpdate.orderedDate,
                comment = preDeliveryInspectionUpdate.comment,
                completedDate = preDeliveryInspectionUpdate.completedDate,
                plannedDate = preDeliveryInspectionUpdate.plannedDate,
            ).also { updateEvent ->
                log.debug(
                    "Updated pre delivery inspection for vehicleId {}, with updates: {}",
                    preDeliveryInspectionToBeUpdated.vehicleId,
                    preDeliveryInspectionUpdate,
                )
                applicationEventPublisher.publishEvent(updateEvent)
            }

        if (oldPlannedDate != preDeliveryInspectionToBeUpdated.plannedDate) {
            applicationEventPublisher.publishEvent(
                PlannedPdiDateChangedEvent(
                    preDeliveryInspectionId = preDeliveryInspectionToBeUpdated.id,
                    occurredOn = OffsetDateTime.now(),
                    plannedDate = preDeliveryInspectionToBeUpdated.plannedDate,
                    vehicleId = preDeliveryInspectionToBeUpdated.vehicleId,
                ),
            )
        }

        return warnings
    }

    /**
     * Checks, if the time between the update (now) and the planned PDI date exceeds the configured [pdiLeadTime] (see FPT1-741).
     */
    private fun checkForPdiLeadTimeViolation(
        id: PreDeliveryInspectionId,
        preDeliveryInspectionUpdate: PreDeliveryInspectionUpdate,
    ): PreDeliveryInspectionUpdateWarning? {
        val plannedDate = preDeliveryInspectionUpdate.plannedDate?.getOrNull() ?: return null
        val today = OffsetDateTime.now()
        if (plannedDate.isBefore(today)) {
            return PreDeliveryInspectionUpdateWarning(
                id = id,
                type = PreDeliveryInspectionUpdateWarning.WarningType.PDI_INVALID_PLANNED_DATE,
                properties =
                    mapOf(
                        "plannedDate" to formatAsDotFormattedString(plannedDate),
                        "today" to formatAsDotFormattedString(today),
                    ),
            )
        }

        val workingDaysBetweenNowAndPlannedPdiDate = workingDaysBetween(OffsetDateTime.now(), plannedDate)

        /**
         * FPT1-962 PDIOrderingEmail will only be sent once at 0:05 (or later if retried). When setting a plannedPdiDate
         * at that day, the email should already be sent and a warning should be shown.
         */
        return if (pdiLeadTime >= workingDaysBetweenNowAndPlannedPdiDate) {
            return PreDeliveryInspectionUpdateWarning(
                id = id,
                type = PreDeliveryInspectionUpdateWarning.WarningType.PDI_LEAD_TIME_VIOLATION,
                properties =
                    mapOf(
                        "pdiLeadTime" to pdiLeadTime.toString(),
                        "workingDaysBetweenNowAndPlannedPdiDate" to workingDaysBetweenNowAndPlannedPdiDate.toString(),
                    ),
            )
        } else {
            null
        }
    }

    /**
     * Checks, if the current PreDeliveryInspection has already been ordered. Only return a warning, if we are trying to update planningDate.
     */
    private fun checkIfPdiAlreadyOrdered(
        preDeliveryInspectionUpdate: PreDeliveryInspectionUpdate,
        preDeliveryInspectionToBeUpdated: PreDeliveryInspection,
    ): PreDeliveryInspectionUpdateWarning? {
        // only continue check if plannedDate is to be updated
        if (null == preDeliveryInspectionUpdate.plannedDate) return null

        return if (null != preDeliveryInspectionToBeUpdated.orderedDate) {
            return PreDeliveryInspectionUpdateWarning(
                id = preDeliveryInspectionToBeUpdated.id,
                type = PreDeliveryInspectionUpdateWarning.WarningType.PDI_ALREADY_ORDERED,
                properties =
                    mapOf(
                        "orderedDate" to formatAsDotFormattedString(preDeliveryInspectionToBeUpdated.orderedDate!!),
                    ),
            )
        } else {
            null
        }
    }

    fun updateOrderedDate(
        preDeliveryInspection: PreDeliveryInspection,
        orderedDate: OffsetDateTime,
    ) {
        preDeliveryInspection.updateOrderedDate(orderedDate).also { updateEvent ->
            applicationEventPublisher.publishEvent(updateEvent)
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(PreDeliveryInspectionUpdateService::class.java)
    }
}

data class PreDeliveryInspectionUpdate(
    val tireSet: Optional<TireSet>?,
    val isRelevant: Optional<Boolean>?,
    val foiling: Optional<Boolean>?,
    val refuel: Optional<Boolean>?,
    val charge: Optional<Boolean>?,
    val digitalLogbook: Optional<Boolean>?,
    val licencePlateMounting: Optional<Boolean>?,
    val orderedDate: Optional<OffsetDateTime>?,
    val completedDate: Optional<OffsetDateTime>?,
    val plannedDate: Optional<OffsetDateTime>?,
    val comment: Optional<String>?,
)

data class PreDeliveryInspectionUpdateWarning(
    val id: PreDeliveryInspectionId,
    val type: WarningType,
    val properties: Map<String, String> = mapOf(),
) {
    enum class WarningType {
        PDI_LEAD_TIME_VIOLATION,
        PDI_ALREADY_ORDERED,
        PDI_INVALID_PLANNED_DATE,
    }
}
