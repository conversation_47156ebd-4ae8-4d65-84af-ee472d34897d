# RELA Module

## Purpose
This module integrates with the RELA tire service appointment system to create and cancel tire service appointments for fleet vehicles.

## Features
1. **Create Tire Service Appointments** - Schedule appointments for tire services
2. **Cancel Appointments** - Cancel existing appointments by order number
3. **Clean Business Interface** - Abstracts German API field names into English business DTOs
4. **Comprehensive Error Handling** - Handles various failure scenarios with specific exceptions

## Architecture

The module follows hexagonal architecture with clear separation of concerns:

```
rela/
├── adapter/out/rest/           # External API integration
│   ├── RelaWebClient.kt        # WebClient interface
│   ├── RelaClient.kt           # Port implementation
│   ├── RelaClientConfiguration.kt
│   └── RelaClientExceptionHandler.kt
├── application/                # Business logic
│   ├── port/                   # Business interfaces
│   │   ├── CreateRelaAppointment.kt
│   │   └── CancelRelaAppointment.kt
│   ├── RelaAppointmentRequest.kt
│   ├── RelaAppointmentResponse.kt
│   ├── RelaCancellationRequest.kt
│   └── RelaExceptions.kt
└── README.md
```

## Configuration

### Environment Variables
- `RELA_ENABLED` - Enable/disable the RELA module (default: false)
- `RELA_BASE_URL` - Base URL for RELA API (default: http://localhost:8080)
- `RELA_TIMEOUT_SECONDS` - Request timeout in seconds (default: 30)
- `RELA_RETRY_ATTEMPTS` - Number of retry attempts (default: 3)

### Application Properties
```yaml
rela:
  enabled: true
  base-url: https://rela-api.example.com
  timeout-seconds: 30
  retry-attempts: 3
```

## Usage

### Creating an Appointment
```kotlin
@Autowired
private lateinit var createRelaAppointment: CreateRelaAppointment

val request = RelaAppointmentRequest(
    appointmentDate = OffsetDateTime.now().plusDays(7),
    appointmentTime = OffsetDateTime.now().plusDays(7).withHour(10),
    vehicleLicensePlate = "S-PL 1234",
    vehicleVin = "WP0ZZZ999SS123456",
    customerLastName = "Müller",
    customerFirstName = "Hans",
    serviceBayNumber = 1,
    serviceTypeId = 2
)

val response = createRelaAppointment.createAppointment(request)
println("Created appointment with order number: ${response.orderNumber}")
```

### Cancelling an Appointment
```kotlin
@Autowired
private lateinit var cancelRelaAppointment: CancelRelaAppointment

val cancellationRequest = RelaCancellationRequest(
    cancelledBy = "Hans Müller",
    cancellationDate = LocalDate.now()
)

cancelRelaAppointment.cancelAppointment("***********", cancellationRequest)
```

## Error Handling

The module provides specific exceptions for different failure scenarios:
- `RelaAppointmentCreationException` - Appointment creation failed
- `RelaAppointmentCancellationException` - Appointment cancellation failed
- `RelaServiceUnavailableException` - RELA service is unreachable
- `RelaValidationException` - Request validation failed

## API Mapping

The module translates between English business DTOs and German RELA API fields:

| Business Field | RELA API Field | Description |
|----------------|----------------|-------------|
| appointmentDate | WerkstattterminDatum | Workshop appointment date |
| appointmentTime | WerkstattterminUhrzeit | Workshop appointment time |
| vehicleLicensePlate | KFZKennzeichen | Vehicle license plate |
| vehicleVin | FIN | Vehicle identification number |
| customerLastName | Name | Customer last name |
| customerFirstName | Vorname | Customer first name |

## Testing

To test the module locally:
1. Set `rela.enabled=true` in your test configuration
2. Use a mock RELA service or WireMock for integration tests
3. The module is disabled by default in test environments

## Future Enhancements
- Circuit breaker pattern for resilience
- Metrics and monitoring integration
- Batch appointment operations
- Appointment status checking
