package com.fleetmanagement.modules.rela.adapter.out.rest

import com.fleetmanagement.modules.rela.adapter.out.rest.model.CancelAppointmentRequestDto
import com.fleetmanagement.modules.rela.adapter.out.rest.model.CreateAppointmentRequestDto
import com.fleetmanagement.modules.rela.application.*
import com.fleetmanagement.modules.rela.application.port.CancelRelaAppointment
import com.fleetmanagement.modules.rela.application.port.CreateRelaAppointment
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.format.DateTimeFormatter

/**
 * Implementation of RELA appointment ports using the RELA WebClient.
 * 
 * This class implements the business ports and handles the translation between
 * business DTOs and external API DTOs, following the hexagonal architecture pattern.
 */
@Component
@ConditionalOnBean(RelaConfiguration::class)
class RelaClient(
    private val relaWebClient: RelaWebClient
) : CreateRelaAppointment, CancelRelaAppointment {
    
    companion object {
        private val log = LoggerFactory.getLogger(RelaClient::class.java)
        private val DATE_FORMATTER = DateTimeFormatter.ISO_OFFSET_DATE_TIME
    }
    
    override fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse {
        log.info("Creating RELA appointment for VIN: ${request.vehicleVin}")
        
        try {
            val createRequest = mapToCreateAppointmentDto(request)
            val response = relaWebClient.createAppointment(createRequest)
            
            log.info("Successfully created RELA appointment with order number: ${response.auftragsnummer}")
            return RelaAppointmentResponse(orderNumber = response.auftragsnummer)
            
        } catch (e: Exception) {
            log.error("Failed to create RELA appointment for VIN: ${request.vehicleVin}", e)
            throw RelaAppointmentCreationException(
                "Failed to create RELA appointment for VIN: ${request.vehicleVin}",
                e
            )
        }
    }
    
    override fun cancelAppointment(orderNumber: String, request: RelaCancellationRequest) {
        log.info("Cancelling RELA appointment with order number: $orderNumber")
        
        try {
            val cancelRequest = mapToCancelAppointmentDto(request)
            relaWebClient.cancelAppointment(orderNumber, cancelRequest)
            
            log.info("Successfully cancelled RELA appointment with order number: $orderNumber")
            
        } catch (e: Exception) {
            log.error("Failed to cancel RELA appointment with order number: $orderNumber", e)
            throw RelaAppointmentCancellationException(
                "Failed to cancel RELA appointment with order number: $orderNumber",
                e
            )
        }
    }
    
    private fun mapToCreateAppointmentDto(request: RelaAppointmentRequest): CreateAppointmentRequestDto {
        return CreateAppointmentRequestDto(
            werkstattterminDatum = request.appointmentDate,
            werkstattterminUhrzeit = request.appointmentTime,
            kfZKennzeichen = request.vehicleLicensePlate,
            FIN = request.vehicleVin,
            name = request.customerLastName,
            vorname = request.customerFirstName,
            buehne = request.serviceBayNumber,
            dienstleistung = request.serviceTypeId,
            typCode = request.vehicleTypeCode,
            typBeschreibung = request.vehicleTypeDescription,
            pcCBCode = request.pccbCode,
            wheelCode = request.wheelCode,
            rdKCode = request.rdkCode,
            raSCode = request.rasCode,
            pcCBBeschreibung = request.pccbDescription,
            reifenbeschreibungDemontage = request.demountingTireDescription,
            ventilbeschreibungDemontage = request.demountingValveDescription,
            reifenbeschreibungMontage = request.mountingTireDescription,
            ventilbeschreibungMontage = request.mountingValveDescription,
            bestelltVon = request.orderedByEmail,
            bestelltAmDatum = request.orderDate,
            bestelltAmUhrzeit = request.orderTime
        )
    }
    
    private fun mapToCancelAppointmentDto(request: RelaCancellationRequest): CancelAppointmentRequestDto {
        return CancelAppointmentRequestDto(
            storniertVon = request.cancelledBy,
            storniertAm = request.cancellationDate
        )
    }
}
