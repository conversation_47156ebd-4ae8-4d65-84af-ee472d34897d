package com.fleetmanagement.modules.rela.adapter.out.rest

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.MediaType
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.support.WebClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory

/**
 * Configuration marker class for RELA module.
 * This class enables the RELA module when the feature flag is enabled.
 */
@Configuration
@ConditionalOnProperty(name = ["rela.enabled"], havingValue = "true")
class RelaConfiguration

/**
 * Configuration for RELA WebClient and related beans.
 * 
 * This configuration sets up the WebClient for communicating with the RELA API,
 * including error handling and HTTP service proxy factory.
 */
@Configuration
@ConditionalOnBean(RelaConfiguration::class)
class RelaClientConfiguration(
    private val relaProperties: RelaProperties
) {
    
    @Bean
    @Qualifier("rela")
    fun relaServiceProxyFactory(
        @Value("\${rela.base-url}") baseUrl: String,
        relaClientExceptionHandler: RelaClientExceptionHandler
    ): HttpServiceProxyFactory {
        val webClient = WebClient
            .builder()
            .baseUrl(baseUrl)
            .defaultHeader("Content-Type", MediaType.APPLICATION_JSON_VALUE)
            .defaultHeader("Accept", MediaType.APPLICATION_JSON_VALUE)
            .filter(ExchangeFilterFunction.ofResponseProcessor(relaClientExceptionHandler::clientErrorResponseProcessor))
            .filter(relaClientExceptionHandler::clientErrorRequestProcessor)
            .build()
        
        val adapter = WebClientAdapter.create(webClient)
        return HttpServiceProxyFactory.builderFor(adapter).build()
    }
    
    @Bean
    @Qualifier("rela")
    fun getRelaWebClient(
        @Qualifier("rela") relaServiceProxyFactory: HttpServiceProxyFactory
    ): RelaWebClient = relaServiceProxyFactory.createClient(RelaWebClient::class.java)
}

/**
 * Configuration properties for RELA integration.
 * 
 * These properties are loaded from application.yml under the 'rela' key.
 */
@ConfigurationProperties("rela")
data class RelaProperties(
    val enabled: Boolean = false,
    val baseUrl: String,
    val timeoutSeconds: Long = 30,
    val retryAttempts: Int = 3
)
