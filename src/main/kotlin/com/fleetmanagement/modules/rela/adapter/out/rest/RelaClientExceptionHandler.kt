package com.fleetmanagement.modules.rela.adapter.out.rest

import com.fleetmanagement.modules.rela.application.RelaServiceUnavailableException
import com.fleetmanagement.modules.rela.application.RelaValidationException
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.ExchangeFunction
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono
import java.net.ConnectException

/**
 * Exception handler for RELA WebClient operations.
 * 
 * This class handles various types of errors that can occur during communication
 * with the RELA API and translates them into appropriate business exceptions.
 */
@Component
class RelaClientExceptionHandler {
    
    companion object {
        private val log = LoggerFactory.getLogger(RelaClientExceptionHandler::class.java)
    }
    
    /**
     * Handles request-level errors (connection issues, etc.)
     */
    fun clientErrorRequestProcessor(
        request: ClientRequest,
        next: ExchangeFunction
    ): Mono<ClientResponse> =
        next.exchange(request).onErrorMap { throwable ->
            when (throwable.cause) {
                is ConnectException -> {
                    log.error("Error during RELA request. Target not reachable.", throwable.cause)
                    RelaServiceUnavailableException("RELA service not reachable", throwable.cause)
                }
                else -> {
                    log.error("Unexpected error during RELA request", throwable)
                    RelaServiceUnavailableException("Unexpected error during RELA request", throwable)
                }
            }
        }
    
    /**
     * Handles response-level errors (HTTP status codes, etc.)
     */
    fun clientErrorResponseProcessor(response: ClientResponse): Mono<ClientResponse> {
        return if (response.statusCode().isError) {
            response.bodyToMono(String::class.java)
                .defaultIfEmpty("No response body")
                .flatMap { body ->
                    val exception = when (response.statusCode()) {
                        HttpStatus.BAD_REQUEST -> {
                            log.error("RELA API returned 400 Bad Request: $body")
                            RelaValidationException("Invalid request to RELA API: $body")
                        }
                        HttpStatus.NOT_ACCEPTABLE -> {
                            log.error("RELA API returned 406 Not Acceptable: $body")
                            RelaValidationException("RELA API rejected request (406): $body")
                        }
                        HttpStatus.INTERNAL_SERVER_ERROR -> {
                            log.error("RELA API returned 500 Internal Server Error: $body")
                            RelaServiceUnavailableException("RELA service internal error: $body")
                        }
                        HttpStatus.SERVICE_UNAVAILABLE -> {
                            log.error("RELA API returned 503 Service Unavailable: $body")
                            RelaServiceUnavailableException("RELA service unavailable: $body")
                        }
                        else -> {
                            log.error("RELA API returned error ${response.statusCode()}: $body")
                            RelaServiceUnavailableException("RELA API error ${response.statusCode()}: $body")
                        }
                    }
                    Mono.error<ClientResponse>(exception)
                }
        } else {
            Mono.just(response)
        }
    }
}
