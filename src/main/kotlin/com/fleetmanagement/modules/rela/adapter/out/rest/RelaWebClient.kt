package com.fleetmanagement.modules.rela.adapter.out.rest

import com.fleetmanagement.modules.rela.adapter.out.rest.model.CancelAppointmentRequestDto
import com.fleetmanagement.modules.rela.adapter.out.rest.model.CreateAppointmentRequestDto
import com.fleetmanagement.modules.rela.adapter.out.rest.model.CreateAppointmentResponseDto
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.service.annotation.PostExchange

/**
 * WebClient interface for RELA tire service appointments.
 * 
 * This interface wraps the generated AppointmentsApi to provide a cleaner
 * Spring WebClient-based interface that integrates better with our architecture.
 */
interface RelaWebClient {
    
    @PostExchange(
        url = "/rela-rest-ws/luckyneutron/appointments",
        accept = ["application/json"]
    )
    fun createAppointment(
        @RequestBody request: CreateAppointmentRequestDto
    ): CreateAppointmentResponseDto
    
    @PostExchange(
        url = "/rela-rest-ws/luckyneutron/appointments/{orderNumber}/cancelation",
        accept = ["application/json"]
    )
    fun cancelAppointment(
        @PathVariable("orderNumber") orderNumber: String,
        @RequestBody request: CancelAppointmentRequestDto
    )
}
