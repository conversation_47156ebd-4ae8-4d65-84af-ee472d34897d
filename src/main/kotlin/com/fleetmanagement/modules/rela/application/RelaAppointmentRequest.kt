package com.fleetmanagement.modules.rela.application

import java.time.OffsetDateTime

/**
 * Business DTO for creating a RELA tire service appointment.
 * 
 * This represents the business model for appointment creation, abstracting away
 * the external API's German field names and providing a clean English interface.
 */
data class RelaAppointmentRequest(
    // Required fields
    val appointmentDate: OffsetDateTime,
    val appointmentTime: OffsetDateTime,
    val vehicleLicensePlate: String,
    val vehicleVin: String,
    val customerLastName: String,
    val customerFirstName: String,
    
    // Optional service details
    val serviceBayNumber: Int? = null,
    val serviceTypeId: Int? = null,
    
    // Optional vehicle details
    val vehicleTypeCode: String? = null,
    val vehicleTypeDescription: String? = null,
    val pccbCode: String? = null,
    val wheelCode: String? = null,
    val rdkCode: String? = null,
    val rasCode: String? = null,
    val pccbDescription: String? = null,
    
    // Optional tire/service descriptions
    val demountingTireDescription: String? = null,
    val demountingValveDescription: String? = null,
    val mountingTireDescription: String? = null,
    val mountingValveDescription: String? = null,
    
    // Optional order details
    val orderedByEmail: String? = null,
    val orderDate: OffsetDateTime? = null,
    val orderTime: OffsetDateTime? = null
)
