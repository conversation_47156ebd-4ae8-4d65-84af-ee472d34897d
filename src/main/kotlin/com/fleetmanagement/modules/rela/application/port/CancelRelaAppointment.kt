package com.fleetmanagement.modules.rela.application.port

import com.fleetmanagement.modules.rela.application.RelaCancellationRequest
import com.fleetmanagement.modules.rela.application.RelaAppointmentCancellationException

/**
 * Port interface for cancelling RELA tire service appointments.
 * 
 * This interface defines the contract for cancelling appointments in the RELA system.
 * It abstracts away the external API details and provides a clean business interface.
 */
fun interface CancelRelaAppointment {
    /**
     * Cancels an existing tire service appointment in the RELA system.
     * 
     * @param orderNumber The order number of the appointment to cancel
     * @param request The cancellation request containing cancellation details
     * @throws RelaAppointmentCancellationException if the appointment cancellation fails
     */
    fun cancelAppointment(orderNumber: String, request: RelaCancellationRequest)
}
