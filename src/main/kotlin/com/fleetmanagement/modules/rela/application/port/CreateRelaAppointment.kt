package com.fleetmanagement.modules.rela.application.port

import com.fleetmanagement.modules.rela.application.RelaAppointmentCreationException
import com.fleetmanagement.modules.rela.application.RelaAppointmentRequest
import com.fleetmanagement.modules.rela.application.RelaAppointmentResponse

/**
 * Port interface for creating RELA tire service appointments.
 * 
 * This interface defines the contract for creating appointments in the RELA system.
 * It abstracts away the external API details and provides a clean business interface.
 */
fun interface CreateRelaAppointment {
    /**
     * Creates a new tire service appointment in the RELA system.
     * 
     * @param request The appointment request containing all necessary details
     * @return The response containing the order number
     * @throws RelaAppointmentCreationException if the appointment creation fails
     */
    fun createAppointment(request: RelaAppointmentRequest): RelaAppointmentResponse
}
