package com.fleetmanagement.modules.tiremanagement

import com.fleetmanagement.modules.tiremanagement.features.dataexport.TireManagementDataExportConfiguration
import com.fleetmanagement.modules.tiremanagement.job.TireManagementEmailJobConfiguration
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import

/**
 * Central module-level configuration.
 *
 * This class serves as the single entry point for managing
 * dependency injection within the module.
 *
 * It is responsible for loading more specific configuration
 * classes and should be controlled via a module-level feature toggle.
 */

@Configuration
@ConditionalOnProperty("tire-management.enabled", havingValue = "true")
@Import(
    TireManagementEmailJobConfiguration::class,
    TireManagementDataExportConfiguration::class,
)
class TireManagementConfiguration
