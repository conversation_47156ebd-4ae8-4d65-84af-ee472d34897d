package com.fleetmanagement.modules.tiremanagement.features.dataexport

import com.aspose.email.SmtpClient
import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.ReadCostCenterUseCase
import com.fleetmanagement.modules.tiremanagement.TireManagementConfigurationProperties
import com.fleetmanagement.modules.tiremanagement.features.dataexport.mailclient.TireManagementDataExportMailClient
import com.fleetmanagement.modules.tiremanagement.features.dataexport.pipeline.TireManagementDataExportPipeline
import com.fleetmanagement.modules.tiremanagement.features.dataexport.pipeline.TireManagementDataExportVehicleFilter
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import org.springframework.context.annotation.Bean

/**
 * Central configuration for all dependency injection related to the data-export feature.
 *
 * This class serves as the single source for wiring beans and dependencies
 * used within the data-export feature.
 *
 */

class TireManagementDataExportConfiguration {
    @Bean("dataExportService")
    fun dataExportService(
        properties: TireManagementConfigurationProperties,
        dataExportPipeline: TireManagementDataExportPipeline,
        smtpClient: SmtpClient,
    ) = TireManagementDataExportService(
        TireManagementDataExportMailClient(
            smtpClient,
            properties.email,
        ),
        dataExportPipeline,
    )

    @Bean
    fun dataExportPipeline(
        readPlannedVehicleTransferUseCase: ReadPlannedVehicleTransferUseCase,
        readVehicleTransferUseCase: ReadVehicleTransferUseCase,
        readRegistrationOrder: ReadRegistrationOrder,
        readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber,
        readVehicleByVehicleId: ReadVehicleByVehicleId,
        readCostCenterUseCase: ReadCostCenterUseCase,
    ) = TireManagementDataExportPipeline(
        TireManagementDataExportVehicleFilter(
            readPlannedVehicleTransferUseCase,
            readVehicleTransferUseCase,
            readRegistrationOrder,
        ),
        readVehiclePersonDetailByEmployeeNumber,
        readVehicleByVehicleId,
        readCostCenterUseCase,
    )

    /*
     * Separated out for integration-tests
     */
    @Bean("smtpClient")
    fun smtpClient(properties: TireManagementConfigurationProperties): SmtpClient =
        SmtpClient(
            properties.email.smtpHost,
            properties.email.smtpPort,
            properties.email.smtpUsername,
            properties.email.smtpPassword,
        )
}
