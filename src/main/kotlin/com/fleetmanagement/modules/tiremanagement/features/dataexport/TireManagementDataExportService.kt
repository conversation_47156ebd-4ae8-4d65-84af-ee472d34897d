package com.fleetmanagement.modules.tiremanagement.features.dataexport

import com.fleetmanagement.modules.tiremanagement.features.dataexport.mailclient.TireManagementDataExportMailClient
import com.fleetmanagement.modules.tiremanagement.features.dataexport.pipeline.TireManagementDataExportPipeline

class TireManagementDataExportService(
    private val mailClient: TireManagementDataExportMailClient,
    private val dataExportPipeline: TireManagementDataExportPipeline,
) {
    fun createAndSendDataExport() {
        val dataExportCSV = dataExportPipeline.execute()
        mailClient.sendEmail(dataExportCSV)
    }
}
