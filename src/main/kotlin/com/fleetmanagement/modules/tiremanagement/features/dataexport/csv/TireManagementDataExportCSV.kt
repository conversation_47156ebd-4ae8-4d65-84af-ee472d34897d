package com.fleetmanagement.modules.tiremanagement.features.dataexport.csv

import com.opencsv.CSVWriter
import java.io.ByteArrayInputStream
import java.io.InputStream
import java.io.StringWriter
import java.nio.charset.StandardCharsets

class TireManagementDataExportCSV {
    private val rows: MutableList<TireManagementDataExportCSVRow> = mutableListOf()

    fun addRow(row: TireManagementDataExportCSVRow) {
        rows.add(row)
    }

    fun inputStream(): InputStream {
        val stringWriter = StringWriter()
        CSVWriter(
            stringWriter,
            ';',
            '"',
            '\\',
            "\n",
        ).use { csvWriter ->
            rows.forEach { row ->
                csvWriter.writeNext(
                    row.toTypedArray(),
                )
            }
        }
        val csvContent = stringWriter.toString().trim()
        val inputStream = ByteArrayInputStream(csvContent.toByteArray(StandardCharsets.UTF_8))
        return inputStream
    }

    fun rowCount(): Int = rows.size

    fun extract(predicate: (TireManagementDataExportCSVRow) -> Boolean): TireManagementDataExportCSVRow? = rows.firstOrNull(predicate)
}
