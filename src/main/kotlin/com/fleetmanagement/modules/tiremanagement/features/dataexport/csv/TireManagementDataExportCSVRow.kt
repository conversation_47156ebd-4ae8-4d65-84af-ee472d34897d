package com.fleetmanagement.modules.tiremanagement.features.dataexport.csv

data class TireManagementDataExportCSVRow(
    val vin: String,
    val licencePlate: String,
    val personLastName: String,
    val personFirstName: String,
    val personBusinessEmail: String,
    val personPrivateEmail: String,
    val personDepartment: String,
    val costCenterCarrier: String,
    val vehicleDataOrderType: String,
    val vehicleDataModel: String,
    val optionCodeRims: String,
    val optionDescriptionRims: String,
    val optionCodeCentralLocking: String,
    val optionDescriptionCentralLocking: String,
    val optionCodeBrakingDiscsFront: String,
    val optionDescriptionBrakingDiscsFront: String,
) {
    fun toTypedArray(): Array<String> =
        listOf(
            vin,
            licencePlate,
            personLastName,
            personFirstName,
            personBusinessEmail,
            personPrivateEmail,
            personDepartment,
            costCenterCarrier,
            vehicleDataOrderType,
            vehicleDataModel,
            optionCodeRims,
            optionDescriptionRims,
            optionCodeCentralLocking,
            optionDescriptionCentralLocking,
            optionCodeBrakingDiscsFront,
            optionDescriptionBrakingDiscsFront,
        ).toTypedArray()
}
