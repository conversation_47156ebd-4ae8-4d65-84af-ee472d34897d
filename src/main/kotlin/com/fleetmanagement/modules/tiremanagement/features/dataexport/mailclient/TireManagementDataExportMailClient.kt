package com.fleetmanagement.modules.tiremanagement.features.dataexport.mailclient

import com.aspose.email.Attachment
import com.aspose.email.MailAddress
import com.aspose.email.MailMessage
import com.aspose.email.SmtpClient
import com.fleetmanagement.modules.tiremanagement.TireManagementEmailConfigurationProperties
import com.fleetmanagement.modules.tiremanagement.features.dataexport.csv.TireManagementDataExportCSV
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

class TireManagementDataExportMailClient(
    private val smtpClient: SmtpClient,
    private val configuration: TireManagementEmailConfigurationProperties,
) {
    fun sendEmail(exportCSV: TireManagementDataExportCSV) {
        val inputStream = exportCSV.inputStream()
        val attachment = Attachment(inputStream, "${utcTimestampForFilename()}_Stammdaten_Porsche_VIW.csv")

        smtpClient.send(prepareMailMessageFrom(attachment))
    }

    private fun prepareMailMessageFrom(csvFile: Attachment): MailMessage =
        MailMessage().apply {
            subject = "#crypt_*${csvFile.name}"
            htmlBody = "Anbei erhalten Sie den Export der Daten für das Reifenmanagement."
            setFrom(MailAddress(configuration.sender))
            to.addMailAddress(MailAddress(configuration.recipient))
            attachments.addItem(csvFile)
        }
}

private fun utcTimestampForFilename(): String {
    val formatter = DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")
    val utcNow = ZonedDateTime.now(ZoneOffset.UTC)
    return utcNow.format(formatter)
}
