package com.fleetmanagement.modules.tiremanagement.features.dataexport.pipeline

import com.fleetmanagement.modules.consigneedatasheet.application.port.costcenter.ReadCostCenterUseCase
import com.fleetmanagement.modules.tiremanagement.features.dataexport.csv.TireManagementDataExportCSV
import com.fleetmanagement.modules.tiremanagement.features.dataexport.csv.TireManagementDataExportCSVRow
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber

class TireManagementDataExportPipeline(
    private val vehiclesFilter: TireManagementDataExportVehicleFilter,
    private val readVehiclePersonDetail: ReadVehiclePersonDetailByEmployeeNumber,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val readCostCenterUseCase: ReadCostCenterUseCase,
) {
    fun execute(): TireManagementDataExportCSV {
        val filteredVehicles = vehiclesFilter.filterVehiclesForDataExport()

        if (filteredVehicles.isEmpty()) {
            return TireManagementDataExportCSV()
        }

        val csv = TireManagementDataExportCSV()
        val costCenterMap = readCostCenterUseCase.readAllCostCenter().associate { it.id to it.description }
        filteredVehicles.forEach { filteredVehicle ->
            val vehicleData = readVehicleByVehicleId.readVehicleById(filteredVehicle.vehicleId)
            val personData =
                filteredVehicle.employeeNumber?.value?.let {
                    readVehiclePersonDetail.readVehiclePersonDetailByEmployeeNumber(it)
                }
            val vehicleOptionRims = vehicleData?.optionCodeForRims()
            val vehicleOptionCentralLocking = vehicleData?.optionCodeForCentralLocking()
            val vehicleOptionBrakingDiscsFront = vehicleData?.optionCodeForBrakingDiscsFront()
            val costCenterDescription =
                filteredVehicle.depreciationRelevantCostCenterId
                    ?.let { costCenterMap[it] }
                    .orEmpty()

            csv.addRow(
                TireManagementDataExportCSVRow(
                    vin = vehicleData?.vin.orEmpty(),
                    licencePlate = filteredVehicle.licenseNumber,
                    personLastName = personData?.lastName.orEmpty(),
                    personFirstName = personData?.firstName.orEmpty(),
                    personBusinessEmail = personData?.companyEmail.orEmpty(),
                    personPrivateEmail = personData?.privateEmail.orEmpty(),
                    personDepartment = personData?.department.orEmpty(),
                    costCenterCarrier = costCenterDescription,
                    vehicleDataOrderType = vehicleData?.model?.orderType.orEmpty(),
                    vehicleDataModel = vehicleData?.model?.description.orEmpty(),
                    optionCodeRims = vehicleOptionRims?.optionCode.orEmpty(),
                    optionDescriptionRims = vehicleOptionRims?.optionDescription.orEmpty(),
                    optionCodeBrakingDiscsFront = vehicleOptionBrakingDiscsFront?.optionCode.orEmpty(),
                    optionDescriptionBrakingDiscsFront = vehicleOptionBrakingDiscsFront?.optionDescription.orEmpty(),
                    optionCodeCentralLocking = vehicleOptionCentralLocking?.optionCode.orEmpty(),
                    optionDescriptionCentralLocking = vehicleOptionCentralLocking?.optionDescription.orEmpty(),
                ),
            )
        }
        return csv
    }
}
