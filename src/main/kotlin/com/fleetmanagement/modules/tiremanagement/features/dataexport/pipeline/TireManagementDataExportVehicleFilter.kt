package com.fleetmanagement.modules.tiremanagement.features.dataexport.pipeline

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadPlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.entities.AbstractVehicleTransferEntity
import com.fleetmanagement.modules.vehicletransfer.domain.entities.PlannedVehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import org.slf4j.LoggerFactory
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable.unpaged
import java.util.UUID

class TireManagementDataExportVehicleFilter(
    private val readPlannedVehicleTransferUseCase: ReadPlannedVehicleTransferUseCase,
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase,
    private val readRegistrationOrder: ReadRegistrationOrder,
) {
    private val logger = LoggerFactory.getLogger(TireManagementDataExportVehicleFilter::class.java)

    fun filterVehiclesForDataExport(): List<TireManagementDataExportFilteredVehicle> {
        logger.info("Starting to filter vehicles for data-export")

        val activeTransfers =
            readVehicleTransferUseCase
                .findAllTransfersForUtilizationAreaZuffenhausenAndLeipzig(
                    unpaged(),
                ).toList()
        logger.info("Retrieved ${activeTransfers.size} active transfers")

        val plannedTransfers = mutableListOf<PlannedVehicleTransfer>()
        fetchAllPlannedTransfersSortByPlannedDeliveryDate(plannedTransfers)
        logger.info("Retrieved ${plannedTransfers.size} planned transfers")

        val vehicleWithTransferMap = combineAndPickExactlyOneTransferForVehicle(activeTransfers, plannedTransfers)

        if (vehicleWithTransferMap.keys.isEmpty()) {
            logger.info("No vehicle-transfer fit the criteria for Data-Export CSV, quitting")
            return emptyList()
        }

        val (data, errors) =
            readRegistrationOrder.getLatestOrdersBy(vehicleWithTransferMap.keys.toList())
        if (!errors.isNullOrEmpty()) {
            logger.error(
                "Send export to RELAS Failed.Could not retrieve active-vehicle registrations {}",
                errors,
            )
            throw RuntimeException(
                "Cannot create TireManagementDataCSVExport: ${errors.joinToString(",")}",
            )
        }

        logger.info("Filtering of vehicles for Data-Export CSV completed")
        return data
            .filter { it.licencePlate != null }
            .map {
                checkNotNull(it.licencePlate)
                TireManagementDataExportFilteredVehicle(
                    vehicleId = it.vehicleId,
                    licenseNumber = it.licencePlate,
                    employeeNumber = getEmployeeNumberFrom(vehicleWithTransferMap[it.vehicleId]),
                    depreciationRelevantCostCenterId = getCostCenterId(vehicleWithTransferMap[it.vehicleId]),
                )
            }
    }

    private fun fetchAllPlannedTransfersSortByPlannedDeliveryDate(plannedTransfers: MutableList<PlannedVehicleTransfer>) {
        var pageNumber = 0
        var page: Page<PlannedVehicleTransfer>

        do {
            page =
                readPlannedVehicleTransferUseCase.findAllPlannedTransfersForUtilizationAreaZuffenhausenAndLeipzig(
                    PageRequest.of(pageNumber, PAGE_SIZE),
                )
            plannedTransfers.addAll(page.content)
            pageNumber++
        } while (page.hasNext())
    }

    private fun combineAndPickExactlyOneTransferForVehicle(
        activeTransfers: List<VehicleTransfer>,
        plannedTransfers: List<PlannedVehicleTransfer>,
    ): Map<UUID, AbstractVehicleTransferEntity> {
        val activeTransfersByVehicle = activeTransfers.associateBy { it.vehicleId }

        val filteredPlannedTransfers =
            plannedTransfers
                .filter { it.vehicleId !in activeTransfersByVehicle.keys }
                .groupBy { it.vehicleId }
                .mapNotNull { (_, transfers) ->
                    // Since plannedTransfers are sorted by deliveryDate ascending pick the first transfer per vehicle
                    transfers.firstOrNull()
                }

        return (activeTransfers + filteredPlannedTransfers).associateBy { it.vehicleId }
    }

    private fun getCostCenterId(transfer: AbstractVehicleTransferEntity?) = transfer?.depreciationRelevantCostCenterId?.value

    private fun getEmployeeNumberFrom(transfer: AbstractVehicleTransferEntity?): EmployeeNumber? {
        val person = transfer?.internalContactPersonOrVehicleResponsiblePerson()
        if (person == null) {
            logger.warn("No internal contact person or vehicle-responsible person set on vehicle-transfer")
            return null
        }
        return person
    }

    private fun AbstractVehicleTransferEntity.internalContactPersonOrVehicleResponsiblePerson(): EmployeeNumber? =
        internalContactPerson ?: vehicleResponsiblePerson

    companion object {
        const val PAGE_SIZE = 100
    }
}
