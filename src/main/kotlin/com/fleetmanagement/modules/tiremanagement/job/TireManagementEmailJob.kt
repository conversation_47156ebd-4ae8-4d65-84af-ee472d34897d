/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.tiremanagement.job

import com.fleetmanagement.modules.tiremanagement.features.dataexport.TireManagementDataExportService
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory

@DisallowConcurrentExecution
class TireManagementEmailJob(
    private val exportService: TireManagementDataExportService,
) : Job {
    override fun execute(p0: JobExecutionContext?) {
        log.info("Starting scheduled TireManagementEmailJob Job.")
        exportService.createAndSendDataExport()
        log.info("Finished scheduled TireManagementEmailJob Job.")
    }

    companion object {
        private val log = LoggerFactory.getLogger(TireManagementEmailJob::class.java)
    }
}
