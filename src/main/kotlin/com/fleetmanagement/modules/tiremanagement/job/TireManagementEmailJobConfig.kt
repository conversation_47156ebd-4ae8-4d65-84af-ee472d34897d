package com.fleetmanagement.modules.tiremanagement.job

import com.fleetmanagement.jobmanagement.configuration.QuartzJobManager
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobKey
import org.quartz.Scheduler
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.beans.factory.ObjectProvider
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.TimeZone

class TireManagementEmailJobConfiguration {
    @Bean("tireManagementEmailJobDetail")
    fun tireManagementEmailJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(TireManagementEmailJob::class.java)
            .withIdentity("TireManagementEmailJob")
            .withDescription("Tire-Management Email Job")
            .storeDurably()
            .build()

    @Bean("tireManagementEmailJobTrigger")
    fun tireManagementEmailJobTrigger(
        @Qualifier("tireManagementEmailJobDetail") tireManagementEmailJobDetail: JobDetail,
        @Value("\${tire-management.data-export.cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(tireManagementEmailJobDetail)
            .withIdentity("TireManagementEmailJobTrigger")
            .withDescription("Tire-Management Email Job Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}

@Configuration
@ConditionalOnMissingBean(TireManagementEmailJobConfiguration::class)
class TireManagementEmailDisabledJobConfiguration {
    @Bean("tireManagementEmailJob")
    fun pauseQuartzJob(schedulerProvider: ObjectProvider<Scheduler>): QuartzJobManager =
        QuartzJobManager(
            jobKey = JobKey("TireManagementEmailJob"),
            schedulerProvider = schedulerProvider,
            isJobEnabled = false,
        )
}
