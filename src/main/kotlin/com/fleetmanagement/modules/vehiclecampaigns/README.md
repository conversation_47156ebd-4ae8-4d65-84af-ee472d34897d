# Vehicle Campaigns 

## Purpose 
This is an integration module that was started for the following requirements

1. Sync Aktion/Campaign information associated for vehicles in Fleet-Vehicle-Manager 
2. Provide a module level API for other domains within Fleet-Vehicle-Manager to consume this information

## Integration with DMSI 

Please refer to the HTTP client file `vehicle-actions.http` for the DMSI Sync API 

### Architecture 

![High Level Overview](./docs/vehicle-campaigns-high-level-overview.drawio.svg)

### Architecture Decisions 

#### Ability to move to an Event(s)-Based Trigger

While DMSI-Service integration is not a "just-in-time" integration, there could be a time when we would need a more just in time integration.

At that time, we should be able to replace the scheduled jobs with event-listeners. 

The filtering and syncing with DMSI has also been split out to create a very 'generic' solution for syncing information with external systems.

#### Use of a specific cron schedule to avoid overrunning our rate-limits 

The sync-job can be scheduled in such a manner that we do not over-burden the DMSI service and also we do not exhaust our rate-limit

