package com.fleetmanagement.modules.vehiclecampaigns

import liquibase.integration.spring.SpringLiquibase
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import javax.sql.DataSource

@Configuration
class VehicleCampaignsDBConfiguration {
    @Bean
    fun vehicleCampaignsLiquibase(datasource: DataSource): SpringLiquibase {
        val liquibase = SpringLiquibase()
        liquibase.changeLog = "classpath:/db/changelog/db.changelog-vehiclecampaigns.yml"
        liquibase.liquibaseSchema = "vehiclecampaigns"
        liquibase.dataSource = datasource
        return liquibase
    }
}
