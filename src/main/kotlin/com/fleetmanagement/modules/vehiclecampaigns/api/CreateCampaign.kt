package com.fleetmanagement.modules.vehiclecampaigns.api

import com.fleetmanagement.modules.vehiclecampaigns.api.dtos.VehicleCampaign

interface CreateCampaign {
    /**
     * Creates a campaign in the FVM system with the given parameters.
     * Note: This campaign will NOT be available on the DMSI system.
     *
     * @param id The unique identifier for the campaign.
     * @param description An optional description of the campaign. Can be null.
     * @param isProcessable An optional flag indicating whether the campaign is processable. Can be null.
     *
     * @throws CreateCampaignFailedException If the campaign creation process fails.
     */
    fun createCampaign(
        id: String,
        description: String?,
        isProcessable: Boolean?,
    ): VehicleCampaign
}
