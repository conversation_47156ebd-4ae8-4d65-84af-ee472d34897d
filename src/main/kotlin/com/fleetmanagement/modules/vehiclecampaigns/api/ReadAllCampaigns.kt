package com.fleetmanagement.modules.vehiclecampaigns.api

import com.fleetmanagement.modules.vehiclecampaigns.api.dtos.VehicleCampaign

/**
 * Module API to fetch all campaigns available in system.
 */
interface ReadAllCampaigns {
    /**
     * Retrieves all the campaign details that are available in FVM system.
     *
     * @return list of [VehicleCampaign] if available otherwise empty list
     */
    fun readAllCampaigns(): List<VehicleCampaign>
}
