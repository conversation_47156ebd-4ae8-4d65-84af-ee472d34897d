package com.fleetmanagement.modules.vehiclecampaigns.api

import com.fleetmanagement.modules.vehiclecampaigns.api.dtos.VehicleCampaign

interface UpdateCampaign {
    /**
     * Updates existing campaign in FVM system.
     *
     * @param campaignId The unique campaign identifier
     * @param isProcessable The flag updated by FVM user denotes whether the campaign is processable or not
     * @return [VehicleCampaign] updated campaign
     */
    fun updateCampaign(
        campaignId: String,
        isProcessable: Boolean,
    ): VehicleCampaign
}
