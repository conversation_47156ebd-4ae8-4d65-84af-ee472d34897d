package com.fleetmanagement.modules.vehiclecampaigns.api

import java.util.UUID

sealed class VehicleCampaignsException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException(message, cause) {
    /**
     * Exception thrown when no campaign information is available for a given VIN. When
     * this exception is thrown, it means that the sync with the DMSI (external) system has
     * not yet happened
     *
     * @param vin The vehicle identification number (VIN) for which campaign information is unavailable.
     */
    class CampaignInformationNoSyncedException(
        val vin: String? = null,
        val vehicleId: UUID? = null,
    ) : VehicleCampaignsException("No campaign information available for vehicle with vin: $vin vehicleId: $vehicleId")

    /**
     * Exception representing an unknown or unexpected error in the vehicle campaigns module.
     *
     * @param message A descriptive message about the unknown error.
     * @param cause The underlying cause of the exception, if available.
     */
    class UnknownException(
        override val message: String,
        cause: Throwable?,
    ) : VehicleCampaignsException(message, cause)

    /**
     * Exception thrown when fetching campaigns from DB is failed.
     *
     * @param message A descriptive message about the unknown error.
     * @param cause The underlying cause of the exception, if available.
     */
    class FailedToReadCampaignsException(
        override val message: String,
        cause: Throwable?,
    ) : VehicleCampaignsException(message, cause)

    /**
     * Exception when creating a new campaign because there is already
     * a campaign with the same id
     */
    class CannotCreateCampaignWithCampaignId(
        val campaignId: String,
    ) : VehicleCampaignsException(
            "Cannot create campaign with campaignId: $campaignId",
            null,
        )

    /**
     * Exception thrown when a specified campaign is not found in FVM system
     *
     * @param message A descriptive message about the campaign ID.
     */
    class CampaignNotFoundException(
        val campaignId: String,
    ) : VehicleCampaignsException(
            message = "Campaign not found for id $campaignId",
        )
}
