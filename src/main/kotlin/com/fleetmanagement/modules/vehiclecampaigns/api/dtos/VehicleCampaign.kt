package com.fleetmanagement.modules.vehiclecampaigns.api.dtos

import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignEntity
import java.time.OffsetDateTime

data class VehicleCampaign(
    val campaignId: String,
    val campaignDescription: String?,
    val isProcessable: Boolean?,
    val createdAt: OffsetDateTime,
)

fun JPACampaignEntity.toDTO() =
    VehicleCampaign(
        campaignId = this.campaignId,
        campaignDescription = this.campaignDescription,
        isProcessable = this.isProcessable,
        createdAt = this.createdAt,
    )
