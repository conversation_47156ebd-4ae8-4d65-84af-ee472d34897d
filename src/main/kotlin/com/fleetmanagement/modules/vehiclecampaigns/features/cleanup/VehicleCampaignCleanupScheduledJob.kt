package com.fleetmanagement.modules.vehiclecampaigns.features.cleanup

import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(VehicleCampaignsCleanupConfiguration::class)
@DisallowConcurrentExecution
class VehicleCampaignCleanupScheduledJob(
    private val cleanupService: VehicleCampaignCleanupService,
) : Job {
    private val log = LoggerFactory.getLogger(VehicleCampaignCleanupScheduledJob::class.java)

    override fun execute(p0: JobExecutionContext?) {
        log.info("Starting scheduled VehicleCampaignCleanupScheduledJob")
        cleanupService.removeExpiredCampaigns()
        log.info("Finished scheduled VehicleCampaignCleanupScheduledJob")
    }
}
