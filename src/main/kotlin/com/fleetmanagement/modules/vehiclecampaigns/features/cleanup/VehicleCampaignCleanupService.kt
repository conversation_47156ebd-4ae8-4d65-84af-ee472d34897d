package com.fleetmanagement.modules.vehiclecampaigns.features.cleanup

import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignRepository
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignVehicleRepository
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime

@Service
@ConditionalOnBean(VehicleCampaignsCleanupConfiguration::class)
class VehicleCampaignCleanupService(
    @Autowired val jpaCampaignVehicleRepository: JPACampaignVehicleRepository,
    @Autowired val jpaCampaignRepository: JPACampaignRepository,
) {
    @Transactional
    fun removeExpiredCampaigns() {
        val expirationThreshold = OffsetDateTime.now().minusHours(24)
        val expiredVehicles = jpaCampaignVehicleRepository.findAllByUpdatedAtBefore(expirationThreshold)
        jpaCampaignVehicleRepository.deleteAllInBatch(expiredVehicles)
        jpaCampaignRepository.deleteOrphanedCampaigns()
    }
}
