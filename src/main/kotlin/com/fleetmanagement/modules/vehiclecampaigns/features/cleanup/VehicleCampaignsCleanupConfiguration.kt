package com.fleetmanagement.modules.vehiclecampaigns.features.cleanup

import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.TimeZone

@Configuration
@ConditionalOnProperty(name = ["vehicle-campaigns.enabled"], havingValue = "true")
class VehicleCampaignsCleanupConfiguration {
    @Bean("vehicleCampaignCleanupScheduledJobDetail")
    fun initializeVehicleCampaignCleanupScheduledJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(VehicleCampaignCleanupScheduledJob::class.java)
            .withIdentity("vehicleCampaignCleanupScheduledJob")
            .withDescription("Clean up expired vehicle campaigns")
            .storeDurably()
            .build()

    @Bean("vehicleCampaignCleanupScheduledJobTrigger")
    fun initializeJobTrigger(
        @Qualifier("vehicleCampaignCleanupScheduledJobDetail") initializeVehicleCampaignCleanupScheduledJobDetail: JobDetail,
        @Value("\${vehicle-campaigns.cleanup-vehicles.cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(initializeVehicleCampaignCleanupScheduledJobDetail)
            .withIdentity("InitializeVehicleCampaignCleanupScheduledJobTrigger")
            .withDescription("InitializeVehicleCampaignCleanupScheduledJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
