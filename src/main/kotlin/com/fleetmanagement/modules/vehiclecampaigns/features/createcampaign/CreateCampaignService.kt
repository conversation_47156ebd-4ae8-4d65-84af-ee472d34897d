package com.fleetmanagement.modules.vehiclecampaigns.features.createcampaign

import com.fleetmanagement.modules.vehiclecampaigns.api.CreateCampaign
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException.CannotCreateCampaignWithCampaignId
import com.fleetmanagement.modules.vehiclecampaigns.api.dtos.VehicleCampaign
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignEntity
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class CreateCampaignService(
    private val campaignRepository: JPACampaignRepository,
) : CreateCampaign {
    @Transactional
    override fun createCampaign(
        id: String,
        description: String?,
        isProcessable: Boolean?,
    ): VehicleCampaign {
        try {
            if (campaignRepository.existsByCampaignId(id)) {
                throw CannotCreateCampaignWithCampaignId(id)
            }
            val savedCampaign =
                campaignRepository.save(
                    JPACampaignEntity(id).also {
                        it.updateDescription(description)
                        it.updateIsProcessable(isProcessable)
                    },
                )
            return VehicleCampaign(
                campaignId = savedCampaign.campaignId,
                campaignDescription = savedCampaign.campaignDescription,
                isProcessable = savedCampaign.isProcessable,
                createdAt = savedCampaign.createdAt,
            )
        } catch (exception: VehicleCampaignsException) {
            throw exception
        } catch (exception: Exception) {
            throw VehicleCampaignsException.UnknownException("Cannot create campaign with campaign-id $id", exception)
        }
    }
}
