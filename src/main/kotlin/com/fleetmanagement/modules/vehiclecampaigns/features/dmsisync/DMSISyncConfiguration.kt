package com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync

import com.fleetmanagement.modules.vehiclecampaigns.VehicleCampaignsConfiguration
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob.DMSIScheduledJob
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.TimeZone

@Configuration
@ConditionalOnBean(VehicleCampaignsConfiguration::class)
class DMSISyncConfiguration {
    @Bean("dmsiScheduledJobDetail")
    fun initializeDMSIScheduledJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(DMSIScheduledJob::class.java)
            .withIdentity("dmsiScheduledJob")
            .withDescription("Sync Campaign Information with DMSI")
            .storeDurably()
            .build()

    @Bean("dmsiScheduledJobTrigger")
    fun initializeDMSIScheduledJobTrigger(
        @Qualifier("dmsiScheduledJobDetail") initializeDMSIScheduledJobDetail: JobDetail,
        @Value("\${vehicle-campaigns.dmsi.cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(initializeDMSIScheduledJobDetail)
            .withIdentity("InitializeDMSIScheduledJobTrigger")
            .withDescription("InitializeDMSIScheduledJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
