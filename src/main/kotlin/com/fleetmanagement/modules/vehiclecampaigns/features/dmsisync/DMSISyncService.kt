package com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync

import com.fleetmanagement.modules.vehiclecampaigns.VehicleCampaignsConfiguration
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignRepository
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignVehicleEntity
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignVehicleRepository
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob.DMSISyncJob
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob.DMSISyncJobRepository
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob.DMSISyncJobResult
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.client.DMSIWebClient
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.config.DMSIWebClientProperties
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.shared.DMSIServiceCampaign
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@ConditionalOnBean(VehicleCampaignsConfiguration::class)
class DMSISyncService(
    private val dmsiWebClient: DMSIWebClient,
    private val dmsiWebClientProperties: DMSIWebClientProperties,
    private val dmsiRepository: DMSISyncJobRepository,
    private val campaignRepository: JPACampaignRepository,
    private val campaignVehicleRepository: JPACampaignVehicleRepository,
) {
    private val logger = LoggerFactory.getLogger(DMSISyncService::class.java)

    @Transactional
    fun syncCampaignInformation(batchSize: Int) {
        val jobs = DMSISyncJob.nextPendingJobs(batchSize, dmsiRepository)
        jobs.forEach { job ->
            val result =
                job.execute(
                    dmsiWebClient,
                    dmsiWebClientProperties,
                )
            when (result) {
                is DMSISyncJobResult.Success -> updateVehicleCampaignUsing(job.vehicleId, result)
                is DMSISyncJobResult.Failure -> logger.error("Could not sync campaigns for vehicle with VIN: ${result.vin}")
            }
        }
    }

    private fun updateVehicleCampaignUsing(
        vehicleId: UUID,
        result: DMSISyncJobResult.Success,
    ) {
        val vin = result.vin
        val dmsiCampaigns = result.campaign?.serviceCampaign ?: emptyList()

        val savedCampaignVehicle = campaignVehicleRepository.findByVin(vin)
        if (savedCampaignVehicle == null) {
            // First time creation of Vehicle
            val newVehicle = JPACampaignVehicleEntity(vehicleId, vin)
            dmsiCampaigns
                .filter { it.serviceCampaignIdentifier?.serviceCampaignUID?.value != null }
                .forEach { dmsiCampaign ->
                    newVehicle.associateWithCampaign(dmsiCampaign, campaignRepository)
                }
            campaignVehicleRepository.save(newVehicle)
        } else {
            // Existing Vehicle, overwrite campaigns
            savedCampaignVehicle.removeAllCampaigns()
            dmsiCampaigns
                .filter { it.serviceCampaignIdentifier?.serviceCampaignUID?.value != null }
                .forEach { dmsiCampaign ->
                    savedCampaignVehicle.associateWithCampaign(dmsiCampaign, campaignRepository)
                }
        }
    }
}

private fun JPACampaignVehicleEntity.associateWithCampaign(
    campaign: DMSIServiceCampaign,
    campaignRepository: JPACampaignRepository,
) {
    this.associateWithCampaign(
        campaignId = checkNotNull(campaign.serviceCampaignIdentifier?.serviceCampaignUID?.value),
        campaignDescription = campaign.campaignDescription?.value,
        campaignRepository = campaignRepository,
    )
}
