package com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models

import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Modifying
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.time.OffsetDateTime

@Entity
@Table(name = "campaigns", schema = "vehiclecampaigns")
class JPACampaignEntity(
    @Column(name = "campaign_id")
    val campaignId: String,
) {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long? = null

    @Column(name = "created_at")
    @CreationTimestamp
    val createdAt: OffsetDateTime = OffsetDateTime.now()

    @Column(name = "updated_at")
    @UpdateTimestamp
    val updatedAt: OffsetDateTime = OffsetDateTime.now()

    @Column(name = "campaign_description")
    var campaignDescription: String? = null
        private set

    @Column(name = "is_processable")
    var isProcessable: Boolean? = null
        private set

    fun updateDescription(campaignDescription: String?) {
        this.campaignDescription = campaignDescription
    }

    fun updateIsProcessable(campaignIsProcessable: Boolean?) {
        this.isProcessable = campaignIsProcessable
    }
}

@Repository
interface JPACampaignRepository : JpaRepository<JPACampaignEntity, Long> {
    fun findByCampaignId(campaignId: String): JPACampaignEntity?

    fun existsByCampaignId(campaignId: String): Boolean

    fun findAllByOrderByCreatedAtDesc(): List<JPACampaignEntity>

    @Modifying
    @Query(
        "DELETE FROM vehiclecampaigns.campaigns c WHERE NOT EXISTS (SELECT 1 FROM  vehiclecampaigns.vehicle_campaign vc WHERE vc.campaign_id = c.id)",
        nativeQuery = true,
    )
    fun deleteOrphanedCampaigns()
}
