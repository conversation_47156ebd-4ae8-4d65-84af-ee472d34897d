package com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models

import jakarta.persistence.CascadeType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.JoinColumn
import jakarta.persistence.JoinTable
import jakarta.persistence.ManyToMany
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.OffsetDateTime
import java.util.UUID

@Entity
@Table(name = "vehicle", schema = "vehiclecampaigns")
data class JPACampaignVehicleEntity(
    @Column(name = "vehicle_id")
    val vehicleId: UUID,
    @Column(name = "vin")
    val vin: String,
) {
    fun associateWithCampaign(
        campaignId: String,
        campaignDescription: String?,
        campaignRepository: JPACampaignRepository,
    ) {
        val existingCampaign = campaignRepository.findByCampaignId(campaignId)
        if (existingCampaign == null) {
            val newCampaign =
                JPACampaignEntity(campaignId).also {
                    it.updateDescription(campaignDescription)
                }
            val savedCampaign = campaignRepository.save(newCampaign)
            this.campaigns.add(savedCampaign)
        } else {
            existingCampaign.updateDescription(campaignDescription)
            this.campaigns.add(existingCampaign)
        }
        this.precomputedCampaignIds = this.campaigns.joinToString(separator = ",") { it.campaignId }
    }

    fun removeAllCampaigns() {
        this.campaigns.clear()
        this.precomputedCampaignIds = ""
    }

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long? = null

    @ManyToMany(cascade = [CascadeType.MERGE, CascadeType.PERSIST])
    @JoinTable(
        name = "vehicle_campaign",
        schema = "vehiclecampaigns",
        joinColumns = [JoinColumn(name = "vehicle_id")],
        inverseJoinColumns = [JoinColumn(name = "campaign_id")],
    )
    var campaigns: MutableList<JPACampaignEntity> = mutableListOf()
        private set

    @Column(name = "precomputed_campaign_ids")
    var precomputedCampaignIds: String? = ""
        private set

    @Column(name = "created_at")
    @CreationTimestamp
    val createdAt: OffsetDateTime = OffsetDateTime.now()

    @Column(name = "updated_at")
    @UpdateTimestamp
    val updatedAt: OffsetDateTime = OffsetDateTime.now()
}

@Repository
interface JPACampaignVehicleRepository : JpaRepository<JPACampaignVehicleEntity, Long> {
    fun findByVin(vin: String): JPACampaignVehicleEntity?

    fun findByVehicleId(vehicleId: UUID): JPACampaignVehicleEntity?

    fun findAllByUpdatedAtBefore(updatedAtBefore: OffsetDateTime): Iterable<JPACampaignVehicleEntity>
}
