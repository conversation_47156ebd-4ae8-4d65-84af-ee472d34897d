package com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob

import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.DMSISyncConfiguration
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.DMSISyncService
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(DMSISyncConfiguration::class)
@DisallowConcurrentExecution
class DMSIScheduledJob(
    private val dmsiSyncService: DMSISyncService,
) : Job {
    val batchSize = 1

    private val log = LoggerFactory.getLogger(DMSIScheduledJob::class.java)

    override fun execute(p0: JobExecutionContext?) {
        log.info("Starting scheduled DMSIScheduledJob")
        dmsiSyncService.syncCampaignInformation(batchSize)
        log.info("Finished scheduled DMSIScheduledJob")
    }
}
