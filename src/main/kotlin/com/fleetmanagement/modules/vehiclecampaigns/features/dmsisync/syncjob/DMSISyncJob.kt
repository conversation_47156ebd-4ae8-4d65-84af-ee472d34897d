package com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob

import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.client.DMSIWebClient
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.config.DMSIWebClientProperties
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.requests.DMSIGetCampaignsByVINRequest
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.requests.DMSIGetVehicleDataAvailabilityRequest
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.responses.DMSIServiceCampaigns
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.shared.DMSIVehicleRef
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.shared.DMSIVehicleVIN
import io.hypersistence.utils.hibernate.type.json.JsonBinaryType
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.Type
import org.hibernate.annotations.UpdateTimestamp
import org.slf4j.LoggerFactory
import java.time.OffsetDateTime
import java.util.UUID

@Entity
@Table(name = "dmsi_sync_jobs", schema = "vehiclecampaigns")
class DMSISyncJob(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id") val id: Long? = null,
    @Column(name = "vehicle_id") val vehicleId: UUID,
    @Column(name = "vin")
    val vin: String,
    status: DMSISyncJobStatus,
    hasOpenRecall: Boolean?,
    campaigns: DMSIServiceCampaigns?,
    @Column(name = "created_at")
    @CreationTimestamp val createdAt: OffsetDateTime? = null,
    @Column(name = "updated_at")
    @UpdateTimestamp val updatedAt: OffsetDateTime? = null,
) {
    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    var status: DMSISyncJobStatus = status
        private set

    @Column(name = "has_open_recall")
    var hasOpenRecall: Boolean? = hasOpenRecall
        private set

    @Type(JsonBinaryType::class)
    @Column(name = "campaigns", columnDefinition = "jsonb")
    var campaigns: DMSIServiceCampaigns? = campaigns
        private set

    fun execute(
        dmsiWebClient: DMSIWebClient,
        dmsiWebClientProperties: DMSIWebClientProperties,
    ): DMSISyncJobResult {
        try {
            val kvpsIdentifier = dmsiWebClientProperties.xPersonalId

            val response =
                dmsiWebClient.getVehicleDataAvailabilityByVIN(
                    kvpsIdentifier = kvpsIdentifier,
                    request =
                        DMSIGetVehicleDataAvailabilityRequest(
                            languageID = "de-DE",
                            vehicleRef =
                                DMSIVehicleRef(
                                    vin =
                                        DMSIVehicleVIN(
                                            value = this.vin,
                                        ),
                                ),
                        ),
                )
            if (response.recallInformation?.hasOpenRecall != true) {
                throw NoCampaignInformationForVehicle("Vehicle hasOpenRecall field is not true")
            }

            val campaigns =
                dmsiWebClient.getCampaignsByVIN(
                    kvpsIdentifier = kvpsIdentifier,
                    request =
                        DMSIGetCampaignsByVINRequest(
                            languageID = "de-DE",
                            openRecallsIND = true,
                            vehicleRef =
                                DMSIVehicleRef(
                                    vin =
                                        DMSIVehicleVIN(
                                            value = this.vin,
                                        ),
                                ),
                        ),
                )

            if (campaigns.serviceCampaigns == null) {
                throw OpenRecallIsTrueButNoCampaignsAvailableException()
            }

            logger.info("Successfully synced campaign details for VIN ${this.vin}")
            this.status = DMSISyncJobStatus.SYNCED
            this.hasOpenRecall = true
            this.campaigns = campaigns.serviceCampaigns
            return DMSISyncJobResult.Success(
                vin = this.vin,
                campaign = campaigns.serviceCampaigns,
            )
        } catch (e: NoCampaignInformationForVehicle) {
            logger.warn("No campaign information for vehicle with VIN $vin: ${e.context}")
            this.status = DMSISyncJobStatus.SYNCED
            this.hasOpenRecall = false
            this.campaigns = null
            return DMSISyncJobResult.Success(
                vin = this.vin,
                campaign = null,
            )
        } catch (e: OpenRecallIsTrueButNoCampaignsAvailableException) {
            logger.error(
                "DMSI API behaved in an unexpected manner. " +
                    "Campaigns for vehicle with VIN $vin should " +
                    "not be null when there are open recalls",
            )
            this.status = DMSISyncJobStatus.FAILED
            this.hasOpenRecall = true
            this.campaigns = null
            return DMSISyncJobResult.Failure(
                vin = this.vin,
                error = e,
            )
        } catch (e: Exception) {
            logger.error(
                "An unknown exception has occurred " +
                    "during campaign-information sync for vehicle " +
                    "with VIN: $vin",
                e,
            )
            this.status = DMSISyncJobStatus.FAILED
            this.hasOpenRecall = false
            this.campaigns = null
            return DMSISyncJobResult.Failure(
                vin = this.vin,
                error = e,
            )
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(DMSISyncJob::class.java)

        fun schedule(
            vin: String,
            vehicleId: UUID,
            repository: DMSISyncJobRepository,
        ): DMSISyncJob {
            val pendingJob = repository.findPendingJob(vin)
            if (pendingJob != null) {
                return pendingJob
            }

            return repository.save(
                DMSISyncJob(
                    vin = vin,
                    status = DMSISyncJobStatus.PENDING,
                    vehicleId = vehicleId,
                    hasOpenRecall = null,
                    campaigns = null,
                ),
            )
        }

        fun nextPendingJobs(
            number: Int,
            repository: DMSISyncJobRepository,
        ): List<DMSISyncJob> = repository.findPendingJobs(number)

        fun latestSuccessfulSyncJobFor(
            vin: String,
            repository: DMSISyncJobRepository,
        ): DMSISyncJob? = repository.findLatestSyncedJob(vin)

        fun latestSyncJobFor(
            vin: String,
            repository: DMSISyncJobRepository,
        ): DMSISyncJob? = repository.findLatestJob(vin)

        fun pendingSyncJobFor(
            vin: String,
            repository: DMSISyncJobRepository,
        ): DMSISyncJob? = repository.findPendingJob(vin)
    }
}

private class NoCampaignInformationForVehicle(
    val context: String,
) : RuntimeException()

private class OpenRecallIsTrueButNoCampaignsAvailableException : RuntimeException()
