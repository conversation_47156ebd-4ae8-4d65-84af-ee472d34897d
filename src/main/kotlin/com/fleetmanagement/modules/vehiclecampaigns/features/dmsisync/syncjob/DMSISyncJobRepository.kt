package com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob

import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository

@Repository
interface DMSISyncJobRepository : JpaRepository<DMSISyncJob, Long> {
    @Query("SELECT e FROM DMSISyncJob e WHERE e.status = 'PENDING' ORDER BY e.createdAt ASC")
    fun findPendingJobs(number: Int): List<DMSISyncJob>

    @Query("SELECT e FROM DMSISyncJob e WHERE e.status = 'PENDING' AND e.vin = :vin ORDER BY e.createdAt ASC")
    fun findPendingJob(vin: String): DMSISyncJob?

    @Query("SELECT e FROM DMSISyncJob e WHERE e.status = 'SYNCED' and e.vin = :vin ORDER BY e.updatedAt DESC")
    fun findLatestSyncedJob(vin: String): DMSISyncJob?

    @Query("SELECT e FROM DMSISyncJob e WHERE e.vin = :vin ORDER BY e.updatedAt DESC")
    fun findLatestJob(vin: String): DMSISyncJob?
}
