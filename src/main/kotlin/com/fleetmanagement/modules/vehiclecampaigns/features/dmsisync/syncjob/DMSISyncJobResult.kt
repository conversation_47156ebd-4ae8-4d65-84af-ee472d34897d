package com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob

import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.responses.DMSIServiceCampaigns

sealed class DMSISyncJobResult {
    data class Success(
        val vin: String,
        val campaign: DMSIServiceCampaigns?,
    ) : DMSISyncJobResult()

    data class Failure(
        val vin: String,
        val error: Exception,
    ) : DMSISyncJobResult()
}
