package com.fleetmanagement.modules.vehiclecampaigns.features.filtervehicles

import com.fleetmanagement.modules.vehiclecampaigns.VehicleCampaignsConfiguration
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(VehicleCampaignsConfiguration::class)
@DisallowConcurrentExecution
class VehicleCampaignFilterScheduledJob(
    private val filterVehicleService: VehicleCampaignFilterVehicleService,
) : Job {
    private val log = LoggerFactory.getLogger(VehicleCampaignFilterScheduledJob::class.java)

    override fun execute(p0: JobExecutionContext?) {
        log.info("Starting scheduled VehicleCampaignFilterScheduledJob")
        filterVehicleService.filterVehiclesForCampaignInformationSync()
        log.info("Finished scheduled VehicleCampaignFilterScheduledJob")
    }
}
