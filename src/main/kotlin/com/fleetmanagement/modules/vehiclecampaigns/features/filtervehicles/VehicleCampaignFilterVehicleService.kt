package com.fleetmanagement.modules.vehiclecampaigns.features.filtervehicles

import com.fleetmanagement.modules.vehiclecampaigns.VehicleCampaignsConfiguration
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob.DMSISyncJob
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.syncjob.DMSISyncJobRepository
import com.fleetmanagement.modules.vehicledata.api.ReadVehiclesReadyForCampaignSync
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadPlannedVehicleTransferUseCase
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.data.domain.PageRequest
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@ConditionalOnBean(VehicleCampaignsConfiguration::class)
class VehicleCampaignFilterVehicleService(
    private val configuration: VehicleCampaignsFilterVehiclesConfigurationProperties,
    private val readPlannedVehicleTransferUseCase: ReadPlannedVehicleTransferUseCase,
    private val readVehiclesReadyForCampaignSync: ReadVehiclesReadyForCampaignSync,
    private val dmsiSyncJobRepository: DMSISyncJobRepository,
) {
    private val logger = LoggerFactory.getLogger(VehicleCampaignFilterVehicleService::class.java)

    @Transactional
    fun filterVehiclesForCampaignInformationSync() {
        val pageSize = configuration.vehicleTransferBatchSize
        var currentPage = 0

        val maxVehiclesToSchedulePerRun = configuration.maxVehiclesToSchedulePerRun
        var counterForFilteredVehicles = 0

        do {
            val response =
                readPlannedVehicleTransferUseCase.findAllTransfersWithUtilizationAreaZuffenhausenAndStatusPlanned(
                    PageRequest.of(currentPage, pageSize),
                )
            val vehicleIdsFromVehicleTransfer = response.toList().map { it.vehicleId }

            val vehicleIdsWithAdditionalFiltering =
                readVehiclesReadyForCampaignSync.readVehiclesReadyForCampaignSyncByIds(
                    vehicleIdsFromVehicleTransfer,
                )

            vehicleIdsWithAdditionalFiltering
                .take(maxVehiclesToSchedulePerRun - counterForFilteredVehicles)
                .forEach { vehicle ->
                    if (vehicle.vin == null) {
                        logger.error("Possible Data Issue: Vehicle with Id ${vehicle.id} was ready for campaign-data sync but has no VIN")
                        return@forEach
                    }

                    DMSISyncJob.schedule(vehicle.vin, vehicle.id, dmsiSyncJobRepository)
                    counterForFilteredVehicles += 1
                }
            currentPage += 1
        } while (currentPage < response.totalPages && counterForFilteredVehicles < maxVehiclesToSchedulePerRun)

        logger.info("Scheduled $counterForFilteredVehicles vehicles for sync")
        if (counterForFilteredVehicles == maxVehiclesToSchedulePerRun) {
            logger.error(
                "Exceeded threshold of $maxVehiclesToSchedulePerRun" +
                    " vehicles for DMSI Filter Sync." +
                    " Might need to extend threshold after consulting" +
                    " with DMSI Team",
            )
        }
    }
}
