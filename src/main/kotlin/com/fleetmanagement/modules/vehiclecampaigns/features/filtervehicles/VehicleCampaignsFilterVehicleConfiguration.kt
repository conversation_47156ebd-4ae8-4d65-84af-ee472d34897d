package com.fleetmanagement.modules.vehiclecampaigns.features.filtervehicles

import com.fleetmanagement.modules.vehiclecampaigns.VehicleCampaignsConfiguration
import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.time.ZoneOffset
import java.util.TimeZone

@Configuration
@ConditionalOnBean(VehicleCampaignsConfiguration::class)
class VehicleCampaignsFilterVehicleConfiguration {
    @Bean("vehicleCampaignFilterScheduledJobDetail")
    fun initializeVehicleCampaignFilterScheduledJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(VehicleCampaignFilterScheduledJob::class.java)
            .withIdentity("vehicleCampaignFilterScheduledJob")
            .withDescription("Fetch Vehicles Ready for DMSI Campaign Sync")
            .storeDurably()
            .build()

    @Bean("vehicleCampaignFilterScheduledJobTrigger")
    fun initializeDMSIScheduledJobTrigger(
        @Qualifier("vehicleCampaignFilterScheduledJobDetail") initializeVehicleCampaignFilterScheduledJobDetail: JobDetail,
        @Value("\${vehicle-campaigns.filter-vehicles.cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(initializeVehicleCampaignFilterScheduledJobDetail)
            .withIdentity("InitializeVehicleCampaignFilterScheduledJobTrigger")
            .withDescription("InitializeVehicleCampaignFilterScheduledJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
