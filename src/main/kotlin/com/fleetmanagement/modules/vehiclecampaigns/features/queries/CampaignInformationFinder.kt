package com.fleetmanagement.modules.vehiclecampaigns.features.queries

import com.fleetmanagement.modules.vehiclecampaigns.api.CampaignsForVehicleByVehicleId
import com.fleetmanagement.modules.vehiclecampaigns.api.OpenActionsForVehicleByVIN
import com.fleetmanagement.modules.vehiclecampaigns.api.ReadAllCampaigns
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException.*
import com.fleetmanagement.modules.vehiclecampaigns.api.dtos.VehicleCampaign
import com.fleetmanagement.modules.vehiclecampaigns.api.dtos.toDTO
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignRepository
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignVehicleRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
@Transactional(readOnly = true)
class CampaignInformationFinder(
    private val campaignVehicleRepository: JPACampaignVehicleRepository,
    private val jpaCampaignRepository: JPACampaignRepository,
) : OpenActionsForVehicleByVIN,
    CampaignsForVehicleByVehicleId,
    ReadAllCampaigns {
    @Transactional(
        noRollbackFor = [VehicleCampaignsException::class],
    )
    override fun findNumberOfOpenActionsForVehicle(vin: String): Int {
        try {
            val availableCampaigns =
                campaignVehicleRepository.findByVin(vin)
                    ?: throw CampaignInformationNoSyncedException(vin)
            return availableCampaigns.campaigns.size
        } catch (e: VehicleCampaignsException) {
            throw e
        } catch (exception: Exception) {
            throw UnknownException("Could not fetch campaigns for VIN $vin", exception)
        }
    }

    override fun findCampaignsForVehicle(vehicleId: UUID): List<VehicleCampaign> {
        try {
            val campaignVehicle =
                campaignVehicleRepository.findByVehicleId(vehicleId)
                    ?: throw CampaignInformationNoSyncedException(vehicleId = vehicleId)
            return campaignVehicle.campaigns.map {
                it.toDTO()
            }
        } catch (e: VehicleCampaignsException) {
            throw e
        } catch (exception: Exception) {
            throw UnknownException("Could not fetch campaigns for vehicle-id $vehicleId", exception)
        }
    }

    /**
     * Implementation for API [ReadAllCampaigns] to fetch all the campaigns available in system.
     */
    override fun readAllCampaigns(): List<VehicleCampaign> =
        try {
            jpaCampaignRepository.findAllByOrderByCreatedAtDesc().map {
                it.toDTO()
            }
        } catch (exception: Exception) {
            throw FailedToReadCampaignsException("Could not fetch campaigns from DB", exception)
        }
}
