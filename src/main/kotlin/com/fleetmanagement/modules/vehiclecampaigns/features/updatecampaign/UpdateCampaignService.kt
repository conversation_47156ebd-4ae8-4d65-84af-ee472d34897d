package com.fleetmanagement.modules.vehiclecampaigns.features.updatecampaign

import com.fleetmanagement.modules.vehiclecampaigns.api.UpdateCampaign
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException.CampaignNotFoundException
import com.fleetmanagement.modules.vehiclecampaigns.api.dtos.VehicleCampaign
import com.fleetmanagement.modules.vehiclecampaigns.api.dtos.toDTO
import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.models.JPACampaignRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class UpdateCampaignService(
    private val jpaCampaignRepository: JPACampaignRepository,
) : UpdateCampaign {
    @Transactional
    override fun updateCampaign(
        campaignId: String,
        isProcessable: <PERSON>olean,
    ): VehicleCampaign {
        val campaign =
            jpaCampaignRepository.findByCampaignId(campaignId) ?: throw CampaignNotFoundException(
                campaignId = campaignId,
            )
        campaign.updateIsProcessable(isProcessable)
        return campaign.toDTO()
    }
}
