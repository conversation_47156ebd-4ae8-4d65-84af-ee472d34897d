/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.client

import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.requests.DMSIGetCampaignsByVINRequest
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.requests.DMSIGetVehicleDataAvailabilityRequest
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.responses.DMSGetCampaignsByVINResponse
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.responses.DMSVehicleDataAvailabilityByVINResponse
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.service.annotation.PostExchange

interface DMSIWebClient {
    @PostExchange(url = "/vdas/v141/{kvps}/getDataAvailability", accept = ["application/json"])
    fun getVehicleDataAvailabilityByVIN(
        @PathVariable("kvps") kvpsIdentifier: String,
        @RequestBody request: DMSIGetVehicleDataAvailabilityRequest,
    ): DMSVehicleDataAvailabilityByVINResponse

    @PostExchange(url = "/vrm/v110/{kvps}/getCampaignsByVehicle", accept = ["application/json"])
    fun getCampaignsByVIN(
        @PathVariable("kvps") kvpsIdentifier: String,
        @RequestBody request: DMSIGetCampaignsByVINRequest,
    ): DMSGetCampaignsByVINResponse
}
