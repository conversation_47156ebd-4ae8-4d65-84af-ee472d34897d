/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.config

import com.fleetmanagement.modules.vehiclecampaigns.features.dmsisync.DMSISyncConfiguration
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.client.DMSIExceptionHandler
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.client.DMSIWebClient
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.security.oauth2.client.*
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.InMemoryReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.web.reactive.function.client.ServerOAuth2AuthorizedClientExchangeFilterFunction
import org.springframework.security.oauth2.client.web.server.AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository
import org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository
import org.springframework.web.reactive.function.client.ExchangeFilterFunction
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.support.WebClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory

@Configuration
@ConditionalOnBean(DMSISyncConfiguration::class)
class DMSIWebClientConfiguration {
    @Bean
    @Qualifier("dmsi")
    fun reactiveDMSIClientRegistrationRepository(
        clientRegistrationRepository: ClientRegistrationRepository,
    ): ReactiveClientRegistrationRepository =
        InMemoryReactiveClientRegistrationRepository(
            clientRegistrationRepository.findByRegistrationId(CLIENT_REGISTRATION_ID),
        )

    @Bean
    @Qualifier("dmsi")
    fun reactiveOAuth2AuthorizedDMSIClientService(
        @Qualifier("dmsi")
        clientRegistrationRepository: ReactiveClientRegistrationRepository,
    ): ReactiveOAuth2AuthorizedClientService = InMemoryReactiveOAuth2AuthorizedClientService(clientRegistrationRepository)

    @Bean
    @Qualifier("dmsi")
    fun dmsiReactiveAuthorizedClientRepository(
        @Qualifier("dmsi") authorizedClientService: ReactiveOAuth2AuthorizedClientService,
    ): ServerOAuth2AuthorizedClientRepository = AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository(authorizedClientService)

    @Bean
    @Qualifier("dmsi")
    fun reactiveOAuth2AuthorizedDMSIClientManager(
        @Qualifier("dmsi")
        clientRegistrationRepository: ReactiveClientRegistrationRepository,
        @Qualifier("dmsi") authorizedClientService: ReactiveOAuth2AuthorizedClientService,
    ): ReactiveOAuth2AuthorizedClientManager =
        AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager(
            clientRegistrationRepository,
            authorizedClientService,
        ).apply {
            setAuthorizedClientProvider(
                ClientCredentialsReactiveOAuth2AuthorizedClientProvider(),
            )
        }

    @Bean
    @Qualifier("dmsi")
    fun vehicleCampaignDMSIHttpServiceProxyFactory(
        dmsiWebClientProperties: DMSIWebClientProperties,
        @Qualifier("dmsi") authorizedClientManager: ReactiveOAuth2AuthorizedClientManager,
        dmsiExceptionHandler: DMSIExceptionHandler,
    ): HttpServiceProxyFactory {
        val oauth2Filter =
            ServerOAuth2AuthorizedClientExchangeFilterFunction(authorizedClientManager)
        oauth2Filter.setDefaultClientRegistrationId(CLIENT_REGISTRATION_ID)

        val webClient =
            WebClient
                .builder()
                .baseUrl(dmsiWebClientProperties.baseUri)
                .defaultHeader(X_PERSONAL_ID, dmsiWebClientProperties.xPersonalId)
                .defaultHeader(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .filter(
                    ExchangeFilterFunction.ofResponseProcessor(
                        dmsiExceptionHandler::clientErrorResponseProcessor,
                    ),
                ).filter(dmsiExceptionHandler::clientErrorRequestProcessor)
                .filter(oauth2Filter)
                .build()

        val adapter = WebClientAdapter.create(webClient)

        return HttpServiceProxyFactory.builderFor(adapter).build()
    }

    @Bean
    @Qualifier("dmsi")
    fun dmsiWebClient(
        @Qualifier("dmsi")
        httpServiceProxyFactory: HttpServiceProxyFactory,
    ): DMSIWebClient = httpServiceProxyFactory.createClient(DMSIWebClient::class.java)

    companion object {
        const val CLIENT_REGISTRATION_ID = "dmsi"
        const val X_PERSONAL_ID = "X-Personal-ID"
    }
}
