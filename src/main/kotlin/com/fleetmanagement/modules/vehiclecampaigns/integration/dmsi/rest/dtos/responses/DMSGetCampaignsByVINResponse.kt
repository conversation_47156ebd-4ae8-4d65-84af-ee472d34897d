package com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.responses

import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.shared.DMSIServiceCampaign
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.shared.DMSIVehicleRef

data class DMSIServiceCampaigns(
    val serviceCampaign: List<DMSIServiceCampaign>? = null,
)

data class DMSGetCampaignsByVINResponse(
    val vehicleRef: DMSIVehicleRef? = null,
    val serviceCampaigns: DMSIServiceCampaigns? = null,
)
