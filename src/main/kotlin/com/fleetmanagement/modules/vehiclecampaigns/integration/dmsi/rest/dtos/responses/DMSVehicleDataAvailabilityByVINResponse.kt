package com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.responses

import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.shared.DMSIStringValue
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.shared.DMSIStringValueLocalized
import com.fleetmanagement.modules.vehiclecampaigns.integration.dmsi.rest.dtos.shared.DMSIVehicleRef

data class DMSVehicleDataAvailabilityByVINResponse(
    val vehicleRef: DMSIVehicleRef? = null,
    val vehicleInformation: DMSVehicleInformation? = null,
    val recallInformation: DMSRecallInformation? = null,
    val additionalNotes: DMSAdditionalNotes? = null,
)

data class DMSWarrantyDetails(
    val priority: DMSIStringValue? = null,
    val description: DMSIStringValueLocalized? = null,
)

data class DMSOpenRecallDetails(
    val priority: DMSIStringValue? = null,
    val description: DMSIStringValueLocalized? = null,
)

data class DMSAdditionalNote(
    val priority: DMSIStringValue? = null,
    val description: DMSIStringValueLocalized? = null,
)

data class DMSAdditionalNotes(
    val additionalNote: List<DMSAdditionalNote>? = null,
)

data class DMSRecallInformation(
    val status: DMSIStringValue? = null,
    val hasOpenRecall: Boolean? = null,
    val openRecallDetails: DMSOpenRecallDetails? = null,
)

data class DMSVehicleInformation(
    val status: DMSIStringValue,
    val hasWarrantyData: Boolean?,
    val warrantyDetails: DMSWarrantyDetails?,
    val isRecycled: Boolean?,
)
