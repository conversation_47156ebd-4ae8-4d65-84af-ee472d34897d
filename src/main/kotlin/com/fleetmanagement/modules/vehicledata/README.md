# Vehicle-Data Module

## Purpose 
This module integrates with various internal systems within Porsche to fetch and store data that is relevant to Fleet-Vehicle-Management.

## Integration with Source Systems
### Porsche Vehicle Hub 
The module has two integrations with Porsche Vehicle Hub (PVH) - one through the Gravity API Gateway and another through Streamzilla

[Documentation](integrations/README.md)

## Features
1. **Read Vehicle-Data** by unique identifer (VIN, VGUID) [Link to documentation](features/readvehicle/README.md)
2. **Refresh Vehicle-Data** from source-system [Link to documentation](features/refreshvehicle/README.md)
3. **Archive Vehicle-Data** to comply with legal-requirements


> **Important Note:** The terminology used in the code should match the list of features that are mentioned above.