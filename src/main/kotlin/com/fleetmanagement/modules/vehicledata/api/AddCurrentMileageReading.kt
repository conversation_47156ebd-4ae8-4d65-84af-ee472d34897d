/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource
import java.time.OffsetDateTime
import java.util.*

fun interface AddCurrentMileageReading {
    /**
     * Will add a new mileage reading for given vehicle
     */
    fun addCurrentMileageReading(
        vehicleId: UUID,
        mileage: Int,
        readDate: OffsetDateTime,
        source: MileageReadingSource,
    )
}
