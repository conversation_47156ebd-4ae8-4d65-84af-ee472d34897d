/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType

fun interface CreateOrUpdateFinancialAssetTypeUpdate {
    fun createOrUpdateFinancialAssetTypeUpdate(
        vin: String,
        financialAssetType: FinancialAssetType,
    )
}
