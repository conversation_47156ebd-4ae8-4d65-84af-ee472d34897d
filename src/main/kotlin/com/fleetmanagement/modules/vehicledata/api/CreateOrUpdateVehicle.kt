package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.CreateUIVehicleDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateVehicleDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO

interface CreateOrUpdateVehicle {
    fun createOrUpdateVehicle(
        createVehicleDto: CreateUIVehicleDTO,
        creatingUsername: String,
    ): VehicleDTO

    fun createOrUpdateVehicle(createVehicleDto: CreateVehicleDTO): VehicleDTO
}
