/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.CreateVehicleDTO

interface CreateVehicle {
    /**
     * Will create given vehicle.
     * This is considered a manual creation and will publish a ManualVehicleCreatedEvent.
     * This will lead to automatic (empty) PDI creation but will NOT create you an initial PlannedVehicleTransfer.
     *
     * @throws VehicleAlreadyExists if vehicle alreay exists for given VIN
     */
    fun manuallyCreateVehicle(createVehicleDto: CreateVehicleDTO)
}
