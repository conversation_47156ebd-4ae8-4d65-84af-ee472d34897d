package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleOptionTagDTO

interface CreateVehicleOptionTag {
    /**
     * Creates new option tag in FVM system and flags it as nonCustomerAdequate
     * If the option with optionId already available in system then the existing option will
     * be flagged as nonCustomerAdequate
     */
    fun createNonCustomerAdequateOptionTag(optionId: String): VehicleOptionTagDTO
}
