package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.ImportVehicleDto
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO

interface VehicleImportInterface {
    fun importVehicle(
        importVehicleDto: ImportVehicleDto,
        importingUsername: String,
    ): VehicleDTO

    fun initialLoadVehicle(vin: String): VehicleDTO
}

class VehicleAlreadyExists(
    override val message: String?,
    val properties: Map<String, String>? = emptyMap(),
) : RuntimeException() {
    companion object {
        const val KEY_PRODUCTION_NUMBER = "production.number"
        const val KEY_MODEL_YEAR = "model.year"
    }
}
