package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

/**
 * API for reading all vehicles with pagination support.
 */
interface ReadAllVehicles {
    /**
     * Fetches all vehicles with pagination support.
     *
     * @param pageable the pagination information
     * @return a page of VehicleDTOs
     */
    fun readAllVehicles(pageable: Pageable): Page<VehicleDTO>
}
