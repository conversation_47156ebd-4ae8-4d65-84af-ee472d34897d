/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingDTO
import java.util.UUID

fun interface ReadMileageReadings {
    /**
     * Returns all mileage readings for vehicle
     */
    fun readMileageReadings(vehicleId: UUID): List<MileageReadingDTO>
}
