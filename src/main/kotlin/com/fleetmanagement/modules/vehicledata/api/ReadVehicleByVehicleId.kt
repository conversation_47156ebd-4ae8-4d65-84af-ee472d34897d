package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import java.util.UUID

interface ReadVehicleByVehicleId {
    /**
     * Retrieves the vehicle information for the given vehicle ID.
     *
     * This method takes a UUID representing the ID of the vehicle
     * and attempts to find and return the corresponding vehicle details
     * encapsulated in a `VehicleDTO` object. If no vehicle
     * with the given ID is found, the method returns `null`.
     *
     * @param id The unique identifier (UUID) of the vehicle to be retrieved.
     * @return A `VehicleDTO` object containing the vehicle details if found,
     *         or `null` if
     *         no vehicle with the given ID exists.
     */
    fun readVehicleById(id: UUID): VehicleDTO?

    fun readVehiclesByIds(ids: List<UUID>): List<VehicleDTO>
}
