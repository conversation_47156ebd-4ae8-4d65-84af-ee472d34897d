/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleOptionTagDTO
import java.util.*

interface ReadVehicleOptionTags {
    fun readAll(): List<VehicleOptionTagDTO>

    fun readAllNonCustomerAdequateOptionTags(): List<VehicleOptionTagDTO>
}
