package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable

interface ReadVehiclesByOptionTagId {
    /**
     * Retrieves all the vehicles those are having given option tag id.
     *
     * This method takes text representing the ID of the vehicle option tag
     * and attempts to find and return the corresponding vehicles
     * encapsulated in a `VehicleDTO` object. If no vehicle
     * with the given option tag ID is found, the method returns empty list.
     *
     * @param optionTagId The identifier of the vehicle option tag.
     * @return Page of `VehicleDTO` object containing the vehicle details if found,
     *         or empty list if
     *         no vehicle with the given option tag ID exists.
     */
    fun readAllVehiclesByOptionTagId(
        optionTagId: String,
        pageable: Pageable,
    ): Page<VehicleDTO>
}
