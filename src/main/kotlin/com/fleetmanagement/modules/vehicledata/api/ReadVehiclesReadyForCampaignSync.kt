package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import java.util.UUID

interface ReadVehiclesReadyForCampaignSync {
    /**
     * Retrieves the vehicles ready for campaign sync for the given vehicle IDs.
     *
     * This method takes multiple UUIDs representing the ID of the vehicles
     * and attempts to find and return the corresponding vehicle-details
     * encapsulated in a `List<VehicleDTO>` object. If no vehicle
     * with the given ID is found, the response does not contain data
     * for the vehicle
     *
     * @param ids List of unique identifiers (UUID) of the vehicle to be retrieved.
     * @return A `List<VehicleDTO>` object containing the vehicle details if found,
     */
    fun readVehiclesReadyForCampaignSyncByIds(ids: List<UUID>): List<VehicleDTO>
}
