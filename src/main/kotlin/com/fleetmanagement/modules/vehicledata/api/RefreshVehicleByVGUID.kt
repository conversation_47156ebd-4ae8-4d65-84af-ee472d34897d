/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO

interface RefreshVehicleByVGUID {
    /**
     * Refreshes the vehicle information from source system and retrieves it for the given VGUID.
     *
     * This method takes a string representing the VGUID of the vehicle
     * and attempts to refresh with source system and return the updated vehicle details
     * encapsulated in a `VehicleDTO` object. If no vehicle
     * with the given ID is found, the method returns `null`.
     *
     * @param id The string identifier (VGUID) of the vehicle to be refreshed.
     * @return A `VehicleDTO` object containing the vehicle details if found,
     *         or `null` if
     *         no vehicle with the given VGUID exists.
     */
    fun refreshVehicleByVGUID(vguid: String): VehicleDTO
}
