/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.api

import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleInit
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZonedDateTime
import java.util.*

interface UpdateVehicle {
    /**
     * Currently lacking a better name, this function will 'initialize' given vehicle
     * with data from matching consignee.
     * Normally only called once during vehicle creation.
     */
    fun initialize(
        id: VehicleId,
        initData: VehicleInit,
    )

    /**
     * Will update [com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity] identified by given id.
     * Update is currently only allowed for administrators. Not being part of any administrator group will skip update.
     *
     * No version-check will be performed using this function, so only use it for DLZ based updates right now (see FPT1-1023 for details).
     *
     * @throws VehicleNotFoundException in case vehicle can not be found
     * @throws VehicleUpdateException in case the vehicle is not updated for any reason
     */
    fun updateVehicle(
        vehicleId: UUID,
        vehicleUpdateDto: VehicleUpdateDto,
        modifier: String = "VehicleUpdate",
    )

    /**
     * Will update [com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity] identified by given vin.
     *
     * This method is intended to be used during migration. No side-effects are triggered (e.g. no domain events)
     * Domain validations still apply.
     *
     * @throws VehicleNotFoundException in case vehicle can not be found
     */
    fun updateMigrationVehicle(
        vin: String,
        vehicleUpdateDto: MigrationVehicleUpdateDto,
    )

    /**
     * Will uodate VTStamm owned properties on a vehicle. Should only be used by VTStamm adapters.
     *
     * @param the name to put in history entries
     *
     * @throws VehicleUpdateException in case the vehicle is not updated for any reason
     * @throws VehicleNotFoundException in case vehicle can not be found
     */
    fun updateVTStammVehicle(
        vin: String,
        vehicleUpdateDto: VTStammVehicleUpdateDto,
        modifier: String,
    )

    /**
     * Will update [com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity] identified by given vin.
     *
     * This method is intended to be used during migration. No side-effects are triggered (e.g. no domain events)
     * Domain validations still apply.
     *
     * @throws VehicleNotFoundException in case vehicle can not be found
     */
    fun hotfixProductionEndDate(
        vin: String,
        productionEndDate: OffsetDateTime,
    )
}

fun interface UpdateVehicleStatus {
    fun updateVehicleStatus(
        vehicleId: UUID,
        vehicleStatus: VehicleStatus,
    )
}

fun interface UpdateVehicleFactoryCarPreparationOrderNumber {
    fun updateVehicleFactoryCarPreparationOrderNumber(
        vehicleId: UUID,
        factoryCarPreparationOrderNumber: String,
    )
}

fun interface UpdateCostEstimationOrderedDate {
    fun updateCostEstimationOrderedDate(
        vehicleIds: List<UUID>,
        costEstimationOrderedDate: OffsetDateTime,
    )
}

fun interface UpdateVehicleBlockedForSale {
    /**
     * Will update the Vehicle whether it is blocked for sale or not.
     * @param vehicleId UUID of the vehicle that need to be updated
     * @param blockedForSale flag denotes whether vehicle is blocked from sales or not
     * @param source denotes the ownership who is setting the flag (manual, non-customer adequate, etc.)
     */
    fun updateVehicleBlockedForSale(
        vehicleId: UUID,
        blockedForSale: Boolean,
        source: String,
    )
}

fun interface UpdateVehicleNextProcess {
    /**
     * Will set new nextProcess value to the given vehicle.
     * Call this before trying to trigger a vehicle transfer return process, as proper nextProcess is required for the return to succeed.
     * @param vehicleId UUID of the vehicle that needs to be updated
     * @param nextProcess nextProcess value to be set
     */
    fun updateNextProcess(
        vehicleId: UUID,
        nextProcess: NextProcess,
    )
}

/**
 * used to update financial asset type for an existing FVM vehicle
 */
fun interface UpdateVehicleFinancialAssetType {
    fun updateVehicleFinancialAssetType(
        vehicleId: UUID,
        financialAssetType: FinancialAssetType,
    )
}

/**
 * used to sync financial asset type for an existing FVM vehicle or unknown vehicle
 */
fun interface UpdateFinancialAssetType {
    fun updateFinancialAssetType(
        vin: String,
        financialAssetType: FinancialAssetType,
    )
}

fun interface UpdateVehicleSoldDate {
    /**
     * Will update the vehicle sold date when the invoice is completed.
     * @param vehicleId UUID of the vehicle that needs to be updated
     * @param soldDate when the car has been sold
     * @param modifier the user who is modifying the vehicle
     */
    fun updateVehicleSoldDate(
        vehicleId: UUID,
        soldDate: OffsetDateTime?,
        modifier: String,
    )
}

fun interface ResetStolenDate {
    fun resetStolenDate(vehicleId: UUID)
}

fun interface ResetApprovedForScrappingDate {
    fun resetApprovedForScrappingDate(vehicleId: UUID)
}

fun interface ResetPreparationDoneDate {
    fun resetPreparationDoneDate(vehicleId: UUID)
}

fun interface ResetCostEstimationOrderedDate {
    fun resetCostEstimationOrderedDate(vehicleId: UUID)
}

fun interface ResetIsResidualValueMarket {
    fun resetIsResidualValueMarket(vehicleId: UUID)
}

fun interface ResetProfitabilityAuditDate {
    fun resetProfitabilityAuditDate(vehicleId: UUID)
}

object BlockedForSaleSource {
    /**
     * used when blocked for sale flag updated for a vehicle during non-customer adequate vehicle check
     */
    const val NON_CUSTOMER_ADEQUATE_CHECK = "NON_CUSTOMER_ADEQUATE"

    /**
     * used when blocked for sale flag updated for a vehicle during consignee data initialization
     */
    const val CONSIGNEE_DATA = "CONSIGNEE_DATA"

    /**
     * used when blocked for sale flag updated for a vehicle during updates from UI
     */
    const val MANUAL_UPDATE = "MANUAL"
}

data class VehicleUpdateDto(
    val referenceId: Optional<String>? = null,
    val purchaseOrderDate: Optional<Date>? = null,
    val requestedDeliveryDate: Optional<Date>? = null,
    val deliveryType: Optional<String>? = null,
    val primaryStatus: Optional<String>? = null,
    val preproductionVehicle: Optional<Boolean>? = null,
    val blockedForSale: Optional<Boolean>? = null,
    val scrapVehicle: Optional<Boolean>? = null,
    val primaryFuelType: Optional<FuelType>? = null,
    val secondaryFuelType: Optional<FuelType>? = null,
    val currentTires: Optional<TireSet>? = null,
    val externalLeaseStart: Optional<ZonedDateTime>? = null,
    val externalLeaseEnd: Optional<ZonedDateTime>? = null,
    val externalLeaseLessee: Optional<String>? = null,
    val externalLeaseRate: Optional<Float>? = null,
    val amountSeats: Optional<Int>? = null,
    val engineCapacity: Optional<Float>? = null,
    val netPriceWithExtras: Optional<BigDecimal>? = null,
    val soldDate: Optional<OffsetDateTime>? = null,
    val scrappedDate: Optional<OffsetDateTime>? = null,
    val stolenDate: Optional<OffsetDateTime>? = null,
    val soldCupCarDate: Optional<OffsetDateTime>? = null,
    val approvedForScrappingDate: Optional<OffsetDateTime>? = null,
    val scrappedVehicleOfferedDate: Optional<OffsetDateTime>? = null,
    val vehicleSentToSalesDate: Optional<OffsetDateTime>? = null,
    val costEstimationOrderedDate: Optional<OffsetDateTime>? = null,
    val isResidualValueMarket: Optional<Boolean>? = null,
    val profitabilityAuditDate: Optional<OffsetDateTime>? = null,
    val comment: Optional<String>? = null,
    val preparationDoneDate: Optional<OffsetDateTime>? = null,
    val isPreparationNecessary: Optional<Boolean>? = null,
    val nextProcess: Optional<NextProcess>? = null,
    val tireSetChangeCompletedDate: Optional<OffsetDateTime>? = null,
    val tireSetChangeOrderedDate: Optional<OffsetDateTime>? = null,
    val tireSetChangeComment: Optional<String>? = null,
    val vehicleEvaluationComment: Optional<String>? = null,
    val pcComplaintCheckComment: Optional<String>? = null,
    val appraisalNetPrice: Optional<BigDecimal>? = null,
    val currentMileage: Optional<Int>? = null,
    val currentMileageReadDate: Optional<OffsetDateTime>? = null,
    val keyReturned: Optional<OffsetDateTime>? = null,
    val tuevAppointment: Optional<LocalDate>? = null,
    val modelDescription: Optional<String>? = null,
    val orderType: Optional<String>? = null,
    val countryInfo: Optional<String>? = null,
    val exteriorColor: Optional<String>? = null,
    val interiorColor: Optional<String>? = null,
    val productionEndDate: Optional<OffsetDateTime>? = null,
    val raceCar: Optional<Boolean>? = null,
    val classic: Optional<Boolean>? = null,
)

data class VTStammVehicleUpdateDto(
    val equipmentNumber: Long,
    val tuevAppointment: Optional<LocalDate>? = null,
    val internalDesignation: Optional<String>? = null,
    val internalVehicleDescription: Optional<String>? = null,
    val subjectToConfidentiality: Optional<Boolean>? = null,
    val confidentialityClassification: Optional<String>? = null,
    val subjectToConfidentialityStartDate: Optional<LocalDate>? = null,
    val subjectToConfidentialityEndDate: Optional<LocalDate>? = null,
    val recordFactoryExit: Optional<Boolean>? = null,
    val camouflageRequired: Optional<Boolean>? = null,
    val rangeDevelopment: Optional<String>? = null,
    val modelDescriptionDevelopment: Optional<String>? = null,
    val typeOfUseVTS: Optional<String>? = null,
    val statusVTS: Optional<String>? = null,
)

data class MigrationVehicleUpdateDto(
    val blockedForSale: Optional<Boolean>?, // blockedForSale
    val scrapVehicle: Optional<Boolean>?, // scrapVehicle
    val assetType: Optional<FinancialAssetType>?, // assetType
    val netPriceWithExtras: Optional<BigDecimal>?, // netPriceWithExtras
    val soldDate: Optional<OffsetDateTime>?, // soldDate
    val scrappedDate: Optional<OffsetDateTime>?, // scrappedDate
    val stolenDate: Optional<OffsetDateTime>?, // stolenDate
    val soldCupCarDate: Optional<OffsetDateTime>?, // soldCupCarDate
    val approvedForScrappingDate: Optional<OffsetDateTime>?, // approvedForScrappingDate
    val scrappedVehicleOfferedDate: Optional<OffsetDateTime>?, // scrappedVehicleOfferedDate
    val vehicleSentToSalesDate: Optional<OffsetDateTime>?, // vehicleSentToSalesDate
    val costEstimationOrderedDate: Optional<OffsetDateTime>?, // costEstimationOrderedDate
    val isResidualValueMarket: Optional<Boolean>?, // isResidualValueMarket
    val profitabilityAuditDate: Optional<OffsetDateTime>?, // profitabilityAuditDate
    val preparationDoneDate: Optional<OffsetDateTime>?, // preparationDoneDate
    val isPreparationNecessary: Optional<Boolean>?, // isPreparationNecessary
    val nextProcess: Optional<NextProcess>?, // nextProcess
    val appraisalNetPrice: Optional<BigDecimal>?, // Nettobetrag Gutachten
    val factoryCarPreparationOrderNumber: Optional<String>?,
    val isUsedCar: Optional<Boolean>?,
    val maximumServiceLifeInMonths: Optional<Int>?,
    val isClassic: Optional<Boolean>,
    val equipmentNumber: Optional<Long>?,
    val equipmentId: Optional<String>?,
    val zp8Date: Optional<OffsetDateTime>?,
    val newVehicleInvoiceDate: Optional<LocalDate>?,
)
