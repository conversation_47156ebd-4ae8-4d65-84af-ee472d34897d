/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.domain

import org.slf4j.LoggerFactory

class Department(
    private val referenceId: String,
) {
    private val logger = LoggerFactory.getLogger(Department::class.java)

    fun asCommaSeparatedList(): String =
        consigneeDepartments
            .filter { it.first == referenceId }
            .map { it.second }
            .distinct()
            .joinToString()

    companion object {
        @SuppressWarnings("kotlin:S1192") // favor readability since this is a look up
        private val consigneeDepartments =
            listOf(
                "1004000" to "",
                "1001020" to "Erprobung",
                "1001030" to "Erprobung",
                "1001050" to "Erprobung",
                "1001070" to "Erprobung",
                "1009020" to "Erprobung",
                "1003000" to "",
                "1001010" to "",
                "1003101" to "BRZ",
                "1003105" to "EWM",
                "1003106" to "EL",
                "1003107" to "EM",
                "1003111" to "GOP",
                "1003112" to "GOP",
                "1003113" to "GQF",
                "1003114" to "GQQ",
                "1003116" to "MEM",
                "1003117" to "MUD",
                "1003121" to "PCon",
                "1003122" to "PD",
                "1003123" to "PEG",
                "1003124" to "PES",
                "1003126" to "PLG",
                "1003127" to "PLog",
                "1003131" to "PZQ Crash",
                "1003134" to "V4",
                "1003135" to "V5",
                "1003136" to "VAM, VAQ",
                "1003137" to "VAS, VAR, VAB",
                "1003138" to "VAT",
                "1003140" to "VIF",
                "1003141" to "VIW",
                "1003144" to "VME",
                "1003145" to "VME",
                "1003146" to "VME",
                "1003147" to "VME",
                "1003148" to "VMM",
                "1003149" to "VPM",
                "1003150" to "VPV1",
                "1003151" to "VPV2",
                "1003152" to "VPV3",
                "1003153" to "VPV4",
                "1003155" to "PFS-VR",
                "1003156" to "GQP",
                "1003200" to "GO",
                "1003201" to "VMP",
                "1003202" to "EG - BE",
                "1003203" to "VME",
                "1003204" to "",
                "1003205" to "GQ",
                "1003206" to "VPQ",
                "1003300" to "",
                "1001046" to "ToGe Leasing",
                "1001086" to "ToGe Leasing",
                "1001047" to "ToGe Leasing",
                "1001087" to "ToGe Leasing",
                "1001048" to "ToGe Leasing",
                "1001088" to "ToGe Leasing",
                "1001090" to "",
                "1003501" to "BRZ",
                "1003505" to "EVG - Serie",
                "1003506" to "EM",
                "1003507" to "EMT",
                "1003511" to "GOU",
                "1003512" to "GOU",
                "1003513" to "GQI",
                "1003514" to "GQQ",
                "1003516" to "MMK",
                "1003517" to "MUD",
                "1003521" to "",
                "1003522" to "PD",
                "1003523" to "PEG",
                "1003524" to "PES",
                "1003526" to "PLG",
                "1003527" to "Plog",
                "1003531" to "PZQ Crash",
                "1003534" to "V4",
                "1003535" to "V5",
                "1003536" to "VAN, VAQ",
                "1003537" to "VAS, VAR, VAB",
                "1003538" to "VAT",
                "1003540" to "VIM",
                "1003541" to "VIW",
                "1003544" to "VME",
                "1003545" to "VME",
                "1003546" to "VME",
                "1003547" to "VME",
                "1003548" to "VMB",
                "1003549" to "VPM",
                "1003550" to "VPV1",
                "1003551" to "VPV2",
                "1003552" to "VPV3",
                "1003553" to "VPV4",
                "1003554" to "EVG",
                "1003555" to "PFS-VR",
                "1003556" to "GQP",
                "1003557" to "VPV2",
                "1003600" to "GO",
                "1003601" to "VM HEV",
                "1003602" to "EG - BE",
                "1003603" to "VME",
                "1003604" to "",
                "1003605" to "GQG",
                "1003606" to "VMQ",
                "1003700" to "",
                "1001041" to "ToGe Leasing",
                "1001081" to "ToGe Leasing",
                "1001042" to "ToGe Leasing",
                "1001082" to "ToGe Leasing",
                "1001043" to "ToGe Leasing",
                "1001083" to "ToGe Leasing",
                "1001045" to "DLF",
                "1001040" to "DLF",
                "1001065" to "DLF",
                "1001060" to "DLF",
                "1001085" to "DLF",
                "1001080" to "DLF",
            )
    }
}
