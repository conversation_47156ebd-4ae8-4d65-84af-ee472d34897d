/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.domain

class LeasingType(
    val referenceId: String,
) {
    fun asCommaSeparatedList(): String =
        consigneeLeasingTypes
            .filter { it.first == referenceId }
            .map { it.second }
            .distinct()
            .joinToString()

    companion object {
        val consigneeLeasingTypes =
            listOf(
                "1004000" to "AF",
                "1001020" to "AF",
                "1001030" to "AF",
                "1001050" to "AF",
                "1001070" to "AF",
                "1009020" to "AF",
                "1003000" to "AF",
                "1001010" to "AF",
                "1003101" to "AF",
                "1003105" to "AF",
                "1003106" to "AF",
                "1003107" to "AF",
                "1003111" to "AF",
                "1003112" to "AF",
                "1003113" to "AF",
                "1003114" to "AF",
                "1003116" to "AF",
                "1003117" to "AF",
                "1003121" to "AF",
                "1003122" to "AF",
                "1003123" to "AF",
                "1003124" to "AF",
                "1003126" to "AF",
                "1003127" to "AF",
                "1003131" to "AF",
                "1003134" to "AF",
                "1003135" to "AF",
                "1003136" to "AF",
                "1003137" to "AF",
                "1003138" to "AF",
                "1003140" to "AF",
                "1003141" to "AF",
                "1003144" to "AF",
                "1003145" to "AF",
                "1003146" to "AF",
                "1003147" to "AF",
                "1003148" to "AF",
                "1003149" to "AF",
                "1003150" to "AF",
                "1003151" to "AF",
                "1003152" to "AF",
                "1003153" to "AF",
                "1003155" to "AF",
                "1003156" to "AF",
                "1003200" to "AF",
                "1003201" to "AF",
                "1003202" to "AF",
                "1003203" to "AF",
                "1003204" to "AF",
                "1003205" to "AF",
                "1003206" to "AF",
                "1003300" to "AF",
                "1001046" to "DLF",
                "1001086" to "DLF",
                "1001047" to "DLF",
                "1001087" to "DLF",
                "1001048" to "DLF",
                "1001088" to "DLF",
                "1001090" to "AF",
                "1003501" to "AF",
                "1003505" to "AF",
                "1003506" to "AF",
                "1003507" to "AF",
                "1003511" to "AF",
                "1003512" to "AF",
                "1003513" to "AF",
                "1003514" to "AF",
                "1003516" to "AF",
                "1003517" to "AF",
                "1003521" to "AF",
                "1003522" to "AF",
                "1003523" to "AF",
                "1003524" to "AF",
                "1003526" to "AF",
                "1003527" to "AF",
                "1003531" to "AF",
                "1003534" to "AF",
                "1003535" to "AF",
                "1003536" to "AF",
                "1003537" to "AF",
                "1003538" to "AF",
                "1003540" to "AF",
                "1003541" to "AF",
                "1003544" to "AF",
                "1003545" to "AF",
                "1003546" to "AF",
                "1003547" to "AF",
                "1003548" to "AF",
                "1003549" to "AF",
                "1003550" to "AF",
                "1003551" to "AF",
                "1003552" to "AF",
                "1003553" to "AF",
                "1003554" to "AF",
                "1003555" to "AF",
                "1003556" to "AF",
                "1003557" to "AF",
                "1003600" to "AF",
                "1003601" to "AF",
                "1003602" to "AF",
                "1003603" to "AF",
                "1003604" to "AF",
                "1003605" to "AF",
                "1003606" to "AF",
                "1003700" to "AF",
                "1001041" to "DLF",
                "1001081" to "DLF",
                "1001042" to "DLF",
                "1001082" to "DLF",
                "1001043" to "DLF",
                "1001083" to "DLF",
                "1001045" to "DLF",
                "1001040" to "DLF",
                "1001065" to "DLF",
                "1001060" to "DLF",
                "1001085" to "DLF",
                "1001080" to "DLF",
            )
    }
}
