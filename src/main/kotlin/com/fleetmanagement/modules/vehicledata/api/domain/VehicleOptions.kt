package com.fleetmanagement.modules.vehicledata.api.domain

import com.fasterxml.jackson.databind.JsonNode
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleOptionDTO

class VehicleOptions(
    private val options: <PERSON>sonNode,
) {
    fun getOptionUsingFamily(family: String): VehicleOptionDTO? {
        val individualOptions =
            options
                .path("current")
                .path("individualOptions")
        check(individualOptions.isArray)

        val optionWithFamily =
            individualOptions.firstOrNull { option ->
                option.path("family").asText() == family
            }
        return optionWithFamily?.let {
            VehicleOptionDTO(
                optionCode = it.path("id")?.asText(),
                optionDescription = it.path("optionDescription")?.path("description")?.asText(),
            )
        }
    }

    fun getOptionUsingId(id: String): VehicleOptionDTO? {
        val individualOptions =
            options
                .path("current")
                .path("individualOptions")
        check(individualOptions.isArray)

        val optionWithId =
            individualOptions.firstOrNull { option ->
                option.path("id").asText() == id
            }
        return optionWithId?.let {
            VehicleOptionDTO(
                optionCode = it.path("id")?.asText(),
                optionDescription = it.path("optionDescription")?.path("description")?.asText(),
            )
        }
    }
}
