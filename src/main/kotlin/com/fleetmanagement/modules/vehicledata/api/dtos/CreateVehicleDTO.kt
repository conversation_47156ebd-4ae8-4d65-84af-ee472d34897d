/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.vehicledata.api.domain.*
import com.fleetmanagement.modules.vehicledata.repository.entities.*
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.*

data class CreateVehicleDTO(
    val vin: String,
    val manufacturer: String,
    // also mislabeled as equiId
    val internalVehicleDescription: String? = null,
    val modelDescription: String? = null,
    val modelDescriptionDevelopment: String? = null,
    val internalDesignation: String? = null,
    val vehicleType: VehicleType,
    val source: VehicleSource,
    val orderType: String? = null,
    val financialAssetType: FinancialAssetType? = null,
    val equipmentNumber: Long? = null,
    val amountSeats: String? = null,
    val engineCapacity: Int? = null,
    val licencePlate: String? = null,
    val registrationDate: ZonedDateTime? = null,
    val technicalModelYear: String? = null,
    val primaryFuelType: FuelType? = null,
    val currentTires: TireSet? = null,
    val secondaryFuelType: FuelType? = null,
    val netPriceWithExtras: String? = null,
    val inEmbargo: Boolean? = null,
    val colorExterior: String? = null,
    val colorExteriorDescription: String? = null,
    val colorInterior: String? = null,
    val pmpDataOdometer: String? = null,
    val enginePower: String? = null,
    val pmpDataOdometerDate: Date? = null,
    val blockedForSale: Boolean? = null,
    val scrappedDate: OffsetDateTime? = null,
    val scrapVehicle: Boolean? = null,
    val driveType: String? = null,
    val productionEndDate: LocalDate? = null,
    // countryInfo
    val cnrValue: String? = null,
    val tuevAppointment: LocalDate? = null,
    val modelRangeDevelopment: String? = null,
    // only used by vtstamm
    val subjectToConfidentiality: Boolean? = null,
    val confidentialityClassification: String? = null,
    val subjectToConfidentialityStartDate: LocalDate? = null,
    val subjectToConfidentialityEndDate: LocalDate? = null,
    val recordFactoryExit: Boolean? = null,
    val camouflageRequired: Boolean? = null,
    val typeOfUseVTS: String? = null,
    val statusVTS: String? = null,
)

fun CreateVehicleDTO.toJPAVehicleEntity() =
    JPAVehicleEntity(
        source = this.source,
        vin = this.vin,
        equipmentNumber = this.equipmentNumber,
        equiId = internalVehicleDescription,
        tuevAppointment = tuevAppointment,
        modelInfo =
            JPAModelInfo(
                modelDescription = this.modelDescription,
                orderType = this.orderType,
                modelDescriptionDevelopment = this.modelDescriptionDevelopment,
                vehicleType = this.vehicleType,
                rangeDevelopment = this.modelRangeDevelopment,
            ).apply { manufacturer = <EMAIL> },
        technicalInfo =
            JPATechnicalInfo(
                amountSeats = this.amountSeats?.toInt(),
                engineCapacity = this.engineCapacity?.toFloat(),
                powerkw = this.enginePower?.toInt(),
            ),
        consumptionInfo =
            JPAConsumptionInfo(
                primaryFuelType = this.primaryFuelType,
                secondaryFuelType = this.secondaryFuelType,
                driveType = this.driveType,
            ),
        productionInfo =
            JPAProductionInfo(
                technicalModelYear = this.technicalModelYear?.toInt(),
                productionEndDate =
                    this.productionEndDate?.let {
                        Date.from(it.atStartOfDay(ZoneId.of("UTC")).toInstant())
                    },
            ),
        priceInfo =
            JPAPriceInfo().apply {
                this.updateNetPriceWithExtras(
                    netPriceWithExtras =
                        <EMAIL>?.let {
                            BigDecimal(it)
                        },
                )
            },
        pmpData = JPAPmpData(odometer = this.pmpDataOdometer?.toInt(), timestamp = this.pmpDataOdometerDate),
        currentTires = this.currentTires,
        fleetInfo =
            JPAFleetInfo(
                scrappedDate = this.scrappedDate,
                scrapVehicle = this.scrapVehicle,
            ),
        orderInfo = JPAOrderInfo(blockedForSale = this.blockedForSale),
        colorInfo =
            JPAColorInfo(
                exterior = this.colorExterior,
                exteriorDescription = this.colorExteriorDescription,
                interior = this.colorInterior,
            ),
        embargoInfo = JPAEmbargoInfo(inEmbargo = this.inEmbargo),
        countryInfo =
            JPACountryInfo(
                cnrValue = this.cnrValue,
            ),
        // only used by vtstamm
        vtstammInfo =
            JPAVTStammInfo(
                subjectToConfidentialityEndDate = this.subjectToConfidentialityEndDate,
                subjectToConfidentialityStartDate = this.subjectToConfidentialityStartDate,
                subjectToConfidentiality = this.subjectToConfidentiality,
                confidentialityClassification = this.confidentialityClassification,
                recordFactoryExit = this.recordFactoryExit,
                camouflageRequired = this.camouflageRequired,
                internalDesignation = internalDesignation,
                typeOfUseVTS = this.typeOfUseVTS,
                statusVTS = this.statusVTS,
            ),
    ).apply {
        financialAssetType = <EMAIL>
    }
