/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

import java.time.OffsetDateTime

data class FleetInfoDTO(
    val soldDate: OffsetDateTime?,
    val scrappedDate: OffsetDateTime?,
    val stolenDate: OffsetDateTime?,
    val soldCupCarDate: OffsetDateTime?,
    val approvedForScrappingDate: OffsetDateTime?,
    val scrappedVehicleOfferedDate: OffsetDateTime?,
    val vehicleSentToSalesDate: OffsetDateTime?,
    val costEstimationOrderedDate: OffsetDateTime?,
    val isResidualValueMarket: Boolean?,
    val profitabilityAuditDate: OffsetDateTime?,
    val scrapVehicle: Boolean?,
    val comment: String?,
    val isClassic: Boolean,
    val raceCar: Boolean,
)
