/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.api.dtos

import java.time.OffsetDateTime

data class MileageReadingDTO(
    val mileage: Int,
    val readDate: OffsetDateTime,
    val source: MileageReadingSource,
    val createdBy: String?,
)

enum class MileageReadingSource {
    CAR_SYNC,
    MANUAL,
    DELIVERY,
    RETURN,
    FMS,
}
