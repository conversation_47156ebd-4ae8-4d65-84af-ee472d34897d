/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

import com.fleetmanagement.emhshared.domain.VehicleType

data class ModelDTO(
    val description: String?, // eg: Macan,
    val year: Int?,
    val productId: String?,
    val productCode: String?,
    val orderType: String?,
    val vehicleType: VehicleType?,
    val manufacturer: String?,
    val range: String?,
    val rangeDevelopment: String?,
    val modelDescriptionDevelopment: String?,
) {
    fun hasRangeBoxster(): Boolean {
        if (range == null) return false
        return range.contains("Boxster", ignoreCase = true)
    }
}

fun ModelDTO?.isPorsche() = this != null && this.manufacturer == "PORSCHE"
