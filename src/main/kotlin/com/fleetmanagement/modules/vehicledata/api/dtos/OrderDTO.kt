/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

import java.util.*

data class OrderDTO(
    val department: String?,
    val tradingPartnerNumber: String? = null,
    val purposeOrderType: String? = null,
    val importerShortName: String? = null,
    val portCode: String? = null,
    val commissionNumber: String? = null,
    val invoiceNumber: String? = null,
    val invoiceDate: Date? = null,
    val purchaseOrderDate: Date? = null,
    val requestedDeliveryDate: Date? = null,
    val customerDeliveryDate: Date? = null,
    val deliveryType: String? = null,
    val primaryStatus: String? = null,
    val leasingType: String?,
    val preproductionVehicle: Boolean? = null,
    val blockedForSale: Boolean? = null,
    val blockedForSaleSource: String? = null,
)
