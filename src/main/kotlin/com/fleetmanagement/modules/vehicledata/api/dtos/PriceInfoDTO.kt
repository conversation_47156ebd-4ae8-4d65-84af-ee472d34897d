/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

import com.fasterxml.jackson.databind.JsonNode
import java.math.BigDecimal

data class PriceInfoDTO(
    val grossPrice: BigDecimal?,
    val grossPriceWithExtras: BigDecimal?,
    val grossPricePlan: BigDecimal?,
    val grossPriceNewCar: BigDecimal?,
    val netPrice: BigDecimal?,
    val netPriceWithExtras: BigDecimal?,
    val netPriceNewCar: BigDecimal?,
    val valueAddedTax: BigDecimal?,
    val factoryNetPriceEUR: BigDecimal?,
    val factoryGrossPriceEUR: BigDecimal?,
    val piaEvent: JsonNode? = null,
)
