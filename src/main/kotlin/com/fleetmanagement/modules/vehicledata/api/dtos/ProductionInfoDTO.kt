package com.fleetmanagement.modules.vehicledata.api.dtos

import java.util.*

data class ProductionInfoDTO(
    val number: String?,
    val numberVW: String?,
    val endDate: Date?,
    val technicalModelYear: Int?,
    val factory: String?,
    val factoryVW: Int?,
    val quoteMonth: Int?,
    val quoteYear: Int?,
    val plannedEndDate: Date?,
    val gearBoxClass: Char?,
)

fun ProductionInfoDTO?.hasZP8Date() = this != null && this.endDate != null
