/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import java.time.OffsetDateTime

data class ReturnInfoDTO(
    val nextProcess: NextProcess?,
    val isUsedCar: Boolean?,
    val keyReturned: OffsetDateTime?,
    val factoryCarPreparationOrderNumber: String?,
)
