/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

data class TechnicalInfoDTO(
    val amountSeats: Int?,
    val engineCapacity: Float?,
    val cargoVolume: Int? = null,
    val vehicleWidthMirrorsExtended: Int? = null,
    val maximumChargingPowerDc: Int? = null,
    val grossVehicleWeight: Int? = null,
    val curbWeightEu: Int? = null,
    val totalPowerKw: Int? = null,
    val curbWeightDin: Int? = null,
    val maximumPayload: Int? = null,
    val chargingTimeAc22Kw: Float? = null,
    val chargingTimeAc11kw0100: Float? = null,
    val chargingTimeAc96kw0100: Float? = null,
    val netBatteryCapacity: Float? = null,
    val grossBatteryCapacity: Float? = null,
    val acceleration0100Kmh: Float? = null,
    val acceleration0100KmhLaunchControl: Float? = null,
    val topSpeed: Int? = null,
    val height: Int? = null,
    val widthMirrorsFolded: Int? = null,
    val length: Int? = null,
    val acceleration80120Kmh: Float? = null,
    val maxRoofLoadWithPorscheRoofTransportSystem: Int? = null,
    val chargingTimeDcMaxPower580: Float? = null,
    val powerKw: Int? = null,
)
