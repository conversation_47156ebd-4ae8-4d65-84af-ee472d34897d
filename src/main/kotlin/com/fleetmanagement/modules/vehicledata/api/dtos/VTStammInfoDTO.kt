/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.api.dtos

import java.time.LocalDate

data class VTStammInfoDTO(
    val subjectToConfidentiality: Boolean?,
    val confidentialityClassification: String?,
    val subjectToConfidentialityStartDate: LocalDate?,
    val subjectToConfidentialityEndDate: LocalDate?,
    val recordFactoryExit: Boolean?,
    val camouflageRequired: Boolean?,
    val internalDesignation: String? = null,
    val typeOfUseVTS: String? = null,
    val statusVTS: String? = null,
)
