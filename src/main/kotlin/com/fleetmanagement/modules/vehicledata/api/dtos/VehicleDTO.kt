/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.vehicledata.api.domain.*
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAMileageReadingEntity
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.*

data class VehicleDTO(
    val id: UUID,
    val vin: String?,
    val vguid: String?,
    val equiId: String?,
    val source: VehicleSource = VehicleSource.UNKNOWN,
    val referenceId: String?,
    val equipmentNumber: Long?,
    val financialAssetType: FinancialAssetType? = null,
    val options: JsonNode?,
    val currentTires: TireSet? = TireSet.WR,
    val externalLeaseStart: ZonedDateTime? = null,
    val externalLeaseEnd: ZonedDateTime? = null,
    val externalLeaseRate: Float? = null,
    val externalLeaseLessee: String? = null,
    val tuevAppointment: LocalDate? = null,
    val status: String? = null,
    val createdAt: ZonedDateTime?,
    val model: ModelDTO?,
    val consumption: ConsumptionInfoDTO?,
    val order: OrderDTO?,
    val production: ProductionInfoDTO?,
    val country: CountryInfoDTO?,
    val pmp: PmpDataDTO?,
    val embargo: EmbargoInfoDTO?,
    val color: ColorInfoDTO?,
    val price: PriceInfoDTO?,
    val technical: TechnicalInfoDTO?,
    val fleet: FleetInfoDTO?,
    val delivery: DeliveryInfoDTO?,
    val returnInfo: ReturnInfoDTO?,
    val tireSetChange: TireSetChangeDto?,
    val wltpInfo: WltpDataDTO?,
    val evaluation: EvaluationDTO?,
    val repairfixCarId: String?,
    val currentMileage: CurrentMileageDTO?,
    val vtstamm: VTStammInfoDTO?,
) {
    fun optionCodeForRims(): VehicleOptionDTO? {
        if (options == null) return null
        return VehicleOptions(options).getOptionUsingFamily("RAD")
    }

    fun optionCodeForCentralLocking(): VehicleOptionDTO? {
        if (options == null) return null
        val vehicleOptions = VehicleOptions(options)
        return vehicleOptions.getOptionUsingId("1PJ") ?: vehicleOptions.getOptionUsingId("478")
    }

    fun optionCodeForBrakingDiscsFront(): VehicleOptionDTO? {
        if (options == null) return null
        val vehicleOptions = VehicleOptions(options)
        if (model != null && model.hasRangeBoxster()) {
            return vehicleOptions.getOptionUsingId("450")
        }
        return vehicleOptions.getOptionUsingFamily("BAV")
    }

    companion object {
        fun from(
            jpaVehicleEntity: JPAVehicleEntity,
            objectMapper: ObjectMapper,
        ): VehicleDTO =
            VehicleDTO(
                equipmentNumber = jpaVehicleEntity.equipmentNumber,
                equiId = jpaVehicleEntity.equiId,
                id = checkNotNull(jpaVehicleEntity.id) { "a vehicle from the database should have a vehicle-id" },
                vin = jpaVehicleEntity.vin,
                vguid = jpaVehicleEntity.vguid,
                source = jpaVehicleEntity.source,
                referenceId = jpaVehicleEntity.referenceId,
                status = jpaVehicleEntity.status.name,
                tuevAppointment = jpaVehicleEntity.tuevAppointment,
                model =
                    ModelDTO(
                        description = jpaVehicleEntity.modelInfo?.modelDescription,
                        orderType = jpaVehicleEntity.modelInfo?.orderType,
                        vehicleType = jpaVehicleEntity.modelInfo?.vehicleType,
                        manufacturer = jpaVehicleEntity.modelInfo?.manufacturer,
                        year = jpaVehicleEntity.modelInfo?.modelYear,
                        productId = jpaVehicleEntity.modelInfo?.productId,
                        productCode = jpaVehicleEntity.modelInfo?.productCode,
                        range = jpaVehicleEntity.modelInfo?.range,
                        rangeDevelopment = jpaVehicleEntity.modelInfo?.rangeDevelopment,
                        modelDescriptionDevelopment = jpaVehicleEntity.modelInfo?.modelDescriptionDevelopment,
                    ),
                order =
                    OrderDTO(
                        department = computedDepartmentFrom(jpaVehicleEntity)?.asCommaSeparatedList(),
                        leasingType = computedLeasingTypeFrom(jpaVehicleEntity)?.asCommaSeparatedList(),
                        tradingPartnerNumber = jpaVehicleEntity.orderInfo?.tradingPartnerNumber,
                        purposeOrderType = jpaVehicleEntity.orderInfo?.purposeOrderType,
                        importerShortName = jpaVehicleEntity.orderInfo?.importerShortName,
                        portCode = jpaVehicleEntity.orderInfo?.portCode,
                        commissionNumber = jpaVehicleEntity.orderInfo?.commissionNumber,
                        invoiceNumber = jpaVehicleEntity.orderInfo?.invoiceNumber,
                        invoiceDate = jpaVehicleEntity.orderInfo?.invoiceDate,
                        purchaseOrderDate = jpaVehicleEntity.orderInfo?.purchaseOrderDate,
                        requestedDeliveryDate = jpaVehicleEntity.orderInfo?.requestedDeliveryDate,
                        customerDeliveryDate = jpaVehicleEntity.orderInfo?.customerDeliveryDate,
                        deliveryType = jpaVehicleEntity.orderInfo?.deliveryType,
                        primaryStatus = jpaVehicleEntity.orderInfo?.primaryStatus,
                        preproductionVehicle = jpaVehicleEntity.orderInfo?.preproductionVehicle,
                        blockedForSale = jpaVehicleEntity.orderInfo?.blockedForSale,
                        blockedForSaleSource = jpaVehicleEntity.orderInfo?.blockedForSaleSource,
                    ),
                options = jpaVehicleEntity.options?.let { objectMapper.readTree(it) },
                consumption =
                    ConsumptionInfoDTO(
                        driveType = jpaVehicleEntity.consumptionInfo?.driveType,
                        typification = jpaVehicleEntity.consumptionInfo?.typification,
                        primaryFuelType = jpaVehicleEntity.consumptionInfo?.primaryFuelType,
                        secondaryFuelType = jpaVehicleEntity.consumptionInfo?.secondaryFuelType,
                    ),
                production =
                    ProductionInfoDTO(
                        number = jpaVehicleEntity.productionInfo?.productionNumber,
                        numberVW = jpaVehicleEntity.productionInfo?.productionNumberVw,
                        endDate = jpaVehicleEntity.productionInfo?.productionEndDate,
                        technicalModelYear = jpaVehicleEntity.productionInfo?.technicalModelYear,
                        factory = jpaVehicleEntity.productionInfo?.factory,
                        factoryVW = jpaVehicleEntity.productionInfo?.factoryVw,
                        quoteMonth = jpaVehicleEntity.productionInfo?.quoteMonth,
                        quoteYear = jpaVehicleEntity.productionInfo?.quoteYear,
                        plannedEndDate = jpaVehicleEntity.productionInfo?.plannedProductionEndDate,
                        gearBoxClass = jpaVehicleEntity.productionInfo?.gearBoxClass,
                    ),
                country =
                    CountryInfoDTO(
                        bnrValue = jpaVehicleEntity.countryInfo?.bnrValue,
                        cnrValue = jpaVehicleEntity.countryInfo?.cnrValue,
                        cnrCountryDescription = jpaVehicleEntity.countryInfo?.cnrCountryDescription,
                        cnrCountryDescriptionLanguage = jpaVehicleEntity.countryInfo?.cnrCountryDescriptionLanguage,
                    ),
                pmp =
                    PmpDataDTO(
                        odometer = jpaVehicleEntity.pmpData?.odometer,
                        timestamp = jpaVehicleEntity.pmpData?.timestamp,
                    ),
                embargo =
                    EmbargoInfoDTO(
                        inEmbargo = jpaVehicleEntity.embargoInfo?.inEmbargo,
                    ),
                color =
                    ColorInfoDTO(
                        exterior = jpaVehicleEntity.colorInfo?.exterior,
                        exteriorDescription = jpaVehicleEntity.colorInfo?.exteriorDescription,
                        exteriorDescriptionLanguage = jpaVehicleEntity.colorInfo?.exteriorDescriptionLanguage,
                        interiorDescriptionLanguage = jpaVehicleEntity.colorInfo?.interiorDescriptionLanguage,
                        interior = jpaVehicleEntity.colorInfo?.interior,
                        interiorDescription = jpaVehicleEntity.colorInfo?.interiorDescription,
                    ),
                createdAt = jpaVehicleEntity.createdAt,
                price =
                    PriceInfoDTO(
                        grossPrice = jpaVehicleEntity.priceInfo?.grossPrice,
                        grossPriceWithExtras = jpaVehicleEntity.priceInfo?.grossPriceWithExtras,
                        grossPricePlan = jpaVehicleEntity.priceInfo?.grossPricePlan,
                        grossPriceNewCar = jpaVehicleEntity.priceInfo?.grossPriceNewCar,
                        netPrice = jpaVehicleEntity.priceInfo?.netPrice,
                        netPriceWithExtras = jpaVehicleEntity.priceInfo?.netPriceWithExtras,
                        netPriceNewCar = jpaVehicleEntity.priceInfo?.netPriceNewCar,
                        valueAddedTax = jpaVehicleEntity.priceInfo?.valueAddedTax,
                        factoryNetPriceEUR = jpaVehicleEntity.priceInfo?.factoryNetPriceEUR,
                        factoryGrossPriceEUR = jpaVehicleEntity.priceInfo?.factoryGrossPriceEUR,
                        piaEvent = jpaVehicleEntity.priceInfo?.piaEvent?.let { objectMapper.readTree(it) },
                    ),
                technical =
                    TechnicalInfoDTO(
                        amountSeats = jpaVehicleEntity.technicalInfo?.amountSeats,
                        engineCapacity = jpaVehicleEntity.technicalInfo?.engineCapacity,
                        cargoVolume = jpaVehicleEntity.technicalInfo?.cargoVolume,
                        vehicleWidthMirrorsExtended = jpaVehicleEntity.technicalInfo?.vehicleWidthmirrorsExtended,
                        maximumChargingPowerDc = jpaVehicleEntity.technicalInfo?.maximumChargingPowerdc,
                        grossVehicleWeight = jpaVehicleEntity.technicalInfo?.grossVehicleWeight,
                        curbWeightEu = jpaVehicleEntity.technicalInfo?.curbWeightEu,
                        totalPowerKw = jpaVehicleEntity.technicalInfo?.totalPowerkw,
                        curbWeightDin = jpaVehicleEntity.technicalInfo?.curbWeightdin,
                        maximumPayload = jpaVehicleEntity.technicalInfo?.maximumPayload,
                        chargingTimeAc22Kw = jpaVehicleEntity.technicalInfo?.chargingTimeac22Kw,
                        chargingTimeAc11kw0100 = jpaVehicleEntity.technicalInfo?.chargingTimeac11kw0100,
                        chargingTimeAc96kw0100 = jpaVehicleEntity.technicalInfo?.chargingTimeac96kw0100,
                        netBatteryCapacity = jpaVehicleEntity.technicalInfo?.netBatteryCapacity,
                        grossBatteryCapacity = jpaVehicleEntity.technicalInfo?.grossBatteryCapacity,
                        acceleration0100Kmh = jpaVehicleEntity.technicalInfo?.acceleration0100Kmh,
                        acceleration0100KmhLaunchControl = jpaVehicleEntity.technicalInfo?.acceleration0100KmhlaunchControl,
                        topSpeed = jpaVehicleEntity.technicalInfo?.topSpeed,
                        height = jpaVehicleEntity.technicalInfo?.height,
                        widthMirrorsFolded = jpaVehicleEntity.technicalInfo?.widthMirrorsFolded,
                        length = jpaVehicleEntity.technicalInfo?.length,
                        acceleration80120Kmh = jpaVehicleEntity.technicalInfo?.acceleration80120Kmh,
                        maxRoofLoadWithPorscheRoofTransportSystem =
                            jpaVehicleEntity.technicalInfo
                                ?.maxRoofLoadwithPorscheRoofTransportSystem,
                        chargingTimeDcMaxPower580 = jpaVehicleEntity.technicalInfo?.chargingTimedcMaxPower580,
                        powerKw = jpaVehicleEntity.technicalInfo?.powerkw,
                    ),
                tireSetChange =
                    TireSetChangeDto(
                        comment = jpaVehicleEntity.tireSetChange?.comment,
                        orderedDate = jpaVehicleEntity.tireSetChange?.orderedDate,
                        completedDate = jpaVehicleEntity.tireSetChange?.completedDate,
                    ),
                currentTires = jpaVehicleEntity.currentTires,
                financialAssetType = jpaVehicleEntity.financialAssetType,
                externalLeaseStart = jpaVehicleEntity.externalLeaseStart,
                externalLeaseEnd = jpaVehicleEntity.externalLeaseEnd,
                externalLeaseLessee = jpaVehicleEntity.externalLeaseLessee,
                externalLeaseRate = jpaVehicleEntity.externalLeaseRate,
                fleet =
                    FleetInfoDTO(
                        soldDate = jpaVehicleEntity.fleetInfo?.soldDate,
                        scrappedDate = jpaVehicleEntity.fleetInfo?.scrappedDate,
                        stolenDate = jpaVehicleEntity.fleetInfo?.stolenDate,
                        soldCupCarDate = jpaVehicleEntity.fleetInfo?.soldCupCarDate,
                        approvedForScrappingDate = jpaVehicleEntity.fleetInfo?.approvedForScrappingDate,
                        scrappedVehicleOfferedDate = jpaVehicleEntity.fleetInfo?.scrappedVehicleOfferedDate,
                        vehicleSentToSalesDate = jpaVehicleEntity.fleetInfo?.vehicleSentToSalesDate,
                        costEstimationOrderedDate = jpaVehicleEntity.fleetInfo?.costEstimationOrderedDate,
                        isResidualValueMarket = jpaVehicleEntity.fleetInfo?.isResidualValueMarket,
                        profitabilityAuditDate = jpaVehicleEntity.fleetInfo?.profitabilityAuditDate,
                        scrapVehicle = jpaVehicleEntity.fleetInfo?.scrapVehicle,
                        comment = jpaVehicleEntity.fleetInfo?.comment,
                        isClassic = jpaVehicleEntity.fleetInfo?.isClassic ?: false,
                        raceCar = jpaVehicleEntity.fleetInfo?.raceCar ?: false,
                    ),
                delivery =
                    DeliveryInfoDTO(
                        preparationDoneDate = jpaVehicleEntity.deliveryInfo?.preparationDoneDate,
                        isPreparationNecessary = jpaVehicleEntity.deliveryInfo?.isPreparationNecessary,
                    ),
                returnInfo =
                    ReturnInfoDTO(
                        nextProcess = jpaVehicleEntity.returnInfo?.nextProcess,
                        isUsedCar = jpaVehicleEntity.returnInfo?.isUsedCar,
                        keyReturned = jpaVehicleEntity.returnInfo?.keyReturned,
                        factoryCarPreparationOrderNumber = jpaVehicleEntity.returnInfo?.factoryCarPreparationOrderNumber,
                    ),
                wltpInfo =
                    WltpDataDTO(
                        vehicleWeight = jpaVehicleEntity.wltpInfo?.vehicleWeight,
                        electricRange = jpaVehicleEntity.wltpInfo?.electricRange,
                        co2Combined = jpaVehicleEntity.wltpInfo?.co2Combined,
                        electricRangeCity = jpaVehicleEntity.wltpInfo?.electricRangeCity,
                    ),
                evaluation =
                    EvaluationDTO(
                        appraisalNetPrice = jpaVehicleEntity.evaluationInfo?.appraisalNetPrice,
                        vehicleEvaluationComment = jpaVehicleEntity.evaluationInfo?.vehicleEvaluationComment,
                        pcComplaintCheckComment = jpaVehicleEntity.evaluationInfo?.pcComplaintCheckComment,
                    ),
                repairfixCarId = jpaVehicleEntity.repairfixCarId,
                currentMileage = null,
                vtstamm =
                    VTStammInfoDTO(
                        subjectToConfidentiality = jpaVehicleEntity.vtstammInfo?.subjectToConfidentiality,
                        confidentialityClassification = jpaVehicleEntity.vtstammInfo?.confidentialityClassification,
                        subjectToConfidentialityStartDate = jpaVehicleEntity.vtstammInfo?.subjectToConfidentialityStartDate,
                        subjectToConfidentialityEndDate = jpaVehicleEntity.vtstammInfo?.subjectToConfidentialityEndDate,
                        recordFactoryExit = jpaVehicleEntity.vtstammInfo?.recordFactoryExit,
                        camouflageRequired = jpaVehicleEntity.vtstammInfo?.camouflageRequired,
                        internalDesignation = jpaVehicleEntity.vtstammInfo?.internalDesignation,
                        typeOfUseVTS = jpaVehicleEntity.vtstammInfo?.typeOfUseVTS,
                        statusVTS = jpaVehicleEntity.vtstammInfo?.statusVTS,
                    ),
            )

        private fun computedDepartmentFrom(jpaVehicleEntity: JPAVehicleEntity): Department? =
            jpaVehicleEntity.referenceId?.let { Department(it) }

        private fun computedLeasingTypeFrom(jpaVehicleEntity: JPAVehicleEntity): LeasingType? =
            jpaVehicleEntity.referenceId?.let {
                LeasingType(it)
            }
    }
}

data class VehicleInit(
    val scrapVehicle: Boolean?,
    val preproductionVehicle: Boolean?,
    val blockedForSale: Boolean?,
    val manufacturer: String?,
    val vehicleType: VehicleType?,
    val tireSet: TireSet?,
)

@JvmInline
value class VehicleId(
    val value: UUID,
)

fun JPAMileageReadingEntity.toCurrentMileageDTO() =
    CurrentMileageDTO(
        mileage = mileage,
        readDate = readDate,
    )
