/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.dtos

import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleOptionTag
import java.time.ZonedDateTime

data class VehicleOptionTagDTO(
    val optionId: String,
    val optionDescription: String?,
    val nonCustomerAdequate: <PERSON><PERSON><PERSON>,
    val createdAt: ZonedDateTime?,
) {
    companion object {
        fun from(vehicleOptionTag: JPAVehicleOptionTag): VehicleOptionTagDTO =
            VehicleOptionTagDTO(
                optionId = vehicleOptionTag.optionId,
                optionDescription = vehicleOptionTag.optionDescription,
                nonCustomerAdequate = vehicleOptionTag.nonCustomerAdequate,
                createdAt = vehicleOptionTag.createdAt,
            )
    }
}
