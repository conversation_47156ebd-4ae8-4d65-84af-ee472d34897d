/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.api.events

import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import java.io.Serializable
import java.time.OffsetDateTime
import java.util.*

/**
 * this interface should be extended for all events related to vehicle creation
 */
interface VehicleCreatedEvent : Serializable {
    val occurredOn: OffsetDateTime
    val eventId: UUID
    val vehicleId: UUID
}

/**
 * AutomaticVehicleCreatedEvent will be published when a new vehicle
 * is created automatically via PVH
 */
data class AutomaticVehicleCreatedEvent(
    override val occurredOn: OffsetDateTime,
    override val eventId: UUID,
    override val vehicleId: UUID,
    val consigneeNumber: String?,
    val requestedDeliveryDate: Date?,
) : VehicleCreatedEvent {
    companion object {
        fun from(
            vehicleId: UUID,
            vehicle: JPAVehicleEntity,
        ): AutomaticVehicleCreatedEvent =
            AutomaticVehicleCreatedEvent(
                eventId = UUID.randomUUID(),
                occurredOn = OffsetDateTime.now(),
                vehicleId = vehicleId,
                consigneeNumber = vehicle.referenceId,
                requestedDeliveryDate = vehicle.orderInfo?.requestedDeliveryDate,
            )
    }
}

data class VehicleUpdatedEvent(
    val occurredOn: OffsetDateTime = OffsetDateTime.now(),
    val eventId: UUID = UUID.randomUUID(),
    val vehicleId: UUID,
) : Serializable {
    companion object {
        fun from(vehicleId: UUID): VehicleUpdatedEvent =
            VehicleUpdatedEvent(
                eventId = UUID.randomUUID(),
                occurredOn = OffsetDateTime.now(),
                vehicleId = vehicleId,
            )
    }
}

data class VehicleNextProcessChangedEvent(
    val occurredOn: OffsetDateTime = OffsetDateTime.now(),
    val eventId: UUID = UUID.randomUUID(),
    val vehicleId: UUID,
    val nextProcess: NextProcess?,
) : Serializable

data class VehicleStatusChangedEvent(
    val occurredOn: OffsetDateTime = OffsetDateTime.now(),
    val eventId: UUID = UUID.randomUUID(),
    val vehicleId: UUID,
    val status: VehicleStatus,
) : Serializable

/**
 * VehicleOptionsUpdatedEvent will be published when a vehicle is created or contains options in KAFKA event header metadata
 */
data class VehicleOptionsUpdatedEvent(
    val occurredOn: OffsetDateTime = OffsetDateTime.now(),
    val eventId: UUID = UUID.randomUUID(),
    val vehicleId: UUID,
) : Serializable

/**
 * ManualVehicleCreatedEvent will be published when a new vehicle
 * is created manually via FVM
 */
data class ManualVehicleCreatedEvent(
    override val occurredOn: OffsetDateTime = OffsetDateTime.now(),
    override val eventId: UUID = UUID.randomUUID(),
    override val vehicleId: UUID,
) : VehicleCreatedEvent
