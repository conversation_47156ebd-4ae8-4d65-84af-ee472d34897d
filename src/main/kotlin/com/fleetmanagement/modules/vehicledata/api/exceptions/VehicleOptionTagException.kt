/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.exceptions

sealed class VehicleOptionTagException(
    override val message: String,
    override val cause: Throwable? = null,
) : RuntimeException(message, cause) {
    /**
     * Exception is thrown when there is a problem while reading option tags from FVM system
     */
    class VehicleOptionTagReadException(
        override val message: String,
        override val cause: Throwable? = null,
    ) : VehicleOptionTagException(message, cause)

    /**
     * Exception is thrown when there is a problem while adding option tags to FVM system
     */
    class VehicleOptionTagCreateException(
        val optionId: String,
        override val cause: Throwable? = null,
    ) : VehicleOptionTagException("can not create option: $optionId", cause)

    /**
     * Exception is thrown when a give option tag is not found in FVM
     */
    class VehicleOptionTagNotFoundException(
        val optionId: String,
    ) : VehicleOptionTagException("can not find option: $optionId in system", null)
}
