/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.api.exceptions

import com.fleetmanagement.emhshared.domain.ConstraintViolation
import com.fleetmanagement.emhshared.domain.ViolationType
import java.util.*

data class VehicleUpdateException(
    override val message: String?,
    override val cause: Throwable? = null,
    val constraintViolation: VehicleUpdateConstraintViolation?,
) : RuntimeException()

data class VehicleUpdateConstraintViolation(
    val vehicleId: UUID? = null,
    override val type: ViolationType,
    override val propertyName: String? = null,
) : ConstraintViolation

data class VehicleUpdateNotAllowedActiveTransferExistsException(
    val propertyName: String,
    // name of the property that can not be set, due to active vehicle exists constraint
) : RuntimeException("Property [$propertyName] can not be set, as active vehicle exists.")
