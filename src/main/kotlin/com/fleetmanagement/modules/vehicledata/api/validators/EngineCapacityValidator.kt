package com.fleetmanagement.modules.vehicledata.api.validators

import com.fleetmanagement.modules.vehicledata.api.dtos.CreateUIVehicleDTO
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass

@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [EngineCapacityValidator::class])
annotation class ValidEngineCapacity(
    val type: String = "error.validation.requires.engine-capacity",
    val message: String = "EngineCapacity needs to be set if vehicleType is differ from TRAILER",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class EngineCapacityValidator : ConstraintValidator<ValidEngineCapacity, CreateUIVehicleDTO> {
    override fun isValid(
        dto: CreateUIVehicleDTO,
        context: ConstraintValidatorContext,
    ): Boolean {
        if (dto.vehicleType != "TRAILER" && dto.engineCapacity == null) {
            context.disableDefaultConstraintViolation()
            context
                .buildConstraintViolationWithTemplate("error.validation.requires.engine-capacity")
                .addPropertyNode("engineCapacity")
                .addConstraintViolation()
            return false
        }
        return true
    }
}
