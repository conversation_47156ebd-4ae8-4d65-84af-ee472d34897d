package com.fleetmanagement.modules.vehicledata.api.validators

import com.fleetmanagement.modules.vehicledata.api.dtos.CreateUIVehicleDTO
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass

@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [EnginePowerValidator::class])
annotation class ValidEnginePower(
    val type: String = "error.validation.requires.engine-power",
    val message: String = "EnginePower needs to be set if vehicleType is differ from TRAILER",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class EnginePowerValidator : ConstraintValidator<ValidEnginePower, CreateUIVehicleDTO> {
    override fun isValid(
        dto: CreateUIVehicleDTO,
        context: ConstraintValidatorContext,
    ): Boolean {
        if (dto.vehicleType != "TRAILER" && dto.enginePower == null) {
            context.disableDefaultConstraintViolation()
            context
                .buildConstraintViolationWithTemplate("error.validation.requires.engine-power")
                .addPropertyNode("enginePower")
                .addConstraintViolation()
            return false
        }
        return true
    }
}
