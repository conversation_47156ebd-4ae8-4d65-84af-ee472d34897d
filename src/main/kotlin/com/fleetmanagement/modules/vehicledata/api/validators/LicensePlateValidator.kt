package com.fleetmanagement.modules.vehicledata.api.validators

import com.fleetmanagement.modules.vehicledata.api.dtos.CreateUIVehicleDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateVehicleDTO
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass

@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [LicensePlateValidator::class])
annotation class ValidLicensePlate(
    val type: String = "error.validation.requires.licence-plate",
    val message: String = "LicensePlate is required if registration date is filled",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

private val validLicensePlateRegex = """^[A-ZÖÜÄ]{1,3}-[A-Z]{1,2}\s{0,1}[1-9]{1}[\d]{0,3}[EH]{0,1}$""".toRegex()

private fun isValidLength(licensePlate: String): Boolean {
    val stripped = licensePlate.replace("-", "").replace(" ", "")
    return stripped.length <= 8
}

class LicensePlateValidator : ConstraintValidator<ValidLicensePlate, Any> {
    override fun isValid(
        value: Any,
        context: ConstraintValidatorContext,
    ): Boolean {
        val (registrationDate, licencePlate) =
            when (value) {
                is CreateVehicleDTO -> value.registrationDate to value.licencePlate
                is CreateUIVehicleDTO -> value.registrationDate to value.licencePlate
                else -> return true
            }

        if (registrationDate != null && licencePlate == null) {
            context.disableDefaultConstraintViolation()
            context
                .buildConstraintViolationWithTemplate("error.validation.requires.licence-plate")
                .addPropertyNode("licencePlate")
                .addConstraintViolation()
            return false
        }
        if (licencePlate != null) {
            val isValid = validateLicensePlate(licencePlate)
            if (!isValid) {
                context.disableDefaultConstraintViolation()
                context
                    .buildConstraintViolationWithTemplate("error.validation.invalid.licence-plate")
                    .addPropertyNode("licencePlate")
                    .addConstraintViolation()
                return false
            }
        }
        return true
    }
}

fun validateLicensePlate(licencePlate: String): Boolean = validLicensePlateRegex.matches(licencePlate.trim()) && isValidLength(licencePlate)
