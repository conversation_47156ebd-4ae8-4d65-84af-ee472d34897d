package com.fleetmanagement.modules.vehicledata.api.validators

import com.fleetmanagement.modules.vehicledata.api.dtos.CreateUIVehicleDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateVehicleDTO
import jakarta.validation.Constraint
import jakarta.validation.ConstraintValidator
import jakarta.validation.ConstraintValidatorContext
import jakarta.validation.Payload
import kotlin.reflect.KClass

@Target(AnnotationTarget.CLASS)
@Retention(AnnotationRetention.RUNTIME)
@Constraint(validatedBy = [RegistrationDateValidator::class])
annotation class ValidRegistrationDate(
    val type: String = "error.validation.requires.registration-date",
    val message: String = "RegistrationDate is required if licence plate is filled",
    val groups: Array<KClass<*>> = [],
    val payload: Array<KClass<out Payload>> = [],
)

class RegistrationDateValidator : ConstraintValidator<ValidRegistrationDate, Any> {
    override fun isValid(
        value: Any,
        context: ConstraintValidatorContext,
    ): Boolean {
        val (licencePlate, registrationDate) =
            when (value) {
                is CreateUIVehicleDTO -> value.licencePlate to value.registrationDate
                is CreateVehicleDTO -> value.licencePlate to value.registrationDate
                else -> return true
            }

        if (licencePlate != null && registrationDate == null) {
            context.disableDefaultConstraintViolation()
            context
                .buildConstraintViolationWithTemplate("error.validation.requires.registration-date")
                .addPropertyNode("registrationDate")
                .addConstraintViolation()
            return false
        }
        return true
    }
}
