/*
* This code is protected by intellectual property rights.
* Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
* © 2024 Dr. Ing. h.c. F. Porsche AG.
*/
package com.fleetmanagement.modules.vehicledata.features.calculatevehiclestatus

import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspection
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehicletransfer.domain.entities.PlannedVehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.PlannedVehicleTransferStatus
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import org.springframework.stereotype.Component

interface StatusRule : Comparable<StatusRule> {
    // position in execution order of rules. rules are executed from lowest to highest number
    val order: Int

    // the expected vehicle status, if the rule check returns [true]
    val status: VehicleStatus

    /**
     * Runs the rule against given Vehicle and VehicleTransfer information.
     *
     * @return 'true' if the rule matches, 'false' otherwise
     */
    fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean

    override fun compareTo(other: StatusRule): Int = order.compareTo(other.order)
}

@Component
class VehicleOrderCancelledRule : StatusRule {
    override val order: Int = 1
    override val status: VehicleStatus = VehicleStatus.SX100

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has primary status FX99 or AX99
        return vehicle.orderInfo?.isOrderCancelled() ?: false
    }
}

@Component
class VehicleScrappedRule : StatusRule {
    override val order: Int = 2
    override val status: VehicleStatus = VehicleStatus.SX99

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has scrap date set
        return null != vehicle.fleetInfo?.scrappedDate
    }
}

@Component
class VehicleSoldRule : StatusRule {
    override val order: Int = 3
    override val status: VehicleStatus = VehicleStatus.SX98

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has sold date set
        return null != vehicle.fleetInfo?.soldDate
    }
}

@Component
class VehicleSoldOutsideFMSRule : StatusRule {
    override val order: Int = 4
    override val status: VehicleStatus = VehicleStatus.SX97

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has soldCup date set
        return null != vehicle.fleetInfo?.soldCupCarDate
    }
}

@Component
class VehicleStolenRule : StatusRule {
    override val order: Int = 5
    override val status: VehicleStatus = VehicleStatus.SX90

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has stolen date set
        return null != vehicle.fleetInfo?.stolenDate
    }
}

@Component
class VehicleApprovedForScrappingRule : StatusRule {
    override val order: Int = 6
    override val status: VehicleStatus = VehicleStatus.S680

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has approvedForScrapping date set marked for scrapping
        return null != vehicle.fleetInfo?.approvedForScrappingDate
    }
}

@Component
class VehicleApprovedForReuseRule : StatusRule {
    override val order: Int = 7
    override val status: VehicleStatus = VehicleStatus.S650

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has scrappedVehicleOffered date set
        return null != vehicle.fleetInfo?.scrappedVehicleOfferedDate
    }
}

@Component
class ScrapVehicleWithdrawnRule : StatusRule {
    override val order: Int = 8
    override val status: VehicleStatus = VehicleStatus.S600

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has nextProcess==scrappedCar set
        return NextProcess.SCRAPPED_CAR_RETURNED == vehicle.returnInfo?.nextProcess
    }
}

@Component
class PreparationFinishedRule : StatusRule {
    override val order: Int = 9
    override val status: VehicleStatus = VehicleStatus.S420

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has preparationDone date set
        return null != vehicle.deliveryInfo?.preparationDoneDate
    }
}

@Component
class PreparationRequiredRule : StatusRule {
    override val order: Int = 10
    override val status: VehicleStatus = VehicleStatus.S410

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has preparationRequired flag set to true
        val preparationNecessary = true == vehicle.deliveryInfo?.isPreparationNecessary
        val noActiveVehicleTransfers = vehicleTransfers.none { VehicleTransferStatus.ACTIVE == it.status }
        val noPlannedVehicleTransfers =
            plannedVehicleTransfers.none { PlannedVehicleTransferStatus.PLANNED == it.status }
        return preparationNecessary && noPlannedVehicleTransfers && noActiveVehicleTransfers
    }
}

@Component
class VehicleSentToSalesRule : StatusRule {
    override val order: Int = 11
    override val status: VehicleStatus = VehicleStatus.S400

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has vehicleSentToSales date set
        return null != vehicle.fleetInfo?.vehicleSentToSalesDate
    }
}

@Component
class CostEstimationOrderedRule : StatusRule {
    override val order: Int = 12
    override val status: VehicleStatus = VehicleStatus.S360

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has costEstimationOrdered date set
        return null != vehicle.fleetInfo?.costEstimationOrderedDate
    }
}

@Component
class VehicleMarkedForSaleRule : StatusRule {
    override val order: Int = 13
    override val status: VehicleStatus = VehicleStatus.S350

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has nextProcess==sales
        return NextProcess.SALES == vehicle.returnInfo?.nextProcess
    }
}

@Component
class ResidualValueMarketRule : StatusRule {
    override val order: Int = 14
    override val status: VehicleStatus = VehicleStatus.S340

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has residualMarketValue set
        return vehicle.fleetInfo?.isResidualValueMarket ?: false
    }
}

@Component
class ProfitabilityAuditRule : StatusRule {
    override val order: Int = 15
    override val status: VehicleStatus = VehicleStatus.S330

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has profitabilityAuditDate set
        return null != vehicle.fleetInfo?.profitabilityAuditDate
    }
}

@Component
class ProfitabilityAuditInPreparationRule : StatusRule {
    override val order: Int = 16
    override val status: VehicleStatus = VehicleStatus.S320

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has nextProcess=ProfitabilityAudit
        return NextProcess.PROFITABILITY_AUDIT_IN_PREPARATION == vehicle.returnInfo?.nextProcess
    }
}

@Component
class ReusageCheckRule : StatusRule {
    override val order: Int = 17
    override val status: VehicleStatus = VehicleStatus.S310

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has nextProcess=reusageCheck
        return NextProcess.CHECK_IF_REUSAGE_IS_POSSIBLE == vehicle.returnInfo?.nextProcess
    }
}

@Component
class VehicleReturnedRule : StatusRule {
    override val order: Int = 18
    override val status: VehicleStatus = VehicleStatus.S300

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        /**
         *  latest finished vehicle transfer of vehicle has returnDate set
         * there are no other active or planned vehicle transfers
         */
        val latestFinishedVehicleTransfer =
            vehicleTransfers
                .filter { VehicleTransferStatus.FINISHED == it.status }
                .maxByOrNull { it.created }
        val noActiveVehicleTransfers = vehicleTransfers.none { VehicleTransferStatus.ACTIVE == it.status }
        val noPlannedVehicleTransfers =
            plannedVehicleTransfers.none { PlannedVehicleTransferStatus.PLANNED == it.status }
        return null != latestFinishedVehicleTransfer?.returnDate && noActiveVehicleTransfers && noPlannedVehicleTransfers
    }
}

@Component
class VehicleKeyReturnedRule : StatusRule {
    override val order: Int = 19
    override val status: VehicleStatus = VehicleStatus.S290

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has keyReturned date set and exactly one active vehicleTransfer
        val hasActiveVehicleTransfer = 1 == vehicleTransfers.filter { VehicleTransferStatus.ACTIVE == it.status }.size
        return null != vehicle.returnInfo?.keyReturned && hasActiveVehicleTransfer
    }
}

@Component
class VehicleDeliveredRule : StatusRule {
    override val order: Int = 20
    override val status: VehicleStatus = VehicleStatus.S200

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has exactly one active VehicleTransfer with delivery date set
        val activeVehicleTransfer =
            vehicleTransfers
                .singleOrNull { VehicleTransferStatus.ACTIVE == it.status }
        return null != activeVehicleTransfer?.deliveryDate
    }
}

@Component
class VehicleReadyForDeliveryRule : StatusRule {
    override val order: Int = 21
    override val status: VehicleStatus = VehicleStatus.S140

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        /**
         *"oldest" planned vehicle transfer of vehicle has plannedDelivery date, responsible person, cost center, vehicle usage and usage group set
         * vehicle has no open damages and no open actions and pdiCompleted Date is set
         */
        val oldestPlannedVehicleTransfer = plannedVehicleTransfers.minByOrNull { it.created }
        val plannedVehicleTransferHasAllRequiredPropertiesSet =
            null != oldestPlannedVehicleTransfer?.plannedDeliveryDate &&
                null != oldestPlannedVehicleTransfer.vehicleResponsiblePerson &&
                null != oldestPlannedVehicleTransfer.vehicleUsage &&
                null != oldestPlannedVehicleTransfer.usageGroup &&
                null != oldestPlannedVehicleTransfer.depreciationRelevantCostCenterId
        val hasNoOpenDamages = 0 == vehicle.numberOfOpenDamages
        val hasNoOpenActions = 0 == numberOfOpenActions
        val pdiIsCompleted = null != preDeliveryInspection?.completedDate
        return plannedVehicleTransferHasAllRequiredPropertiesSet && hasNoOpenDamages && hasNoOpenActions && pdiIsCompleted
    }
}

@Component
class ActionsOrDamagesAfterPdiRule : StatusRule {
    override val order: Int = 22
    override val status: VehicleStatus = VehicleStatus.S130

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        // vehicle has at least one planned vehicle transfer and open damages or open actions
        val hasPlannedVehicleTransfer =
            plannedVehicleTransfers
                .any { PlannedVehicleTransferStatus.PLANNED == it.status }
        val hasOpenDamages = 0 < vehicle.numberOfOpenDamages
        val hasOpenActions = 0 < numberOfOpenActions
        return hasPlannedVehicleTransfer && (hasOpenDamages || hasOpenActions)
    }
}

@Component
class DeliveryPlannedRule : StatusRule {
    override val order: Int = 23
    override val status: VehicleStatus = VehicleStatus.S120

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        /**
         *"oldest" planned vehicle transfer of vehicle has plannedDelivery date, responsible person, vehicle usage and usage group set
         */
        val oldestPlannedVehicleTransfer = plannedVehicleTransfers.minByOrNull { it.created }
        val plannedVehicleTransferHasAllRequiredPropertiesSet =
            null != oldestPlannedVehicleTransfer?.plannedDeliveryDate &&
                null != oldestPlannedVehicleTransfer.vehicleResponsiblePerson &&
                null != oldestPlannedVehicleTransfer.vehicleUsage &&
                null != oldestPlannedVehicleTransfer.usageGroup
        return plannedVehicleTransferHasAllRequiredPropertiesSet
    }
}

@Component
class SchedulingPossibleRule : StatusRule {
    override val order: Int = 24
    override val status: VehicleStatus = VehicleStatus.S100

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        /**
         *"oldest" planned vehicle transfer of vehicle has responsible person, cost center, vehicle usage and usage group set
         * and vehicle usedCar == false
         */
        val oldestPlannedVehicleTransfer = plannedVehicleTransfers.minByOrNull { it.created }
        val plannedVehicleTransferHasAllRequiredPropertiesSet =
            null != oldestPlannedVehicleTransfer?.vehicleResponsiblePerson &&
                null != oldestPlannedVehicleTransfer.vehicleUsage &&
                null != oldestPlannedVehicleTransfer.usageGroup &&
                null != oldestPlannedVehicleTransfer.depreciationRelevantCostCenterId
        val isNotUsedCar = false == vehicle.returnInfo?.isUsedCar
        return plannedVehicleTransferHasAllRequiredPropertiesSet && isNotUsedCar
    }
}

@Component
class VehicleResponsiblePersonMissingRule : StatusRule {
    override val order: Int = 25
    override val status: VehicleStatus = VehicleStatus.S097

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        /**
         *"oldest" planned vehicle transfer of vehicle has no responsible person but vehicle usage set
         * and vehicle usedCar == false
         */
        val oldestPlannedVehicleTransfer = plannedVehicleTransfers.minByOrNull { it.created }
        val plannedVehicleTransferHasAllRequiredPropertiesSet =
            null != oldestPlannedVehicleTransfer?.vehicleUsage &&
                null == oldestPlannedVehicleTransfer.vehicleResponsiblePerson
        val isNotUsedCar = false == vehicle.returnInfo?.isUsedCar
        return plannedVehicleTransferHasAllRequiredPropertiesSet && isNotUsedCar
    }
}

@Component
class VehicleResponsiblePersonSetRule : StatusRule {
    override val order: Int = 26
    override val status: VehicleStatus = VehicleStatus.S096

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        /**
         *"oldest" planned vehicle transfer of vehicle has responsible person but no vehicle usage set
         * and vehicle usedCar == false
         */
        val oldestPlannedVehicleTransfer = plannedVehicleTransfers.minByOrNull { it.created }
        val plannedVehicleTransferHasAllRequiredPropertiesSet =
            null != oldestPlannedVehicleTransfer?.vehicleResponsiblePerson &&
                null == oldestPlannedVehicleTransfer.vehicleUsage
        val isNotUsedCar = false == vehicle.returnInfo?.isUsedCar
        return plannedVehicleTransferHasAllRequiredPropertiesSet && isNotUsedCar
    }
}

@Component
class NewVehicleWithoutUseRule : StatusRule {
    override val order: Int = 27
    override val status: VehicleStatus = VehicleStatus.S095

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        /**
         *"oldest" planned vehicle transfer of vehicle has no responsible person nor vehicle usage set
         * and vehicle usedCar == false
         */
        val oldestPlannedVehicleTransfer = plannedVehicleTransfers.minByOrNull { it.created }
        val plannedVehicleTransferHasAllRequiredPropertiesSet =
            null != oldestPlannedVehicleTransfer &&
                null == oldestPlannedVehicleTransfer.vehicleResponsiblePerson &&
                null == oldestPlannedVehicleTransfer.vehicleUsage
        val isNotUsedCar = false == vehicle.returnInfo?.isUsedCar
        return plannedVehicleTransferHasAllRequiredPropertiesSet && isNotUsedCar
    }
}

@Component
class UsedVehicleVehicleTransferPlannedRule : StatusRule {
    override val order: Int = 28
    override val status: VehicleStatus = VehicleStatus.S094

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        /**
         *"oldest" planned vehicle transfer of vehicle has responsible person, vehicle usage, usage group and cost center carrier set
         * and vehicle usedCar == true
         */
        val oldestPlannedVehicleTransfer = plannedVehicleTransfers.minByOrNull { it.created }
        val plannedVehicleTransferHasAllRequiredPropertiesSet =
            null != oldestPlannedVehicleTransfer?.vehicleResponsiblePerson &&
                null != oldestPlannedVehicleTransfer.depreciationRelevantCostCenterId &&
                null != oldestPlannedVehicleTransfer.usageGroup &&
                null != oldestPlannedVehicleTransfer.vehicleUsage
        val isUsedCar = true == vehicle.returnInfo?.isUsedCar
        return plannedVehicleTransferHasAllRequiredPropertiesSet && isUsedCar
    }
}

@Component
class UsedVehicleAvailableRule : StatusRule {
    override val order: Int = 29
    override val status: VehicleStatus = VehicleStatus.S093

    override fun matches(
        vehicle: JPAVehicleEntity,
        plannedVehicleTransfers: List<PlannedVehicleTransfer>,
        vehicleTransfers: List<VehicleTransfer>,
        preDeliveryInspection: PreDeliveryInspection?,
        numberOfOpenActions: Int,
    ): Boolean {
        /**
         *"oldest" planned vehicle transfer of vehicle has no responsible person set
         * and vehicle usedCar == true
         */
        val oldestPlannedVehicleTransfer = plannedVehicleTransfers.minByOrNull { it.created }
        val plannedVehicleTransferHasAllRequiredPropertiesSet =
            null != oldestPlannedVehicleTransfer &&
                null == oldestPlannedVehicleTransfer.vehicleResponsiblePerson
        val isUsedCar = true == vehicle.returnInfo?.isUsedCar
        return plannedVehicleTransferHasAllRequiredPropertiesSet && isUsedCar
    }
}
