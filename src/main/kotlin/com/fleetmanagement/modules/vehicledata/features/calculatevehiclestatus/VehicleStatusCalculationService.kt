/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.calculatevehiclestatus

import com.fleetmanagement.modules.predeliveryinspection.application.PreDeliveryInspectionFinder
import com.fleetmanagement.modules.vehiclecampaigns.api.OpenActionsForVehicleByVIN
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException
import com.fleetmanagement.modules.vehiclecampaigns.api.VehicleCampaignsException.CampaignInformationNoSyncedException
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import java.util.*

@Component
class VehicleStatusCalculationService(
    private val vehicleRepository: JPAVehicleRepository,
    private val vehicleTransferFinder: VehicleTransferFinder,
    private val plannedVehicleTransferFinder: PlannedVehicleTransferFinder,
    private val preDeliveryInspectionFinder: PreDeliveryInspectionFinder,
    private val openActionsForVehicleByVIN: OpenActionsForVehicleByVIN,
    statusRules: List<StatusRule>,
) {
    private val statusRules: List<StatusRule> = statusRules.sorted()

    /**
     * Calculates and return [VehicleStatus] for given vehicle.
     *
     * @return current [VehicleStatus] or [VehicleStatus.E000] in case no status rule matches
     * @throws VehicleNotFoundException in case no vehicle with given id exists.
     */
    fun calculateStatusForVehicle(vehicleId: UUID): VehicleStatus {
        val vehicle =
            vehicleRepository
                .findById(vehicleId)
                .orElseThrow { VehicleNotFoundException("Could not find vehicle with id: [$vehicleId].") }
        val vehicleVin = vehicle.vin
        if (null == vehicleVin) {
            log.warn("Vehicle with id: [$vehicleId] is missing VIN.")
            return VehicleStatus.E000
        }

        val vehicleTransfers = vehicleTransferFinder.findVehicleTransfersByVehicleId(vehicleId = vehicleId)
        val plannedVehicleTransfers =
            plannedVehicleTransferFinder.findPlannedVehicleTransfersByVehicleId(vehicleId = vehicleId)
        val preDeliveryInspection =
            preDeliveryInspectionFinder.findPreDeliveryInspectionByVehicleId(vehicleId = vehicleId)
        val numberOfOpenActions =
            try {
                openActionsForVehicleByVIN.findNumberOfOpenActionsForVehicle(vin = vehicleVin)
            } catch (exception: CampaignInformationNoSyncedException) {
                // FPT1-1019 treat unmanaged vehicles as if they have 0 campaigns/actions
                ZERO_OPEN_ACTIONS
            } catch (exception: VehicleCampaignsException) {
                log.warn(
                    "Could not determine number of open actions for vehicle with vin: [$vehicleVin]. VehicleStatus can not be calculated correctly and will be set to ${VehicleStatus.E000}.",
                )
                return VehicleStatus.E000
            }

        // execute all rules in order and return on first match
        return statusRules
            .firstOrNull {
                it.matches(
                    vehicle = vehicle,
                    plannedVehicleTransfers = plannedVehicleTransfers,
                    vehicleTransfers = vehicleTransfers,
                    preDeliveryInspection = preDeliveryInspection,
                    numberOfOpenActions = numberOfOpenActions,
                )
            }?.status ?: VehicleStatus.E000
    }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleStatusCalculationService::class.java)
        private const val ZERO_OPEN_ACTIONS = 0
    }
}
