/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.calculatevehiclestatus

import com.fleetmanagement.modules.vehicledata.features.updatevehicle.UpdateVehicleStatusService
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class VehicleStatusRecalculationService(
    private val updateVehicleStatusService: UpdateVehicleStatusService,
    private val vehicleRepository: JPAVehicleRepository,
) {
    /**
     * Will fetch all vehicles with unknown/error status [VehicleStatus.E000]
     * and recalculate and update status.
     */
    @Transactional
    fun recalculateAndUpdateVehicleStatusForVehiclesInErrorStatus() {
        val vehiclesWithE000 = vehicleRepository.findAllByStatus(vehicleStatus = VehicleStatus.E000)

        vehiclesWithE000.forEach {
            log.info("Recalculating status for vehicle with id: [${it.id}].")
            updateVehicleStatusService.updateVehicleStatusAsync(requireNotNull(it.id))
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleStatusRecalculationService::class.java)
    }
}
