/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.calculatevehiclestatus.job

import com.fleetmanagement.modules.vehicledata.features.calculatevehiclestatus.VehicleStatusRecalculationService
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.TimeZone

@Component
@DisallowConcurrentExecution
class RecalculateVehicleStatusJob(
    private val vehicleStatusRecalculationService: VehicleStatusRecalculationService,
) : Job {
    override fun execute(context: JobExecutionContext?) {
        log.info("Starting scheduled RecalculateVehicleStatusJob Job.")
        vehicleStatusRecalculationService.recalculateAndUpdateVehicleStatusForVehiclesInErrorStatus()
        log.info("Finished scheduled RecalculateVehicleStatusJob Job.")
    }

    companion object {
        private val log = LoggerFactory.getLogger(RecalculateVehicleStatusJob::class.java)
    }
}

@Configuration
class RecalculateVehicleStatusJobConfig {
    @Bean("recalculateVehicleStatusJobDetail")
    fun recalculateVehicleStatusJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(RecalculateVehicleStatusJob::class.java)
            .withIdentity("recalculateVehicleStatusJob")
            .withDescription("RecalculateVehicleStatus Job")
            .storeDurably()
            .build()

    @Bean("recalculateVehicleStatusJobTrigger")
    fun recalculateVehicleStatusJobTrigger(
        @Qualifier("recalculateVehicleStatusJobDetail") recalculateVehicleStatusJobDetail: JobDetail,
        @Value("\${vehicle-data.status.recalculation-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(recalculateVehicleStatusJobDetail)
            .withIdentity("RecalculateVehicleStatusJobTrigger")
            .withDescription("RecalculateVehicleStatusJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
