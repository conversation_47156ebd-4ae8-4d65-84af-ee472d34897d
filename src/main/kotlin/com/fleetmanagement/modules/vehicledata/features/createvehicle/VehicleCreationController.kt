/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.createvehicle

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.vehicledata.api.CreateOrUpdateVehicle
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateVehicleDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.api.validators.ValidLicensePlate
import com.fleetmanagement.modules.vehicledata.api.validators.ValidRegistrationDate
import com.fleetmanagement.modules.vehicleregistration.api.WriteRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateVehicleRegistration
import io.opentelemetry.instrumentation.annotations.WithSpan
import jakarta.validation.Valid
import jakarta.validation.constraints.NotBlank
import jakarta.validation.constraints.Pattern
import org.slf4j.LoggerFactory
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.time.OffsetDateTime
import java.time.ZonedDateTime
import java.util.Date

@RestController
@RequestMapping("/vehicles")
class VehicleCreationController(
    private val vehicleCreationService: CreateOrUpdateVehicle,
    private val writeRegistrationOrder: WriteRegistrationOrder,
) {
    private val logger = LoggerFactory.getLogger(VehicleCreationController::class.java)

    @WithSpan("create.vehicle")
    @PostMapping
    fun createVehicle(
        @Valid @RequestBody createVehicleRequest: CreateVehicleRequestDto,
    ): ResponseEntity<VehicleDTO> {
        val createVehicleDTO = createVehicleRequest.toCreateVehicleDTO()
        val vehicle =
            vehicleCreationService.createOrUpdateVehicle(
                createVehicleDTO,
            )

        runCatching {
            createVehicleRequest.licencePlate?.let {
                writeRegistrationOrder.createRegistrationOrders(
                    listOf(
                        CreateVehicleRegistration(
                            vin = vehicle.vin,
                            licencePlate = it,
                            registrationType = 1,
                            registrationDate = createVehicleRequest.registrationDate,
                        ),
                    ),
                )
            }
        }.onFailure { e ->
            logger.error(
                "Suppressing error while manual order creation for vehicle ${vehicle.vin} with licence plate ${createVehicleRequest.licencePlate}",
                e,
            )
        }

        return ResponseEntity.status(HttpStatus.CREATED).body(vehicle)
    }
}

@ValidRegistrationDate
@ValidLicensePlate
data class CreateVehicleRequestDto(
    @field:NotBlank(message = "error.validation.not-blank") @field:Pattern(
        regexp = "^[A-HJ-NPR-Z0-9]{17}$",
        message = "error.validation.pattern",
    ) val vin: String?,
    @field:NotBlank(message = "error.validation.not-blank") val manufacturer: String?,
    @field:NotBlank(message = "error.validation.not-blank") val modelDescription: String?,
    val amountSeats: String?,
    val engineCapacity: Int?,
    val licencePlate: String?,
    val registrationDate: ZonedDateTime?,
    @field:Pattern(regexp = "^\\d{4}$", message = "error.validation.year") val technicalModelYear: String?,
    @field:Pattern(
        regexp = "E10|SUPER|REGULAR|DIESEL|ELECTRIC",
        message = "error.validation.invalid-fuel-type",
    ) val primaryFuelType: String?,
    @field:Pattern(regexp = "SR|WR", message = "error.validation.invalid-fuel-type") val currentTires: String?,
    @field:NotBlank(message = "error.validation.not-blank") val orderType: String?,
    @field:Pattern(
        regexp = "E10|SUPER|REGULAR|DIESEL|ELECTRIC",
        message = "error.validation.invalid-fuel-type",
    ) val secondaryFuelType: String?,
    val netPriceWithExtras: String?,
    @field:Pattern(
        regexp = "PKW|TRUCK|TRAILER|MOTORCYCLE|TRANSPORTER",
        message = "error.validation.invalid-vehicle-type",
    ) val vehicleType: String?,
    val inEmbargo: Boolean?,
    val colorExterior: String?,
    val colorExteriorDescription: String?,
    val pmpDataOdometer: String?,
    val enginePower: String?,
    val pmpDataOdometerDate: Date?,
    val blockedForSale: Boolean?,
    val scrappedDate: OffsetDateTime?,
    val driveType: String?,
    @field:NotBlank(message = "error.validation.not-blank") @field:Pattern(
        regexp = "AV|UV|FE",
        message = "error.validation.invalid-financial-asset-type",
    ) val financialAssetType: String?,
    @field:Pattern(
        regexp = "EQUI",
        message = "error.validation.invalid-source",
    ) val source: String = VehicleSource.EQUI.toString(),
    val equipmentNumber: Long?,
) {
    fun toCreateVehicleDTO() =
        CreateVehicleDTO(
            vin = this.vin!!,
            manufacturer = this.manufacturer!!,
            modelDescription = this.modelDescription!!,
            amountSeats = this.amountSeats,
            engineCapacity = this.engineCapacity,
            licencePlate = this.licencePlate,
            registrationDate = this.registrationDate,
            technicalModelYear = this.technicalModelYear,
            primaryFuelType = this.primaryFuelType?.let { FuelType.valueOf(this.primaryFuelType) },
            currentTires = this.currentTires?.let { TireSet.valueOf(this.currentTires) },
            orderType = this.orderType!!,
            secondaryFuelType = this.secondaryFuelType?.let { FuelType.valueOf(this.secondaryFuelType) },
            netPriceWithExtras = this.netPriceWithExtras,
            vehicleType = VehicleType.valueOf(this.vehicleType!!),
            inEmbargo = this.inEmbargo,
            colorExterior = this.colorExterior,
            colorExteriorDescription = this.colorExteriorDescription,
            pmpDataOdometer = this.pmpDataOdometer,
            enginePower = this.enginePower,
            pmpDataOdometerDate = this.pmpDataOdometerDate,
            blockedForSale = this.blockedForSale,
            scrappedDate = this.scrappedDate,
            driveType = this.driveType,
            financialAssetType = FinancialAssetType.valueOf(this.financialAssetType!!),
            source = VehicleSource.valueOf(this.source),
            equipmentNumber = this.equipmentNumber,
        )
}
