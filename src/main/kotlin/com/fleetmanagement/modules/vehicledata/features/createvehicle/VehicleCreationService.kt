/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.createvehicle

import com.fleetmanagement.modules.vehicledata.api.CreateOrUpdateVehicle
import com.fleetmanagement.modules.vehicledata.api.CreateVehicle
import com.fleetmanagement.modules.vehicledata.api.VehicleAlreadyExists
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateUIVehicleDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateVehicleDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.toJPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.api.events.ManualVehicleCreatedEvent
import com.fleetmanagement.modules.vehicledata.features.refreshvehicle.VehicleDataSyncRefreshService
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.repository.mappers.VehicleDTOMapper
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import kotlin.jvm.optionals.getOrNull

@Service
class VehicleCreationService(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val vehicleDataSyncRefreshService: VehicleDataSyncRefreshService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) : CreateOrUpdateVehicle,
    CreateVehicle {
    private fun checkVehicleExists(vin: String) {
        if (jpaVehicleRepository.findByVin(vin).getOrNull() != null) {
            throw VehicleAlreadyExists(
                message = "Vehicle with vin ($vin) already exists",
                properties = mapOf("vin" to vin),
            )
        }
    }

    private fun createVehicleInternal(
        vin: String,
        modifier: String,
        sendDomainEvents: Boolean,
        vehicleToCreate: JPAVehicleEntity,
    ): VehicleDTO {
        checkVehicleExists(vin)
        val createdVehicle =
            vehicleDataSyncRefreshService.createOrUpdateVehicleObject(
                modifier = modifier,
                sendDomainEvents = sendDomainEvents,
            ) { vehicleToCreate }
        return VehicleDTOMapper.INSTANCE.map(createdVehicle)
    }

    override fun createOrUpdateVehicle(
        createVehicleDto: CreateUIVehicleDTO,
        creatingUsername: String,
    ): VehicleDTO =
        createVehicleInternal(
            vin = createVehicleDto.vin,
            modifier = creatingUsername,
            sendDomainEvents = true,
            vehicleToCreate = createVehicleDto.toJPAVehicleEntity(),
        )

    override fun createOrUpdateVehicle(createVehicleDto: CreateVehicleDTO): VehicleDTO =
        createVehicleInternal(
            vin = createVehicleDto.vin,
            modifier = createVehicleDto.source.toString(),
            sendDomainEvents = false,
            vehicleToCreate = createVehicleDto.toJPAVehicleEntity(),
        )

    override fun manuallyCreateVehicle(createVehicleDto: CreateVehicleDTO) {
        val vehicleAlreadyExists = jpaVehicleRepository.findByVin(createVehicleDto.vin)
        if (vehicleAlreadyExists.isPresent) {
            throw VehicleAlreadyExists(
                message = "Vehicle with vin [${createVehicleDto.vin}] already exists.",
                properties =
                    mapOf(
                        VehicleAlreadyExists.KEY_MODEL_YEAR to
                            vehicleAlreadyExists
                                .get()
                                .modelInfo
                                ?.modelYear
                                ?.toString()
                                .orEmpty(),
                        VehicleAlreadyExists.KEY_PRODUCTION_NUMBER to
                            vehicleAlreadyExists
                                .get()
                                .productionInfo
                                ?.productionNumber
                                .orEmpty(),
                    ),
            )
        }

        val vehicle = createVehicleDto.toJPAVehicleEntity()

        jpaVehicleRepository.save(vehicle)
        applicationEventPublisher.publishEvent(ManualVehicleCreatedEvent(vehicleId = requireNotNull(vehicle.id)))
    }
}
