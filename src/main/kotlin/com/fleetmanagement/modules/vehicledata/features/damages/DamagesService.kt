package com.fleetmanagement.modules.vehicledata.features.damages

import com.fleetmanagement.modules.vehicledata.integrations.dms.streamzilla.dtos.DamageFileUpdatedEventDto
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import kotlin.jvm.optionals.getOrNull

@Service
@Transactional
class DamagesService(
    private val jpaVehicleRepository: JPAVehicleRepository,
) {
    fun updateDamages(event: DamageFileUpdatedEventDto) {
        val vin = event.vin
        val occurredAt = event.occurredAt
        val vehicle = jpaVehicleRepository.findByVin(vin).getOrNull()
        if (vehicle == null) {
            logger.warn("Vehicle with VIN $vin not found, skipping damage update.")
            return
        }
        vehicle.lastUpdatedNumberOfDamages?.let { lastUpdated ->
            if (occurredAt.isAfter(lastUpdated)) {
                updateDamages(vehicle, event)
            }
        } ?: updateDamages(vehicle, event)
    }

    private fun updateDamages(
        vehicle: JPAVehicleEntity,
        event: DamageFileUpdatedEventDto,
    ) {
        logger.info("updateNumberOfDamages for vin ${vehicle.vin}")
        vehicle.updateDamages(
            repairfixCarId = event.externalReference,
            totalNumberOfDamages = event.totalNumberOfDamages,
            numberOfOpenDamages = event.numberOfOpenDamages,
            lastUpdatedNumberOfDamages = event.occurredAt,
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(DamagesService::class.java)
    }
}
