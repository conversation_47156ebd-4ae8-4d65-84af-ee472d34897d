/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.factorycarpreparationordernumber

import org.quartz.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.*

@Component
@DisallowConcurrentExecution
class FactoryCarPreparationOrderNumberJob(
    private val updateFactoryCarPreparationOrderNumbersService: UpdateFactoryCarPreparationOrderNumbersService,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(FactoryCarPreparationOrderNumberJob::class.java)
    }

    override fun execute(context: JobExecutionContext?) {
        log.info("Starting scheduled FactoryCarPreparationOrderNumberJob Job.")
        updateFactoryCarPreparationOrderNumbersService.obtainAndUpdateFactoryCarPreparationOrderNumbers()
        log.info("Finished scheduled FactoryCarPreparationOrderNumberJob Job.")
    }
}

@Configuration
class FactoryCarPreparationOrderNumberJobConfig {
    @Bean("factoryCarPreparationOrderNumberJobDetail")
    fun factoryCarPreparationOrderNumberJobConfig(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(FactoryCarPreparationOrderNumberJob::class.java)
            .withIdentity("factoryCarPreparationOrderNumberJob")
            .withDescription("FactoryCarPreparationOrderNumber Job")
            .storeDurably()
            .build()

    @Bean("factoryCarPreparationOrderNumberJobTrigger")
    fun factoryCarPreparationOrderNumberJobTrigger(
        @Qualifier("factoryCarPreparationOrderNumberJobDetail") factoryCarPreparationOrderNumberJobDetail: JobDetail,
        @Value("\${vehicle-data.factory-car-preparation-order-number.factory-car-preparation-order-number-update-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(factoryCarPreparationOrderNumberJobDetail)
            .withIdentity("FactoryCarPreparationOrderNumberJobTrigger")
            .withDescription("FactoryCarPreparationOrderNumberJob trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
