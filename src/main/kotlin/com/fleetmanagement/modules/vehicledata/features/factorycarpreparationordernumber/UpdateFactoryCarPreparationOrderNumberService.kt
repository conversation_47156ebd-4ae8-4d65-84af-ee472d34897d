/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.factorycarpreparationordernumber

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.paceinternalordernumber.application.port.RequestFactoryCarPreparationOrderNumberUseCase
import com.fleetmanagement.modules.paceinternalordernumber.application.port.RequestFactoryCarPreparationOrderNumberUseCaseException
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicleFactoryCarPreparationOrderNumber
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicledata.features.readvehicle.DeprecatedVehicleDataReadService
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional
class UpdateFactoryCarPreparationOrderNumbersService(
    private val updateFactoryCarPreparationOrderService: UpdateFactoryCarPreparationOrderService,
    private val vehicleDataReadService: DeprecatedVehicleDataReadService,
    private val jpaVehicleRepository: JPAVehicleRepository,
) {
    /**
     * Fetches all [JPAVehicleEntity]s with nextProcess == SALES and
     * no factoryCarPreparationOrderNumber set
     * and fetches new order numbers for those from PACE.
     */
    fun obtainAndUpdateFactoryCarPreparationOrderNumbers() {
        val vehicleToUpdate =
            jpaVehicleRepository.findAllByReturnInfo_NextProcessAndReturnInfo_FactoryCarPreparationOrderNumberIsNull(
                nextProcess = NextProcess.SALES,
            )
        vehicleToUpdate.forEach {
            updateFactoryCarPreparationOrderService.obtainAndUpdateFactoryCarPreparationOrderNumber(it)
        }
    }
}

@Component
class UpdateFactoryCarPreparationOrderService(
    private val updateVehicleFactoryCarPreparationOrderNumber: UpdateVehicleFactoryCarPreparationOrderNumber,
    private val requestFactoryCarPreparationOrderNumberUseCase: RequestFactoryCarPreparationOrderNumberUseCase,
    private val vehicleTransferFinder: VehicleTransferFinder,
) {
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun obtainAndUpdateFactoryCarPreparationOrderNumber(vehicle: JPAVehicleEntity) {
        val vehicleTransfersForVehicle =
            vehicleTransferFinder
                .findVehicleTransfersByVehicleId(vehicleId = requireNotNull(vehicle.id))

        /**
         * Pick the current active one (as nextProcess (the condition for this job) has to be set before return-process)
         * or if job runs "after" return process the last finished one
         */
        val relevantVehicleTransfer =
            vehicleTransfersForVehicle
                .firstOrNull { VehicleTransferStatus.ACTIVE == it.status }
                ?: vehicleTransfersForVehicle
                    .filter { VehicleTransferStatus.FINISHED == it.status }
                    .sortedByDescending { it.returnDate }
                    .firstOrNull()
        // get all the null checks out of the way
        if (null == vehicle.vin) {
            log.warn(
                @Suppress("ktlint:standard:max-line-length")
                "Could not find vin for vehicle with id ${vehicle.id}. Skipping factory car preparation order number update.",
            )
            return
        }
        if (null == vehicle.modelInfo?.range) {
            log.warn(
                "Vehicle with id [${vehicle.id}] is missing modelRange information. Skipping factory car preparation order number update.",
            )
            return
        }
        if (null == relevantVehicleTransfer) {
            log.warn(
                "Could not find any matching vehicle transfer for vehicle with id [${vehicle.id}]. Skipping factory car preparation order number update.",
            )
            return
        }
        if (null == relevantVehicleTransfer.vehicleResponsiblePerson) {
            log.warn(
                "Vehicle transfer with key [${relevantVehicleTransfer.key.value}] is missing vehicle responsible person. Skipping factory car preparation order number update.",
            )
            return
        }

        // fetch and update
        getNewFactoryCarPreparationOrderNumber(
            vehicleResponsiblePerson = requireNotNull(relevantVehicleTransfer.vehicleResponsiblePerson),
            vin = requireNotNull(vehicle.vin),
            licensePlate = relevantVehicleTransfer.licensePlate,
            modelRange = requireNotNull(vehicle.modelInfo?.range),
        )?.also { factoryCarPreparationOrderNumber ->
            updateVehicleFactoryCarPreparationOrderNumber.updateVehicleFactoryCarPreparationOrderNumber(
                factoryCarPreparationOrderNumber = factoryCarPreparationOrderNumber.value,
                vehicleId = requireNotNull(vehicle.id),
            )
        }
    }

    private fun getNewFactoryCarPreparationOrderNumber(
        vehicleResponsiblePerson: EmployeeNumber,
        licensePlate: String?,
        vin: String,
        modelRange: String,
    ): RequestFactoryCarPreparationOrderNumberUseCase.OrderNumber? =
        try {
            requestFactoryCarPreparationOrderNumberUseCase.requestFactoryCarPreparationOrderNumber(
                employeeNumber = vehicleResponsiblePerson,
                licensePlate = licensePlate,
                vin = vin,
                modelRange = modelRange,
            )
        } catch (exception: RequestFactoryCarPreparationOrderNumberUseCaseException) {
            log.warn("Error while trying to obtain factory car preparation order number.", exception)
            null
        }

    companion object {
        private val log = LoggerFactory.getLogger(UpdateFactoryCarPreparationOrderService::class.java)
    }
}
