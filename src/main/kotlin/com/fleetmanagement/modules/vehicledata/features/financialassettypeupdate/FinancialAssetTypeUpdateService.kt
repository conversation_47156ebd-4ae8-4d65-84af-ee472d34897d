/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.financialassettypeupdate

import com.fleetmanagement.modules.vehicledata.api.CreateOrUpdateFinancialAssetTypeUpdate
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.repository.JPAFinancialAssetTypeUpdateRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAFinancialAssetTypeUpdate
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional
class FinancialAssetTypeUpdateService(
    private val financialAssetTypeUpdateRepository: JPAFinancialAssetTypeUpdateRepository,
) : CreateOrUpdateFinancialAssetTypeUpdate {
    override fun createOrUpdateFinancialAssetTypeUpdate(
        vin: String,
        financialAssetType: FinancialAssetType,
    ) {
        val financialAssetTypeUpdate = financialAssetTypeUpdateRepository.findByVin(vin)
        if (financialAssetTypeUpdate == null) {
            financialAssetTypeUpdateRepository.save(
                JPAFinancialAssetTypeUpdate(
                    vin = vin,
                    financialAssetType = financialAssetType,
                ),
            )
        } else {
            financialAssetTypeUpdate.update(financialAssetType)
        }
    }
}
