/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.financialassettypeupdate

import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicleFinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.VehicleNotFoundInFVM
import com.fleetmanagement.modules.vehicledata.repository.JPAFinancialAssetTypeUpdateRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAFinancialAssetTypeUpdate
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
class ProcessFinancialAssetTypeUpdate(
    private val financialAssetTypeUpdateRepository: JPAFinancialAssetTypeUpdateRepository,
    private val readVehicleByVIN: ReadVehicleByVIN,
    private val updateVehicleFinancialAssetType: UpdateVehicleFinancialAssetType,
) {
    @Transactional
    fun processFinancialAssetTypeUpdate(financialAssetTypeUpdate: JPAFinancialAssetTypeUpdate) {
        /**
         * sync financial asset type to FVM vehicles if exists, else skip
         */
        val vehicle = readVehicleByVIN.readVehicleByVIN(financialAssetTypeUpdate.vin)
        updateVehicleFinancialAssetType.updateVehicleFinancialAssetType(
            vehicleId = vehicle.id,
            financialAssetType = financialAssetTypeUpdate.financialAssetType,
        )
        financialAssetTypeUpdateRepository.deleteById(financialAssetTypeUpdate.id)
    }
}

@Component
class SyncFinancialAssetTypeUpdateService(
    private val financialAssetTypeUpdateRepository: JPAFinancialAssetTypeUpdateRepository,
    private val processFinancialAssetTypeUpdate: ProcessFinancialAssetTypeUpdate,
) {
    fun syncFinancialAssetTypeUpdateToVehicle() {
        val listOfFinancialAssetTypeUpdate = financialAssetTypeUpdateRepository.findAll()
        listOfFinancialAssetTypeUpdate.forEach { financialAssetTypeUpdate ->
            try {
                processFinancialAssetTypeUpdate.processFinancialAssetTypeUpdate(financialAssetTypeUpdate)
            } catch (_: VehicleNotFoundInFVM) {
                return@forEach
            }
        }
    }
}
