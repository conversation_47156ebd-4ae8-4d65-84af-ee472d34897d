/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.financialassettypeupdate.job

import com.fleetmanagement.modules.vehicledata.features.financialassettypeupdate.SyncFinancialAssetTypeUpdateService
import org.quartz.*
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.*

@Component
@DisallowConcurrentExecution
class SyncFinancialAssetTypeUpdateJob(
    private val syncFinancialAssetTypeUpdateService: SyncFinancialAssetTypeUpdateService,
) : Job {
    override fun execute(context: JobExecutionContext?) {
        log.info("Starting scheduled SyncFinancialAssetTypeUpdateJob Job.")
        syncFinancialAssetTypeUpdateService.syncFinancialAssetTypeUpdateToVehicle()
        log.info("Finished scheduled SyncFinancialAssetTypeUpdateJob Job.")
    }

    companion object {
        private val log = LoggerFactory.getLogger(SyncFinancialAssetTypeUpdateJob::class.java)
    }
}

@Configuration
class SyncFinancialAssetTypeUpdateJobConfig {
    @Bean("syncFinancialAssetTypeUpdateJobDetail")
    fun syncFinancialAssetTypeUpdateJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(SyncFinancialAssetTypeUpdateJob::class.java)
            .withIdentity("syncFinancialAssetTypeUpdateJob")
            .withDescription("SyncFinancialAssetTypeUpdate Job")
            .storeDurably()
            .build()

    @Bean("syncFinancialAssetTypeUpdateJobTrigger")
    fun syncFinancialAssetTypeUpdateJobTrigger(
        @Qualifier("syncFinancialAssetTypeUpdateJobDetail") syncFinancialAssetTypeUpdateJobDetail: JobDetail,
        @Value("\${vehicle-data.financial-asset-type-update.financial-asset-type-update-sync-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(syncFinancialAssetTypeUpdateJobDetail)
            .withIdentity("SyncFinancialAssetTypeUpdateJobTrigger")
            .withDescription("SyncFinancialAssetTypeUpdateJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
