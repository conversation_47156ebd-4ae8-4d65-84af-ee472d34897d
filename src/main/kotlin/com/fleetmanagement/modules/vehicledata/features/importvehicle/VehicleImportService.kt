/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.importvehicle

import com.fleetmanagement.modules.vehicledata.api.VehicleAlreadyExists
import com.fleetmanagement.modules.vehicledata.api.VehicleImportInterface
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.dtos.ImportVehicleDto
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.features.refreshvehicle.VehicleDataSyncRefreshService
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.repository.mappers.VehicleDTOMapper
import org.springframework.stereotype.Service
import kotlin.jvm.optionals.getOrNull

@Service
class VehicleImportService(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val vehicleDataSyncRefreshService: VehicleDataSyncRefreshService,
) : VehicleImportInterface {
    fun isValidVin(vin: String): Boolean {
        if (vin.length != 17) return false
        val vinRegex = Regex("^[A-HJ-NPR-Z0-9]{17}$")
        return vinRegex.matches(vin)
    }

    override fun initialLoadVehicle(vin: String): VehicleDTO {
        val vehicleToImport = mapVehicleData(vin, isVin = true)
        vehicleToImport.setSource(VehicleSource.PVH)

        val importedVehicle =
            vehicleDataSyncRefreshService.createOrUpdateVehicleObject(
                modifier = "INITIAL_PVH_LOAD",
                sendDomainEvents = false,
            ) { vehicleToImport }

        return VehicleDTOMapper.INSTANCE.map(importedVehicle)
    }

    override fun importVehicle(
        importVehicleDto: ImportVehicleDto,
        importingUsername: String,
    ): VehicleDTO {
        val identifier = importVehicleDto.identifier
        val referenceId = importVehicleDto.reference
        val vehicleToImport =
            when {
                isValidVin(identifier) -> mapVehicleData(identifier, isVin = true)
                else -> mapVehicleData(identifier, isVin = false)
            }

        vehicleToImport.setSource(VehicleSource.PVH_IMPORT)
        referenceId?.let { vehicleToImport.setReferenceId(referenceId) }

        val importedVehicle =
            vehicleDataSyncRefreshService.createOrUpdateVehicleObject(importingUsername) { vehicleToImport }

        return VehicleDTOMapper.INSTANCE.map(importedVehicle)
    }

    fun mapVehicleData(
        identifier: String,
        isVin: Boolean,
    ): JPAVehicleEntity {
        val existingVehicle =
            if (isVin) {
                jpaVehicleRepository.findByVin(identifier).getOrNull()
            } else {
                jpaVehicleRepository.findByVguid(identifier).getOrNull()
            }
        val label = if (isVin) "vin" else "vguid"
        existingVehicle?.let {
            throw VehicleAlreadyExists(
                message = "Vehicle with $label ($identifier) already exists",
                properties = mapOf(label to identifier),
            )
        }

        return if (isVin) {
            vehicleDataSyncRefreshService.mapVehicleDataFromPVHByVin(identifier)
        } else {
            vehicleDataSyncRefreshService.mapVehicleDataFromPVHByVguid(identifier)
        }
    }
}
