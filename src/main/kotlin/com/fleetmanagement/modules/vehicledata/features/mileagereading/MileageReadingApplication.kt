package com.fleetmanagement.modules.vehicledata.features.mileagereading

import com.fleetmanagement.emhshared.USAGE_GROUP_DESCRIPTION_PERSON
import com.fleetmanagement.modules.carsync.adapter.out.Carsync
import com.fleetmanagement.modules.carsync.application.port.ReadTripsInvoiceUseCase
import com.fleetmanagement.modules.consigneedatasheet.application.port.usagegroup.ReadUsageGroupUseCase
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import java.util.UUID

@Component
@ConditionalOnBean(Carsync::class)
class MileageReadingApplication(
    private val readUsageGroupUseCase: ReadUsageGroupUseCase,
    private val mileageReadingService: MileageReadingService,
    private val readTripsInvoiceUseCase: ReadTripsInvoiceUseCase,
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(MileageReadingApplication::class.java)
    }

    fun syncMileage() {
        val usageGroupPersonId =
            try {
                readUsageGroupUseCase
                    .readAllUsageGroups()
                    .single { it.description == USAGE_GROUP_DESCRIPTION_PERSON }
                    .id
            } catch (e: IllegalArgumentException) {
                logger.info("Multiple 'Person' usage groups found in consignee data. Cannot sync mileage.")
                return
            } catch (e: NoSuchElementException) {
                logger.info("No 'Person' usage group found in consignee data. Cannot sync mileage.")
                return
            }

        val vehicleIds =
            readVehicleTransferUseCase
                .findAllActiveTransfersForUsageGroup(usageGroupPersonId)
                .map { it.vehicleId }
        logger.info("Found ${vehicleIds.size} vehicles for carSync mileage synchronization")
        vehicleIds.forEach { vehicleId ->
            syncMileageForVehicle(vehicleId)
        }
    }

    private fun syncMileageForVehicle(vehicleId: UUID) {
        val endTime = OffsetDateTime.now()
        val startTime = endTime.minusMonths(1)
        val vin = readVehicleByVehicleId.readVehicleById(vehicleId)?.vin
        if (vin == null) {
            logger.info("VIN not found for vehicleId=$vehicleId. Skipping.")
            return
        }

        val endKm = fetchEndKm(vin = vin, startTime = startTime, endTime = endTime)
        if (endKm == null) {
            logger.info("endKm is null for VIN=$vin. Skipping.")
            return
        }

        val currentMileage = mileageReadingService.readMileageReadings(vehicleId).maxByOrNull { it.readDate }
        if (currentMileage != null && (currentMileage.mileage != endKm || currentMileage.readDate != endTime)) {
            mileageReadingService.addCurrentMileageReading(
                vehicleId = vehicleId,
                mileage = endKm,
                readDate = endTime,
                source = MileageReadingSource.CAR_SYNC,
            )
        }
    }

    private fun fetchEndKm(
        vin: String,
        startTime: OffsetDateTime,
        endTime: OffsetDateTime,
    ): Int? {
        val tripInvoice =
            readTripsInvoiceUseCase.readTripInvoice(
                vin = vin,
                startTime = startTime,
                endTime = endTime,
            )
        if (tripInvoice == null) {
            logger.info("Trip invoice not found for VIN=$vin. Skipping.")
            return null
        }
        return tripInvoice.endKm
    }
}
