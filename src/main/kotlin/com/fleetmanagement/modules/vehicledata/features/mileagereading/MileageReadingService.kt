/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.mileagereading

import com.fleetmanagement.modules.vehicledata.api.AddCurrentMileageReading
import com.fleetmanagement.modules.vehicledata.api.ReadMileageReadings
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingDTO
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource
import com.fleetmanagement.modules.vehicledata.repository.JPAMileageReadingRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAMileageReadingEntity
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.toVehicleTransferKey
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferDeliveredEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferReturnedEvent
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionalEventListener
import java.time.OffsetDateTime
import java.util.*
import com.fleetmanagement.modules.vehicledata.repository.entities.MileageReadingSource as EntityMileageReadingSource

@Component
class MileageReadingService(
    private val jpaMileageReadingRepository: JPAMileageReadingRepository,
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase,
) : AddCurrentMileageReading,
    ReadMileageReadings {
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handleVehicleTransferReturnedEvent(event: VehicleTransferReturnedEvent) {
        val vehicleTransfer =
            readVehicleTransferUseCase.findVehicleTransfersByVehicleTransferKey(event.vehicleTransferKey.toVehicleTransferKey())

        if (null == vehicleTransfer) {
            log.warn("Could not find vehicle transfer with key [${event.vehicleId}]. No mileage reading will be created.")
            return
        }

        val returnMileage = vehicleTransfer.mileageAtReturn

        if (null == returnMileage) {
            log.warn("Vehicle transfer with key [${event.vehicleId}] has no return mileage set. No mileage reading will be created.")
            return
        }

        createAndAddNewMileageReading(
            vehicleId = event.vehicleId,
            readDate = OffsetDateTime.now(),
            source = EntityMileageReadingSource.RETURN,
            mileage = returnMileage,
        )
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener
    fun handleVehicleTransferDeliveredEvent(event: VehicleTransferDeliveredEvent) {
        val vehicleTransfer =
            readVehicleTransferUseCase.findVehicleTransfersByVehicleTransferKey(event.vehicleTransferKey.toVehicleTransferKey())

        if (null == vehicleTransfer) {
            log.warn("Could not find vehicle transfer with key [${event.vehicleId}]. No mileage reading will be created.")
            return
        }

        val deliveryMileage = vehicleTransfer.mileageAtDelivery

        if (null == deliveryMileage) {
            log.warn("Vehicle transfer with key [${event.vehicleId}] has no delivery mileage set. No mileage reading will be created.")
            return
        }

        createAndAddNewMileageReading(
            vehicleId = event.vehicleId,
            readDate = OffsetDateTime.now(),
            source = EntityMileageReadingSource.DELIVERY,
            mileage = deliveryMileage,
        )
    }

    override fun readMileageReadings(vehicleId: UUID): List<MileageReadingDTO> {
        val mileageReadingEntities = jpaMileageReadingRepository.findAllByVehicleId(vehicleId)
        return mileageReadingEntities.map { mileageReadingEntity -> mileageReadingEntity.toMileageReading() }
    }

    private fun createAndAddNewMileageReading(
        vehicleId: UUID,
        readDate: OffsetDateTime,
        source: EntityMileageReadingSource,
        mileage: Int,
    ) = jpaMileageReadingRepository.save(
        JPAMileageReadingEntity(
            vehicleId = vehicleId,
            readDate = readDate,
            source = source,
            mileage = mileage,
        ),
    )

    override fun addCurrentMileageReading(
        vehicleId: UUID,
        mileage: Int,
        readDate: OffsetDateTime,
        source: MileageReadingSource,
    ) {
        createAndAddNewMileageReading(
            vehicleId = vehicleId,
            readDate = readDate,
            source = source.toMileageReadingSource(),
            mileage = mileage,
        )
    }

    companion object {
        private val log = LoggerFactory.getLogger(MileageReadingService::class.java)
    }
}

private fun MileageReadingSource.toMileageReadingSource(): EntityMileageReadingSource =
    when (this) {
        MileageReadingSource.MANUAL -> EntityMileageReadingSource.MANUAL
        MileageReadingSource.DELIVERY -> EntityMileageReadingSource.DELIVERY
        MileageReadingSource.CAR_SYNC -> EntityMileageReadingSource.CAR_SYNC
        MileageReadingSource.RETURN -> EntityMileageReadingSource.RETURN
        MileageReadingSource.FMS -> EntityMileageReadingSource.FMS
    }

private fun EntityMileageReadingSource.toMileageReadingSource(): MileageReadingSource =
    when (this) {
        EntityMileageReadingSource.MANUAL -> MileageReadingSource.MANUAL
        EntityMileageReadingSource.DELIVERY -> MileageReadingSource.DELIVERY
        EntityMileageReadingSource.CAR_SYNC -> MileageReadingSource.CAR_SYNC
        EntityMileageReadingSource.RETURN -> MileageReadingSource.RETURN
        EntityMileageReadingSource.FMS -> MileageReadingSource.FMS
    }

private fun JPAMileageReadingEntity.toMileageReading(): MileageReadingDTO =
    MileageReadingDTO(
        mileage = mileage,
        source = source.toMileageReadingSource(),
        readDate = readDate,
        createdBy = createdBy,
    )
