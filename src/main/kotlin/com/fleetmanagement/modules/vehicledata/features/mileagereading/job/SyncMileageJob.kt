package com.fleetmanagement.modules.vehicledata.features.mileagereading.job

import com.fleetmanagement.modules.carsync.adapter.out.Carsync
import com.fleetmanagement.modules.vehicledata.features.mileagereading.MileageReadingApplication
import org.quartz.CronScheduleBuilder
import org.quartz.DisallowConcurrentExecution
import org.quartz.Job
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.JobExecutionContext
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.stereotype.Component
import java.time.ZoneOffset
import java.util.TimeZone

@Component
@DisallowConcurrentExecution
@ConditionalOnBean(Carsync::class)
class SyncMileageJob(
    private val mileageReadingApplication: MileageReadingApplication,
) : Job {
    companion object {
        private val log = LoggerFactory.getLogger(SyncMileageJob::class.java)
    }

    override fun execute(context: JobExecutionContext?) {
        log.info("Starting scheduled syncMileage Job.")
        mileageReadingApplication.syncMileage()
        log.info("Finished scheduled syncMileage Job.")
    }
}

@Configuration
@ConditionalOnBean(Carsync::class)
class SyncMileageJobConfig {
    @Bean("syncMileageJobDetail")
    fun syncMileageJobDetail(): JobDetail =
        JobBuilder
            .newJob()
            .ofType(SyncMileageJob::class.java)
            .withIdentity("syncMileageJob")
            .withDescription("SyncMileage Job")
            .storeDurably()
            .build()

    @Bean("syncMileageJobTrigger")
    fun syncMileageJobTrigger(
        @Qualifier("syncMileageJobDetail") syncMileageJobDetail: JobDetail,
        @Value("\${carsync.scheduler.sync-mileage-job-cron}") cron: String,
    ): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(syncMileageJobDetail)
            .withIdentity("SyncMileageJobTrigger")
            .withDescription("SyncMileageJob Trigger")
            .withSchedule(
                CronScheduleBuilder
                    .cronSchedule(cron)
                    .inTimeZone(TimeZone.getTimeZone(ZoneOffset.UTC)),
            ).build()
}
