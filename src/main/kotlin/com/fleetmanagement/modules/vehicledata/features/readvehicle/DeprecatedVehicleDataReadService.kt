/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.readvehicle

import com.fleetmanagement.modules.vehicledata.features.refreshvehicle.VehicleDataSyncRefreshService
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import org.springframework.stereotype.Service
import kotlin.jvm.optionals.getOrNull

@Service
@Deprecated("This class will be removed soon. To read vehicle-data use module api implementations")
class DeprecatedVehicleDataReadService(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val vehicleDataSyncRefreshService: VehicleDataSyncRefreshService,
) {
    @Deprecated("Must be removed, please do not use this")
    fun readVehicleDataByEquiIdOrCreateIfMissing(equiId: String): JPAVehicleEntity =
        jpaVehicleRepository.findByEquiId(equiId).getOrNull()
            ?: vehicleDataSyncRefreshService.refreshVehiclesByEquiId(equiId)

    @Deprecated("Must be removed, please do not use this")
    fun readVehicleDataByEquipmentNumberOrCreateIfMissing(equipmentNumber: Long): JPAVehicleEntity =
        jpaVehicleRepository.findByEquipmentNumber(equipmentNumber).getOrNull()
            ?: vehicleDataSyncRefreshService.refreshVehiclesByEquipmentNumber(equipmentNumber)

    @Deprecated("Must be removed, please do not use this")
    fun readVehicleDataByVINOrCreateIfMissing(vin: String): JPAVehicleEntity =
        jpaVehicleRepository.findByVin(vin).getOrNull()
            ?: vehicleDataSyncRefreshService.refreshVehiclesByVIN(vin)

    @Deprecated("Must be removed, please do not use this")
    fun readVehicleDataByVGUIDs(vguids: List<String>): List<JPAVehicleEntity> {
        val savedVehicles = jpaVehicleRepository.findByVguidIn(vguids)
        val savedVehicleVguids = savedVehicles.map { it.vguid }.toSet()

        val vehicleToReload = vguids.filterNot { vguid -> vguid in savedVehicleVguids }
        val reloadedVehicles = vehicleDataSyncRefreshService.refreshVehiclesByVGUIDs(vehicleToReload)

        return savedVehicles + reloadedVehicles
    }

    @Deprecated("Must be removed, please do not use this")
    fun readVehicleDataByVGUID(vguid: String): JPAVehicleEntity =
        jpaVehicleRepository.findByVguid(vguid).getOrNull()
            ?: vehicleDataSyncRefreshService.refreshVehiclesByVGUID(vguid)
}
