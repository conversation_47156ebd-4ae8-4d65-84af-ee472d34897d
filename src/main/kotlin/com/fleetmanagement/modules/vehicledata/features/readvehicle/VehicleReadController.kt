/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.readvehicle

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import io.opentelemetry.instrumentation.annotations.SpanAttribute
import io.opentelemetry.instrumentation.annotations.WithSpan
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.*
import java.net.URLDecoder
import java.nio.charset.StandardCharsets

@RestController
@RequestMapping("/vehicles")
class VehicleReadController(
    private val vehicleDataService: DeprecatedVehicleDataReadService,
    private val objectMapper: ObjectMapper,
) {
    @WithSpan("read.vehicle")
    @GetMapping("/vin/{vin}")
    fun byVinNumber(
        @SpanAttribute("vin") @PathVariable vin: String,
    ): ResponseEntity<VehicleDTO> {
        val vehicleDataOrNull = vehicleDataService.readVehicleDataByVINOrCreateIfMissing(vin)
        return ResponseEntity.ok(VehicleDTO.from(vehicleDataOrNull, objectMapper))
    }

    @WithSpan("read.vehicle")
    @GetMapping("/equipmentNumber/{equipment_number}")
    fun byEquipmentNumber(
        @SpanAttribute("equipment_number") @PathVariable("equipment_number") equipmentNumber: Long,
    ): ResponseEntity<VehicleDTO> {
        val vehicleDataOrNull = vehicleDataService.readVehicleDataByEquipmentNumberOrCreateIfMissing(equipmentNumber)
        return ResponseEntity.ok(VehicleDTO.from(vehicleDataOrNull, objectMapper))
    }

    @WithSpan("read.vehicle")
    @GetMapping("/equiId/{equi_id}")
    fun byEquiId(
        @SpanAttribute("equi_id") @PathVariable("equi_id") equiId: String,
    ): ResponseEntity<VehicleDTO> {
        val vehicleDataOrNull = vehicleDataService.readVehicleDataByEquiIdOrCreateIfMissing(equiId)
        return ResponseEntity.ok(VehicleDTO.from(vehicleDataOrNull, objectMapper))
    }

    @WithSpan("read.vehicle")
    @GetMapping("/vguid/{vguid}")
    fun byVguid(
        @SpanAttribute("vguid") @PathVariable vguid: String,
    ): ResponseEntity<VehicleDTO> {
        val vehicleDataOrNull = vehicleDataService.readVehicleDataByVGUID(urlDecode(vguid))
        return ResponseEntity.ok(VehicleDTO.from(vehicleDataOrNull, objectMapper))
    }

    @WithSpan("read.vehicle")
    @PostMapping("/vguid")
    fun byVguids(
        @SpanAttribute("vguids") @RequestBody vguids: List<String>,
    ): ResponseEntity<List<VehicleDTO>> {
        val vehicleDataList =
            vehicleDataService.readVehicleDataByVGUIDs(vguids.map { urlDecode(it) }).map {
                VehicleDTO.from(it, objectMapper)
            }
        return ResponseEntity.ok(vehicleDataList)
    }

    private fun urlDecode(s: String): String = URLDecoder.decode(s, StandardCharsets.UTF_8)
}
