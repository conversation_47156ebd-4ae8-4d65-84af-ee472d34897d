package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadAllVehicles
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class DefaultReadAllVehicles(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val objectMapper: ObjectMapper,
) : ReadAllVehicles {
    override fun readAllVehicles(pageable: Pageable): Page<VehicleDTO> =
        jpaVehicleRepository.findAll(pageable).map { VehicleDTO.from(it, objectMapper) }
}
