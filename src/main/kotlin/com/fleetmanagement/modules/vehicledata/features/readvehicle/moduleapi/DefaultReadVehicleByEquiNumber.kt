package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByEquiNumber
import com.fleetmanagement.modules.vehicledata.api.VehicleNotFoundInFVM
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class DefaultReadVehicleByEquiNumber(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val objectMapper: ObjectMapper,
) : ReadVehicleByEquiNumber {
    override fun readVehicleByEquiNumber(equiNumber: String): VehicleDTO {
        val vehicleEntity = jpaVehicleRepository.findByEquiId(equiNumber)
        return vehicleEntity
            .map { VehicleDTO.from(it, objectMapper) }
            .orElseThrow { VehicleNotFoundInFVM("EquiID", equiNumber) }
    }
}
