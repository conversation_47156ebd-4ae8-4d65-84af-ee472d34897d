/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.VehicleNotFoundInFVM
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
@Transactional(readOnly = true)
class DefaultReadVehicleByVIN(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val objectMapper: ObjectMapper,
) : ReadVehicleByVIN {
    override fun readVehicleByVIN(vin: String): VehicleDTO {
        val vehicleEntity = jpaVehicleRepository.findByVin(vin)
        return vehicleEntity
            .map { VehicleDTO.from(it, objectMapper) }
            .orElseThrow { VehicleNotFoundInFVM("VIN", vin) }
    }
}
