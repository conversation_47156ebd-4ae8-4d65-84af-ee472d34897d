package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class DefaultReadVehicleByVehicleId(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val objectMapper: ObjectMapper,
) : ReadVehicleByVehicleId {
    override fun readVehicleById(id: UUID): VehicleDTO? =
        jpaVehicleRepository
            .findById(id)
            .map { VehicleDTO.from(it, objectMapper) }
            .orElse(null)

    override fun readVehiclesByIds(ids: List<UUID>): List<VehicleDTO> =
        jpaVehicleRepository
            .findAllById(ids)
            .map { VehicleDTO.from(it, objectMapper) }
}
