package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehiclesActivatedForAccounting
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class DefaultReadVehiclesActivatedForAccounting(
    private val vehicleRepository: JPAVehicleRepository,
    private val objectMapper: ObjectMapper,
) : ReadVehiclesActivatedForAccounting {
    override fun readVehiclesActivatedForAccounting(pageable: Pageable): Page<VehicleDTO> =
        vehicleRepository
            .findAllByIsActivatedForAccountingIsTrue(pageable)
            .map { VehicleDTO.from(it, objectMapper) }
}
