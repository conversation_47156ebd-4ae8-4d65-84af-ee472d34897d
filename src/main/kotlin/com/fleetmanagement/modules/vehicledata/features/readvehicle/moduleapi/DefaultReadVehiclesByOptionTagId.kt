package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehiclesByOptionTagId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.stereotype.Service

@Service
class DefaultReadVehiclesByOptionTagId(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val objectMapper: ObjectMapper,
) : ReadVehiclesByOptionTagId {
    override fun readAllVehiclesByOptionTagId(
        optionTagId: String,
        pageable: Pageable,
    ): Page<VehicleDTO> =
        jpaVehicleRepository
            .findAllByOptionTagId(optionTagId, pageable)
            .map { VehicleDTO.from(it, objectMapper) }
}
