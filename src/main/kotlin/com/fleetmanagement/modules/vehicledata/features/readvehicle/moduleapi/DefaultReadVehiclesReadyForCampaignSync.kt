package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehiclesReadyForCampaignSync
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAModelInfo
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAProductionInfo
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import org.springframework.data.jpa.domain.Specification
import org.springframework.stereotype.Service
import java.util.Date
import java.util.UUID

@Service
class DefaultReadVehiclesReadyForCampaignSync(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val objectMapper: ObjectMapper,
) : ReadVehiclesReadyForCampaignSync {
    override fun readVehiclesReadyForCampaignSyncByIds(ids: List<UUID>): List<VehicleDTO> =
        if (ids.isNotEmpty()) {
            jpaVehicleRepository
                .findAll(specificationVehiclesReadyForCampaignSync(ids))
                .map { VehicleDTO.from(it, objectMapper) }
                .toList()
        } else {
            emptyList()
        }

    companion object {
        private const val VEHICLE_ID = "id"
        private const val MANUFACTURER = "manufacturer"
        private const val MANUFACTURER_PORSCHE = "PORSCHE"
        private const val MODEL_INFO = "modelInfo"
        private const val PRODUCTION_INFO = "productionInfo"
        private const val PRODUCTION_END_DATE = "productionEndDate"

        private fun specificationVehiclesReadyForCampaignSync(ids: List<UUID>) =
            Specification<JPAVehicleEntity> { root, _, criteriaBuilder ->
                criteriaBuilder.and(
                    root.get<UUID>(VEHICLE_ID).`in`(ids),
                    criteriaBuilder.equal(
                        criteriaBuilder.lower(root.get<JPAModelInfo>(MODEL_INFO).get(MANUFACTURER)),
                        MANUFACTURER_PORSCHE.lowercase(),
                    ),
                    criteriaBuilder.isNotNull(
                        root.get<JPAProductionInfo>(PRODUCTION_INFO).get<Date>(PRODUCTION_END_DATE),
                    ),
                )
            }
    }
}
