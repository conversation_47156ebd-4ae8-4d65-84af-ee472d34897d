/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.readvehicle.moduleapi

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.RefreshVehicleByVGUID
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.features.refreshvehicle.VehicleDataSyncRefreshService
import org.springframework.stereotype.Service

@Service
class DefaultRefreshVehicleByVGUID(
    private val vehicleDataSyncRefreshService: VehicleDataSyncRefreshService,
    private val objectMapper: ObjectMapper,
) : RefreshVehicleByVGUID {
    override fun refreshVehicleByVGUID(vguid: String): VehicleDTO {
        val vehicleData = vehicleDataSyncRefreshService.refreshVehiclesByVGUID(vguid)
        return VehicleDTO.from(vehicleData, objectMapper)
    }
}
