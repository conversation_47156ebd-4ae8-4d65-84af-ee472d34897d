# Refresh Vehicle 

This feature is used to update vehicle-data in the fleet-management database with the data from source systems within Porsche 

Some examples of these source-systems are 
1. Porsche Vehicle Hub (PVH) 
2. EQUI (for Weissach vehicles)
3. PIA AM (P06) - Porsche Intergierte Auftragsabwicklung & Ressourcenmanagement: Auftragsmanagement

# PVH Kafka update logic
Current logic implemented in VehicleUpdatesFilter.kt (except status check on update)
```mermaid
flowchart LR
    isImporterCodeAcceptable{is importerCode\nPIV/PIS/PIX?}
    isHeaderCreate{Message action\nHeader Database-Action}
    ENDUpdateIfExist["update if exists"]
    ENDImport["import as new vehicle"]
    ENDIgnore["X"]
    isStatusUpdatable{is status updatable\n=not sold/scrapped/stolen}
    
    style ENDIgnore fill: #890000, stroke: #333, stroke-width: 2px
    
    msg[PVH Kafka Message] --> isHeaderCreate
    isHeaderCreate -- INSERT --> isImporterCodeAcceptable
    isHeaderCreate -- UPDATE --> isStatusUpdatable
    isStatusUpdatable -- yes --> ENDUpdateIfExist
    isStatusUpdatable -- no --> ENDIgnore
    isImporterCodeAcceptable -- Yes --> ENDImport
    isImporterCodeAcceptable -- No --> ENDIgnore
    style ENDImport fill: #008900, stroke: #333, stroke-width: 2px
    style ENDUpdateIfExist fill: #998900, stroke: #333, stroke-width: 2px
```

