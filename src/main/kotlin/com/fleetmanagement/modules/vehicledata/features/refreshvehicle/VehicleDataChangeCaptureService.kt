package com.fleetmanagement.modules.vehicledata.features.refreshvehicle

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.api.events.VehicleStatusChangedEvent
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehiclehistory.api.VehicleChanges
import com.fleetmanagement.modules.vehiclehistory.api.dto.VehicleChangeEvent
import com.fleetmanagement.modules.vehiclehistory.api.dto.VehicleChangeEventPayload
import org.slf4j.LoggerFactory
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Service
import java.time.OffsetDateTime
import kotlin.jvm.optionals.getOrNull

@Service
class VehicleDataChangeCaptureService(
    private val vehicleChanges: VehicleChanges,
    private val objectMapper: ObjectMapper,
    private val jpaVehicleRepository: JPAVehicleRepository,
) {
    private val logger = LoggerFactory.getLogger(VehicleDataChangeCaptureService::class.java)

    fun captureChanges(
        vehicleEntity: JPAVehicleEntity,
        modifier: String,
    ) {
        listOf(vehicleEntity).forEach { vehicle ->
            try {
                val vehicleDTO = VehicleDTO.from(vehicle, objectMapper)
                vehicleChanges.addVehicleChange(
                    VehicleChangeEvent(
                        vehicleId = vehicleDTO.id,
                        modifiedAt = OffsetDateTime.now(),
                        modifiedBy = modifier,
                        payload = VehicleChangeEventPayload(vehicle = vehicleDTO),
                    ),
                )
            } catch (exception: Exception) {
                logger.error(
                    "Failed to capture change history while fetching vehicle ${vehicle.id} from PVH ($modifier)",
                    exception,
                )
            }
        }
    }

    @EventListener
    fun handleVehicleStatusChangedEvent(event: VehicleStatusChangedEvent) {
        jpaVehicleRepository.findById(event.vehicleId).getOrNull()?.also {
            captureChanges(
                vehicleEntity = it,
                modifier = "VehicleStatusUpdate",
            )
        }
    }
}
