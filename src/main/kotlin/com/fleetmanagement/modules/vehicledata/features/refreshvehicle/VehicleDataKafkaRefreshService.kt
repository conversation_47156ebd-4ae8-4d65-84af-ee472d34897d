/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.refreshvehicle

import com.fleetmanagement.modules.vehicledata.integrations.pvh.streamzilla.KafkaMapper
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus.SX90
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus.SX97
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus.SX98
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus.SX99
import de.porsche.pvh.async.processing.api.KafkaVehicle
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional

@Service
class VehicleDataKafkaRefreshService(
    private val jpaVehicleRepository: JPAVehicleRepository,
    private val vehicleDataSyncRefreshService: VehicleDataSyncRefreshService,
) {
    private val logger = LoggerFactory.getLogger(VehicleDataKafkaRefreshService::class.java)

    companion object {
        private val NON_PVH_UPDATABLE_VEHICLE_STATUSES = setOf(SX90, SX97, SX98, SX99)
        private val IMPORTABLE_ORDER_IMPORTER_CODES = setOf("PIS", "PIV", "PIX")
    }

    @Transactional
    fun createVehicle(pvhKafkaVehicle: KafkaVehicle) {
        if (!isAcceptableImporterCode(pvhKafkaVehicle)) {
            logger.info("Not creating vehicle with VIN ${pvhKafkaVehicle.vin}, importer code does not match.")
            return
        }
        logger.info("Creating vehicle with VIN ${pvhKafkaVehicle.vin}.") // dashboard relevant log
        val createdVehicle: JPAVehicleEntity =
            mergePVHSyncAndPVHKafkaVehicle(pvhKafkaVehicle)
        vehicleDataSyncRefreshService.createOrUpdateVehicleObject { createdVehicle }
    }

    @Transactional
    fun updateIfExist(
        pvhKafkaVehicle: KafkaVehicle,
        updatedFields: List<String>,
    ) {
        val existingVehicle = jpaVehicleRepository.findByVguid(pvhKafkaVehicle.vguid)
        if (existingVehicle.isPresent) {
            updateExistingVehicle(pvhKafkaVehicle, existingVehicle.get(), updatedFields)
        } else {
            handleMigrationGapIfApplicable(updatedFields, pvhKafkaVehicle)
        }
    }

    /**
     * checks if this vehicle was missed during migration and if so, creates it
     * This gap is expected and will be closed over time as vehicles reach ZP8.
     */
    private fun handleMigrationGapIfApplicable(
        updatedFields: List<String>,
        pvhKafkaVehicle: KafkaVehicle,
    ) {
        if (isZP8ReachedWithThisUpdate(updatedFields) && isAcceptableImporterCode(pvhKafkaVehicle)) {
            logger.info("Creating missing vehicle with VIN ${pvhKafkaVehicle.vin}. This is an expected gap of the FMS>FVM migration.")
            createVehicle(pvhKafkaVehicle)
        }
    }

    /**
     * This method ensures vehicle data is fetched from PVH Sync and then PVH Kafka data is merged on top of it.
     */
    private fun mergePVHSyncAndPVHKafkaVehicle(pvhKafkaVehicle: KafkaVehicle): JPAVehicleEntity =
        try {
            val vehicle = vehicleDataSyncRefreshService.mapVehicleDataFromPVHByVguid(pvhKafkaVehicle.vguid)
            KafkaMapper.INSTANCE.map(pvhKafkaVehicle, vehicle)
        } catch (exception: Exception) {
            logger.error(
                "Failed while processing message from PVH for vguid: ${pvhKafkaVehicle.vguid}",
                exception,
            )
            KafkaMapper.INSTANCE.map(pvhKafkaVehicle)
        }

    private fun updateExistingVehicle(
        pvhKafkaVehicle: KafkaVehicle,
        existingVehicle: JPAVehicleEntity,
        updatedFields: List<String>,
    ) {
        if (existingVehicle.status !in NON_PVH_UPDATABLE_VEHICLE_STATUSES) {
            logger.info("Updating existing vehicle with VIN ${pvhKafkaVehicle.vin}.") // dashboard relevant log
            val isZP8Update = isZP8ReachedWithThisUpdate(updatedFields)
            val isOptionsDataUpdated = isOptionsDataUpdated(updatedFields)
            val updatedVehicle =
                if (isOptionsDataUpdated or isZP8Update) {
                    mergePVHSyncAndPVHKafkaVehicle(pvhKafkaVehicle)
                } else {
                    KafkaMapper.INSTANCE.map(pvhKafkaVehicle, existingVehicle)
                }

            vehicleDataSyncRefreshService.createOrUpdateVehicleObject(
                validateNKF = isOptionsDataUpdated,
            ) { updatedVehicle }
        }
    }

    private fun isZP8ReachedWithThisUpdate(updatedFields: List<String>): Boolean =
        updatedFields.contains("productionInfo.productionEndDate")

    private fun isOptionsDataUpdated(updatedFields: List<String>): Boolean = updatedFields.contains("options")

    private fun isAcceptableImporterCode(kafkaVehicle: KafkaVehicle): Boolean =
        kafkaVehicle.orderInfo?.tradingPartner?.importerCode in IMPORTABLE_ORDER_IMPORTER_CODES
}
