/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.refreshvehicle

import com.fleetmanagement.modules.consigneedatasheet.application.port.consigneedata.ReadConsigneeDataUseCase
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicle
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleInit
import com.fleetmanagement.modules.vehicledata.api.events.AutomaticVehicleCreatedEvent
import com.fleetmanagement.modules.vehicledata.api.events.ManualVehicleCreatedEvent
import com.fleetmanagement.modules.vehicledata.api.events.VehicleOptionsUpdatedEvent
import com.fleetmanagement.modules.vehicledata.api.events.VehicleUpdatedEvent
import com.fleetmanagement.modules.vehicledata.api.exceptions.PVHVehicleNotFoundException
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import com.fleetmanagement.modules.vehicledata.features.vehicleoption.VehicleOptionTagsService
import com.fleetmanagement.modules.vehicledata.integrations.equi.EquiClient
import com.fleetmanagement.modules.vehicledata.integrations.pvh.api.PVHClient
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.repository.mappers.PVHVehicleMapper
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferInitializedEvent
import feign.FeignException.NotFound
import feign.FeignException.TooManyRequests
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.event.EventListener
import org.springframework.http.HttpStatus
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.UUID
import com.fleetmanagement.modules.consigneedatasheet.domain.TireSet as ConsigneeTireSet

@Service
class VehicleDataSyncRefreshService(
    private val vehicleRepository: JPAVehicleRepository,
    private val pvhClient: PVHClient,
    private val equiClient: EquiClient,
    private val vehicleDataChangeCaptureService: VehicleDataChangeCaptureService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val readConsigneeDataUseCase: ReadConsigneeDataUseCase,
    private val updateVehicle: UpdateVehicle,
    private val vehicleOptionTags: VehicleOptionTagsService,
) {
    private val logger = LoggerFactory.getLogger(VehicleDataSyncRefreshService::class.java)

    @EventListener
    @Transactional
    fun handleInitializePlanedVehicleTransfer(event: PlannedVehicleTransferInitializedEvent) {
        val vehicleId = event.vehicleId
        val consignee = event.consignee ?: return

        val consigneeData =
            readConsigneeDataUseCase.readConsigneeDataByConsignee(consignee).firstOrNull()

        if (null == consigneeData) {
            log.warn(
                "On handling planned vehicle transfer could not find consignee data for $consignee for vehicle $vehicleId",
            )
            return
        }

        updateVehicle.initialize(
            id = VehicleId(vehicleId),
            initData =
                VehicleInit(
                    preproductionVehicle = consigneeData.isPreproductionVehicle,
                    blockedForSale = consigneeData.isBlockedForSale,
                    scrapVehicle = consigneeData.isScrapped,
                    manufacturer = consigneeData.manufacturer,
                    vehicleType = consigneeData.vehicleType,
                    tireSet = consigneeData.pdiCurrentTireSet?.toTireSet(),
                ),
        )
        log.debug(
            "Successfully handled planned vehicle transfer for vehicle with id: {}",
            vehicleId,
        )
    }

    @Transactional
    fun refreshVehiclesByVIN(vin: String): JPAVehicleEntity =
        createOrUpdateVehicleObject {
            mapVehicleObjectFromPVHByVIN(vin)
        }

    @Transactional
    fun refreshVehiclesByEquiId(equiId: String): JPAVehicleEntity =
        createOrUpdateVehicleObject {
            mapVehicleObjectByEquiID(equiId)
        }

    @Transactional
    fun refreshVehiclesByEquiIds(equiIds: List<String>): List<JPAVehicleEntity> =
        equiIds.map { createOrUpdateVehicleObject { mapVehicleObjectByEquiID(it) } }

    @Transactional
    fun refreshVehiclesByEquipmentNumber(equipmentNumber: Long): JPAVehicleEntity =
        createOrUpdateVehicleObject {
            mapVehicleObjectByEquipmentNumber(equipmentNumber)
        }

    @Transactional
    fun refreshVehiclesByEquipmentNumbers(equipmentNumbers: List<Long>): List<JPAVehicleEntity> =
        equipmentNumbers.map {
            createOrUpdateVehicleObject { mapVehicleObjectByEquipmentNumber(it) }
        }

    @Transactional
    fun refreshVehiclesByVGUID(vguid: String): JPAVehicleEntity =
        createOrUpdateVehicleObject {
            mapVehicleObjectFromPVHByVGUID(vguid)
        }

    fun mapVehicleDataFromPVHByVguid(vguid: String): JPAVehicleEntity = mapVehicleObjectFromPVHByVGUID(vguid)

    fun mapVehicleDataFromPVHByVin(vin: String): JPAVehicleEntity = mapVehicleObjectFromPVHByVIN(vin)

    @Transactional
    fun refreshVehiclesByVGUIDs(vguids: List<String>): List<JPAVehicleEntity> =
        vguids.mapNotNull { vguid ->
            runCatching { createOrUpdateVehicleObject { mapVehicleObjectFromPVHByVGUID(vguid) } }
                .onFailure { exception ->
                    when (exception) {
                        is TooManyRequests ->
                            logger.warn(
                                "Rate limit for PVH reached!",
                            ) // don't log error else we flood the alerts (need to alert on metrics)
                        is NotFound -> logger.warn("No vehicle for VGUID $vguid")
                        is PVHVehicleNotFoundException ->
                            logger.error("PVH Exception: ${exception.message}", exception)

                        else -> logger.error("Failed to load vehicle for vguid: $vguid", exception)
                    }
                }.getOrNull()
        }

    @Transactional
    fun createOrUpdateVehicleObject(
        modifier: String = "PVH-SYNC",
        validateNKF: Boolean = false,
        sendDomainEvents: Boolean = true,
        mappedVehicle: () -> JPAVehicleEntity,
    ): JPAVehicleEntity {
        val newOrUpdatedVehicleObject = mappedVehicle()
        val isNewVehicleObject = newOrUpdatedVehicleObject.id == null
        val vehicleToCreateOrUpdate =
            executePreVehicleCreateOrUpdateTasks(newOrUpdatedVehicleObject, isNewVehicleObject)
        val savedVehicle = vehicleRepository.save(vehicleToCreateOrUpdate)
        executePostVehicleCreateOrUpdateTasks(savedVehicle, isNewVehicleObject, modifier, validateNKF, sendDomainEvents)
        if (isNewVehicleObject) {
            logger.info("Created vehicle with id ${savedVehicle.id}")
        } else {
            logger.info("Updated vehicle with id ${savedVehicle.id}")
        }
        return savedVehicle
    }

    private fun executePreVehicleCreateOrUpdateTasks(
        vehicle: JPAVehicleEntity,
        isNewVehicle: Boolean,
    ): JPAVehicleEntity {
        if (isNewVehicle) {
            vehicle.modelInfo?.calculateModelRange()
            if (listOf(VehicleSource.PVH_IMPORT, VehicleSource.PVH).contains(vehicle.source)) {
                vehicle.modelInfo?.setDefaultManufacturer()
            }
        }
        return vehicle
    }

    private fun executePostVehicleCreateOrUpdateTasks(
        savedVehicle: JPAVehicleEntity,
        isNewVehicleObject: Boolean,
        modifier: String,
        validateNKF: Boolean,
        sendDomainEvents: Boolean,
    ) {
        vehicleDataChangeCaptureService.captureChanges(savedVehicle, modifier)
        vehicleOptionTags.createTagsAsync(savedVehicle.id, savedVehicle.options)
        if (sendDomainEvents) {
            publishDomainEvent(savedVehicle, isNewVehicleObject, validateNKF)
        }
    }

    private fun publishDomainEvent(
        savedVehicle: JPAVehicleEntity,
        isNewVehicleObject: Boolean,
        validateNKF: Boolean,
    ) {
        val savedVehicleId = savedVehicle.id
        if (savedVehicleId == null) {
            logger.warn("Null vehicleId found, application event cannot be published")
            return
        }

        val event =
            when {
                isNewVehicleObject && savedVehicle.source != VehicleSource.MANUAL ->
                    AutomaticVehicleCreatedEvent.from(
                        savedVehicleId,
                        savedVehicle,
                    )

                isNewVehicleObject && savedVehicle.source == VehicleSource.MANUAL ->
                    ManualVehicleCreatedEvent(
                        eventId = UUID.randomUUID(),
                        occurredOn = OffsetDateTime.now(),
                        vehicleId = savedVehicleId,
                    )

                else -> VehicleUpdatedEvent.from(savedVehicleId)
            }
        applicationEventPublisher.publishEvent(event)

        if (validateNKF or isNewVehicleObject) {
            applicationEventPublisher.publishEvent(
                VehicleOptionsUpdatedEvent(
                    vehicleId = requireNotNull(savedVehicle.id) { "Vehicle ID should not be null" },
                ),
            )
        }
    }

    private fun mapVehicleObjectFromPVHByVIN(vin: String): JPAVehicleEntity {
        val pvhResponse = pvhClient.vehicleDataByVin(vin)
        val vehicleFromPVH = pvhResponse.body

        if (pvhResponse.statusCode == HttpStatus.NO_CONTENT || vehicleFromPVH == null) {
            throw PVHVehicleNotFoundException("vin($vin)")
        }

        val existingVehicle =
            vehicleRepository.findByVin(vin).orElseGet {
                JPAVehicleEntity(vin = vehicleFromPVH.vin, vguid = vehicleFromPVH.vguid)
            }

        return PVHVehicleMapper.INSTANCE.map(vehicleFromPVH, existingVehicle)
    }

    private fun mapVehicleObjectFromPVHByVGUID(vguid: String): JPAVehicleEntity {
        val pvhResponse = pvhClient.vehicleDataByVguid(vguid)
        val vehicleFromPVH = pvhResponse.body

        if (pvhResponse.statusCode == HttpStatus.NO_CONTENT || vehicleFromPVH == null) {
            throw PVHVehicleNotFoundException("vguid($vguid)")
        }

        val existingVehicle =
            vehicleRepository.findByVguid(vguid).orElseGet {
                JPAVehicleEntity(vin = vehicleFromPVH.vin, vguid = vehicleFromPVH.vguid)
            }

        return PVHVehicleMapper.INSTANCE.map(vehicleFromPVH, existingVehicle)
    }

    private fun mapVehicleObjectByEquiID(equiId: String): JPAVehicleEntity =
        equiClient.findVehicleDataByEquiId(equiId)
            ?: throw VehicleNotFoundException("Vehicle with equiId $equiId not found")

    private fun mapVehicleObjectByEquipmentNumber(equipmentNumber: Long): JPAVehicleEntity =
        equiClient.findVehicleDataByEquipmentNumber(equipmentNumber)
            ?: throw VehicleNotFoundException(
                "Vehicle with equipment number: $equipmentNumber not found",
            )

    private fun ConsigneeTireSet.toTireSet(): TireSet =
        when (this) {
            ConsigneeTireSet.SR -> TireSet.SR
            ConsigneeTireSet.WR -> TireSet.WR
        }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleDataSyncRefreshService::class.java)
    }
}
