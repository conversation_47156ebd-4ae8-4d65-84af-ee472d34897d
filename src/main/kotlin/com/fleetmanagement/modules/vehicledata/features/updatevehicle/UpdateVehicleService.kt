/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.updatevehicle

import com.fleetmanagement.modules.vehicledata.api.*
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.dtos.MileageReadingSource
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleInit
import com.fleetmanagement.modules.vehicledata.api.events.VehicleNextProcessChangedEvent
import com.fleetmanagement.modules.vehicledata.api.events.VehicleStatusChangedEvent
import com.fleetmanagement.modules.vehicledata.api.events.VehicleUpdatedEvent
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleNotFoundException
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import com.fleetmanagement.modules.vehicledata.features.refreshvehicle.VehicleDataChangeCaptureService
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAProductionInfo
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehicletransfer.application.port.ReadVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferManualCreatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferCancelledEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferReturnedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferStatus
import org.slf4j.LoggerFactory
import org.springframework.context.ApplicationEventPublisher
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener
import java.time.OffsetDateTime
import java.util.*
import kotlin.jvm.optionals.getOrElse
import kotlin.jvm.optionals.getOrNull

@Component
@Transactional
class UpdateVehicleService(
    private val vehicleRepository: JPAVehicleRepository,
    private val vehicleDataChangeCaptureService: VehicleDataChangeCaptureService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val readVehicleTransferUseCase: ReadVehicleTransferUseCase,
    private val addCurrentMileageReading: AddCurrentMileageReading,
    private val createOrUpdateFinancialAssetTypeUpdate: CreateOrUpdateFinancialAssetTypeUpdate,
) : UpdateVehicle,
    UpdateVehicleStatus,
    UpdateVehicleFactoryCarPreparationOrderNumber,
    UpdateVehicleBlockedForSale,
    UpdateCostEstimationOrderedDate,
    UpdateVehicleNextProcess,
    UpdateFinancialAssetType,
    UpdateVehicleSoldDate,
    UpdateVehicleFinancialAssetType,
    ResetStolenDate,
    ResetApprovedForScrappingDate,
    ResetPreparationDoneDate,
    ResetCostEstimationOrderedDate,
    ResetIsResidualValueMarket,
    ResetProfitabilityAuditDate {
    override fun initialize(
        id: VehicleId,
        initData: VehicleInit,
    ) {
        // do not fail on missing vehicle, as exception handling is currently lacking
        val vehicle = vehicleRepository.findById(id.value).getOrNull() ?: return

        vehicle.initialize(
            scrapVehicle = initData.scrapVehicle,
            preproductionVehicle = initData.preproductionVehicle,
            blockedForSale = initData.blockedForSale,
            blockedForSaleSource = BlockedForSaleSource.CONSIGNEE_DATA,
            vehicleType = initData.vehicleType,
            manufacturer = initData.manufacturer,
            tireSet = initData.tireSet,
        )

        // FIXME this should not be called directly to reduce dependencies
        // (switch to proper domain events, and handle them within change capture service)
        vehicleDataChangeCaptureService.captureChanges(
            vehicleEntity = vehicle,
            modifier = "PlannedVehicleTransferInitializedEvent",
        )
    }

    override fun updateVehicle(
        vehicleId: UUID,
        vehicleUpdateDto: VehicleUpdateDto,
        modifier: String,
    ) {
        val vehicle =
            vehicleRepository.findById(vehicleId).getOrElse {
                throw VehicleNotFoundException(
                    message = "Vehicle with id [$vehicleId] could not be found.",
                )
            }

        // FPT1-1136 add a new current mileage reading if updated value was supplied
        addNewMileageReading(
            vehicleId = vehicleId,
            currentMileage = vehicleUpdateDto.currentMileage,
            readDate = vehicleUpdateDto.currentMileageReadDate,
        )

        val nextProcessBeforeUpdate = vehicle.returnInfo?.nextProcess

        val hasActiveVehicleTransfer =
            null != readVehicleTransferUseCase.findActiveVehicleTransferForVehicle(vehicleId = vehicleId)

        vehicle
            .updatePrivileged(
                hasActiveVehicleTransfer = hasActiveVehicleTransfer,
                referenceId = vehicleUpdateDto.referenceId,
                purchaseOrderDate = vehicleUpdateDto.purchaseOrderDate,
                requestedDeliveryDate = vehicleUpdateDto.requestedDeliveryDate,
                deliveryType = vehicleUpdateDto.deliveryType,
                primaryStatus = vehicleUpdateDto.primaryStatus,
                preproductionVehicle = vehicleUpdateDto.preproductionVehicle,
                blockedForSale = vehicleUpdateDto.blockedForSale,
                blockedForSaleSource = BlockedForSaleSource.MANUAL_UPDATE,
                scrapVehicle = vehicleUpdateDto.scrapVehicle,
                primaryFuelType = vehicleUpdateDto.primaryFuelType,
                secondaryFuelType = vehicleUpdateDto.secondaryFuelType,
                currentTires = vehicleUpdateDto.currentTires,
                externalLeaseStart = vehicleUpdateDto.externalLeaseStart,
                externalLeaseEnd = vehicleUpdateDto.externalLeaseEnd,
                externalLeaseLessee = vehicleUpdateDto.externalLeaseLessee,
                externalLeaseRate = vehicleUpdateDto.externalLeaseRate,
                amountSeats = vehicleUpdateDto.amountSeats,
                engineCapacity = vehicleUpdateDto.engineCapacity,
                netPriceWithExtras = vehicleUpdateDto.netPriceWithExtras,
                soldDate = vehicleUpdateDto.soldDate,
                scrappedDate = vehicleUpdateDto.scrappedDate,
                stolenDate = vehicleUpdateDto.stolenDate,
                soldCupCarDate = vehicleUpdateDto.soldCupCarDate,
                approvedForScrappingDate = vehicleUpdateDto.approvedForScrappingDate,
                scrappedVehicleOfferedDate = vehicleUpdateDto.scrappedVehicleOfferedDate,
                vehicleSentToSalesDate = vehicleUpdateDto.vehicleSentToSalesDate,
                costEstimationOrderedDate = vehicleUpdateDto.costEstimationOrderedDate,
                isResidualValueMarket = vehicleUpdateDto.isResidualValueMarket,
                profitabilityAuditDate = vehicleUpdateDto.profitabilityAuditDate,
                comment = vehicleUpdateDto.comment,
                preparationDoneDate = vehicleUpdateDto.preparationDoneDate,
                isPreparationNecessary = vehicleUpdateDto.isPreparationNecessary,
                nextProcess = vehicleUpdateDto.nextProcess,
                tireSetChangeOrderedDate = vehicleUpdateDto.tireSetChangeOrderedDate,
                tireSetChangeCompletedDate = vehicleUpdateDto.tireSetChangeCompletedDate,
                tireSetChangeComment = vehicleUpdateDto.tireSetChangeComment,
                appraisalNetPrice = vehicleUpdateDto.appraisalNetPrice,
                pcComplaintCheckComment = vehicleUpdateDto.pcComplaintCheckComment,
                vehicleEvaluationComment = vehicleUpdateDto.vehicleEvaluationComment,
                isClassic = vehicleUpdateDto.classic,
                keyReturned = vehicleUpdateDto.keyReturned,
                tuevAppointment = vehicleUpdateDto.tuevAppointment,
                modelDescription = vehicleUpdateDto.modelDescription,
                orderType = vehicleUpdateDto.orderType,
                countryInfo = vehicleUpdateDto.countryInfo,
                exteriorColor = vehicleUpdateDto.exteriorColor,
                interiorColor = vehicleUpdateDto.interiorColor,
                productionEndDate = vehicleUpdateDto.productionEndDate,
                raceCar = vehicleUpdateDto.raceCar,
            )?.also {
                applicationEventPublisher.publishEvent(it)

                // FIXME this should not be called directly to reduce dependencies
                // (switch to proper domain events, and handle them within change capture service)
                vehicleDataChangeCaptureService.captureChanges(
                    vehicleEntity = vehicle,
                    modifier = modifier,
                )
            }

        if (nextProcessBeforeUpdate != vehicle.returnInfo?.nextProcess) {
            applicationEventPublisher.publishEvent(
                VehicleNextProcessChangedEvent(
                    vehicleId = requireNotNull(vehicle.id),
                    nextProcess = vehicle.returnInfo?.nextProcess,
                ),
            )
        }
    }

    /**
     * Will check if user supplied a new mileage reading and create one if necessary.
     * This is done, as the creation of new mileage readings did not get a dedicated process/endpoint
     * but got bundled up with generic DLZ update.
     * Be careful, as behavior differs from a normal update, as we do not just change properties,
     * but create and save a new reading for each 'update' of the current mileage.
     *
     * @param currentMileage the new mileage used for adding a new reading, reading is only created if an actual value is present
     * @param readDate the read date provided by FMV. Currently not supported (ignored), as it cannot be changed on FVM side. This might change in the future.
     */
    private fun addNewMileageReading(
        vehicleId: UUID,
        currentMileage: Optional<Int>?,
        @Suppress("UNUSED_PARAMETER") readDate: Optional<OffsetDateTime>?,
    ) {
        if (null == currentMileage || currentMileage.isEmpty) {
            return
        }
        addCurrentMileageReading.addCurrentMileageReading(
            vehicleId = vehicleId,
            mileage = currentMileage.get(),
            readDate = OffsetDateTime.now(),
            // always manual, other sources will have dedicated services
            source = MileageReadingSource.MANUAL,
        )
    }

    override fun updateMigrationVehicle(
        vin: String,
        vehicleUpdateDto: MigrationVehicleUpdateDto,
    ) {
        val vehicle =
            vehicleRepository.findByVin(vin).getOrElse {
                throw VehicleNotFoundException(
                    message = "Vehicle with VIN [$vin] could not be found.",
                )
            }
        val hasActiveVehicleTransfer =
            null != readVehicleTransferUseCase.findActiveVehicleTransferForVehicle(vehicleId = requireNotNull(vehicle.id))
        vehicle
            .updatePrivileged(
                hasActiveVehicleTransfer = hasActiveVehicleTransfer,
                referenceId = null,
                purchaseOrderDate = null,
                requestedDeliveryDate = null,
                deliveryType = null,
                primaryStatus = null,
                preproductionVehicle = null,
                blockedForSale = vehicleUpdateDto.blockedForSale,
                blockedForSaleSource = BlockedForSaleSource.MANUAL_UPDATE,
                scrapVehicle = vehicleUpdateDto.scrapVehicle,
                primaryFuelType = null,
                secondaryFuelType = null,
                currentTires = null,
                externalLeaseStart = null,
                externalLeaseEnd = null,
                externalLeaseLessee = null,
                externalLeaseRate = null,
                amountSeats = null,
                engineCapacity = null,
                netPriceWithExtras = null,
                soldDate = vehicleUpdateDto.soldDate,
                scrappedDate = vehicleUpdateDto.scrappedDate,
                stolenDate = vehicleUpdateDto.stolenDate,
                soldCupCarDate = vehicleUpdateDto.soldCupCarDate,
                approvedForScrappingDate = vehicleUpdateDto.approvedForScrappingDate,
                scrappedVehicleOfferedDate = vehicleUpdateDto.scrappedVehicleOfferedDate,
                vehicleSentToSalesDate = vehicleUpdateDto.vehicleSentToSalesDate,
                costEstimationOrderedDate = vehicleUpdateDto.costEstimationOrderedDate,
                isResidualValueMarket = vehicleUpdateDto.isResidualValueMarket,
                profitabilityAuditDate = vehicleUpdateDto.profitabilityAuditDate,
                comment = null,
                preparationDoneDate = vehicleUpdateDto.preparationDoneDate,
                isPreparationNecessary = vehicleUpdateDto.isPreparationNecessary,
                nextProcess = vehicleUpdateDto.nextProcess,
                tireSetChangeOrderedDate = null,
                tireSetChangeCompletedDate = null,
                tireSetChangeComment = null,
                appraisalNetPrice = vehicleUpdateDto.appraisalNetPrice,
                pcComplaintCheckComment = null,
                vehicleEvaluationComment = null,
                // TODO: maximumServiceLifeInMonths will be migrated to vehicle as part of FPT1-1147, uncomment this then
                // maximumServiceLifeInMonths = vehicleUpdateDto.maximumServiceLifeInMonths,
                isClassic = vehicleUpdateDto.isClassic,
                keyReturned = null,
                tuevAppointment = null,
                productionEndDate = null,
                countryInfo = null,
                orderType = null,
                modelDescription = null,
                interiorColor = null,
                exteriorColor = null,
                raceCar = Optional.of(false),
            )
        vehicle.updateEquiData(vehicleUpdateDto.equipmentId, vehicleUpdateDto.equipmentNumber)
        vehicleUpdateDto.factoryCarPreparationOrderNumber?.ifPresent { vehicle.updateFactoryCarPreparationOrderNumber(it) }
        vehicleUpdateDto.isUsedCar?.ifPresent { vehicle.updateIsUsedCar(it) }
        val productionInfo = vehicle.productionInfo ?: JPAProductionInfo()
        productionInfo.updatePrivileged(productionEndDate = vehicleUpdateDto.zp8Date)
        vehicleUpdateDto.newVehicleInvoiceDate?.ifPresent { vehicle.updateNewVehicleInvoiceDate(it) }
        vehicleUpdateDto.netPriceWithExtras?.ifPresent { netPrice ->
            when (vehicle.source) {
                VehicleSource.PVH, VehicleSource.PVH_IMPORT -> {
                    vehicle.updateFactoryPrice(netPrice)
                }

                VehicleSource.MANUAL -> {
                    vehicle.updateNetPriceWithExtras(netPrice)
                }

                else -> {}
            }
        }
    }

    override fun updateVTStammVehicle(
        vin: String,
        vehicleUpdateDto: VTStammVehicleUpdateDto,
        modifier: String,
    ) {
        val vehicle =
            vehicleRepository.findByVin(vin).getOrElse {
                throw VehicleNotFoundException(
                    message = "Vehicle with VIN [$vin] could not be found.",
                )
            }

        vehicle
            .updateVTStamm(
                equipmentNumber = vehicleUpdateDto.equipmentNumber,
                tuevAppointment = vehicleUpdateDto.tuevAppointment,
                internalVehicleDescription = vehicleUpdateDto.internalVehicleDescription,
                internalDesignation = vehicleUpdateDto.internalDesignation,
                subjectToConfidentiality = vehicleUpdateDto.subjectToConfidentiality,
                subjectToConfidentialityStartDate = vehicleUpdateDto.subjectToConfidentialityStartDate,
                subjectToConfidentialityEndDate = vehicleUpdateDto.subjectToConfidentialityEndDate,
                confidentialityClassification = vehicleUpdateDto.confidentialityClassification,
                recordFactoryExit = vehicleUpdateDto.recordFactoryExit,
                camouflageRequired = vehicleUpdateDto.camouflageRequired,
                rangeDevelopment = vehicleUpdateDto.rangeDevelopment,
                modelDescriptionDevelopment = vehicleUpdateDto.modelDescriptionDevelopment,
                typeOfUseVTS = vehicleUpdateDto.typeOfUseVTS,
                statusVTS = vehicleUpdateDto.statusVTS,
            )?.also {
                applicationEventPublisher.publishEvent(it)

                // FIXME this should not be called directly to reduce dependencies
                // (switch to proper domain events, and handle them within change capture service)
                vehicleDataChangeCaptureService.captureChanges(
                    vehicleEntity = vehicle,
                    modifier = modifier,
                )
            }
    }

    override fun hotfixProductionEndDate(
        vin: String,
        productionEndDate: OffsetDateTime,
    ) {
        val vehicle =
            vehicleRepository.findByVin(vin).getOrElse {
                throw VehicleNotFoundException(
                    message = "Vehicle with VIN [$vin] could not be found.",
                )
            }
        vehicle.updateProductionEndDate(
            productionEndDate = productionEndDate,
        )
    }

    private fun checkForVersionMismatch(
        vehicle: JPAVehicleEntity,
        providedVersion: Int,
    ) {
        if (vehicle.version != providedVersion) {
            val message = "Could not update vehicle with id [${vehicle.id}]. Invalid version supplied."
            throw VehicleUpdateException(
                message = message,
                cause = ConcurrentModificationException(message),
                constraintViolation = null,
            )
        }
    }

    @Synchronized // synchronized to avoid issues with concurrent vehicle entity modifications
    override fun updateVehicleStatus(
        vehicleId: UUID,
        vehicleStatus: VehicleStatus,
    ) {
        val vehicle =
            vehicleRepository.findById(vehicleId).getOrNull()

        if (null == vehicle) {
            log.warn("Could not update vehicle status for $vehicleId, as vehicle with id $vehicleId does not exist.")
        } else {
            log.info("Updating status for $vehicleId, old: [${vehicle.status.name}], new: [${vehicleStatus.name}].")
            val statusChanged = vehicle.updateStatus(vehicleStatus = vehicleStatus)
            if (statusChanged) {
                applicationEventPublisher.publishEvent(
                    VehicleStatusChangedEvent(vehicleId = vehicleId, status = vehicle.status),
                )
            }
        }
    }

    override fun updateVehicleFactoryCarPreparationOrderNumber(
        vehicleId: UUID,
        factoryCarPreparationOrderNumber: String,
    ) {
        val vehicle =
            vehicleRepository.findById(vehicleId).getOrNull()

        if (null == vehicle) {
            log.warn("Could not update factory car preparation order number for $vehicleId, as vehicle with id $vehicleId does not exist.")
        } else {
            vehicle.updateFactoryCarPreparationOrderNumber(factoryCarPreparationOrderNumber = factoryCarPreparationOrderNumber)
        }
    }

    override fun updateVehicleBlockedForSale(
        vehicleId: UUID,
        blockedForSale: Boolean,
        source: String,
    ) {
        val vehicle = vehicleRepository.findById(vehicleId).getOrNull()
        if (null == vehicle) {
            log.warn("Could not update vehicle blocked for sale for $vehicleId, as vehicle with id $vehicleId does not exist.")
        } else {
            vehicle.updateBlockedForSale(blockedForSale, source)
        }
    }

    override fun updateFinancialAssetType(
        vin: String,
        financialAssetType: FinancialAssetType,
    ) {
        val vehicle = vehicleRepository.findByVin(vin).getOrNull()
        if (null == vehicle) {
            /**
             * it is possible to receive financial asset type for vehicles which are not yet in FVM system,
             * we will store these in a separate table and sync to FVM vehicles later
             */
            createOrUpdateFinancialAssetTypeUpdate.createOrUpdateFinancialAssetTypeUpdate(
                vin = vin,
                financialAssetType = financialAssetType,
            )
        } else {
            vehicle.updateFinancialAssetType(financialAssetType)
        }
    }

    override fun updateVehicleFinancialAssetType(
        vehicleId: UUID,
        financialAssetType: FinancialAssetType,
    ) {
        vehicleRepository
            .findById(vehicleId)
            .getOrNull()
            ?.apply {
                updateFinancialAssetType(financialAssetType)
            }
            ?: log.warn("Could not update vehicle financialAssetType to $financialAssetType for $vehicleId, vehicle not found")
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun updateVehicleOnManualVehicleTransferCreated(event: PlannedVehicleTransferManualCreatedEvent) {
        val vehicle =
            vehicleRepository.findById(event.vehicleId).getOrNull()

        if (null == vehicle) {
            log.warn("vehicle with id $event.vehicleId does not exist.")
        } else {
            vehicle.updateNextProcess(null)
            vehicle.updateScrappedOfferDate(null)
            vehicleDataChangeCaptureService.captureChanges(
                vehicleEntity = vehicle,
                modifier = "PlannedVehicleTransferManualCreatedEvent",
            )
        }
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun updateVehicleOnReturnedEvent(event: VehicleTransferReturnedEvent) {
        val vehicle =
            vehicleRepository.findById(event.vehicleId).getOrNull()

        if (null == vehicle) {
            log.warn("vehicle with id $event.vehicleId does not exist.")
        } else {
            vehicle.updateIsUsedCar(true)
            vehicleDataChangeCaptureService.captureChanges(
                vehicleEntity = vehicle,
                modifier = "VehicleTransferReturnedEvent",
            )
        }
    }

    /**
     * if a vehicle transfer is cancelled
     * (and there are no other RETURNED vehicleTransfers, aka "the first VT was cancelled"),
     * the usedCar flag has to be set to false again
     */
    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun updateVehicleOnCanceledEvent(event: VehicleTransferCancelledEvent) {
        val finishedVehicleTransfers =
            readVehicleTransferUseCase
                .findVehicleTransfersByVehicleId(event.vehicleId)
                .filter { it.status == VehicleTransferStatus.FINISHED }
        if (finishedVehicleTransfers.isNotEmpty()) {
            log.info(" there are no other RETURNED vehicleTransfers no need to update")
            return
        }

        val vehicle =
            vehicleRepository.findById(event.vehicleId).getOrNull()

        if (null == vehicle) {
            log.warn("vehicle with id $event.vehicleId does not exist.")
        } else {
            vehicle.updateIsUsedCar(false)
            vehicleDataChangeCaptureService.captureChanges(
                vehicleEntity = vehicle,
                modifier = "VehicleTransferCancelledEvent",
            )
        }
    }

    override fun updateCostEstimationOrderedDate(
        vehicleIds: List<UUID>,
        costEstimationOrderedDate: OffsetDateTime,
    ) {
        if (vehicleIds.isEmpty()) return

        val vehicles =
            vehicleRepository.findAllById(vehicleIds)

        vehicles.forEach {
            it.updateCostEstimationOrderedDate(costEstimationOrderedDate = costEstimationOrderedDate)
            applicationEventPublisher.publishEvent(
                VehicleUpdatedEvent(vehicleId = requireNotNull(it.id)),
            )
            vehicleDataChangeCaptureService.captureChanges(
                vehicleEntity = it,
                modifier = "CostEstimationOrderedDateUpdate",
            )
        }
    }

    override fun updateNextProcess(
        vehicleId: UUID,
        nextProcess: NextProcess,
    ) {
        val vehicle =
            vehicleRepository.findById(vehicleId).getOrNull()

        if (null == vehicle) {
            log.warn("Could not update nextProcess, as vehicle with id $vehicleId does not exist.")
        } else {
            val nextProcessBeforeUpdate = vehicle.returnInfo?.nextProcess

            vehicle.updateNextProcess(nextProcess = nextProcess)

            if (nextProcessBeforeUpdate != vehicle.returnInfo?.nextProcess) {
                applicationEventPublisher.publishEvent(
                    VehicleNextProcessChangedEvent(
                        vehicleId = requireNotNull(vehicle.id),
                        nextProcess = vehicle.returnInfo?.nextProcess,
                    ),
                )
            }
        }
    }

    override fun updateVehicleSoldDate(
        vehicleId: UUID,
        soldDate: OffsetDateTime?,
        modifier: String,
    ) {
        val vehicle = vehicleRepository.findById(vehicleId).getOrNull() ?: return
        val hasActiveVehicleTransfer =
            null != readVehicleTransferUseCase.findActiveVehicleTransferForVehicle(vehicleId = vehicleId)
        vehicle
            .updateSoldDate(
                hasActiveVehicleTransfer = hasActiveVehicleTransfer,
                soldDate = soldDate,
            ).also {
                vehicleDataChangeCaptureService.captureChanges(
                    vehicleEntity = vehicle,
                    modifier = modifier,
                )
                applicationEventPublisher.publishEvent(VehicleUpdatedEvent(vehicleId = vehicleId))
            }
    }

    override fun resetStolenDate(vehicleId: UUID) {
        val vehicle = vehicleRepository.findById(vehicleId).getOrNull()
        if (null == vehicle) {
            log.warn("Could not reset stolenDate, as vehicle with id $vehicleId does not exist.")
        } else {
            vehicle.resetStolenDate()
        }
    }

    override fun resetApprovedForScrappingDate(vehicleId: UUID) {
        val vehicle = vehicleRepository.findById(vehicleId).getOrNull()
        if (null == vehicle) {
            log.warn("Could not reset ApprovedForScrappingDate, as vehicle with id $vehicleId does not exist.")
        } else {
            vehicle.resetApprovedForScrappingDate()
        }
    }

    override fun resetPreparationDoneDate(vehicleId: UUID) {
        val vehicle = vehicleRepository.findById(vehicleId).getOrNull()
        if (null == vehicle) {
            log.warn("Could not reset PreparationDoneDate, as vehicle with id $vehicleId does not exist.")
        } else {
            vehicle.resetPreparationDoneDate()
        }
    }

    override fun resetCostEstimationOrderedDate(vehicleId: UUID) {
        val vehicle = vehicleRepository.findById(vehicleId).getOrNull()
        if (null == vehicle) {
            log.warn("Could not reset CostEstimationOrderedDate, as vehicle with id $vehicleId does not exist.")
        } else {
            vehicle.resetCostEstimationOrderedDate()
        }
    }

    override fun resetIsResidualValueMarket(vehicleId: UUID) {
        val vehicle = vehicleRepository.findById(vehicleId).getOrNull()
        if (null == vehicle) {
            log.warn("Could not reset IsResidualValueMarket, as vehicle with id $vehicleId does not exist.")
        } else {
            vehicle.resetIsResidualValueMarket()
        }
    }

    override fun resetProfitabilityAuditDate(vehicleId: UUID) {
        val vehicle = vehicleRepository.findById(vehicleId).getOrNull()
        if (null == vehicle) {
            log.warn("Could not reset ProfitabilityAuditDate, as vehicle with id $vehicleId does not exist.")
        } else {
            vehicle.resetProfitabilityAuditDate()
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(UpdateVehicleService::class.java)
    }
}
