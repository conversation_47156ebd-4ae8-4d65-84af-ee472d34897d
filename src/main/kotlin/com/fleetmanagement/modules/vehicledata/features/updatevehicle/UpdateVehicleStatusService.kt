/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.features.updatevehicle

import com.fleetmanagement.modules.predeliveryinspection.application.PreDeliveryInspectionFinder
import com.fleetmanagement.modules.predeliveryinspection.domain.PreDeliveryInspectionUpdatedEvent
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicleStatus
import com.fleetmanagement.modules.vehicledata.api.events.VehicleCreatedEvent
import com.fleetmanagement.modules.vehicledata.api.events.VehicleUpdatedEvent
import com.fleetmanagement.modules.vehicledata.features.calculatevehiclestatus.VehicleStatusCalculationService
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.domain.*
import org.springframework.context.event.EventListener
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Component
class UpdateVehicleStatusService(
    private val updateVehicleStatus: UpdateVehicleStatus,
    private val vehicleStatusCalculationService: VehicleStatusCalculationService,
    private val vehicleTransferFinder: VehicleTransferFinder,
    private val plannedVehicleTransferFinder: PlannedVehicleTransferFinder,
    private val preDeliveryInspectionFinder: PreDeliveryInspectionFinder,
) {
    @EventListener
    @Transactional
    fun handleVehicleUpdatedEvent(event: VehicleUpdatedEvent) {
        val newStatus = vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = event.vehicleId)

        updateVehicleStatus.updateVehicleStatus(vehicleId = event.vehicleId, vehicleStatus = newStatus)
    }

    @EventListener
    @Transactional
    fun handleVehicleCreatedEvent(event: VehicleCreatedEvent) {
        val newStatus = vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = event.vehicleId)

        updateVehicleStatus.updateVehicleStatus(vehicleId = event.vehicleId, vehicleStatus = newStatus)
    }

    @EventListener
    @Transactional
    fun handleVehicleTransferUpdatedEvent(event: VehicleTransferUpdatedEvent) {
        val vehicleTransfer = vehicleTransferFinder.getVehicleTransfer(key = event.vehicleTransferKey)
        val newStatus = vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = vehicleTransfer.vehicleId)

        updateVehicleStatus.updateVehicleStatus(vehicleId = vehicleTransfer.vehicleId, vehicleStatus = newStatus)
    }

    @EventListener
    @Transactional
    fun handlePlannedVehicleTransferCreatedEvent(event: PlannedVehicleTransferCreatedEvent) {
        val plannedVehicleTransfer =
            plannedVehicleTransferFinder.getPlannedVehicleTransfer(key = event.plannedVehicleTransferKey)
        val newStatus =
            vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = plannedVehicleTransfer.vehicleId)

        updateVehicleStatus.updateVehicleStatus(vehicleId = plannedVehicleTransfer.vehicleId, vehicleStatus = newStatus)
    }

    @EventListener
    @Transactional
    fun handlePlannedVehicleTransferInitializedEvent(event: PlannedVehicleTransferInitializedEvent) {
        val plannedVehicleTransfer =
            plannedVehicleTransferFinder.getPlannedVehicleTransfer(key = event.plannedVehicleTransferKey)
        val newStatus =
            vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = plannedVehicleTransfer.vehicleId)

        updateVehicleStatus.updateVehicleStatus(vehicleId = plannedVehicleTransfer.vehicleId, vehicleStatus = newStatus)
    }

    @EventListener
    @Transactional
    fun handleVehicleTransferDeliveredEvent(event: VehicleTransferDeliveredEvent) {
        val vehicleTransfer = vehicleTransferFinder.getVehicleTransfer(key = event.vehicleTransferKey)
        val newStatus = vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = vehicleTransfer.vehicleId)

        updateVehicleStatus.updateVehicleStatus(vehicleId = vehicleTransfer.vehicleId, vehicleStatus = newStatus)
    }

    @EventListener
    @Transactional
    fun handlePlannedVehicleTransferUpdatedEvent(event: PlannedVehicleTransferUpdatedEvent) {
        val plannedVehicleTransfer =
            plannedVehicleTransferFinder.getPlannedVehicleTransfer(key = event.plannedVehicleTransferKey)
        val newStatus =
            vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = plannedVehicleTransfer.vehicleId)

        updateVehicleStatus.updateVehicleStatus(vehicleId = plannedVehicleTransfer.vehicleId, vehicleStatus = newStatus)
    }

    @EventListener
    @Transactional
    fun handlePreDeliveryInspectionUpdatedEvent(event: PreDeliveryInspectionUpdatedEvent) {
        val preDeliveryInspection =
            preDeliveryInspectionFinder.getPreDeliveryInspection(id = event.preDeliveryInspectionId)
        val newStatus =
            vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = preDeliveryInspection.vehicleId)

        updateVehicleStatus.updateVehicleStatus(vehicleId = preDeliveryInspection.vehicleId, vehicleStatus = newStatus)
    }

    @EventListener
    @Transactional
    fun handleVehicleTransferMigratedEvent(event: VehicleTransferMigratedEvent) {
        // because we do not know what got migrated
        val plannedVehicleTransfer =
            plannedVehicleTransferFinder.findPlannedVehicleTransferByKey(key = event.vehicleTransferKey)
        if (null != plannedVehicleTransfer) {
            val newStatus =
                vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = event.vehicleId)
            updateVehicleStatus.updateVehicleStatus(vehicleId = event.vehicleId, vehicleStatus = newStatus)
            return
        }

        // we have to try both
        val vehicleTransfer =
            vehicleTransferFinder.findVehicleTransferByKey(key = event.vehicleTransferKey)
        if (null != vehicleTransfer) {
            val newStatus =
                vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = event.vehicleId)
            updateVehicleStatus.updateVehicleStatus(vehicleId = event.vehicleId, vehicleStatus = newStatus)
        }
    }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun updateVehicleStatusAsync(vehicleId: UUID) {
        val newStatus = vehicleStatusCalculationService.calculateStatusForVehicle(vehicleId = vehicleId)

        updateVehicleStatus.updateVehicleStatus(vehicleId = vehicleId, vehicleStatus = newStatus)
    }
}
