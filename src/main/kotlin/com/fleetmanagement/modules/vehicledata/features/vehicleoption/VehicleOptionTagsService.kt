/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.vehicleoption

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.CreateVehicleOptionTag
import com.fleetmanagement.modules.vehicledata.api.DeleteVehicleOptionTag
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleOptionTags
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleOptionTagDTO
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleOptionTagException.VehicleOptionTagNotFoundException
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleOptionTagsRepository
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleOptionTag
import org.slf4j.LoggerFactory
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class VehicleOptionTagsService(
    private val vehicleOptionTagsRepository: JPAVehicleOptionTagsRepository,
    private val objectMapper: ObjectMapper,
) : ReadVehicleOptionTags,
    CreateVehicleOptionTag,
    DeleteVehicleOptionTag {
    private val logger = LoggerFactory.getLogger(VehicleOptionTagsService::class.java)

    override fun readAll(): List<VehicleOptionTagDTO> =
        vehicleOptionTagsRepository.findAll().mapNotNull {
            VehicleOptionTagDTO.from(it)
        }

    override fun readAllNonCustomerAdequateOptionTags(): List<VehicleOptionTagDTO> =
        vehicleOptionTagsRepository.findAllByNonCustomerAdequateTrue().map {
            VehicleOptionTagDTO.from(it)
        }

    @Async
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    fun createTagsAsync(
        vehicleId: UUID?,
        options: String?,
    ) {
        if (options.isNullOrBlank()) return
        try {
            val tags = extractTagsFromJson(options)
            if (tags.isEmpty()) return
            val newTags =
                tags.filterNot { tag ->
                    val existingTag = vehicleOptionTagsRepository.findFirstByOptionId(tag.optionId)
                    if (existingTag != null) {
                        if (tag.optionDescription != null && (existingTag.optionDescription != tag.optionDescription)) {
                            existingTag.updateOptionDescription(tag.optionDescription)
                        }
                        true // Filter out because existing tag found
                    } else {
                        false // Keep because it's a new tag
                    }
                }
            vehicleOptionTagsRepository.saveAll(newTags)
            logger.info("Successfully created tags for vehicle $vehicleId")
        } catch (e: RuntimeException) {
            logger.warn("Failed to create option tags for vehicle $vehicleId", e)
        }
    }

    private fun extractTagsFromJson(jsonString: String): Set<JPAVehicleOptionTag> {
        val jsonNode = objectMapper.readTree(jsonString)
        val optionsNode = jsonNode["current"]?.get("individualOptions") ?: return emptySet()

        return optionsNode
            .mapNotNull { option ->
                val id = getNonBlankText(option, "id")
                if (id != null) {
                    val description = getNonBlankText(option["optionDescription"], "description")
                    JPAVehicleOptionTag(optionId = id, optionDescription = description)
                } else {
                    null
                }
            }.toSet()
    }

    private fun getNonBlankText(
        node: JsonNode?,
        fieldName: String,
    ): String? =
        node
            ?.get(fieldName)
            ?.takeIf {
                it.isTextual && it.asText().isNotBlank()
            }?.asText()

    @Transactional
    override fun createNonCustomerAdequateOptionTag(optionId: String): VehicleOptionTagDTO {
        val optionTagEntity =
            vehicleOptionTagsRepository
                .findFirstByOptionId(optionId)
                ?: vehicleOptionTagsRepository.save(JPAVehicleOptionTag(optionId = optionId))
        optionTagEntity.updateNonCustomerAdequate(true)
        return VehicleOptionTagDTO.from(optionTagEntity)
    }

    @Transactional
    override fun deleteNonCustomerAdequateOptionTag(optionId: String) {
        val optionTagEntity = vehicleOptionTagsRepository.findFirstByOptionId(optionId)
        optionTagEntity?.updateNonCustomerAdequate(false)
            ?: run { throw VehicleOptionTagNotFoundException(optionId) }
    }
}
