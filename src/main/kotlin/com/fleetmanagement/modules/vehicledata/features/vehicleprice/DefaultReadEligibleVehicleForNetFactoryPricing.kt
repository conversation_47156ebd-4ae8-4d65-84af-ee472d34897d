/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.vehicleprice

import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.features.vehicleprice.moduleapi.ReadEligibleVehicleForNetFactoryPricing
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehicledata.repository.mappers.VehicleDTOMapper
import org.springframework.stereotype.Service
import kotlin.jvm.optionals.getOrNull

@Service
class DefaultReadEligibleVehicleForNetFactoryPricing(
    private val jpaVehicleRepository: JPAVehicleRepository,
) : ReadEligibleVehicleForNetFactoryPricing {
    /**
     * Retrieves vehicle information by commission numbers and importer code.
     *
     * This method attempts to find a vehicle that matches one of the provided commission numbers
     * and the specified importer code. If a matching vehicle is found, its details are returned
     * as a [com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO]. If no matching vehicle is found, `null` is returned.
     *
     * @param commissionNumbers A list of commission numbers to match against.
     * @param importerCode The importer code associated with the vehicle.
     * @return A [com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO] if a matching vehicle is found, or `null` otherwise.
     */
    override fun readVehicleBy(
        commissionNumbers: List<String>,
        importerCode: String,
    ): VehicleDTO? {
        val vehicle =
            jpaVehicleRepository
                .findEligibleVehicleForNetFactoryPricing(
                    commissionNumbers = commissionNumbers,
                    importerShortName = importerCode,
                    cnrValue = "C00",
                    sources = listOf(VehicleSource.PVH, VehicleSource.PVH_IMPORT),
                ).getOrNull()
        return vehicle?.let { VehicleDTOMapper.INSTANCE.map(it) }
    }
}
