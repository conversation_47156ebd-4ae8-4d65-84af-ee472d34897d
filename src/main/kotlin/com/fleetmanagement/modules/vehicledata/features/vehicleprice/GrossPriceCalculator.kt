/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.vehicleprice

import java.math.BigDecimal
import java.math.RoundingMode
import java.time.LocalDate

class GrossPriceCalculator(
    private val taxRates: List<TaxRate> = DefaultTaxRateProvider.getDefaultTaxRates(),
) {
    fun grossPrice(
        netPrice: BigDecimal,
        taxRateLookupDate: LocalDate,
    ): BigDecimal {
        val tax = getTaxPercentage(taxRateLookupDate)
        val grossPrice = netPrice.multiply(BigDecimal.ONE.add(tax))
        return grossPrice.setScale(2, RoundingMode.HALF_UP)
    }

    private fun getTaxPercentage(date: LocalDate = LocalDate.now()): BigDecimal =
        taxRates
            .sortedBy { it.validFrom }
            .last { date.isEqual(it.validFrom) || date.isAfter(it.validFrom) }
            .taxPercentage
}

object DefaultTaxRateProvider {
    /**
     * Tax parameters with percentage and valid from date.
     * Add new entries here when tax changes. If you change it, all vehicles without newVehicleInvoiceDate will be recalcualted.
     *
     * validFrom date has to be a future date for recalculation of all the existing vehicle's
     * gross price otherwise the recalculation job will not process it in the scheduled cycle.
     * However, it will be recalculated when net price of the vehicle is updated explicitly.
     */
    fun getDefaultTaxRates(): List<TaxRate> =
        listOf(
            TaxRate(
                taxPercentage = BigDecimal("0.16"),
                validFrom = LocalDate.parse("1998-04-01"),
            ),
            TaxRate(
                taxPercentage = BigDecimal("0.19"),
                validFrom = LocalDate.parse("2007-01-01"),
            ),
            TaxRate(
                taxPercentage = BigDecimal("0.16"),
                validFrom = LocalDate.parse("2020-07-01"),
            ),
            // VAT 19% valid from Jan 1, 2021 (Post-Corona)
            TaxRate(
                taxPercentage = BigDecimal("0.19"),
                validFrom = LocalDate.parse("2021-01-01"),
            ),
        )
}

data class TaxRate(
    val taxPercentage: BigDecimal,
    val validFrom: LocalDate,
)
