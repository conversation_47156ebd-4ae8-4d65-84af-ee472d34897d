### Gross Price Calculation

#### Overview

Calculates gross prices by applying tax rates valid on a given date. 
Includes a scheduled job to recalculate vehicle gross prices when tax rates change.

## Calculation Logic

- `GrossPriceCalculator.grossPrice(netPrice, date)`:
    - date is set to current date by default
    - Finds the applicable tax rate for given `date`.
    - Calculates gross price based on tax rate and rounds to 2 decimals.

## Adding New Tax Rates

- Add new `TaxParameter` in `TaxParameter.defaultFixedParams` with:
    - `taxPercentage`: new rate (e.g., 0.20 for 20%)
    - `validFrom`: date from which it applies (format `dd.MM.yyyy`)
    - **Make sure the valid from date is a future date for recalculation job to reprocess the existing vehicles**

Example:
```kotlin
TaxParameter( 
    taxPercentage = BigDecimal("0.21"), 
    validFrom = LocalDate.parse("01.07.2026", DateTimeFormatter.ofPattern("dd.MM.yyyy")) 
)
```

## GrossPriceRecalculationJob Job

- Runs daily at midnight (00:00).
- Checks if today's date exactly matches any tax parameter `validFrom` date.
- If yes, recalculates gross prices for **all vehicles with net prices**.
- Logs errors per vehicle without stopping the job.
