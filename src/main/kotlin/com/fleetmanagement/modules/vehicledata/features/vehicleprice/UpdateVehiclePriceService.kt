/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.vehicleprice

import com.fleetmanagement.modules.vehicledata.features.refreshvehicle.VehicleDataChangeCaptureService
import com.fleetmanagement.modules.vehicledata.features.vehicleprice.moduleapi.UpdateVehiclePrice
import com.fleetmanagement.modules.vehicledata.features.vehicleprice.moduleapi.dto.VehiclePriceUpdateDto
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

@Component
@Transactional
class UpdateVehiclePriceService(
    private val vehicleRepository: JPAVehicleRepository,
    private val vehicleDataChangeCaptureService: VehicleDataChangeCaptureService,
) : UpdateVehiclePrice {
    override fun updateVehiclePrice(
        vehicleId: UUID,
        vehiclePriceUpdateDto: VehiclePriceUpdateDto,
    ) {
        val vehicle = vehicleRepository.findById(vehicleId).getOrNull()

        if (null == vehicle) {
            log.warn("Could not update vehicle price for $vehicleId as vehicle does not exist.")
        } else {
            vehicle.updateFactoryPriceAndPiaEvent(
                factoryNetPriceEUR = vehiclePriceUpdateDto.factoryNetPriceEUR,
                piaEvent = vehiclePriceUpdateDto.piaEvent,
            )
            vehicleDataChangeCaptureService.captureChanges(
                vehicleEntity = vehicle,
                modifier = "PIAEventConsumer",
            )
        }
    }

    companion object {
        private val log = LoggerFactory.getLogger(UpdateVehiclePriceService::class.java)
    }
}
