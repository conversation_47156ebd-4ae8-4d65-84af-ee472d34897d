/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.vehicleprice.job

import com.fleetmanagement.modules.vehicledata.features.vehicleprice.DefaultTaxRateProvider
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import org.quartz.Job
import org.quartz.JobExecutionContext
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDate

@Component
class GrossPriceRecalculationJob(
    private val repository: JPAVehicleRepository,
) : Job {
    private val logger = LoggerFactory.getLogger(GrossPriceRecalculationJob::class.java)

    @Transactional
    override fun execute(context: JobExecutionContext?) {
        val today = LocalDate.now()

        if (!shouldRecalculate(today)) {
            logger.info("No tax parameter change today ($today). Skipping recalculation.")
            return
        }

        logger.info("Running gross price recalculation for date: $today")

        val eligibleVehicles = repository.findAllByPriceInfoFactoryNetPriceEURNotNull()

        eligibleVehicles.forEach { vehicle ->
            try {
                vehicle.priceInfo?.calculateFactoryGrossPrice()
            } catch (ex: Exception) {
                logger.error("Failed to update gross price for vehicle: ${vehicle.id}", ex)
            }
        }

        logger.info("Successfully recalculated gross price for ${eligibleVehicles.size} vehicles")
    }

    private fun shouldRecalculate(date: LocalDate = LocalDate.now()): Boolean {
        val params = DefaultTaxRateProvider.getDefaultTaxRates()
        return params.any { it.validFrom == date }
    }
}
