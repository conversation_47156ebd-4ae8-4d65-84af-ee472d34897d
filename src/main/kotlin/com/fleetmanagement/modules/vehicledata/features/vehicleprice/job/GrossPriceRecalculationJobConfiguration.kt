/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.vehicleprice.job

import org.quartz.CronScheduleBuilder
import org.quartz.JobBuilder
import org.quartz.JobDetail
import org.quartz.Trigger
import org.quartz.TriggerBuilder
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration

@Configuration
class GrossPriceRecalculationJobConfiguration {
    @Bean
    fun jobDetail(): JobDetail =
        JobBuilder
            .newJob(GrossPriceRecalculationJob::class.java)
            .withIdentity("grossPriceRecalculationJob")
            .storeDurably()
            .build()

    @Bean
    fun trigger(jobDetail: JobDetail): Trigger =
        TriggerBuilder
            .newTrigger()
            .forJob(jobDetail)
            .withIdentity("grossPriceRecalculationTrigger")
            .withSchedule(CronScheduleBuilder.dailyAtHourAndMinute(0, 0)) // Every night at 00:00
            .build()
}
