/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.features.vehicleprice.moduleapi

import com.fleetmanagement.modules.vehicledata.features.vehicleprice.moduleapi.dto.VehiclePriceUpdateDto
import java.util.UUID

/**
 * Provides functionality to update vehicle pricing information.
 */
interface UpdateVehiclePrice {
    /**
     * Updates the factory net price and associated PIA event for a vehicle.
     *
     * @param vehicleId the unique identifier of the vehicle to be updated
     * @param vehiclePriceUpdateDto the DTO containing the updated factory net price and PIA event data
     */
    fun updateVehiclePrice(
        vehicleId: UUID,
        vehiclePriceUpdateDto: VehiclePriceUpdateDto,
    )
}
