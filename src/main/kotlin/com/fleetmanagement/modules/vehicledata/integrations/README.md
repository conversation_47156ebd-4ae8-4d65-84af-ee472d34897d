# Porsche Vehicle Hub Integration

## Implementation 

There are two mechanisms we use to integrate with Porsche Vehicle Hub (PVH)

- Gravity API Gateway 
- Streamzilla (from Porsche)

A combination of both these mechanisms is used to populate our internal database with Fleet-Management specific vehicles.

## Constraints 

### Technical Constraints
- **Rate Limiting:** All calls to PVH through the Gravity API Gateway are rate-limited. 
- **Streamzilla Integration:** This requires a library that we have to manually import into our application since we do not have access to the package-manager that the PVH team uses.

