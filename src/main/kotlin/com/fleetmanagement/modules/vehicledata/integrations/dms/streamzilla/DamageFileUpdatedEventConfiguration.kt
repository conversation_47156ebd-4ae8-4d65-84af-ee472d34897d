package com.fleetmanagement.modules.vehicledata.integrations.dms.streamzilla

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.integrations.dms.streamzilla.dtos.DamageFileUpdatedEventDto
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.listener.adapter.RecordFilterStrategy

@Configuration
@ConditionalOnProperty("vehicle-data.dms.number-of-damages.kafka.enabled", havingValue = "true", matchIfMissing = false)
class DamageFileUpdatedEventConfiguration {
    companion object {
        private val logger = LoggerFactory.getLogger(DamageFileUpdatedEventConfiguration::class.java)
    }

    @Bean("DamageFileUpdatedEventFilter")
    fun damageFileUpdatedEventFilter(objectMapper: ObjectMapper): RecordFilterStrategy<Any, Any> =
        RecordFilterStrategy { consumerRecord ->
            try {
                val damageFileUpdatedEventDto: DamageFileUpdatedEventDto =
                    objectMapper.readValue(
                        consumerRecord.value()?.toString() ?: "{}",
                        DamageFileUpdatedEventDto::class.java,
                    )
                false
            } catch (e: JsonProcessingException) {
                // In case of parsing error, consider the message invalid and filter it out
                logger.warn("encountered an error while parsing the msg, due msg will be filter out ${e.cause}")
                true
            }
        }
}
