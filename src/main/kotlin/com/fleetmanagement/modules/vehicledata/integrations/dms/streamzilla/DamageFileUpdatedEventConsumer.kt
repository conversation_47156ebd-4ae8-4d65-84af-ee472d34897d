package com.fleetmanagement.modules.vehicledata.integrations.dms.streamzilla

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.features.damages.DamagesService
import com.fleetmanagement.modules.vehicledata.integrations.dms.streamzilla.dtos.DamageFileUpdatedEventDto
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(DamageFileUpdatedEventConfiguration::class)
class DamageFileUpdatedEventConsumer(
    private val objectMapper: ObjectMapper,
    private val damagesService: DamagesService,
) {
    private val log = LoggerFactory.getLogger(DamageFileUpdatedEventConsumer::class.java)

    @KafkaListener(
        topics = ["\${vehicle-data.dms.number-of-damages.kafka.consumer.topic}"],
        groupId = "\${vehicle-data.dms.number-of-damages.kafka.consumer.group-id}",
    )
    fun listenForDamageFileUpdatedEvent(message: String) {
        log.info("Received Update For Damage File : $message")
        val countedNumberOfDamages: DamageFileUpdatedEventDto
        try {
            countedNumberOfDamages = parseNumberOfDamagesMessage(message)
            damagesService.updateDamages(countedNumberOfDamages)
            log.debug("Successfully processed update for damage file event for key: ${countedNumberOfDamages.vin}", message)
        } catch (e: JsonProcessingException) {
            log.error("Problem parsing update for damage file event for message: $message", e)
        }
    }

    private fun parseNumberOfDamagesMessage(message: String): DamageFileUpdatedEventDto =
        objectMapper.readValue(message, DamageFileUpdatedEventDto::class.java)
}
