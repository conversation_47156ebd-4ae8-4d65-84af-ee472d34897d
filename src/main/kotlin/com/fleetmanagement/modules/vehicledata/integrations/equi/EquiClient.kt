package com.fleetmanagement.modules.vehicledata.integrations.equi

import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import java.util.*

interface EquiClient {
    fun findVehicleDataByEquiId(equiId: String): JPAVehicleEntity?

    fun findVehicleDataByEquipmentNumber(equipmentNumber: Long): JPAVehicleEntity?
}

@Configuration
class MockedEquiClientConfig {
    @Bean
    @ConditionalOnMissingBean(EquiClient::class)
    fun mockedEquiClient(): EquiClient =
        object : EquiClient {
            override fun findVehicleDataByEquiId(equiId: String): JPAVehicleEntity =
                JPAVehicleEntity(
                    id = UUID.fromString("12345678-1234-5678-1234-************"),
                    equipmentNumber = 123456789,
                    equiId = equiId,
                    vin = "TEST-VIN-$equiId",
                    vguid = "TEST-VGUID-$equiId",
                )

            override fun findVehicleDataByEquipmentNumber(equipmentNumber: Long): JPAVehicleEntity =
                JPAVehicleEntity(
                    id = UUID.fromString("12345678-1234-5678-1234-************"),
                    equipmentNumber = equipmentNumber,
                    equiId = "TEST-EQUI-ID-$equipmentNumber",
                    vin = "TEST-VIN-$equipmentNumber",
                    vguid = "TEST-VGUID-$equipmentNumber",
                )
        }
}
