package com.fleetmanagement.modules.vehicledata.integrations.p40.streamzilla

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.UpdateFinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.integrations.p40.streamzilla.dto.AssetTypeUpdateEventMessageDto
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty("vehicle-data.p40.kafka.enabled", havingValue = "true", matchIfMissing = false)
class FinancialAssetTypeUpdateEventConsumer(
    private val updateFinancialAssetType: UpdateFinancialAssetType,
    private val objectMapper: ObjectMapper,
) {
    @KafkaListener(
        topics = ["\${vehicle-data.p40.kafka.consumer.topic}"],
        groupId = "\${vehicle-data.p40.kafka.consumer.group-id}",
        properties = ["auto.offset.reset=\${vehicle-data.p40.kafka.consumer.auto-offset-reset}"],
        id = LISTENER_ID,
    )
    fun financialAssetTypeUpdateClassListener(event: String?) {
        if (event.isNullOrBlank()) {
            log.warn("Received empty event for financial asset type topic.")
            return
        }
        try {
            log.info("Starting processing of financial asset type update.")
            val message = objectMapper.readValue(event, AssetTypeUpdateEventMessageDto::class.java)
            val (vin, financialAssetType) = getInformationFromMessage(message)
            updateFinancialAssetType.updateFinancialAssetType(vin, financialAssetType)
            log.info("Finished processing of {} to update financial asset type: {}.", vin, financialAssetType)
        } catch (e: JsonProcessingException) {
            log.error("Problem while parsing the message for asset class: $event", e)
        } catch (e: AssetTypeUpdateEventMessageProcessingException) {
            log.warn("Problem while mapping the message for asset class and vin", e)
        } catch (e: IllegalArgumentException) {
            log.warn("Problem while mapping the message for asset class and vin", e)
        }
    }

    private fun getInformationFromMessage(message: AssetTypeUpdateEventMessageDto): Pair<String, FinancialAssetType> {
        val vin =
            message.vin
                ?: throw AssetTypeUpdateEventMessageProcessingException("VIN is missing")
        val assetType =
            message.assetType
                ?: throw AssetTypeUpdateEventMessageProcessingException("Asset type is missing")

        require(vin.isNotBlank()) { "VIN is blank" }
        require(assetType.isNotBlank()) { "Asset type is blank" }

        return vin to parseFinancialAssetType(assetType)
    }

    fun parseFinancialAssetType(assetType: String): FinancialAssetType =
        FinancialAssetType.entries
            .find { it.name.equals(assetType, ignoreCase = true) }
            ?: throw AssetTypeUpdateEventMessageProcessingException("Unknown asset type: $assetType")

    companion object {
        private val log = LoggerFactory.getLogger(FinancialAssetTypeUpdateEventConsumer::class.java)
        private const val LISTENER_ID = "financial_asset_type_streamzilla_listener"
    }
}

class VehicleNotFoundException(
    message: String,
    cause: Throwable? = null,
) : RuntimeException(message, cause)

class AssetTypeUpdateEventMessageProcessingException(
    message: String,
    cause: Throwable? = null,
) : RuntimeException(message, cause)
