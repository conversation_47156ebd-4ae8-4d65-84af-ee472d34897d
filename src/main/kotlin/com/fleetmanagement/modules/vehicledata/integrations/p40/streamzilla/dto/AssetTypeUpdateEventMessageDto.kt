package com.fleetmanagement.modules.vehicledata.integrations.p40.streamzilla.dto

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class AssetTypeUpdateEventMessageDto(
    @JsonProperty("vehicleNumber")
    val vin: String? = null,
    @JsonProperty("assetType")
    val assetType: String? = null,
)
