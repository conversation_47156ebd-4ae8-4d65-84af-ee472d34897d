package com.fleetmanagement.modules.vehicledata.integrations.pia.streamzilla

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.features.vehicleprice.moduleapi.ReadEligibleVehicleForNetFactoryPricing
import com.fleetmanagement.modules.vehicledata.features.vehicleprice.moduleapi.UpdateVehiclePrice
import com.fleetmanagement.modules.vehicledata.features.vehicleprice.moduleapi.dto.VehiclePriceUpdateDto
import com.fleetmanagement.modules.vehicledata.integrations.pia.streamzilla.dtos.BilledItem
import com.fleetmanagement.modules.vehicledata.integrations.pia.streamzilla.dtos.PIAEventMessageDto
import com.fleetmanagement.modules.vehicledata.integrations.pia.streamzilla.dtos.VehiclePriceDto
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component
import java.math.BigDecimal

@Component
@ConditionalOnProperty("vehicle-data.pia.kafka.enabled", havingValue = "true", matchIfMissing = false)
class PIAEventConsumer(
    private val readEligibleVehicleForNetFactoryPricing: ReadEligibleVehicleForNetFactoryPricing,
    private val updateVehicleService: UpdateVehiclePrice,
    private val objectMapper: ObjectMapper,
) {
    @KafkaListener(
        topics = ["\${vehicle-data.pia.kafka.consumer.topic}"],
        groupId = "\${vehicle-data.pia.kafka.consumer.group-id}",
        properties = ["auto.offset.reset=\${vehicle-data.pia.kafka.consumer.auto-offset-reset}"],
        id = LISTENER_ID,
    )
    fun piaPriceListener(event: String?) {
        try {
            if (event.isNullOrBlank()) {
                log.warn("Received empty event for PIA price update.")
                return
            }

            val message = objectMapper.readValue(event, PIAEventMessageDto::class.java)
            val vehicleToUpdate = getEligibleVehicleForPricing(message) ?: return
            log.info("Received price update for fleet vehicle: ${vehicleToUpdate.vehicleId} to process...")
            updateVehicleService.updateVehiclePrice(
                vehicleId = vehicleToUpdate.vehicleId,
                vehiclePriceUpdateDto =
                    VehiclePriceUpdateDto(
                        factoryNetPriceEUR = vehicleToUpdate.factoryNetPriceEUR,
                        piaEvent = event,
                    ),
            )
            log.info("Processed price information for vehicle: ${vehicleToUpdate.vehicleId}")
        } catch (e: JsonProcessingException) {
            log.error("Problem while parsing the message for price event: $event", e)
        }
    }

    private fun getEligibleVehicleForPricing(message: PIAEventMessageDto): VehiclePriceDto? {
        try {
            val importerCode = extractRelevantImporterCode(message) ?: return null
            val orderIdToItemMap = buildOrderIdToItemMap(message, importerCode) ?: return null

            val matchedVehicle =
                readEligibleVehicleForNetFactoryPricing.readVehicleBy(
                    commissionNumbers = orderIdToItemMap.keys.toList(),
                    importerCode = importerCode,
                ) ?: return null

            val matchedCommissionNumber = checkNotNull(matchedVehicle.order?.commissionNumber)
            val matchedBilledItem = checkNotNull(orderIdToItemMap[matchedCommissionNumber])
            val factoryNetPriceEUR = extractFactoryNetPrice(matchedBilledItem)

            return VehiclePriceDto(
                vehicleId = matchedVehicle.id,
                factoryNetPriceEUR = factoryNetPriceEUR,
            )
        } catch (e: Exception) {
            // In case of a parsing error, consider the message invalid and filter it out
            log.warn("Encountered an error while parsing the msg, due msg will be filter out ${e.cause}")
            return null
        }
    }

    private fun extractRelevantImporterCode(message: PIAEventMessageDto): String? =
        message.generalBillingInfo
            ?.importerCode
            ?.takeIf { it in PRICE_RELEVANT_IMPORTER_CODES }

    private fun buildOrderIdToItemMap(
        message: PIAEventMessageDto,
        importerCode: String,
    ): Map<String, BilledItem>? =
        message.billedItems
            .mapNotNull { it.newCarOrderId?.let { id -> id.substringAfter(importerCode) to it } }
            .toMap()
            .takeIf { it.isNotEmpty() }

    private fun extractFactoryNetPrice(item: BilledItem): BigDecimal? =
        runCatching {
            item.pricing
                ?.subtotals
                ?.find { it.subtotalType == "total_vehicle_price" }
                ?.subtotalPrice
                ?.toBigDecimal()
        }.getOrNull()

    companion object {
        private val log = LoggerFactory.getLogger(PIAEventConsumer::class.java)
        private val PRICE_RELEVANT_IMPORTER_CODES = setOf("PIS", "PIX")
        private const val LISTENER_ID = "pia_streamzilla_listener"
    }
}
