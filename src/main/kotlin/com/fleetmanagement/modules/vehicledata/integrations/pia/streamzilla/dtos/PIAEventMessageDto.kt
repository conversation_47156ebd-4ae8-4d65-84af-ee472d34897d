package com.fleetmanagement.modules.vehicledata.integrations.pia.streamzilla.dtos

import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonProperty

@JsonIgnoreProperties(ignoreUnknown = true)
data class PIAEventMessageDto(
    @JsonProperty("new_car_invoice_id")
    val newCarInvoiceId: String? = null,
    @JsonProperty("general_billing_info")
    val generalBillingInfo: GeneralBillingInfo? = null,
    @JsonProperty("billed_items")
    val billedItems: List<BilledItem> = emptyList(),
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class GeneralBillingInfo(
    @JsonProperty("importer_code")
    val importerCode: String? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BilledItem(
    @JsonProperty("new_car_order_id")
    val newCarOrderId: String? = null,
    val pricing: Pricing? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Pricing(
    val subtotals: List<Subtotal> = emptyList(),
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class Subtotal(
    @JsonProperty("subtotal_type")
    val subtotalType: String? = null,
    @JsonProperty("subtotal_price")
    val subtotalPrice: String? = null,
)
