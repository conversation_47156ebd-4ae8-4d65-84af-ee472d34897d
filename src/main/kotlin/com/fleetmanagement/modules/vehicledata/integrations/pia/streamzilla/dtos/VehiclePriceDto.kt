/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pia.streamzilla.dtos

import java.math.BigDecimal
import java.util.*

data class VehiclePriceDto(
    val vehicleId: UUID,
    val factoryNetPriceEUR: BigDecimal? = null,
)
