/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api

import com.fleetmanagement.modules.vehicledata.integrations.pvh.api.configurations.PVHClientConfiguration
import com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto.PVHVehicle
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable

/**
 * PVHClient is a Feign client that interacts with the PVH external API to fetch vehicle data.
 */
@FeignClient(name = "pvh", url = "\${vehicle-data.pvh.base-uri}", configuration = [PVHClientConfiguration::class])
interface PVHClient {
    /**
     * Fetches vehicle data from PVH based on the provided VIN.
     *
     * @param vin Vehicle Identification Number (VIN) for which data is requested.
     * @return A PVHVehicleDataByVinDTO containing the fetched vehicle data.
     */
    @GetMapping(value = ["/vehicles/v1/vin/{vin}/language/de-DE"])
    fun vehicleDataByVin(
        @PathVariable("vin") vin: String,
    ): ResponseEntity<PVHVehicle>

    @GetMapping(value = ["/vehicles/v1/vguid/{vguid}/language/de-DE"])
    fun vehicleDataByVguid(
        @PathVariable("vguid") vguid: String,
    ): ResponseEntity<PVHVehicle>
}
