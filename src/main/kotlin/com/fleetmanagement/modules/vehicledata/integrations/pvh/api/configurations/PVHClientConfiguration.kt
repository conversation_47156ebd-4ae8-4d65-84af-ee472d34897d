/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.configurations

import com.fleetmanagement.modules.vehicledata.integrations.pvh.api.token.VWOIDCAccessTokenService
import com.fleetmanagement.observability.custommetrics.ExternalAPICounter
import feign.RequestInterceptor
import feign.ResponseInterceptor
import io.micrometer.core.instrument.MeterRegistry
import org.slf4j.LoggerFactory
import org.springframework.context.annotation.Bean
import org.springframework.http.HttpStatus
import java.net.URI

class PVHClientConfiguration {
    @Bean
    fun authorizationInterceptor(tokenService: VWOIDCAccessTokenService): RequestInterceptor {
        val logger = LoggerFactory.getLogger(PVHClientConfiguration::class.java)

        return RequestInterceptor { template ->
            logger.info("Authorization Interceptor is called")
            val token = tokenService.getAccessToken()
            template?.header("Authorization", "Bearer $token")
        }
    }

    @Bean
    fun micrometerMetricsResponseInterceptor(meterRegistry: MeterRegistry): ResponseInterceptor =
        ResponseInterceptor { invocationContext, _ ->
            val response = invocationContext.response()
            val uri = URI.create(response.request().url())
            val httpStatus = HttpStatus.resolve(response.status())

            val metric =
                ExternalAPICounter(
                    metricName = "pvh",
                    status =
                        when (httpStatus) {
                            null -> "unknown"
                            else -> if (httpStatus.is2xxSuccessful) "success" else "failure"
                        },
                    uri = uri.path,
                )
            metric.increment(meterRegistry)

            invocationContext.proceed()
        }
}
