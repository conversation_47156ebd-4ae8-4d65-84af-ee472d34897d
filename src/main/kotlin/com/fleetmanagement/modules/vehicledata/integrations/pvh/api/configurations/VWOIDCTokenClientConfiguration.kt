/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.configurations

import feign.codec.ErrorDecoder
import org.springframework.context.annotation.Bean
import org.springframework.http.HttpStatus
import org.springframework.web.client.HttpClientErrorException
import org.springframework.web.client.HttpServerErrorException

class VWOIDCTokenClientConfiguration {
    @Bean
    fun customErrorDecoder(): ErrorDecoder =
        ErrorDecoder { _, response ->
            val responseCode = HttpStatus.valueOf(response.status())
            when {
                responseCode.is4xxClientError -> {
                    HttpClientErrorException(responseCode, "Client error while fetching the token.")
                }
                responseCode.is5xxServerError -> {
                    HttpServerErrorException(responseCode, "Service error while fetching the token.")
                }
                else -> {
                    RuntimeException("An unexpected error occurred while processing the request.")
                }
            }
        }
}
