/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto

import com.fasterxml.jackson.databind.JsonNode

data class PVHVehicle(
    val vguid: String,
    val vin: String?,
    val modelInfo: PVHVehicleDataModelInfoDTO?,
    val consumptionData: PVHVehicleConsumptionData?,
    val orderInfo: PVHVehicleOrderInfo?,
    val colorInfo: PVHVehicleColorInfo?,
    val productionInfo: PVHVehicleProductionInfo?,
    val countryInfo: PVHVehicleCountryInfo?,
    val pmpData: PVHVehiclePMPDataDTO?,
    val metaInformation: PVHVehicleMetaInfoDTO?,
    val options: JsonNode?,
    val technicalData: Map<String, List<PVHVehicleTechnicalDataAttribute>>?,
)
