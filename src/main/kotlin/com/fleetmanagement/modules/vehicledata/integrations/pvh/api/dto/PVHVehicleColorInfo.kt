/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto

data class PVHVehicleColorInfo(
    val colorExterior: String? = null,
    val colorExteriorDescription: PVHVehicleColorDescription? = null,
    val colorInterior: String? = null,
    val colorInteriorDescription: PVHVehicleColorDescription? = null,
)

data class PVHVehicleColorDescription(
    val description: String? = null,
    val language: String? = null,
)
