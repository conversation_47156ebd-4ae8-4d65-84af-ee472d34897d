/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto

data class PVHVehicleCountryInfo(
    val BNR: PVHVehicleCountryBNR?,
    val CNR: PVHVehicleCountryCNR?,
)

data class PVHVehicleCountryBNR(
    val value: String?,
)

data class PVHVehicleCountryCNR(
    val value: String?,
    val countryDescription: PVHVehicleCountryDescription?,
)

data class PVHVehicleCountryDescription(
    var description: String?,
    var language: String?,
)
