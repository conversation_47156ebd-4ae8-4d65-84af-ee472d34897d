/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto

import java.util.Collections.emptyList

data class PVHVehicleDataModelInfoDTO(
    val modelDescription: PVHVehicleDataModelDescriptionDTO?,
    val modelYear: String?,
    val orderType: String?,
    val productId: String? = null,
    val productCode: String? = null,
    val extendedModelInfo: PVHVehicleDataExtendedModelInfoDTO? = null,
)

data class PVHVehicleDataModelDescriptionDTO(
    val description: String,
    val language: String,
)

data class PVHVehicleDataExtendedModelInfoDTO(
    val orderTypeGroups: List<PVHVehicleDataOrderTypeGroupDTO> = emptyList(),
)

data class PVHVehicleDataOrderTypeGroupDTO(
    val category: String?,
    val orderTypeGroup: String?,
)
