/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto

data class PVHVehicleMetaInfoDTO(
    val embargoInformation: PVHVehicleEmbargoInfoDTO?,
)

data class PVHVehicleEmbargoInfoDTO(
    val inEmbargo: Boolean?,
)
