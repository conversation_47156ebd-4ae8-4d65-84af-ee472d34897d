/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto

import com.fasterxml.jackson.annotation.JsonFormat
import java.util.*

data class PVHVehicleOrderInfo(
    val tradingPartnerNumber: String? = null,
    var purposeOrderType: String? = null,
    var importerShortName: String? = null,
    var commissionNumber: String? = null,
    var invoiceNumber: String? = null,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    var invoiceDate: Date? = null,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    var purchaseOrderDate: Date? = null,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    var customerDeliveryDate: Date? = null,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    var requestedDeliveryDate: Date? = null,
    val consigneeNumber: String?,
    var deliveryType: String? = null,
    var primaryStatus: String? = null,
)
