/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto

import com.fasterxml.jackson.annotation.JsonFormat
import java.util.*

data class PVHVehiclePMPDataDTO(
    val header: PVHVehiclePMPDataHeaderDTO?,
)

data class PVHVehiclePMPDataHeaderDTO(
    val odometer: Int?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS", timezone = "UTC")
    val timestamp: Date?, // should this be a timestamp?
)
