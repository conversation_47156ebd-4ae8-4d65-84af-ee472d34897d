/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto

import com.fasterxml.jackson.annotation.JsonFormat
import java.util.*

data class PVHVehicleProductionInfo(
    val productionNumber: Int?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    val productionEndDate: Date?,
    val technicalModelYear: Int?,
    val factory: String?,
    val factoryVw: Int?,
    val quoteMonth: PVHVehicleQuoteMonth?,
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd")
    val plannedProductionEndDate: Date?,
    val gearBoxClass: Char?,
)

data class PVHVehicleQuoteMonth(
    val month: Int,
    val year: Int,
)
