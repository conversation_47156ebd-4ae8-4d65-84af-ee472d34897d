/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto

data class PVHVehicleTechnicalDataAttribute(
    val uniqueAttributeId: String?,
    val technicalData: PVHVehicleTechnicalDataAttributeData?,
)

data class PVHVehicleTechnicalDataAttributeData(
    val valueSingle: PVHVehicleTechnicalDataAttributeDataValueSingle?,
)

data class PVHVehicleTechnicalDataAttributeDataValueSingle(
    val description: String,
)
