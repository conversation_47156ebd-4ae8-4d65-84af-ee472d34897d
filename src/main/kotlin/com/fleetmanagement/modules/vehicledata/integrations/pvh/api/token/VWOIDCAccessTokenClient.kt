/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.token

import com.fleetmanagement.modules.vehicledata.integrations.pvh.api.configurations.VWOIDCTokenClientConfiguration
import feign.Headers
import org.springframework.cloud.openfeign.FeignClient
import org.springframework.http.MediaType
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestBody

/**
 * Feign client interface for making requests to the idp service
 */
@FeignClient(name = "vw-oidc-token", url = "\${vehicle-data.vw-oidc-token.uri}", configuration = [VWOIDCTokenClientConfiguration::class])
interface VWOIDCAccessTokenClient {
    /**
     * Retrieves an access token from the idp service.
     *
     * This method sends a POST request to obtain an access token using client information.
     *
     * @param requestBody The client information as a form-urlencoded request body.
     * @return The obtained access token.
     * @throws Exception If an error occurs during the request.
     */
    @Headers("Content-Type", MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    @PostMapping(consumes = [MediaType.APPLICATION_FORM_URLENCODED_VALUE])
    fun getToken(
        @RequestBody requestBody: String,
    ): AccessToken
}
