/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.api.token

import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Value
import org.springframework.cache.annotation.CacheEvict
import org.springframework.cache.annotation.Cacheable
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Service
import java.util.concurrent.TimeUnit

/**
 * Implementation of TokenService for retrieving access tokens from IDP.
 */
@Service
class VWOIDCAccessTokenService(
    private val oidcTokenClient: VWOIDCAccessTokenClient,
    @Value("\${vehicle-data.pvh.client-id}") private val clientId: String,
    @Value("\${vehicle-data.pvh.client-secret}") private val clientSecret: String,
) {
    private val logger = LoggerFactory.getLogger(VWOIDCAccessTokenService::class.java)

    /**
     * Retrieves an access token. If the token has not been initialized, it fetches the token using the external API.
     * @return The access token as a string or null if not available.
     */
    @Cacheable("tokenCache")
    fun getAccessToken(): String {
        val requestBody = getRequestBody()
        logger.info("Getting a new access token")
        val token = oidcTokenClient.getToken(requestBody)
        return token.accessToken
    }

    /**
     * Evicts the token from cache after configured interval, so it can be fetched again.
     */
    @CacheEvict(value = ["tokenCache"])
    @Scheduled(timeUnit = TimeUnit.SECONDS, fixedRateString = "\${vehicle-data.vw-oidc-token.refresh-interval-in-sec}")
    fun evictCachedToken() {
        logger.info("Evicting access token from cache")
    }

    private fun getRequestBody(): String {
        val data =
            mapOf(
                "client_id" to clientId,
                "client_secret" to clientSecret,
                "grant_type" to "client_credentials",
            )
        return data.entries.joinToString("&") { "${it.key}=${it.value}" }
    }
}
