/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.streamzilla

import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fleetmanagement.modules.vehicledata.api.domain.Department
import com.fleetmanagement.modules.vehicledata.api.domain.LeasingType
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import de.porsche.pvh.async.processing.api.KafkaVehicle
import org.mapstruct.*
import org.mapstruct.factory.Mappers
import java.time.ZoneOffset
import java.util.*

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
@DecoratedWith(ExtendedKafkaMapper::class)
interface KafkaMapper {
    companion object {
        val INSTANCE: KafkaMapper = Mappers.getMapper(KafkaMapper::class.java)
        private val objectMapper =
            jacksonObjectMapper()
                .registerModule(JavaTimeModule())
                .addMixIn(KafkaVehicle.VehicleOptionsState.Options::class.java, OptionsMixin::class.java)

        /**
         * Mixin for jackson ObjectMapper to rename field zOrders to zusatzOrders
         */
        abstract class OptionsMixin {
            @get:JsonProperty("zusatzOrders")
            abstract val zOrders: KafkaVehicle.VehicleOptionsState.Options.ZOrder
        }

        @Named("optionsToString")
        @JvmStatic
        fun optionsToString(options: KafkaVehicle.VehicleOptionsState?): String? =
            options?.let {
                objectMapper.writeValueAsString(it)
            }

        @JvmStatic
        @Named("mapDealerNumberToDepartment")
        fun mapDealerNumberToDepartment(dealerNumber: String?): String? = dealerNumber?.let { Department(it).asCommaSeparatedList() }

        @JvmStatic
        @Named("mapDealerNumberToLeasingType")
        fun mapDealerNumberToLeasingType(dealerNumber: String?): String? = dealerNumber?.let { LeasingType(it).asCommaSeparatedList() }
    }

    @Mappings(
        Mapping(source = "metaInformation.embargoInformation.inEmbargo", target = "embargoInfo.inEmbargo"),
        Mapping(source = "modelInfo.modelType", target = "modelInfo.orderType"),
        Mapping(source = "modelInfo.modelDescription.description", target = "modelInfo.modelDescription"),
        Mapping(source = "colorInfo.interiorColor", target = "colorInfo.interior"),
        Mapping(expression = "java(colorInfo.getBodyColor() + colorInfo.getTopColor())", target = "colorInfo.exterior"),
        Mapping(source = "countryInfo.cnr", target = "countryInfo.cnrValue"),
        Mapping(source = "countryInfo.bnr", target = "countryInfo.bnrValue"),
        Mapping(
            source = "orderInfo.vehicleStatusOem.value",
            target = "orderInfo.primaryStatus",
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        ),
        Mapping(source = "orderInfo.orderType", target = "orderInfo.purposeOrderType"),
        Mapping(source = "orderInfo.tradingPartner.importerNumber", target = "orderInfo.tradingPartnerNumber"),
        Mapping(source = "orderInfo.tradingPartner.importerCode", target = "orderInfo.importerShortName"),
        Mapping(source = "orderInfo.tradingPartner.dealerNumber", target = "orderInfo.consigneeNumber"),
        Mapping(source = "orderInfo.tradingPartner.dealerNumber", target = "referenceId"),
        Mapping(source = "orderInfo.customer.customerDeliveryDate", target = "orderInfo.customerDeliveryDate"),
        Mapping(
            source = "orderInfo.customer.plannedCustomerDeliveryDate",
            target = "orderInfo.requestedDeliveryDate",
        ),
        Mapping(target = "orderInfo.deliveryType", source = "orderInfo.shippingCode"),
        Mapping(
            target = "orderInfo.department",
            source = "orderInfo.tradingPartner.dealerNumber",
            qualifiedByName = ["mapDealerNumberToDepartment"],
        ),
        Mapping(
            target = "orderInfo.leasingType",
            source = "orderInfo.tradingPartner.dealerNumber",
            qualifiedByName = ["mapDealerNumberToLeasingType"],
        ),
        Mapping(source = "productionInfo.plantInfo.plantCode", target = "productionInfo.factory"),
        Mapping(source = "productionInfo.plantInfo.plantCodeVW", target = "productionInfo.factoryVw"),
        Mapping(target = "options", source = "options", qualifiedByName = ["optionsToString"]),
        Mapping(target = "source", constant = "PVH"),
    )
    fun map(vehicle: KafkaVehicle): JPAVehicleEntity

    fun map(vehicles: List<KafkaVehicle>): List<JPAVehicleEntity>

    @Mappings(
        Mapping(source = "metaInformation.embargoInformation.inEmbargo", target = "embargoInfo.inEmbargo"),
        Mapping(source = "modelInfo.modelType", target = "modelInfo.orderType"),
        Mapping(source = "modelInfo.modelDescription.description", target = "modelInfo.modelDescription"),
        Mapping(source = "colorInfo.interiorColor", target = "colorInfo.interior"),
        Mapping(expression = "java(colorInfo.getBodyColor() + colorInfo.getTopColor())", target = "colorInfo.exterior"),
        Mapping(source = "countryInfo.cnr", target = "countryInfo.cnrValue"),
        Mapping(source = "countryInfo.bnr", target = "countryInfo.bnrValue"),
        Mapping(
            source = "orderInfo.vehicleStatusOem.value",
            target = "orderInfo.primaryStatus",
            nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        ),
        Mapping(source = "orderInfo.orderType", target = "orderInfo.purposeOrderType"),
        Mapping(source = "orderInfo.tradingPartner.importerNumber", target = "orderInfo.tradingPartnerNumber"),
        Mapping(source = "orderInfo.tradingPartner.importerCode", target = "orderInfo.importerShortName"),
        Mapping(source = "orderInfo.tradingPartner.dealerNumber", target = "orderInfo.consigneeNumber"),
        Mapping(source = "orderInfo.customer.customerDeliveryDate", target = "orderInfo.customerDeliveryDate"),
        Mapping(
            source = "orderInfo.customer.plannedCustomerDeliveryDate",
            target = "orderInfo.requestedDeliveryDate",
        ),
        Mapping(target = "orderInfo.deliveryType", source = "orderInfo.shippingCode"),
        Mapping(
            target = "orderInfo.department",
            source = "orderInfo.tradingPartner.dealerNumber",
            qualifiedByName = ["mapDealerNumberToDepartment"],
        ),
        Mapping(
            target = "orderInfo.leasingType",
            source = "orderInfo.tradingPartner.dealerNumber",
            qualifiedByName = ["mapDealerNumberToLeasingType"],
        ),
        Mapping(source = "productionInfo.plantInfo.plantCode", target = "productionInfo.factory"),
        Mapping(source = "productionInfo.plantInfo.plantCodeVW", target = "productionInfo.factoryVw"),
        Mapping(target = "options", ignore = true),
        Mapping(target = "source", ignore = true),
    )
    fun map(
        vehicle: KafkaVehicle,
        @MappingTarget jpaVehicleEntity: JPAVehicleEntity,
    ): JPAVehicleEntity
}

abstract class ExtendedKafkaMapper(
    private val delegate: KafkaMapper,
) : KafkaMapper by delegate {
    private fun quoteMonth(allocationMonth: String?): Int? = allocationMonth?.substringAfterLast("-")?.toIntOrNull()

    private fun quoteYear(allocationMonth: String?): Int? = allocationMonth?.substringBefore('-')?.toIntOrNull()

    private fun updateOrderAndProductionInfo(
        kafkaVehicle: KafkaVehicle,
        jpaVehicleEntity: JPAVehicleEntity,
    ) {
        jpaVehicleEntity.orderInfo?.invoiceNumber = kafkaVehicle.invoiceInfo.oem.invoiceNumber
        jpaVehicleEntity.orderInfo?.invoiceDate =
            kafkaVehicle.invoiceInfo.oem.invoiceDate?.let { date ->
                Date.from(
                    date.atStartOfDay(ZoneOffset.UTC).toInstant(),
                )
            }

        jpaVehicleEntity.productionInfo?.quoteMonth = quoteMonth(kafkaVehicle.orderInfo.allocationMonth)
        jpaVehicleEntity.productionInfo?.quoteYear = quoteYear(kafkaVehicle.orderInfo.allocationMonth)
    }

    override fun map(
        vehicle: KafkaVehicle,
        jpaVehicleEntity: JPAVehicleEntity,
    ): JPAVehicleEntity =
        delegate.map(vehicle, jpaVehicleEntity).also {
            updateOrderAndProductionInfo(vehicle, it)
        }

    override fun map(vehicle: KafkaVehicle): JPAVehicleEntity =
        delegate.map(vehicle).also {
            updateOrderAndProductionInfo(vehicle, it)
        }
}
