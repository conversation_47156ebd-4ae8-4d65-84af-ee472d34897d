/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.streamzilla

import org.springframework.beans.factory.annotation.Value
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory
import org.springframework.kafka.core.ConsumerFactory
import org.springframework.kafka.listener.CommonContainerStoppingErrorHandler

@Configuration
@ConditionalOnProperty("vehicle-data.pvh.streamzilla.enabled", havingValue = "true", matchIfMissing = false)
class PVHStreamzillaConfiguration(
    @Value("\${vehicle-data.pvh.streamzilla.stop-consuming-on-error}") private val failOnError: <PERSON><PERSON><PERSON>,
) {
    @Bean
    fun pvhKafkaListenerContainerFactory(consumerFactory: ConsumerFactory<Any, Any>): ConcurrentKafkaListenerContainerFactory<Any, Any> =
        ConcurrentKafkaListenerContainerFactory<Any, Any>().apply {
            this.consumerFactory = consumerFactory
            if (failOnError) {
                setCommonErrorHandler(CommonContainerStoppingErrorHandler())
            }
        }
}
