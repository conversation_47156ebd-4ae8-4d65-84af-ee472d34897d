/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.integrations.pvh.streamzilla

import com.fasterxml.jackson.core.type.TypeReference
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.features.refreshvehicle.VehicleDataKafkaRefreshService
import de.porsche.pvh.async.processing.api.KafkaVehicle
import org.apache.kafka.clients.consumer.ConsumerRecord
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.kafka.config.KafkaListenerEndpointRegistry
import org.springframework.stereotype.Service

@Service
@ConditionalOnBean(PVHStreamzillaConfiguration::class)
class PVHStreamzillaConsumer(
    private val objectMapper: ObjectMapper,
    private val vehicleDataKafkaRefreshService: VehicleDataKafkaRefreshService,
    private val registry: KafkaListenerEndpointRegistry,
) {
    private val logger = LoggerFactory.getLogger(PVHStreamzillaConsumer::class.java)

    companion object {
        const val KAFKA_PVH_TOPIC = "FRA_pvh_Vehicle_Embargo"
        private const val LISTENER_ID = "pvh_streamzilla_listener"
    }

    @KafkaListener(
        topics = [KAFKA_PVH_TOPIC],
        groupId = "\${vehicle-data.pvh.streamzilla.group-id}",
        containerFactory = "pvhKafkaListenerContainerFactory",
        properties = ["auto.offset.reset=\${vehicle-data.pvh.streamzilla.auto-offset-reset}"],
        id = LISTENER_ID,
    )
    fun listen(consumerRecord: ConsumerRecord<Any, String>) {
        logger.info("Handle kafka message for key ${consumerRecord.key()}")
        val message = consumerRecord.value()
        if (isTombstoneMessage(consumerRecord)) {
            return
        }

        val pvhVehicle =
            runCatching {
                objectMapper.readValue(message, KafkaVehicle::class.java)
            }.getOrElse {
                logger.error("Failed to parse message: $message", it)
                throw IllegalArgumentException("Invalid Kafka Vehicle message format", it)
            }
        try {
            when (consumerRecord.getPVHMessageType()) {
                "INSERT" -> {
                    logger.info("PVH message type is INSERT, creating new or updating existing vehicle in FVM")
                    vehicleDataKafkaRefreshService.createVehicle(pvhVehicle)
                }

                else -> {
                    logger.info("PVH message type is not INSERT, updating vehicle only if exists in FVM")
                    vehicleDataKafkaRefreshService.updateIfExist(
                        pvhVehicle,
                        extractUpdatedFieldsHeader(consumerRecord),
                    )
                }
            }
        } catch (exception: Exception) {
            if (pvhVehicle.vguid.trim().endsWith("test", true)) {
                logger.warn("Ignoring exception for test vehicle: ${exception.message}")
                return
            }
            logger.error(
                "Error processing message from kafka. " +
                    "key: ${consumerRecord.key()}; " +
                    "offset: ${consumerRecord.offset()}; " +
                    "partition: ${consumerRecord.partition()}; " +
                    "headers: ${mapHeaders(consumerRecord)}",
                exception,
            )
            throw exception
        }
    }

    private fun extractUpdatedFieldsHeader(consumerRecord: ConsumerRecord<Any, String>): List<String> {
        val fieldsChanged =
            consumerRecord
                .headers()
                .lastHeader("Updated-Fields")
                ?.value()
                ?.let { String(it) }
        val updatedFieldsList =
            fieldsChanged?.let { objectMapper.readValue(it, object : TypeReference<List<String>>() {}) }
                ?: emptyList()
        return updatedFieldsList
    }

    private fun mapHeaders(consumerRecord: ConsumerRecord<Any, String>): List<Pair<String, String>> =
        try {
            consumerRecord.headers().map { Pair(it.key(), String(it.value())) }
        } catch (exception: Exception) {
            logger.error("could map headers ${consumerRecord.headers()}")
            emptyList()
        }

    private fun isTombstoneMessage(consumerRecord: ConsumerRecord<Any, String>): Boolean = consumerRecord.value().isNullOrEmpty()

    fun startConsumer() {
        try {
            registry.getListenerContainer(LISTENER_ID)?.start()
            logger.info("$LISTENER_ID started listening for pvh vehicles")
        } catch (e: Exception) {
            logger.error("Error starting kafka listener $LISTENER_ID", e)
            throw e
        }
    }

    private fun <K, V> ConsumerRecord<K, V>.getPVHMessageType(): String =
        this
            .headers()
            .lastHeader("Database-Action")
            ?.value()
            ?.let { String(it) } ?: "UNKNOWN"
}
