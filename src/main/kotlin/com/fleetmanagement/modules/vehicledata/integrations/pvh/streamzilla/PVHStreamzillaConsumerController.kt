package com.fleetmanagement.modules.vehicledata.integrations.pvh.streamzilla

import com.fleetmanagement.modules.legalhold.PrivateRestController
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.web.bind.annotation.PostMapping

@PrivateRestController
@ConditionalOnProperty("vehicle-data.pvh.streamzilla.stop-consuming-on-error", havingValue = "true", matchIfMissing = false)
@ConditionalOnBean(PVHStreamzillaConfiguration::class)
class PVHStreamzillaConsumerController(
    private val consumerService: PVHStreamzillaConsumer,
) {
    @PostMapping(value = ["/kafka/pvh/consumer/start"])
    fun startPvhConsumer() {
        consumerService.startConsumer()
    }
}
