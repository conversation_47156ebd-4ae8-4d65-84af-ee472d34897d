/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.integrations.vtstamm.rest

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.vehicledata.api.VTStammVehicleUpdateDto
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateVehicleDTO
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleCreateDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleNonVTStammUpdateDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleTypeDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleUpdateDto
import java.time.LocalTime
import java.time.OffsetDateTime
import java.time.ZoneOffset
import kotlin.jvm.optionals.getOrNull

fun VehicleCreateDto.toCreateVehicleDTO(): CreateVehicleDTO =
    CreateVehicleDTO(
        source = VehicleSource.VTSTAMM,
        financialAssetType = FinancialAssetType.FE,
        vin = this.vin,
        manufacturer = this.manufacturer,
        equipmentNumber = this.equipmentNumber,
        vehicleType = this.vehicleType.toVehicleType(),
        blockedForSale = this.blockedForSale,
        scrapVehicle = this.scrapVehicle,
        internalVehicleDescription = this.internalVehicleDescription?.getOrNull(),
        internalDesignation = this.internalDesignation?.getOrNull(),
        modelDescription = this.modelDescription?.getOrNull(),
        modelDescriptionDevelopment = this.modelDescriptionDevelopment?.getOrNull(),
        orderType = this.orderType?.getOrNull(),
        tuevAppointment = this.tuevAppointment?.getOrNull(),
        cnrValue = this.countryInfo?.getOrNull(),
        colorExterior = this.exteriorColor?.getOrNull(),
        colorInterior = this.interiorColor?.getOrNull(),
        productionEndDate = this.productionEndDate?.getOrNull(),
        modelRangeDevelopment = this.modelRangeDevelopment?.getOrNull(),
        subjectToConfidentiality = this.subjectToConfidentiality?.getOrNull(),
        confidentialityClassification = this.confidentialityClassification?.getOrNull(),
        subjectToConfidentialityStartDate = this.subjectToConfidentialityStartDate?.getOrNull(),
        subjectToConfidentialityEndDate = this.subjectToConfidentialityEndDate?.getOrNull(),
        recordFactoryExit = this.recordFactoryExit?.getOrNull(),
        camouflageRequired = this.camouflageRequired?.getOrNull(),
        statusVTS = this.statusVTS?.getOrNull(),
        typeOfUseVTS = this.typeOfUseVTS?.getOrNull(),
    )

fun VehicleUpdateDto.toVehicleUpdate() =
    com.fleetmanagement.modules.vehicledata.api.VehicleUpdateDto(
        modelDescription = this.modelDescription,
        orderType = this.orderType,
        countryInfo = this.countryInfo,
        exteriorColor = this.exteriorColor,
        interiorColor = this.interiorColor,
        productionEndDate =
            this.productionEndDate?.map {
                OffsetDateTime.of(
                    it,
                    LocalTime.MIN,
                    ZoneOffset.of("Z"),
                )
            },
    )

fun VehicleUpdateDto.toVTStammVehicleUpdate() =
    VTStammVehicleUpdateDto(
        equipmentNumber = this.equipmentNumber,
        tuevAppointment = this.tuevAppointment,
        internalDesignation = this.internalDesignation,
        internalVehicleDescription = this.internalVehicleDescription,
        subjectToConfidentiality = this.subjectToConfidentiality,
        confidentialityClassification = this.confidentialityClassification,
        subjectToConfidentialityStartDate = this.subjectToConfidentialityStartDate,
        subjectToConfidentialityEndDate = this.subjectToConfidentialityEndDate,
        recordFactoryExit = this.recordFactoryExit,
        camouflageRequired = this.camouflageRequired,
        rangeDevelopment = this.modelRangeDevelopment,
        modelDescriptionDevelopment = this.modelDescriptionDevelopment,
        statusVTS = this.statusVTS,
        typeOfUseVTS = this.typeOfUseVTS,
    )

fun VehicleNonVTStammUpdateDto.toVTStammVehicleUpdate() =
    VTStammVehicleUpdateDto(
        equipmentNumber = this.equipmentNumber,
        tuevAppointment = this.tuevAppointment,
        internalDesignation = this.internalDesignation,
        internalVehicleDescription = this.internalVehicleDescription,
        subjectToConfidentiality = this.subjectToConfidentiality,
        confidentialityClassification = this.confidentialityClassification,
        subjectToConfidentialityStartDate = this.subjectToConfidentialityStartDate,
        subjectToConfidentialityEndDate = this.subjectToConfidentialityEndDate,
        recordFactoryExit = this.recordFactoryExit,
        camouflageRequired = this.camouflageRequired,
        statusVTS = this.statusVTS,
        typeOfUseVTS = this.typeOfUseVTS,
    )

fun VehicleTypeDto.toVehicleType(): VehicleType =
    when (this) {
        VehicleTypeDto.PKW -> VehicleType.PKW
        VehicleTypeDto.TRAILER -> VehicleType.TRAILER
        VehicleTypeDto.MOTORCYCLE -> VehicleType.MOTORCYCLE
        VehicleTypeDto.TRUCK -> VehicleType.TRUCK
        VehicleTypeDto.TRANSPORTER -> VehicleType.TRANSPORTER
    }
