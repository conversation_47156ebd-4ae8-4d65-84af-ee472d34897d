package com.fleetmanagement.modules.vehicledata.integrations.vtstamm.rest

import com.fleetmanagement.modules.vehicledata.api.CreateVehicle
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicle
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.VtstammVehicleService
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleCreateDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleNonVTStammUpdateDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleUpdateDto
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component

@Component
class VTStammVehicleRestAdapter(
    private val createVehicle: CreateVehicle,
    private val updateVehicle: UpdateVehicle,
    private val readVehicleByVIN: ReadVehicleByVIN,
) : VtstammVehicleService {
    override fun createVehicle(vehicleCreateDto: VehicleCreateDto) {
        log.info("Received VTSTAMM vehicle creation request for VIN [${vehicleCreateDto.vin}].")
        createVehicle.manuallyCreateVehicle(createVehicleDto = vehicleCreateDto.toCreateVehicleDTO())
        log.info("Successfully created VTSTAMM vehicle for VIN [${vehicleCreateDto.vin}].")
    }

    override fun updateNonVTSTammVehicle(
        vin: String,
        vehicleNonVTStammUpdateDto: VehicleNonVTStammUpdateDto,
    ) {
        log.info("Received non-VTSTAMM vehicle update request for VIN [$vin].")
        // fetch vehicle and verify source
        val vehicle = readVehicleByVIN.readVehicleByVIN(vin)

        if (ALLOWED_NON_VTSTAMM_VEHICLE_SOURCES.none { vehicle.source == it }) {
            throw VehicleUpdateNotAllowed(
                "Vehicle with VIN [$vin] and source [${vehicle.source.name}] does not match allowed sources " +
                    "[${ALLOWED_NON_VTSTAMM_VEHICLE_SOURCES.joinToString { it.name }}]",
            )
        }
        // update non-stamm properties
        updateVehicle.updateVTStammVehicle(
            vin = vin,
            vehicleUpdateDto = vehicleNonVTStammUpdateDto.toVTStammVehicleUpdate(),
            modifier = VTSTAMM_MODIFIER,
        )
        log.info("Successfully updated non-VTSTAMM vehicle with VIN [$vin].")
    }

    override fun updateVehicle(
        vin: String,
        vehicleUpdateDto: VehicleUpdateDto,
    ) {
        log.info("Received VTSTAMM vehicle update request for VIN [$vin].")
        // fetch vehicle and verify source
        val vehicle = readVehicleByVIN.readVehicleByVIN(vin)

        if (VehicleSource.VTSTAMM != vehicle.source) {
            throw VehicleUpdateNotAllowed(
                "Vehicle with VIN [$vin] and source [${vehicle.source.name}] does not match allowed source [${VehicleSource.VTSTAMM.name}].",
            )
        }
        // update non-vtstamm properties
        updateVehicle.updateVehicle(
            vehicleId = vehicle.id,
            vehicleUpdateDto = vehicleUpdateDto.toVehicleUpdate(),
            modifier = VTSTAMM_MODIFIER,
        )

        // update vtstamm properties
        updateVehicle.updateVTStammVehicle(
            vin = vin,
            vehicleUpdateDto = vehicleUpdateDto.toVTStammVehicleUpdate(),
            modifier = VTSTAMM_MODIFIER,
        )
        log.info("Successfully updated VTSTAMM vehicle with VIN [$vin].")
    }

    companion object {
        private val log = LoggerFactory.getLogger(VTStammVehicleRestAdapter::class.java)

        // see FPT1-1192
        private val ALLOWED_NON_VTSTAMM_VEHICLE_SOURCES = setOf<VehicleSource>(VehicleSource.PVH_IMPORT, VehicleSource.PVH)
        private val VTSTAMM_MODIFIER = "VTSTAMM"
    }
}

class VehicleUpdateNotAllowed(
    override val message: String?,
) : RuntimeException()
