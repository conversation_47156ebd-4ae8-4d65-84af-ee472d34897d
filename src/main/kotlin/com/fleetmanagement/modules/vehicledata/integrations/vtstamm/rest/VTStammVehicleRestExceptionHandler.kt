/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.integrations.vtstamm.rest

import com.fleetmanagement.modules.vehicledata.api.VehicleAlreadyExists
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.ErrorDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.ErrorTypeDto
import com.fleetmanagement.modules.vtstamm.adapter.incoming.rest.model.VehicleAlreadyExistsErrorDto
import org.springframework.core.annotation.Order
import org.springframework.http.HttpStatus
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.ControllerAdvice
import org.springframework.web.bind.annotation.ExceptionHandler
import java.util.*

@ControllerAdvice("com.fleetmanagement.modules.vtstamm.adapter.incoming.rest")
@Order(1)
class VTStammVehicleRestExceptionHandler {
    @ExceptionHandler(IllegalArgumentException::class)
    private fun handleIllegalArgumentException(exception: IllegalArgumentException): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message = exception.message ?: "Error while trying to service request. Illegal argument provided.",
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.BAD_REQUEST,
        )

    @ExceptionHandler(VehicleAlreadyExists::class)
    private fun handleVehicleAlreadyExists(exception: VehicleAlreadyExists): ResponseEntity<VehicleAlreadyExistsErrorDto> =
        ResponseEntity(
            VehicleAlreadyExistsErrorDto(
                message = exception.message ?: "Error while trying to create vehicle. Vehicle already exists.",
                type = ErrorTypeDto.BUSINESS,
                modelYear = Optional.ofNullable(exception.properties?.get(VehicleAlreadyExists.KEY_MODEL_YEAR)),
                productionNumber = Optional.ofNullable(exception.properties?.get(VehicleAlreadyExists.KEY_PRODUCTION_NUMBER)),
            ),
            HttpStatus.CONFLICT,
        )

    @ExceptionHandler(VehicleUpdateNotAllowed::class)
    private fun handleVehicleUpdateNotAllowed(exception: VehicleUpdateNotAllowed): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message = exception.message ?: "Error while trying to update vehicle. Vehicle update not allowed.",
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.CONFLICT,
        )

    @ExceptionHandler(VehicleUpdateException::class)
    private fun handleVehicleUpdateException(exception: VehicleUpdateException): ResponseEntity<ErrorDto> =
        ResponseEntity(
            ErrorDto(
                message = exception.message ?: "Error while trying to update vehicle.",
                type = ErrorTypeDto.BUSINESS,
            ),
            HttpStatus.CONFLICT,
        )
}
