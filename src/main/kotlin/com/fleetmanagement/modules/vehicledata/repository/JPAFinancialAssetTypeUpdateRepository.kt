/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.repository

import com.fleetmanagement.modules.vehicledata.repository.entities.FinancialAssetTypeUpdateId
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAFinancialAssetTypeUpdate
import org.springframework.data.repository.Repository

interface JPAFinancialAssetTypeUpdateRepository : Repository<JPAFinancialAssetTypeUpdate, FinancialAssetTypeUpdateId> {
    fun save(financialAssetTypeUpdate: JPAFinancialAssetTypeUpdate): JPAFinancialAssetTypeUpdate

    fun findByVin(vin: String): JPAFinancialAssetTypeUpdate?

    fun findAll(): List<JPAFinancialAssetTypeUpdate>

    fun deleteById(financialAssetTypeUpdateId: FinancialAssetTypeUpdateId)
}
