/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.repository

import com.fleetmanagement.modules.vehicledata.repository.entities.JPAMileageReadingEntity
import com.fleetmanagement.modules.vehicledata.repository.entities.MileageReadingId
import org.springframework.data.repository.Repository
import java.util.UUID

interface JPAMileageReadingRepository : Repository<JPAMileageReadingEntity, MileageReadingId> {
    fun save(mileageReading: JPAMileageReadingEntity): JPAMileageReadingEntity

    fun findAllByVehicleId(vehicleId: UUID): List<JPAMileageReadingEntity>
}
