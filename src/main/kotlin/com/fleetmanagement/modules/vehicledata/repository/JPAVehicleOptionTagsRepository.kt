/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository

import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleOptionTag
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository

@Repository
interface JPAVehicleOptionTagsRepository : JpaRepository<JPAVehicleOptionTag, Long> {
    fun findAllByNonCustomerAdequateTrue(): List<JPAVehicleOptionTag>

    fun findFirstByOptionId(optionId: String): JPAVehicleOptionTag?
}
