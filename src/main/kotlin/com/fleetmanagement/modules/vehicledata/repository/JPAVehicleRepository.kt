/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository

import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import com.fleetmanagement.modules.vehicledata.api.domain.VehicleSource
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import org.springframework.data.domain.Page
import org.springframework.data.domain.Pageable
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.JpaSpecificationExecutor
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.time.ZonedDateTime
import java.util.Optional
import java.util.UUID

@Repository
interface JPAVehicleRepository :
    JpaRepository<JPAVehicleEntity, UUID>,
    JpaSpecificationExecutor<JPAVehicleEntity> {
    fun findByVin(vin: String): Optional<JPAVehicleEntity>

    fun findByVguid(vguid: String): Optional<JPAVehicleEntity>

    fun findByVguidIn(vguids: List<String>): List<JPAVehicleEntity>

    fun findByLastUpdatedAtBefore(cutoffDate: ZonedDateTime): List<JPAVehicleEntity>

    fun findByEquiId(equiId: String): Optional<JPAVehicleEntity>

    fun findByEquipmentNumber(equipmentNumber: Long): Optional<JPAVehicleEntity>

    fun findAllByStatus(vehicleStatus: VehicleStatus): List<JPAVehicleEntity>

    fun findAllByIsActivatedForAccountingIsTrue(pageable: Pageable): Page<JPAVehicleEntity>

    fun findAllByPriceInfoFactoryNetPriceEURNotNull(): List<JPAVehicleEntity>

    @Suppress("ktlint:standard:function-naming")
    fun findAllByReturnInfo_NextProcessAndReturnInfo_FactoryCarPreparationOrderNumberIsNull(
        nextProcess: NextProcess,
    ): List<JPAVehicleEntity>

    // TODO: replace jsonb_build_array with jsonb_path_exists
    @Query(
        """
        SELECT * FROM vehicle.vehicle
        WHERE options->'current'->'individualOptions' @> jsonb_build_array(jsonb_build_object('id', :optionTagId))
        """,
        nativeQuery = true,
    )
    fun findAllByOptionTagId(
        @Param("optionTagId") optionTagId: String,
        pageable: Pageable,
    ): Page<JPAVehicleEntity>

    @Query(
        """
            SELECT v FROM JPAVehicleEntity v
            WHERE v.orderInfo.importerShortName = :importerShortName
              AND v.countryInfo.cnrValue = :cnrValue
              AND v.orderInfo.commissionNumber IN :commissionNumbers
              AND v.source IN :sources
        """,
    )
    fun findEligibleVehicleForNetFactoryPricing(
        @Param("commissionNumbers") commissionNumbers: List<String>,
        @Param("importerShortName") importerShortName: String,
        @Param("cnrValue") cnrValue: String,
        @Param("sources") sources: List<VehicleSource>,
    ): Optional<JPAVehicleEntity>
}
