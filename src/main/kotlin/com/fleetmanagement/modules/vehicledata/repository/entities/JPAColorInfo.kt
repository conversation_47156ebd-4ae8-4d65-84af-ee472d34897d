/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "color_info", schema = "vehicle")
open class JPAColorInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "exterior")
    open var exterior: String? = null,
    @Column(name = "exterior_description")
    open var exteriorDescription: String? = null,
    @Column(name = "exterior_description_language")
    open var exteriorDescriptionLanguage: String? = null,
    @Column(name = "interior_description_language")
    open var interiorDescriptionLanguage: String? = null,
    @Column(name = "interior")
    open var interior: String? = null,
    @Column(name = "interior_description")
    open var interiorDescription: String? = null,
) {
    internal fun updatePrivileged(
        colorExterior: Optional<String>?,
        colorExteriorDescription: Optional<String>?,
        colorInterior: Optional<String>?,
        colorInteriorDescription: Optional<String>?,
    ): Boolean {
        var isUpdated = false

        colorExterior
            ?.ifPresentOrElse(
                { this.exterior = it },
                { this.exterior = null },
            )?.also {
                isUpdated = true
            }
        colorExteriorDescription
            ?.ifPresentOrElse(
                { this.exteriorDescription = it },
                { this.exteriorDescription = null },
            )?.also {
                isUpdated = true
            }
        colorInterior
            ?.ifPresentOrElse(
                { this.interior = it },
                { this.interior = null },
            )?.also {
                isUpdated = true
            }
        colorInteriorDescription
            ?.ifPresentOrElse(
                { this.interiorDescription = it },
                { this.interiorDescription = null },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }
}
