/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.util.Optional

@Entity
@Table(name = "consumption_info", schema = "vehicle")
open class JPAConsumptionInfo(
    @Id @GeneratedValue(strategy = GenerationType.IDENTITY) @Column(name = "id") open var id: Long? = null,
    @Column(name = "drive_type") open var driveType: String? = null,
    @Column(name = "typification") open var typification: String? = null,
    @Column(name = "primary_fuel_type") @Enumerated(EnumType.STRING) open var primaryFuelType: FuelType? = null,
    @Column(name = "secondary_fuel_type") @Enumerated(EnumType.STRING) open var secondaryFuelType: FuelType? = null,
) {
    internal fun updatePrivileged(
        primaryFuelType: Optional<FuelType>?,
        secondaryFuelType: Optional<FuelType>?,
    ): Boolean {
        var isUpdated = false

        primaryFuelType
            ?.ifPresentOrElse({ this.primaryFuelType = it }, { this.primaryFuelType = null })
            ?.also { isUpdated = true }
        secondaryFuelType
            ?.ifPresentOrElse({ this.secondaryFuelType = it }, { this.secondaryFuelType = null })
            ?.also { isUpdated = true }

        return isUpdated
    }
}
