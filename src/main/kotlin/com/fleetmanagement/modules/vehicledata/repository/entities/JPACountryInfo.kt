/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "country_info", schema = "vehicle")
open class JPACountryInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "bnr_value")
    open var bnrValue: String? = null,
    @Column(name = "cnr_value")
    open var cnrValue: String? = null,
    @Column(name = "cnr_country_description")
    open var cnrCountryDescription: String? = null,
    @Column(name = "cnr_country_description_language")
    open var cnrCountryDescriptionLanguage: String? = null,
) {
    internal fun updatePrivileged(
        countryInfoBNRValue: Optional<String>?,
        countryInfoCNRValue: Optional<String>?,
        countryInfoCNRValueCountryDescription: Optional<String>?,
    ): Boolean {
        var isUpdated = false

        countryInfoBNRValue
            ?.ifPresentOrElse(
                { this.bnrValue = it },
                { this.bnrValue = null },
            )?.also {
                isUpdated = true
            }
        countryInfoCNRValue
            ?.ifPresentOrElse(
                { this.cnrValue = it },
                { this.cnrValue = null },
            )?.also {
                isUpdated = true
            }
        countryInfoCNRValueCountryDescription
            ?.ifPresentOrElse(
                { this.cnrCountryDescription = it },
                { this.cnrCountryDescription = null },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }
}
