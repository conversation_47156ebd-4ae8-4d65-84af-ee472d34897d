/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateNotAllowedActiveTransferExistsException
import jakarta.persistence.*
import java.time.OffsetDateTime
import java.util.*
import kotlin.jvm.optionals.getOrNull

@Entity
@Table(name = "delivery_info", schema = "vehicle")
open class JPADeliveryInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "preparation_done_date")
    open var preparationDoneDate: OffsetDateTime? = null,
    // FPT1-1190 defaults to false
    @Column(name = "is_preparation_necessary")
    open var isPreparationNecessary: Boolean = false,
) {
    internal fun update(
        preparationDoneDate: Optional<OffsetDateTime>?,
        isPreparationNecessary: Optional<Boolean>?,
        scrapVehicle: Boolean,
        blockedForSale: Boolean,
        vehicleId: UUID,
        hasActiveVehicleTransfer: Boolean,
    ): Boolean {
        var isUpdated = false

        preparationDoneDate?.also {
            updatePreparationDoneDate(
                preparationDoneDate = it.getOrNull(),
                scrapVehicle = scrapVehicle,
                blockedForSale = blockedForSale,
                vehicleId = vehicleId,
                hasActiveVehicleTransfer = hasActiveVehicleTransfer,
            )
        }

        isPreparationNecessary
            ?.ifPresent {
                updateIsPreparationNecessary(
                    it,
                    scrapVehicle = scrapVehicle,
                    blockedForSale = blockedForSale,
                    vehicleId = vehicleId,
                )
            }?.also {
                isUpdated = true
            }

        return isUpdated
    }

    private fun updateIsPreparationNecessary(
        isPreparationNecessary: Boolean,
        scrapVehicle: Boolean,
        blockedForSale: Boolean,
        vehicleId: UUID,
    ) {
        validateBlockedForSaleAndScrappedVehiclePreconditions(
            VehiclePropertyForValidation.IS_PREPARATION_NECESSARY,
            blockedForSale,
            scrapVehicle,
            vehicleId,
        )
        this.isPreparationNecessary = isPreparationNecessary
    }

    private fun updatePreparationDoneDate(
        preparationDoneDate: OffsetDateTime?,
        scrapVehicle: Boolean,
        blockedForSale: Boolean,
        vehicleId: UUID,
        hasActiveVehicleTransfer: Boolean,
    ) {
        if (hasActiveVehicleTransfer) {
            throw VehicleUpdateNotAllowedActiveTransferExistsException(
                propertyName = JPADeliveryInfo::preparationDoneDate.name,
            )
        }
        validateBlockedForSaleAndScrappedVehiclePreconditions(
            VehiclePropertyForValidation.PREPARATION_DONE_DATE,
            blockedForSale,
            scrapVehicle,
            vehicleId,
        )
        this.preparationDoneDate = preparationDoneDate
    }

    internal fun resetPreparationDoneDate() {
        this.preparationDoneDate = null
    }
}
