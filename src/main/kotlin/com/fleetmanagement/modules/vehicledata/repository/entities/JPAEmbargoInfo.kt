/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "embargo_info", schema = "vehicle")
open class JPAEmbargoInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    inEmbargo: Boolean? = null,
) {
    // FPT1-1190 defaults to false
    @Column(name = "in_embargo")
    open var inEmbargo: Boolean = inEmbargo ?: false

    internal fun updatePrivileged(inEmbargo: Optional<Boolean>?): Boolean {
        var isUpdated = false

        inEmbargo
            ?.ifPresent { this.inEmbargo = it }
            ?.also {
                isUpdated = true
            }

        return isUpdated
    }
}
