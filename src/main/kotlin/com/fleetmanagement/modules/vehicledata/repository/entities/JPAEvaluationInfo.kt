package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.math.BigDecimal
import java.util.Optional

@Entity
@Table(name = "evaluation_info", schema = "vehicle")
class JPAEvaluationInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "appraisal_net_price")
    open var appraisalNetPrice: BigDecimal? = null,
    @Column(name = "vehicle_evaluation_comment")
    open var vehicleEvaluationComment: String? = null,
    @Column(name = "pc_complaint_check_comment")
    open var pcComplaintCheckComment: String? = null,
) {
    internal fun updatePrivileged(
        appraisalNetPrice: Optional<BigDecimal>?,
        pcComplaintCheckComment: Optional<String>?,
        vehicleEvaluationComment: Optional<String>?,
    ): Boolean {
        var isUpdated = false

        appraisalNetPrice
            ?.ifPresentOrElse(
                { this.appraisalNetPrice = it },
                { this.appraisalNetPrice = null },
            )?.also {
                isUpdated = true
            }
        pcComplaintCheckComment
            ?.ifPresentOrElse(
                { this.pcComplaintCheckComment = it },
                { this.pcComplaintCheckComment = null },
            )?.also {
                isUpdated = true
            }

        vehicleEvaluationComment
            ?.ifPresentOrElse(
                { this.vehicleEvaluationComment = it },
                { this.vehicleEvaluationComment = null },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }
}
