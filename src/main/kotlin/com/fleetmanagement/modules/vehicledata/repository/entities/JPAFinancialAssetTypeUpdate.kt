/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import jakarta.persistence.AttributeOverride
import jakarta.persistence.Basic
import jakarta.persistence.Column
import jakarta.persistence.Embeddable
import jakarta.persistence.EmbeddedId
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.Table
import org.hibernate.annotations.CreationTimestamp
import java.io.Serializable
import java.time.OffsetDateTime
import java.util.UUID

@Entity
@Table(name = "financial_asset_type_update", schema = "vehicle")
class JPAFinancialAssetTypeUpdate(
    @Column(name = "vin") val vin: String,
    financialAssetType: FinancialAssetType,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: FinancialAssetTypeUpdateId = FinancialAssetTypeUpdateId()

    @CreationTimestamp
    @Column(name = "created_at")
    val created: OffsetDateTime = OffsetDateTime.now()

    @Column(name = "financial_asset_type")
    @Enumerated(EnumType.STRING)
    var financialAssetType: FinancialAssetType = financialAssetType
        private set

    internal fun update(financialAssetType: FinancialAssetType) {
        if (this.financialAssetType != financialAssetType) {
            this.financialAssetType = financialAssetType
        }
    }
}

@Embeddable
data class FinancialAssetTypeUpdateId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable
