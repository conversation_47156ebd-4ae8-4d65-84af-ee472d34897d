/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateNotAllowedActiveTransferExistsException
import jakarta.persistence.*
import java.time.OffsetDateTime
import java.util.*
import kotlin.jvm.optionals.getOrNull

@Entity
@Table(name = "fleet_info", schema = "vehicle")
open class JPAFleetInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "sold_date")
    open var soldDate: OffsetDateTime? = null,
    @Column(name = "scrapped_date")
    open var scrappedDate: OffsetDateTime? = null,
    @Column(name = "stolen_date")
    open var stolenDate: OffsetDateTime? = null,
    @Column(name = "sold_cup_car_date")
    open var soldCupCarDate: OffsetDateTime? = null,
    @Column(name = "approved_for_scrapping_date")
    open var approvedForScrappingDate: OffsetDateTime? = null,
    @Column(name = "scrapped_vehicle_offered_date")
    open var scrappedVehicleOfferedDate: OffsetDateTime? = null,
    @Column(name = "vehicle_sent_to_sales_date")
    open var vehicleSentToSalesDate: OffsetDateTime? = null,
    @Column(name = "cost_estimation_ordered_date")
    open var costEstimationOrderedDate: OffsetDateTime? = null,
    // FPT1-1190 defaults to false
    @Column(name = "is_residual_value_market")
    open var isResidualValueMarket: Boolean = false,
    @Column(name = "profitability_audit_date")
    open var profitabilityAuditDate: OffsetDateTime? = null,
    @Column(name = "comment")
    open var comment: String? = null,
    // FPT1-1190 defaults to false
    @Column(name = "is_classic")
    open var isClassic: Boolean = false,
    @Column(name = "race_car", nullable = false)
    open var raceCar: Boolean = false,
    scrapVehicle: Boolean? = null,
) {
    // FPT1-1190 defaults to false
    @Column(name = "scrap_vehicle")
    open var scrapVehicle: Boolean = scrapVehicle ?: false

    internal fun update(
        soldDate: Optional<OffsetDateTime>?,
        scrappedDate: Optional<OffsetDateTime>?,
        stolenDate: Optional<OffsetDateTime>?,
        soldCupCarDate: Optional<OffsetDateTime>?,
        approvedForScrappingDate: Optional<OffsetDateTime>?,
        scrappedVehicleOfferedDate: Optional<OffsetDateTime>?,
        vehicleSentToSalesDate: Optional<OffsetDateTime>?,
        costEstimationOrderedDate: Optional<OffsetDateTime>?,
        isResidualValueMarket: Optional<Boolean>?,
        profitabilityAuditDate: Optional<OffsetDateTime>?,
        scrapVehicle: Optional<Boolean>?,
        comment: Optional<String>?,
        isClassic: Optional<Boolean>?,
        raceCar: Optional<Boolean>?,
        blockedForSale: Boolean,
        vehicleId: UUID,
        hasActiveVehicleTransfer: Boolean,
    ): Boolean {
        var isUpdated = false
        scrapVehicle
            ?.ifPresent { this.scrapVehicle = it }
            ?.also {
                isUpdated = true
            }

        soldDate
            ?.also {
                updateSoldDate(
                    soldDate = it.getOrNull(),
                    blockedForSale = blockedForSale,
                    vehicleId = vehicleId,
                    hasActiveVehicleTransfer = hasActiveVehicleTransfer,
                )
                isUpdated = true
            }

        scrappedDate
            ?.also {
                updateScrappedDate(
                    scrappedDate = it.getOrNull(),
                    blockedForSale = blockedForSale,
                    vehicleId = vehicleId,
                    hasActiveVehicleTransfer = hasActiveVehicleTransfer,
                )
                isUpdated = true
            }
        stolenDate
            ?.also {
                updateStolenDate(
                    stolenDate = it.getOrNull(),
                    hasActiveVehicleTransfer = hasActiveVehicleTransfer,
                )
                isUpdated = true
            }
        soldCupCarDate
            ?.also {
                updateSoldCupCarDate(
                    soldCupCarDate = it.getOrNull(),
                    hasActiveVehicleTransfer = hasActiveVehicleTransfer,
                )
                isUpdated = true
            }

        approvedForScrappingDate?.also {
            updateApprovedForScrappingDate(
                approvedForScrappingDate = it.getOrNull(),
                blockedForSale = blockedForSale,
                vehicleId = vehicleId,
                hasActiveVehicleTransfer = hasActiveVehicleTransfer,
            )
            isUpdated = true
        }
        scrappedVehicleOfferedDate
            ?.ifPresentOrElse(
                {
                    updateScrappedVehicleOfferedDate(
                        it,
                        blockedForSale,
                        vehicleId,
                    )
                },
                { this.scrappedVehicleOfferedDate = null },
            )?.also {
                isUpdated = true
            }
        vehicleSentToSalesDate
            ?.ifPresentOrElse(
                {
                    updateVehicleSentToSalesDate(
                        it,
                        blockedForSale,
                        vehicleId,
                    )
                },
                { this.vehicleSentToSalesDate = null },
            )?.also {
                isUpdated = true
            }
        costEstimationOrderedDate
            ?.ifPresentOrElse(
                {
                    updateCostEstimationOrderedDate(
                        it,
                        blockedForSale,
                        vehicleId,
                    )
                },
                { this.costEstimationOrderedDate = null },
            )?.also {
                isUpdated = true
            }
        isResidualValueMarket
            ?.ifPresent { this.isResidualValueMarket = it }
            ?.also {
                isUpdated = true
            }
        profitabilityAuditDate
            ?.ifPresentOrElse(
                {
                    updateProfitabilityAuditDate(
                        it,
                        blockedForSale,
                        vehicleId,
                    )
                },
                { this.profitabilityAuditDate = null },
            )?.also {
                isUpdated = true
            }
        comment
            ?.ifPresentOrElse(
                { this.comment = it },
                { this.comment = null },
            )?.also {
                isUpdated = true
            }
        isClassic
            ?.ifPresent { this.isClassic = it }
            ?.also {
                isUpdated = true
            }
        raceCar
            ?.ifPresent { this.raceCar = it }
            ?.also {
                isUpdated = true
            }
        return isUpdated
    }

    fun update(scrapVehicle: Optional<Boolean>?) {
        scrapVehicle?.also { optional ->
            optional.ifPresent { this.scrapVehicle = it }
        }
    }

    // Vehicle in process for sale
    internal fun updateCostEstimationOrderedDate(
        costEstimationOrderedDate: OffsetDateTime?,
        blockedForSale: Boolean,
        vehicleId: UUID,
    ) {
        validateNotFutureDatePrecondition(
            VehiclePropertyForValidation.COST_ESTIMATION_ORDERED_DATE,
            costEstimationOrderedDate,
            vehicleId,
        )
        if (costEstimationOrderedDate != null) {
            validateBlockedForSaleAndScrappedVehiclePreconditions(
                VehiclePropertyForValidation.COST_ESTIMATION_ORDERED_DATE,
                blockedForSale,
                scrapVehicle,
                vehicleId,
            )
        }
        this.costEstimationOrderedDate = costEstimationOrderedDate
    }

    private fun updateVehicleSentToSalesDate(
        vehicleSentToSalesDate: OffsetDateTime,
        blockedForSale: Boolean,
        vehicleId: UUID,
    ) {
        validateNotFutureDatePrecondition(
            property = VehiclePropertyForValidation.VEHICLE_SENT_TO_SALES_DATE,
            date = vehicleSentToSalesDate,
            vehicleId = vehicleId,
        )
        validateBlockedForSaleAndScrappedVehiclePreconditions(
            VehiclePropertyForValidation.VEHICLE_SENT_TO_SALES_DATE,
            blockedForSale,
            this.scrapVehicle,
            vehicleId,
        )
        this.vehicleSentToSalesDate = vehicleSentToSalesDate
    }

    internal fun updateSoldDate(
        soldDate: OffsetDateTime?,
        blockedForSale: Boolean,
        vehicleId: UUID,
        hasActiveVehicleTransfer: Boolean,
    ) {
        validateNotFutureDatePrecondition(
            property = VehiclePropertyForValidation.SOLD_DATE,
            date = soldDate,
            vehicleId = vehicleId,
        )
        if (hasActiveVehicleTransfer) {
            throw VehicleUpdateNotAllowedActiveTransferExistsException(
                propertyName = JPAFleetInfo::soldDate.name,
            )
        }
        validateBlockedForSaleAndScrappedVehiclePreconditions(
            VehiclePropertyForValidation.SOLD_DATE,
            blockedForSale,
            this.scrapVehicle,
            vehicleId,
        )
        this.soldDate = soldDate
    }

    // Vehicle in process for profitability audit
    private fun updateProfitabilityAuditDate(
        profitabilityAuditDate: OffsetDateTime,
        blockedForSale: Boolean,
        vehicleId: UUID,
    ) {
        validateNotFutureDatePrecondition(
            VehiclePropertyForValidation.PROFITABILITY_AUDIT_DATE,
            profitabilityAuditDate,
            vehicleId,
        )
        validateBlockedForSaleAndScrappedVehiclePreconditions(
            VehiclePropertyForValidation.PROFITABILITY_AUDIT_DATE,
            blockedForSale,
            this.scrapVehicle,
            vehicleId,
        )
        this.profitabilityAuditDate = profitabilityAuditDate
    }

    // Vehicle in process for scrapping
    internal fun updateScrappedVehicleOfferedDate(
        scrappedVehicleOfferedDate: OffsetDateTime?,
        blockedForSale: Boolean,
        vehicleId: UUID,
    ) {
        validateNotFutureDatePrecondition(
            VehiclePropertyForValidation.SCRAPPED_VEHICLE_OFFERED_DATE,
            scrappedVehicleOfferedDate,
            vehicleId,
        )
        if (scrappedVehicleOfferedDate != null) {
            validateBlockedForSaleAndScrappedVehiclePreconditions(
                VehiclePropertyForValidation.SCRAPPED_VEHICLE_OFFERED_DATE,
                blockedForSale,
                this.scrapVehicle,
                vehicleId,
            )
        }
        this.scrappedVehicleOfferedDate = scrappedVehicleOfferedDate
    }

    private fun updateApprovedForScrappingDate(
        approvedForScrappingDate: OffsetDateTime?,
        blockedForSale: Boolean,
        vehicleId: UUID,
        hasActiveVehicleTransfer: Boolean,
    ) {
        validateNotFutureDatePrecondition(
            property = VehiclePropertyForValidation.APPROVED_FOR_SCRAPPING_DATE,
            date = approvedForScrappingDate,
            vehicleId = vehicleId,
        )
        if (hasActiveVehicleTransfer) {
            throw VehicleUpdateNotAllowedActiveTransferExistsException(
                propertyName = JPAFleetInfo::approvedForScrappingDate.name,
            )
        }
        validateBlockedForSaleAndScrappedVehiclePreconditions(
            VehiclePropertyForValidation.APPROVED_FOR_SCRAPPING_DATE,
            blockedForSale,
            this.scrapVehicle,
            vehicleId,
        )
        this.approvedForScrappingDate = approvedForScrappingDate
    }

    private fun updateScrappedDate(
        scrappedDate: OffsetDateTime?,
        blockedForSale: Boolean,
        vehicleId: UUID,
        hasActiveVehicleTransfer: Boolean,
    ) {
        validateNotFutureDatePrecondition(
            property = VehiclePropertyForValidation.SCRAPPED_DATE,
            date = scrappedDate,
            vehicleId = vehicleId,
        )
        if (hasActiveVehicleTransfer) {
            throw VehicleUpdateNotAllowedActiveTransferExistsException(
                propertyName = JPAFleetInfo::scrappedDate.name,
            )
        }
        validateBlockedForSaleAndScrappedVehiclePreconditions(
            VehiclePropertyForValidation.SCRAPPED_DATE,
            blockedForSale,
            this.scrapVehicle,
            vehicleId,
        )
        this.scrappedDate = scrappedDate
    }

    private fun updateStolenDate(
        stolenDate: OffsetDateTime?,
        hasActiveVehicleTransfer: Boolean,
    ) {
        validateNotFutureDatePrecondition(
            property = VehiclePropertyForValidation.STOLEN_DATE,
            date = stolenDate,
        )
        if (hasActiveVehicleTransfer) {
            throw VehicleUpdateNotAllowedActiveTransferExistsException(
                propertyName = JPAFleetInfo::stolenDate.name,
            )
        }
        this.stolenDate = stolenDate
    }

    private fun updateSoldCupCarDate(
        soldCupCarDate: OffsetDateTime?,
        hasActiveVehicleTransfer: Boolean,
    ) {
        validateNotFutureDatePrecondition(
            property = VehiclePropertyForValidation.SOLD_CUP_CAR_DATE,
            date = soldCupCarDate,
        )

        if (hasActiveVehicleTransfer) {
            throw VehicleUpdateNotAllowedActiveTransferExistsException(
                propertyName = JPAFleetInfo::soldCupCarDate.name,
            )
        }
        this.soldCupCarDate = soldCupCarDate
    }

    internal fun resetStolenDate() {
        this.stolenDate = null
    }

    internal fun resetApprovedForScrappingDate() {
        this.approvedForScrappingDate = null
    }

    internal fun resetCostEstimationOrderedDate() {
        this.costEstimationOrderedDate = null
    }

    internal fun resetIsResidualValueMarket() {
        this.isResidualValueMarket = false
    }

    internal fun resetProfitabilityAuditDate() {
        this.profitabilityAuditDate = null
    }
}
