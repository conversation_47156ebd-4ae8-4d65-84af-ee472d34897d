/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import org.springframework.data.annotation.CreatedBy
import java.io.Serializable
import java.time.OffsetDateTime
import java.util.*

/**
 * A single mileage reading for a vehicle.
 * Readings are immutable and can be created by their respective service.
 * A view is provided to join the "current" mileage reading on a vehicle if required
 */
@Entity
@Table(name = "mileage_reading", schema = "vehicle")
class JPAMileageReadingEntity(
    // the vehicle this reading refers to
    @Column(name = "vehicle_id") val vehicleId: UUID,
    /* The timestamp this mileage reading was recorded.
     * Might differ from creation timestamp if reading was imported with some delay.
     */
    @Column(name = "read_date") val readDate: OffsetDateTime,
    /* The source for this reading.
     * Indicated the providing interface/system or 'MANUAL' if reading was created by a user or admin.
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "source")
    val source: MileageReadingSource,
    // the mileage recorded during this reading
    @Column(name = "mileage")
    val mileage: Int,
) {
    @EmbeddedId
    @GeneratedValue
    @AttributeOverride(name = "value", column = Column(name = "id"))
    val id: MileageReadingId = MileageReadingId()

    @CreationTimestamp
    @Column(name = "created_date")
    val created: OffsetDateTime = OffsetDateTime.now()

    @CreatedBy
    @Column(name = "created_by")
    val createdBy: String = ""
}

enum class MileageReadingSource {
    CAR_SYNC,
    MANUAL,
    DELIVERY,
    RETURN,
    FMS,
}

@Embeddable
data class MileageReadingId(
    @Basic val value: UUID = UUID.randomUUID(),
) : Serializable
