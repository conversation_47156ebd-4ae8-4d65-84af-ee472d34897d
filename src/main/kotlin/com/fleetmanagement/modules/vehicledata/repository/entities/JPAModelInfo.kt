/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.emhshared.domain.VehicleType
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.EnumType
import jakarta.persistence.Enumerated
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.util.Optional

@Entity
@Table(name = "model_info", schema = "vehicle")
open class JPAModelInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "model_description")
    open var modelDescription: String? = null,
    @Column(name = "model_description_development")
    open var modelDescriptionDevelopment: String? = null,
    @Column(name = "model_description_language")
    open var modelDescriptionLanguage: String? = null,
    @Column(name = "model_year")
    open var modelYear: Int? = null,
    @Column(name = "product_id")
    open var productId: String? = null,
    @Column(name = "product_code")
    open var productCode: String? = null,
    @Column(name = "order_type")
    open var orderType: String? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "vehicle_type")
    open var vehicleType: VehicleType? = null,
    // modelRangeDevelopment / Modellreihe
    @Column(name = "range_development")
    open var rangeDevelopment: String? = null,
) {
    @Column(name = "range")
    open var range: String? = null

    @Column(name = "manufacturer")
    open var manufacturer: String? = null

    internal fun update(
        manufacturer: Optional<String>?,
        vehicleType: Optional<VehicleType>?,
    ) {
        manufacturer?.also { optional ->
            optional.ifPresentOrElse(
                { this.manufacturer = it },
                { this.manufacturer = null },
            )
        }
        vehicleType?.also { optional ->
            optional.ifPresentOrElse(
                { this.vehicleType = it },
                { this.vehicleType = null },
            )
        }
    }

    internal fun updatePrivileged(
        modelDescription: Optional<String>?,
        orderType: Optional<String>?,
        productId: Optional<String>?,
        productCode: Optional<String>?,
        rangeDevelopment: Optional<String>?,
        modelDescriptionDevelopment: Optional<String>?,
    ): Boolean {
        var isUpdated = false

        modelDescription
            ?.ifPresentOrElse(
                { this.modelDescription = it },
                { this.modelDescription = null },
            )?.also {
                isUpdated = true
            }
        orderType
            ?.ifPresentOrElse(
                { this.orderType = it },
                { this.orderType = null },
            )?.also {
                isUpdated = true
            }
        productId
            ?.ifPresentOrElse(
                { this.productId = it },
                { this.productId = null },
            )?.also {
                isUpdated = true
            }
        productCode
            ?.ifPresentOrElse(
                { this.productCode = it },
                { this.productCode = null },
            )?.also {
                isUpdated = true
            }
        rangeDevelopment
            ?.ifPresentOrElse(
                { this.rangeDevelopment = it },
                { this.rangeDevelopment = null },
            )?.also {
                isUpdated = true
            }
        modelDescriptionDevelopment
            ?.ifPresentOrElse(
                { this.modelDescriptionDevelopment = it },
                { this.modelDescriptionDevelopment = null },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }

    internal fun calculateModelRange() {
        this.range =
            this.modelDescription?.let { desc ->
                modelMappings.firstOrNull { it.regex.matches(desc) }?.range?.description
            }
    }

    internal fun setDefaultManufacturer() {
        this.manufacturer = "Porsche"
    }

    companion object {
        val modelMappings =
            listOf(
                ModelMapping(Regex(".*GT.*Cup.*", RegexOption.IGNORE_CASE), ModelRange.CUSTOMER_SPORTS_VEHICLE),
                ModelMapping(Regex(".*Taycan.*", RegexOption.IGNORE_CASE), ModelRange.TAYCAN),
                ModelMapping(Regex(".*Boxster.*|.*Cayman.*|.*718.*", RegexOption.IGNORE_CASE), ModelRange.BOXSTER),
                ModelMapping(Regex(".*911.*", RegexOption.IGNORE_CASE), ModelRange.NINE_ELEVEN),
                ModelMapping(Regex(".*Macan.*", RegexOption.IGNORE_CASE), ModelRange.MACAN),
                ModelMapping(Regex(".*Cayenne.*", RegexOption.IGNORE_CASE), ModelRange.CAYENNE),
                ModelMapping(Regex(".*Panamera.*", RegexOption.IGNORE_CASE), ModelRange.PANAMERA),
                ModelMapping(Regex(".*918.*", RegexOption.IGNORE_CASE), ModelRange.SUPER_SPORTS_CAR),
            )
    }
}

enum class ModelRange(
    val description: String,
) {
    CUSTOMER_SPORTS_VEHICLE("Kd. Sport-Fzg."),
    TAYCAN("Taycan"),
    BOXSTER("Boxster"),
    NINE_ELEVEN("911"),
    MACAN("Macan"),
    CAYENNE("Cayenne"),
    PANAMERA("Panamera"),
    SUPER_SPORTS_CAR("Super-Sportwagen"),
}

data class ModelMapping(
    val regex: Regex,
    val range: ModelRange,
)
