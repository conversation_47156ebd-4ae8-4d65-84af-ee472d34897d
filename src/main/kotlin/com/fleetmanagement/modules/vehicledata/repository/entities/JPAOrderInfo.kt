/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "order_info", schema = "vehicle")
open class JPAOrderInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "consignee_number")
    open var consigneeNumber: String? = null,
    @Column(name = "trading_partner_number")
    open var tradingPartnerNumber: String? = null,
    @Column(name = "purpose_order_type")
    open var purposeOrderType: String? = null,
    @Column(name = "importer_short_name")
    open var importerShortName: String? = null,
    @Column(name = "port_code")
    open var portCode: String? = null,
    @Column(name = "commission_number")
    open var commissionNumber: String? = null,
    @Column(name = "invoice_number")
    open var invoiceNumber: String? = null,
    @Column(name = "invoice_date")
    open var invoiceDate: Date? = null,
    @Column(name = "purchase_order_date")
    open var purchaseOrderDate: Date? = null,
    @Column(name = "requested_delivery_date")
    open var requestedDeliveryDate: Date? = null,
    @Column(name = "customer_delivery_date")
    open var customerDeliveryDate: Date? = null,
    @Column(name = "delivery_type")
    open var deliveryType: String? = null,
    @Column(name = "primary_status")
    open var primaryStatus: String? = null,
    @Column(name = "department")
    open var department: String? = null,
    @Column(name = "leasing_type")
    open var leasingType: String? = null,
    @Column(name = "blocked_for_sale_source")
    open var blockedForSaleSource: String? = null,
    preproductionVehicle: Boolean? = null,
    blockedForSale: Boolean? = null,
) {
    // FPT1-1190 defaults to false
    @Column(name = "blocked_for_sale")
    open var blockedForSale: Boolean = blockedForSale ?: false

    // FPT1-1190 defaults to false
    @Column(name = "preproduction_vehicle")
    open var preproductionVehicle: Boolean = preproductionVehicle ?: false

    internal fun update(
        preproductionVehicle: Optional<Boolean>?,
        blockedForSale: Optional<Boolean>?,
        blockedForSaleSource: String,
    ) {
        preproductionVehicle?.also { optional ->
            optional.ifPresent { this.preproductionVehicle = it }
        }
        blockedForSale?.also { optional ->
            optional.ifPresent {
                this.blockedForSale = it
                this.blockedForSaleSource = blockedForSaleSource
            }
        }
    }

    internal fun updatePrivileged(
        purchaseOrderDate: Optional<Date>?,
        requestedDeliveryDate: Optional<Date>?,
        deliveryType: Optional<String>?,
        primaryStatus: Optional<String>?,
        preproductionVehicle: Optional<Boolean>?,
        blockedForSale: Optional<Boolean>?,
        blockedForSaleSource: String,
    ): Boolean {
        var isUpdated = false

        purchaseOrderDate
            ?.ifPresentOrElse(
                { this.purchaseOrderDate = it },
                { this.purchaseOrderDate = null },
            )?.also {
                isUpdated = true
            }
        requestedDeliveryDate
            ?.ifPresentOrElse(
                { this.requestedDeliveryDate = it },
                { this.requestedDeliveryDate = null },
            )?.also {
                isUpdated = true
            }
        deliveryType
            ?.ifPresentOrElse(
                { this.deliveryType = it },
                { this.deliveryType = null },
            )?.also {
                isUpdated = true
            }
        primaryStatus
            ?.ifPresentOrElse(
                { this.primaryStatus = it },
                { this.primaryStatus = null },
            )?.also {
                isUpdated = true
            }
        preproductionVehicle
            ?.ifPresent { this.preproductionVehicle = it }
            ?.also {
                isUpdated = true
            }
        blockedForSale
            ?.ifPresent {
                this.blockedForSale = it
                this.blockedForSaleSource = blockedForSaleSource
            }?.also {
                isUpdated = true
            }

        return isUpdated
    }

    internal fun updateBlockedForSale(
        blockedForSale: Boolean,
        blockedForSaleSource: String,
    ) {
        this.blockedForSale = blockedForSale
        this.blockedForSaleSource = blockedForSaleSource
    }

    internal fun isOrderCancelled(): Boolean = primaryStatus in setOf("FX99", "AX99")
}
