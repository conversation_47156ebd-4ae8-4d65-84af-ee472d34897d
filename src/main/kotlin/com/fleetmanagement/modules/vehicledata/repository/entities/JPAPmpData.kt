/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "pmp_data", schema = "vehicle")
open class JPAPmpData(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "odometer")
    open var odometer: Int? = null,
    @Column(name = "timestamp")
    open var timestamp: Date? = null,
) {
    internal fun updatePrivileged(
        pmpDataOdometer: Optional<Int>?,
        pmpDataTimestamp: Optional<Date>?,
    ): Boolean {
        var isUpdated = false

        pmpDataOdometer
            ?.ifPresentOrElse(
                { this.odometer = it },
                { this.odometer = null },
            )?.also {
                isUpdated = true
            }
        pmpDataTimestamp
            ?.ifPresentOrElse(
                { this.timestamp = it },
                { this.timestamp = null },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }
}
