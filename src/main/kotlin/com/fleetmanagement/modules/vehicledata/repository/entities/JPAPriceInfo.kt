/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.modules.vehicledata.features.vehicleprice.GrossPriceCalculator
import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import org.hibernate.annotations.ColumnTransformer
import java.math.BigDecimal
import java.time.LocalDate
import java.util.Optional

@Entity
@Table(name = "price_info", schema = "vehicle")
open class JPAPriceInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "gross_price")
    open var grossPrice: BigDecimal? = null,
    @Column(name = "gross_price_with_extras")
    open var grossPriceWithExtras: BigDecimal? = null,
    @Column(name = "gross_price_plan")
    open var grossPricePlan: BigDecimal? = null,
    @Column(name = "gross_price_new_car")
    open var grossPriceNewCar: BigDecimal? = null,
    @Column(name = "net_price")
    open var netPrice: BigDecimal? = null,
    @Column(name = "net_price_with_extras")
    open var netPriceWithExtras: BigDecimal? = null,
    @Column(name = "net_price_new_car")
    open var netPriceNewCar: BigDecimal? = null,
    @Column(name = "value_added_tax")
    open var valueAddedTax: BigDecimal? = null,
    @Column(name = "factory_net_price_eur")
    open var factoryNetPriceEUR: BigDecimal? = null,
    @Column(name = "factory_gross_price_eur")
    open var factoryGrossPriceEUR: BigDecimal? = null,
    @Column(name = "new_vehicle_invoice_date")
    open var newVehicleInvoiceDate: LocalDate? = null,
    @ColumnTransformer(write = "?::jsonb")
    @Column(name = "pia_event", columnDefinition = "jsonb")
    open var piaEvent: String? = null,
) {
    internal fun updatePrivileged(netPriceWithExtras: Optional<BigDecimal>?): Boolean {
        var isUpdated = false

        netPriceWithExtras
            ?.ifPresentOrElse(
                { updateNetPriceWithExtras(netPriceWithExtras = it) },
                { updateNetPriceWithExtras(netPriceWithExtras = null) },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }

    internal fun updateFactoryPriceAndPiaEvent(
        factoryNetPriceEUR: BigDecimal?,
        piaEvent: String,
    ) {
        updateFactoryNetPrice(factoryNetPriceEUR).also {
            this.piaEvent = piaEvent
        }
    }

    internal fun updateFactoryNetPrice(factoryNetPriceEUR: BigDecimal?) {
        this.factoryNetPriceEUR = factoryNetPriceEUR
        calculateFactoryGrossPrice()
    }

    fun calculateFactoryGrossPrice() {
        this.factoryGrossPriceEUR = this.factoryNetPriceEUR?.let { grossPriceFrom(it) }
    }

    internal fun updateNetPriceWithExtras(netPriceWithExtras: BigDecimal?) {
        this.netPriceWithExtras = netPriceWithExtras
        calculateGrossPriceWithExtras()
    }

    private fun calculateGrossPriceWithExtras() {
        this.grossPriceWithExtras =
            this.netPriceWithExtras?.let {
                grossPriceFrom(it)
            }
    }

    private fun grossPriceFrom(decimal: BigDecimal): BigDecimal {
        val taxRateLookupDate = this.newVehicleInvoiceDate ?: LocalDate.now()
        return GrossPriceCalculator().grossPrice(decimal, taxRateLookupDate)
    }

    fun updateNewVehicleInvoiceDate(newVehicleInvoiceDate: LocalDate) {
        this.newVehicleInvoiceDate = newVehicleInvoiceDate
        calculateGrossPriceWithExtras()
        calculateFactoryGrossPrice()
    }
}
