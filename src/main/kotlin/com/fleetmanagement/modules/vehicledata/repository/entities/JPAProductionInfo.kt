/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "production_info", schema = "vehicle")
open class JPAProductionInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "production_number")
    open var productionNumber: String? = null,
    @Column(name = "production_number_vw")
    open var productionNumberVw: String? = null,
    @Column(name = "production_end_date")
    open var productionEndDate: Date? = null,
    @Column(name = "technical_model_year")
    open var technicalModelYear: Int? = null,
    @Column(name = "factory")
    open var factory: String? = null,
    @Column(name = "factory_vw")
    open var factoryVw: Int? = null,
    @Column(name = "quote_month")
    open var quoteMonth: Int? = null,
    @Column(name = "quote_year")
    open var quoteYear: Int? = null,
    @Column(name = "planned_production_end_date")
    open var plannedProductionEndDate: Date? = null,
    @Column(name = "gear_box_class")
    open var gearBoxClass: Char? = null,
) {
    internal fun updatePrivileged(
        productionNumber: Optional<String>? = null,
        productionEndDate: Optional<OffsetDateTime>? = null,
        plannedProductionEndDate: Optional<OffsetDateTime>? = null,
        technicalModelYear: Optional<Int>? = null,
        factory: Optional<String>? = null,
        factoryVW: Optional<Int>? = null,
        quoteMonth: Optional<Int>? = null,
        quoteYear: Optional<Int>? = null,
        gearBoxClass: Optional<Char>? = null,
    ): Boolean {
        var isUpdated = false

        productionNumber
            ?.ifPresentOrElse(
                { this.productionNumber = it },
                { this.productionNumber = null },
            )?.also {
                isUpdated = true
            }
        productionEndDate
            ?.ifPresentOrElse(
                { this.productionEndDate = Date.from(it.toInstant()) },
                { this.productionEndDate = null },
            )?.also {
                isUpdated = true
            }
        plannedProductionEndDate
            ?.ifPresentOrElse(
                { this.plannedProductionEndDate = Date.from(it.toInstant()) },
                { this.plannedProductionEndDate = null },
            )?.also {
                isUpdated = true
            }
        technicalModelYear
            ?.ifPresentOrElse(
                { this.technicalModelYear = it },
                { this.technicalModelYear = null },
            )?.also {
                isUpdated = true
            }
        factory
            ?.ifPresentOrElse(
                { this.factory = it },
                { this.factory = null },
            )?.also {
                isUpdated = true
            }
        factoryVW
            ?.ifPresentOrElse(
                { this.factoryVw = it },
                { this.factoryVw = null },
            )?.also {
                isUpdated = true
            }
        quoteMonth
            ?.ifPresentOrElse(
                { this.quoteMonth = it },
                { this.quoteMonth = null },
            )?.also {
                isUpdated = true
            }
        quoteYear
            ?.ifPresentOrElse(
                { this.quoteYear = it },
                { this.quoteYear = null },
            )?.also {
                isUpdated = true
            }
        gearBoxClass
            ?.ifPresentOrElse(
                { this.gearBoxClass = it },
                { this.gearBoxClass = null },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }
}
