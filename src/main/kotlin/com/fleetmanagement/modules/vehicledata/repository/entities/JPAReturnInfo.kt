/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.vehicledata.api.domain.NextProcess
import jakarta.persistence.*
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "return_info", schema = "vehicle")
open class JPAReturnInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Enumerated(EnumType.STRING)
    @Column(name = "next_process")
    open var nextProcess: NextProcess? = null,
    // FPT1-1190 defaults to false
    @Column(name = "is_used_car")
    open var isUsedCar: Boolean = false,
    // date when key was returned for a pool vehicle
    @Column(name = "key_returned")
    open var keyReturned: OffsetDateTime? = null,
    @Column(name = "factory_car_preparation_order_number ")
    open var factoryCarPreparationOrderNumber: String? = null,
) {
    internal fun updatePrivileged(
        nextProcess: Optional<NextProcess>?,
        scrapVehicle: Boolean,
        blockedForSale: Boolean,
        keyReturned: Optional<OffsetDateTime>?,
    ): Boolean {
        var isUpdated = false

        nextProcess
            ?.ifPresentOrElse(
                {
                    updateNextProcess(
                        nextProcess = it,
                        scrapVehicle = scrapVehicle,
                        blockedForSale = blockedForSale,
                    )
                },
                { this.nextProcess = null },
            )?.also {
                isUpdated = true
            }

        keyReturned
            ?.ifPresentOrElse(
                {
                    validateNotFutureDatePrecondition(
                        property = VehiclePropertyForValidation.KEY_RETURNED,
                        date = it,
                    )
                    this.keyReturned = it
                },
                { this.keyReturned = null },
            )?.also {
                isUpdated = true
            }
        return isUpdated
    }

    private fun updateNextProcess(
        nextProcess: NextProcess,
        scrapVehicle: Boolean,
        blockedForSale: Boolean,
    ) {
        when (nextProcess) {
            NextProcess.SALES -> {
                if (scrapVehicle) {
                    throw VehicleReturnInfoUpdateException(
                        message = "Cannot set nextProcess to ${nextProcess.name}, as vehicle is marked as scrap vehicle.",
                        violationType = ViolationType.VEHICLE_MARKED_AS_SCRAP,
                    )
                }
                if (blockedForSale) {
                    throw VehicleReturnInfoUpdateException(
                        message = "Cannot set nextProcess to ${nextProcess.name}, as vehicle is blocked for sale.",
                        violationType = ViolationType.VEHICLE_BLOCKED_FOR_SALE,
                    )
                }
            }

            NextProcess.PROFITABILITY_AUDIT_IN_PREPARATION -> {
                if (scrapVehicle) {
                    throw VehicleReturnInfoUpdateException(
                        message = "Cannot set nextProcess to ${nextProcess.name}, as vehicle is marked as scrap vehicle.",
                        violationType = ViolationType.VEHICLE_MARKED_AS_SCRAP,
                    )
                }
            }

            NextProcess.SCRAPPED_CAR_RETURNED -> {
                if (!scrapVehicle) {
                    throw VehicleReturnInfoUpdateException(
                        message = "Cannot set nextProcess to ${nextProcess.name}, as vehicle is not marked as scrap vehicle.",
                        violationType = ViolationType.VEHICLE_MARKED_AS_SCRAP,
                    )
                }
            }

            else -> {}
        }
        this.nextProcess = nextProcess
    }

    /**
     * Updates factoryCarPreparationOrderNumber.
     * This is only done, if there is currently no orderNumber set. Existing values are not overridden.
     */
    internal fun updateFactoryCarPreparationOrderNumber(factoryCarPreparationOrderNumber: String) {
        if (null != this.factoryCarPreparationOrderNumber) return

        this.factoryCarPreparationOrderNumber = factoryCarPreparationOrderNumber
    }

    internal fun updateNextProcess(nextProcess: NextProcess?) {
        this.nextProcess = nextProcess
    }

    internal fun updateIsUsedCar(isUsedCar: Boolean) {
        this.isUsedCar = isUsedCar
    }
}

data class VehicleReturnInfoUpdateException(
    override val message: String?,
    val violationType: ViolationType,
) : RuntimeException()
