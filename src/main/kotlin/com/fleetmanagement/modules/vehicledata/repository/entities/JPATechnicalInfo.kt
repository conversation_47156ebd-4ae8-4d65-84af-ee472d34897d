/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "technical_info", schema = "vehicle")
open class JPATechnicalInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "amount_seats")
    open var amountSeats: Int? = null,
    @Column(name = "engine_capacity")
    open var engineCapacity: Float? = null,
    @Column(name = "cargo_volume")
    open var cargoVolume: Int? = null,
    @Column(name = "vehicle_width_mirrors_extended")
    open var vehicleWidthmirrorsExtended: Int? = null,
    @Column(name = "maximum_charging_power_dc")
    open var maximumChargingPowerdc: Int? = null,
    @Column(name = "gross_vehicle_weight")
    open var grossVehicleWeight: Int? = null,
    @Column(name = "curb_Weight_eu")
    open var curbWeightEu: Int? = null,
    @Column(name = "total_power_kw")
    open var totalPowerkw: Int? = null,
    @Column(name = "curb_weight_din")
    open var curbWeightdin: Int? = null,
    @Column(name = "maximum_payload")
    open var maximumPayload: Int? = null,
    @Column(name = "charging_time_ac_22_kw")
    open var chargingTimeac22Kw: Float? = null,
    @Column(name = "charging_time_ac_11_kw_0100")
    open var chargingTimeac11kw0100: Float? = null,
    @Column(name = "charging_time_ac_96_kw_0100")
    open var chargingTimeac96kw0100: Float? = null,
    @Column(name = "net_battery_capacity")
    open var netBatteryCapacity: Float? = null,
    @Column(name = "gross_battery_capacity")
    open var grossBatteryCapacity: Float? = null,
    @Column(name = "acceleration_0100_Kmh")
    open var acceleration0100Kmh: Float? = null,
    @Column(name = "acceleration_0100_kmh_launch_control")
    open var acceleration0100KmhlaunchControl: Float? = null,
    @Column(name = "top_speed")
    open var topSpeed: Int? = null,
    @Column(name = "height")
    open var height: Int? = null,
    @Column(name = "width_mirrors_folded")
    open var widthMirrorsFolded: Int? = null,
    @Column(name = "length")
    open var length: Int? = null,
    @Column(name = "acceleration_80_120_kmh")
    open var acceleration80120Kmh: Float? = null,
    @Column(name = "max_roof_load_with_porsche_roof_transport_system")
    open var maxRoofLoadwithPorscheRoofTransportSystem: Int? = null,
    @Column(name = "charging_time_dc_max_power_580")
    open var chargingTimedcMaxPower580: Float? = null,
    @Column(name = "power_kw")
    open var powerkw: Int? = null,
) {
    internal fun updatePrivileged(
        amountSeats: Optional<Int>?,
        engineCapacity: Optional<Float>?,
    ): Boolean {
        var isUpdated = false

        amountSeats
            ?.ifPresentOrElse(
                { this.amountSeats = it },
                { this.amountSeats = null },
            )?.also {
                isUpdated = true
            }
        engineCapacity
            ?.ifPresentOrElse(
                { this.engineCapacity = it },
                { this.engineCapacity = null },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }
}
