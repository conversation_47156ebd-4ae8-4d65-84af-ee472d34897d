/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import java.time.LocalDate
import java.util.*

@Entity
@Table(name = "vtstamm_info", schema = "vehicle")
class JPAVTStammInfo(
    subjectToConfidentiality: Boolean? = null,
    confidentialityClassification: String? = null,
    subjectToConfidentialityStartDate: LocalDate? = null,
    subjectToConfidentialityEndDate: LocalDate? = null,
    recordFactoryExit: Boolean? = null,
    camouflageRequired: Boolean? = null,
    internalDesignation: String? = null,
    typeOfUseVTS: String? = null,
    statusVTS: String? = null,
) {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long? = null

    @Column(name = "subject_to_confidentiality")
    var subjectToConfidentiality: Boolean = subjectToConfidentiality ?: false
        private set

    @Column(name = "confidentiality_classification")
    var confidentialityClassification: String? = confidentialityClassification
        private set

    @Column(name = "subject_to_confidentiality_start_date")
    var subjectToConfidentialityStartDate: LocalDate? = subjectToConfidentialityStartDate
        private set

    @Column(name = "subject_to_confidentiality_end_date")
    var subjectToConfidentialityEndDate: LocalDate? = subjectToConfidentialityEndDate
        private set

    @Column(name = "record_factory_exit")
    var recordFactoryExit: Boolean = recordFactoryExit ?: false
        private set

    @Column(name = "camouflage_required")
    var camouflageRequired: Boolean = camouflageRequired ?: false
        private set

    @Column(name = "internal_designation")
    var internalDesignation: String? = internalDesignation
        private set

    // verwendungsartVTS
    @Column(name = "type_of_use_vts")
    var typeOfUseVTS: String? = typeOfUseVTS
        private set

    // status VTS
    @Column(name = "status_vts")
    var statusVTS: String? = statusVTS
        private set

    /**
     * Update should only be possible using vtstamm API, NOT through DLZ regardless of privileges
     */
    internal fun updateVTStamm(
        subjectToConfidentiality: Optional<Boolean>?,
        confidentialityClassification: Optional<String>?,
        subjectToConfidentialityStartDate: Optional<LocalDate>?,
        subjectToConfidentialityEndDate: Optional<LocalDate>?,
        recordFactoryExit: Optional<Boolean>?,
        camouflageRequired: Optional<Boolean>?,
        internalDesignation: Optional<String>?,
        statusVTS: Optional<String>?,
        typeOfUseVTS: Optional<String>?,
    ): Boolean {
        var isUpdated = false
        subjectToConfidentiality
            ?.ifPresentOrElse(
                { this.subjectToConfidentiality = it },
                {
                    // treat explicit null as false
                    this.subjectToConfidentiality = false
                },
            )?.also {
                isUpdated = true
            }
        confidentialityClassification
            ?.ifPresentOrElse(
                { this.confidentialityClassification = it },
                {
                    this.confidentialityClassification = null
                },
            )?.also {
                isUpdated = true
            }
        subjectToConfidentialityStartDate
            ?.ifPresentOrElse(
                { this.subjectToConfidentialityStartDate = it },
                {
                    this.subjectToConfidentialityStartDate = null
                },
            )?.also {
                isUpdated = true
            }
        subjectToConfidentialityEndDate
            ?.ifPresentOrElse(
                { this.subjectToConfidentialityEndDate = it },
                {
                    this.subjectToConfidentialityEndDate = null
                },
            )?.also {
                isUpdated = true
            }
        recordFactoryExit
            ?.ifPresentOrElse(
                { this.recordFactoryExit = it },
                {
                    // treat explicit null as false
                    this.recordFactoryExit = false
                },
            )?.also {
                isUpdated = true
            }
        camouflageRequired
            ?.ifPresentOrElse(
                { this.camouflageRequired = it },
                {
                    // treat explicit null as false
                    this.camouflageRequired = false
                },
            )?.also {
                isUpdated = true
            }
        internalDesignation
            ?.ifPresentOrElse(
                { this.internalDesignation = it },
                {
                    this.internalDesignation = null
                },
            )?.also {
                isUpdated = true
            }
        typeOfUseVTS
            ?.ifPresentOrElse(
                { this.typeOfUseVTS = it },
                {
                    this.typeOfUseVTS = null
                },
            )?.also {
                isUpdated = true
            }
        statusVTS
            ?.ifPresentOrElse(
                { this.statusVTS = it },
                {
                    this.statusVTS = null
                },
            )?.also {
                isUpdated = true
            }
        return isUpdated
    }
}
