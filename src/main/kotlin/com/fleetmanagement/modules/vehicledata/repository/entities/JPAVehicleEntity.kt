/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.vehicledata.api.domain.*
import com.fleetmanagement.modules.vehicledata.api.events.VehicleUpdatedEvent
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateConstraintViolation
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import jakarta.persistence.*
import org.hibernate.annotations.ColumnTransformer
import org.hibernate.annotations.CreationTimestamp
import org.hibernate.annotations.UpdateTimestamp
import java.math.BigDecimal
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.ZonedDateTime
import java.util.*
import kotlin.jvm.optionals.getOrNull

@Entity
@Table(name = "vehicle", schema = "vehicle")
open class JPAVehicleEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.UUID)
    @Column(name = "id", nullable = false)
    open val id: UUID? = null,
    @Enumerated(EnumType.STRING) var source: VehicleSource = VehicleSource.UNKNOWN,
    // not named correctly, this is actually 'interne Fahrzeugbezeichnung' (internal vehicle description)
    @Column(name = "equi_id") open var equiId: String? = null,
    @Column(name = "equipment_number") open var equipmentNumber: Long? = null,
    @Column(name = "vin") open var vin: String? = null,
    @Column(name = "vguid", nullable = true) open var vguid: String? = null,
    @Column(name = "reference_id") open var referenceId: String? = null,
    @Column(name = "requires_refresh") open var requiresRefresh: Boolean = false,
    @ColumnTransformer(write = "?::jsonb")
    @Column(name = "options", columnDefinition = "jsonb")
    open var options: String? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "consumption_info_id", referencedColumnName = "id")
    open var consumptionInfo: JPAConsumptionInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "order_info_id", referencedColumnName = "id")
    open var orderInfo: JPAOrderInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "model_info_id", referencedColumnName = "id")
    open var modelInfo: JPAModelInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "production_info_id", referencedColumnName = "id")
    open var productionInfo: JPAProductionInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "country_info_id", referencedColumnName = "id")
    open var countryInfo: JPACountryInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "pmp_data_id", referencedColumnName = "id")
    open var pmpData: JPAPmpData? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "embargo_info_id", referencedColumnName = "id")
    open var embargoInfo: JPAEmbargoInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "color_info_id", referencedColumnName = "id")
    open var colorInfo: JPAColorInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "price_info_id", referencedColumnName = "id")
    open var priceInfo: JPAPriceInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "technical_info_id", referencedColumnName = "id")
    open var technicalInfo: JPATechnicalInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "fleet_info_id", referencedColumnName = "id")
    open var fleetInfo: JPAFleetInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "delivery_info_id", referencedColumnName = "id")
    open var deliveryInfo: JPADeliveryInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "return_info_id", referencedColumnName = "id")
    open var returnInfo: JPAReturnInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "wltp_info_id", referencedColumnName = "id")
    open var wltpInfo: JPAWltpInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "tire_set_change_id", referencedColumnName = "id")
    open var tireSetChange: JpaTireSetChangeEntity? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "evaluation_info_id", referencedColumnName = "id")
    open var evaluationInfo: JPAEvaluationInfo? = null,
    @OneToOne(cascade = [CascadeType.PERSIST, CascadeType.MERGE, CascadeType.REMOVE])
    @JoinColumn(name = "vtstamm_info_id", referencedColumnName = "id")
    open var vtstammInfo: JPAVTStammInfo? = null,
    // new vehicle will always have SR tires
    @Enumerated(EnumType.STRING)
    @Column(name = "current_tires")
    open var currentTires: TireSet? = TireSet.SR,
    @Column(name = "external_lease_start") open var externalLeaseStart: ZonedDateTime? = null,
    @Column(name = "external_lease_end") open var externalLeaseEnd: ZonedDateTime? = null,
    @Column(name = "external_lease_rate") open var externalLeaseRate: Float? = null,
    @Column(name = "external_lease_lessee") open var externalLeaseLessee: String? = null,
    @Column(name = "number_of_damages")
    open var numberOfDamages: Int = 0,
    @Column(name = "number_of_open_damages")
    open var numberOfOpenDamages: Int = 0,
    @Column(name = "repairfix_car_id")
    open var repairfixCarId: String? = null,
    @Column(name = "last_updated_number_of_damages")
    open var lastUpdatedNumberOfDamages: OffsetDateTime? = null,
    @Column(name = "created_at") @CreationTimestamp open var createdAt: ZonedDateTime? = null,
    @Column(name = "last_updated_at") @UpdateTimestamp open var lastUpdatedAt: ZonedDateTime? = null,
    // date of next TÜV (HU/AU) appointment
    @Column(name = "tuev_appointment")
    open var tuevAppointment: LocalDate? = null,
) {
    @Column(name = "is_activated_for_accounting", nullable = false)
    var isActivatedForAccounting: Boolean
        private set

    init {
        isActivatedForAccounting = calculateIsActivatedForAccounting()
    }

    @Column(name = "financial_asset_type")
    @Enumerated(EnumType.STRING)
    open var financialAssetType: FinancialAssetType? = null

    // Vehicle status is set to Error/Unknown by default and will be recalculated by the respective job or on update
    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    open var status: VehicleStatus = VehicleStatus.E000

    @Version
    @Column(name = "version", nullable = false)
    var version: Int = 0

    internal fun setSource(source: VehicleSource) {
        this.source = source
    }

    internal fun setReferenceId(referenceId: String) {
        this.referenceId = referenceId
    }

    internal fun updateDamages(
        repairfixCarId: String,
        totalNumberOfDamages: Int,
        numberOfOpenDamages: Int,
        lastUpdatedNumberOfDamages: OffsetDateTime,
    ) {
        this.repairfixCarId = repairfixCarId
        this.numberOfDamages = totalNumberOfDamages
        this.numberOfOpenDamages = numberOfOpenDamages
        this.lastUpdatedNumberOfDamages = lastUpdatedNumberOfDamages
    }

    internal fun updateEquiData(
        equiId: Optional<String>? = null,
        equiNumber: Optional<Long>? = null,
    ) {
        equiNumber?.let { this.equipmentNumber = equiNumber.getOrNull() }
        equiId?.let { this.equiId = equiId.getOrNull() }
    }

    internal fun initialize(
        scrapVehicle: Boolean?,
        preproductionVehicle: Boolean?,
        blockedForSale: Boolean?,
        manufacturer: String?,
        vehicleType: VehicleType?,
        tireSet: TireSet?,
        blockedForSaleSource: String,
    ) {
        if (null == this.orderInfo) {
            this.orderInfo = JPAOrderInfo()
        }
        this.orderInfo?.update(
            preproductionVehicle = Optional.ofNullable(preproductionVehicle),
            blockedForSale = Optional.ofNullable(blockedForSale),
            blockedForSaleSource = blockedForSaleSource,
        )

        if (null == this.modelInfo) {
            this.modelInfo = JPAModelInfo()
        }
        this.modelInfo?.update(
            manufacturer = Optional.ofNullable(manufacturer),
            vehicleType = Optional.ofNullable(vehicleType),
        )

        if (null == this.fleetInfo) {
            this.fleetInfo = JPAFleetInfo()
        }
        this.fleetInfo!!.update(
            scrapVehicle = Optional.ofNullable(scrapVehicle),
        )

        if (null != tireSet) {
            // do not update currentTireSet if consignee data did not provide something (as
            // production default will always be SR)
            this.currentTires = tireSet
        }
    }

    internal fun updatePrivileged(
        hasActiveVehicleTransfer: Boolean,
        referenceId: Optional<String>?,
        purchaseOrderDate: Optional<Date>?,
        requestedDeliveryDate: Optional<Date>?,
        deliveryType: Optional<String>?,
        primaryStatus: Optional<String>?,
        preproductionVehicle: Optional<Boolean>?,
        blockedForSale: Optional<Boolean>?,
        scrapVehicle: Optional<Boolean>?,
        primaryFuelType: Optional<FuelType>?,
        secondaryFuelType: Optional<FuelType>?,
        currentTires: Optional<TireSet>?,
        externalLeaseStart: Optional<ZonedDateTime>?,
        externalLeaseEnd: Optional<ZonedDateTime>?,
        externalLeaseLessee: Optional<String>?,
        externalLeaseRate: Optional<Float>?,
        amountSeats: Optional<Int>?,
        engineCapacity: Optional<Float>?,
        netPriceWithExtras: Optional<BigDecimal>?,
        soldDate: Optional<OffsetDateTime>?,
        scrappedDate: Optional<OffsetDateTime>?,
        stolenDate: Optional<OffsetDateTime>?,
        soldCupCarDate: Optional<OffsetDateTime>?,
        approvedForScrappingDate: Optional<OffsetDateTime>?,
        scrappedVehicleOfferedDate: Optional<OffsetDateTime>?,
        vehicleSentToSalesDate: Optional<OffsetDateTime>?,
        costEstimationOrderedDate: Optional<OffsetDateTime>?,
        isResidualValueMarket: Optional<Boolean>?,
        profitabilityAuditDate: Optional<OffsetDateTime>?,
        comment: Optional<String>?,
        preparationDoneDate: Optional<OffsetDateTime>?,
        isPreparationNecessary: Optional<Boolean>?,
        nextProcess: Optional<NextProcess>?,
        tireSetChangeCompletedDate: Optional<OffsetDateTime>?,
        tireSetChangeOrderedDate: Optional<OffsetDateTime>?,
        tireSetChangeComment: Optional<String>?,
        appraisalNetPrice: Optional<BigDecimal>?,
        pcComplaintCheckComment: Optional<String>?,
        vehicleEvaluationComment: Optional<String>?,
        blockedForSaleSource: String,
        isClassic: Optional<Boolean>?,
        keyReturned: Optional<OffsetDateTime>?,
        tuevAppointment: Optional<LocalDate>?,
        modelDescription: Optional<String>?,
        orderType: Optional<String>?,
        countryInfo: Optional<String>?,
        exteriorColor: Optional<String>?,
        interiorColor: Optional<String>?,
        productionEndDate: Optional<OffsetDateTime>?,
        raceCar: Optional<Boolean>?,
    ): VehicleUpdatedEvent? {
        var isUpdated = false

        // vehicle data
        referenceId?.ifPresentOrElse({ this.referenceId = it }, { this.referenceId = null })?.also {
            isUpdated = true
        }
        if (null == this.consumptionInfo) {
            this.consumptionInfo = JPAConsumptionInfo()
        }
        this.consumptionInfo
            ?.updatePrivileged(
                primaryFuelType = primaryFuelType,
                secondaryFuelType = secondaryFuelType,
            )?.also { if (it) isUpdated = true }
        currentTires
            ?.ifPresentOrElse({ this.currentTires = it }, { this.currentTires = null })
            ?.also { isUpdated = true }
        externalLeaseStart
            ?.ifPresentOrElse({ this.externalLeaseStart = it }, { this.externalLeaseStart = null })
            ?.also { isUpdated = true }
        externalLeaseEnd
            ?.ifPresentOrElse({ this.externalLeaseEnd = it }, { this.externalLeaseEnd = null })
            ?.also { isUpdated = true }
        externalLeaseLessee
            ?.ifPresentOrElse(
                { this.externalLeaseLessee = it },
                { this.externalLeaseLessee = null },
            )?.also { isUpdated = true }
        externalLeaseRate
            ?.ifPresentOrElse({ this.externalLeaseRate = it }, { this.externalLeaseRate = null })
            ?.also { isUpdated = true }
        tuevAppointment
            ?.ifPresentOrElse({ this.tuevAppointment = it }, { this.tuevAppointment = null })
            ?.also { isUpdated = true }

        // order info
        if (null == this.orderInfo) {
            this.orderInfo = JPAOrderInfo()
        }
        this.orderInfo
            ?.updatePrivileged(
                purchaseOrderDate = purchaseOrderDate,
                requestedDeliveryDate = requestedDeliveryDate,
                deliveryType = deliveryType,
                primaryStatus = primaryStatus,
                preproductionVehicle = preproductionVehicle,
                blockedForSale = blockedForSale,
                blockedForSaleSource = blockedForSaleSource,
            )?.also { if (it) isUpdated = true }

        // country info
        if (null == this.countryInfo) {
            this.countryInfo = JPACountryInfo()
        }

        this.countryInfo
            ?.updatePrivileged(
                countryInfoCNRValue = countryInfo,
                countryInfoBNRValue = null,
                countryInfoCNRValueCountryDescription = null,
            )?.let { countryInfoUpdated ->
                if (countryInfoUpdated) isUpdated = true
            }

        // production info
        if (null == this.productionInfo) {
            this.productionInfo = JPAProductionInfo()
        }

        this.productionInfo
            ?.updatePrivileged(
                productionEndDate = productionEndDate,
            )?.let { countryInfoUpdated ->
                if (countryInfoUpdated) isUpdated = true
            }

        // technical info
        if (null == this.technicalInfo) {
            this.technicalInfo = JPATechnicalInfo()
        }
        this.technicalInfo
            ?.updatePrivileged(
                amountSeats = amountSeats,
                engineCapacity = engineCapacity,
            )?.also { if (it) isUpdated = true }

        // price info
        if (null == this.priceInfo) {
            this.priceInfo = JPAPriceInfo()
        }
        this.priceInfo
            ?.updatePrivileged(netPriceWithExtras = netPriceWithExtras)
            ?.also { if (it) isUpdated = true }

        // fleet info
        if (null == this.fleetInfo) {
            this.fleetInfo = JPAFleetInfo()
        }
        this.fleetInfo
            ?.update(
                soldDate = soldDate,
                scrappedDate = scrappedDate,
                stolenDate = stolenDate,
                soldCupCarDate = soldCupCarDate,
                approvedForScrappingDate = approvedForScrappingDate,
                scrappedVehicleOfferedDate = scrappedVehicleOfferedDate,
                vehicleSentToSalesDate = vehicleSentToSalesDate,
                costEstimationOrderedDate = costEstimationOrderedDate,
                isResidualValueMarket = isResidualValueMarket,
                profitabilityAuditDate = profitabilityAuditDate,
                scrapVehicle = scrapVehicle,
                comment = comment,
                isClassic = isClassic,
                blockedForSale = true == this.orderInfo?.blockedForSale,
                raceCar = raceCar,
                vehicleId = requireNotNull(this.id),
                hasActiveVehicleTransfer = hasActiveVehicleTransfer,
            )?.also { if (it) isUpdated = true }

        // delivery info
        if (null == this.deliveryInfo) {
            this.deliveryInfo = JPADeliveryInfo()
        }
        this.deliveryInfo
            ?.update(
                preparationDoneDate = preparationDoneDate,
                isPreparationNecessary = isPreparationNecessary,
                scrapVehicle = true == this.fleetInfo?.scrapVehicle,
                blockedForSale = true == this.orderInfo?.blockedForSale,
                vehicleId = requireNotNull(this.id),
                hasActiveVehicleTransfer = hasActiveVehicleTransfer,
            )?.also { if (it) isUpdated = true }

        // return info
        if (null == this.returnInfo) {
            this.returnInfo = JPAReturnInfo()
        }
        try {
            this.returnInfo
                ?.updatePrivileged(
                    nextProcess = nextProcess,
                    scrapVehicle = true == this.fleetInfo?.scrapVehicle,
                    blockedForSale = true == this.orderInfo?.blockedForSale,
                    keyReturned = keyReturned,
                )?.also { if (it) isUpdated = true }
        } catch (exception: VehicleReturnInfoUpdateException) {
            throw VehicleUpdateException(
                cause = exception,
                message = exception.message,
                constraintViolation =
                    VehicleUpdateConstraintViolation(
                        vehicleId = this.id,
                        type = exception.violationType,
                    ),
            )
        }

        // color info
        if (null == this.colorInfo) {
            this.colorInfo = JPAColorInfo()
        }

        this.colorInfo
            ?.updatePrivileged(
                colorExterior = exteriorColor,
                colorInterior = interiorColor,
                colorExteriorDescription = null,
                colorInteriorDescription = null,
            )?.let { colorInfoUpdated ->
                if (colorInfoUpdated) isUpdated = true
            }

        // evaluation info
        if (null == this.evaluationInfo) {
            this.evaluationInfo = JPAEvaluationInfo()
        }
        this.evaluationInfo
            ?.updatePrivileged(
                appraisalNetPrice = appraisalNetPrice,
                vehicleEvaluationComment = vehicleEvaluationComment,
                pcComplaintCheckComment = pcComplaintCheckComment,
            )?.also { if (it) isUpdated = true }

        // tire set change
        if (null == this.tireSetChange) {
            this.tireSetChange = JpaTireSetChangeEntity()
        }
        this.tireSetChange
            ?.updatePrivileged(
                completedDate = tireSetChangeCompletedDate,
                orderedDate = tireSetChangeOrderedDate,
                comment = tireSetChangeComment,
            )?.also { if (it) isUpdated = true }

        // Update model information
        if (null == this.modelInfo) {
            this.modelInfo = JPAModelInfo()
        }

        this.modelInfo
            ?.updatePrivileged(
                rangeDevelopment = null,
                modelDescriptionDevelopment = null,
                orderType = orderType,
                modelDescription = modelDescription,
                productCode = null,
                productId = null,
            )?.let { modelInfoUpdated ->
                if (modelInfoUpdated) isUpdated = true
            }

        return if (isUpdated) VehicleUpdatedEvent(vehicleId = requireNotNull(this.id)) else null
    }

    /**
     * A dedicated update function for either VTSTamm properties or properties that are also updatable by vtstamm.
     * Do NOT call this from DLZ update functions!
     */
    @Suppress("SpellCheckingInspection")
    internal fun updateVTStamm(
        subjectToConfidentiality: Optional<Boolean>?,
        confidentialityClassification: Optional<String>?,
        subjectToConfidentialityStartDate: Optional<LocalDate>?,
        subjectToConfidentialityEndDate: Optional<LocalDate>?,
        recordFactoryExit: Optional<Boolean>?,
        camouflageRequired: Optional<Boolean>?,
        rangeDevelopment: Optional<String>?,
        modelDescriptionDevelopment: Optional<String>?,
        tuevAppointment: Optional<LocalDate>?,
        internalVehicleDescription: Optional<String>?,
        equipmentNumber: Long,
        internalDesignation: Optional<String>?,
        typeOfUseVTS: Optional<String>?,
        statusVTS: Optional<String>?,
    ): VehicleUpdatedEvent? {
        var isUpdated = false

        // Update TÜV appointment
        tuevAppointment?.let {
            this.tuevAppointment = it.orElse(null)
            isUpdated = true
        }
        internalVehicleDescription?.let {
            this.equiId = it.orElse(null)
            isUpdated = true
        }

        if (equipmentNumber != this.equipmentNumber) {
            this.equipmentNumber = equipmentNumber
            isUpdated = true
        }

        // Update model information
        if (null == this.modelInfo) {
            this.modelInfo = JPAModelInfo()
        }

        this.modelInfo
            ?.updatePrivileged(
                rangeDevelopment = rangeDevelopment,
                modelDescriptionDevelopment = modelDescriptionDevelopment,
                orderType = null,
                modelDescription = null,
                productCode = null,
                productId = null,
            )?.let { modelInfoUpdated ->
                if (modelInfoUpdated) isUpdated = true
            }

        // Update confidentiality information
        if (null == this.vtstammInfo) {
            this.vtstammInfo = JPAVTStammInfo()
        }

        this.vtstammInfo
            ?.updateVTStamm(
                subjectToConfidentiality = subjectToConfidentiality,
                confidentialityClassification = confidentialityClassification,
                subjectToConfidentialityStartDate = subjectToConfidentialityStartDate,
                subjectToConfidentialityEndDate = subjectToConfidentialityEndDate,
                recordFactoryExit = recordFactoryExit,
                camouflageRequired = camouflageRequired,
                internalDesignation = internalDesignation,
                typeOfUseVTS = typeOfUseVTS,
                statusVTS = statusVTS,
            )?.let { confidentialityUpdated ->
                if (confidentialityUpdated) isUpdated = true
            }

        // Return event only if something was updated
        return if (isUpdated) {
            VehicleUpdatedEvent(vehicleId = requireNotNull(this.id))
        } else {
            null
        }
    }

    internal fun updateStatus(vehicleStatus: VehicleStatus): Boolean {
        if (this.status != vehicleStatus) {
            // only update status if there is an actual change
            this.status = vehicleStatus
            this.isActivatedForAccounting = calculateIsActivatedForAccounting()
            return true
        }
        return false
    }

    internal fun updateCostEstimationOrderedDate(costEstimationOrderedDate: OffsetDateTime) {
        if (null == this.fleetInfo) {
            this.fleetInfo = JPAFleetInfo()
        }
        this.fleetInfo?.updateCostEstimationOrderedDate(
            costEstimationOrderedDate = costEstimationOrderedDate,
            blockedForSale = true == this.orderInfo?.blockedForSale,
            vehicleId = requireNotNull(this.id),
        )
    }

    private fun calculateIsActivatedForAccounting(): Boolean = status !in inactiveStatuses && financialAssetType in validAssetTypes

    /**
     * Updates factoryCarPreparationOrderNumber.
     * This is only done, if there is currently no orderNumber set. Existing values are not overridden.
     */
    internal fun updateFactoryCarPreparationOrderNumber(factoryCarPreparationOrderNumber: String) {
        if (null == this.returnInfo) {
            this.returnInfo = JPAReturnInfo()
        }
        this.returnInfo?.updateFactoryCarPreparationOrderNumber(factoryCarPreparationOrderNumber = factoryCarPreparationOrderNumber)
    }

    internal fun updateProductionEndDate(productionEndDate: OffsetDateTime) {
        if (null == this.productionInfo) {
            this.productionInfo = JPAProductionInfo()
        }
        this.productionInfo?.updatePrivileged(productionEndDate = Optional.of(productionEndDate))
    }

    internal fun updateNextProcess(nextProcess: NextProcess?) {
        if (null == this.returnInfo) {
            this.returnInfo = JPAReturnInfo()
        }
        this.returnInfo?.updateNextProcess(nextProcess)
    }

    internal fun updateScrappedOfferDate(scrappedVehicleOfferedDate: OffsetDateTime?) {
        this.fleetInfo?.updateScrappedVehicleOfferedDate(
            scrappedVehicleOfferedDate,
            blockedForSale = true == this.orderInfo?.blockedForSale,
            vehicleId = requireNotNull(this.id),
        )
    }

    internal fun updateIsUsedCar(isUsedCar: Boolean) {
        this.returnInfo?.updateIsUsedCar(isUsedCar)
    }

    internal fun updateSoldDate(
        soldDate: OffsetDateTime?,
        hasActiveVehicleTransfer: Boolean,
    ) {
        this.fleetInfo?.updateSoldDate(
            soldDate = soldDate,
            hasActiveVehicleTransfer = hasActiveVehicleTransfer,
            blockedForSale = true == this.orderInfo?.blockedForSale,
            vehicleId = requireNotNull(this.id),
        )
    }

    fun updateBlockedForSale(
        blockedForSale: Boolean,
        source: String,
    ) {
        if (null == this.orderInfo) {
            this.orderInfo = JPAOrderInfo()
        }
        this.orderInfo?.updateBlockedForSale(
            blockedForSale = blockedForSale,
            blockedForSaleSource = source,
        )
    }

    internal fun updateFactoryPriceAndPiaEvent(
        factoryNetPriceEUR: BigDecimal?,
        piaEvent: String,
    ) {
        if (null == this.priceInfo) {
            this.priceInfo = JPAPriceInfo()
        }
        this.priceInfo?.updateFactoryPriceAndPiaEvent(factoryNetPriceEUR = factoryNetPriceEUR, piaEvent = piaEvent)
    }

    internal fun updateFactoryPrice(factoryNetPriceEUR: BigDecimal?) {
        if (null == this.priceInfo) {
            this.priceInfo = JPAPriceInfo()
        }
        this.priceInfo?.updateFactoryNetPrice(factoryNetPriceEUR = factoryNetPriceEUR)
    }

    internal fun updateNetPriceWithExtras(netPriceWithExtras: BigDecimal?) {
        if (null == this.priceInfo) {
            this.priceInfo = JPAPriceInfo()
        }
        this.priceInfo?.updateNetPriceWithExtras(netPriceWithExtras = netPriceWithExtras)
    }

    internal fun updateFinancialAssetType(financialAssetType: FinancialAssetType) {
        if (this.financialAssetType != financialAssetType) {
            this.financialAssetType = financialAssetType
            this.isActivatedForAccounting = calculateIsActivatedForAccounting()
        }
    }

    fun updateNewVehicleInvoiceDate(newVehicleInvoiceDate: LocalDate) {
        if (null == this.priceInfo) {
            this.priceInfo = JPAPriceInfo()
        }
        this.priceInfo?.updateNewVehicleInvoiceDate(newVehicleInvoiceDate = newVehicleInvoiceDate)
    }

    internal fun resetStolenDate() {
        if (null == this.fleetInfo) {
            this.fleetInfo = JPAFleetInfo()
        }
        this.fleetInfo?.resetStolenDate()
    }

    internal fun resetApprovedForScrappingDate() {
        if (null == this.fleetInfo) {
            this.fleetInfo = JPAFleetInfo()
        }
        this.fleetInfo?.resetApprovedForScrappingDate()
    }

    internal fun resetCostEstimationOrderedDate() {
        if (null == this.fleetInfo) {
            this.fleetInfo = JPAFleetInfo()
        }
        this.fleetInfo?.resetCostEstimationOrderedDate()
    }

    internal fun resetIsResidualValueMarket() {
        if (null == this.fleetInfo) {
            this.fleetInfo = JPAFleetInfo()
        }
        this.fleetInfo?.resetIsResidualValueMarket()
    }

    internal fun resetProfitabilityAuditDate() {
        if (null == this.fleetInfo) {
            this.fleetInfo = JPAFleetInfo()
        }
        this.fleetInfo?.resetProfitabilityAuditDate()
    }

    internal fun resetPreparationDoneDate() {
        if (null == this.deliveryInfo) {
            this.deliveryInfo = JPADeliveryInfo()
        }
        this.deliveryInfo?.resetPreparationDoneDate()
    }

    companion object {
        val inactiveStatuses =
            setOf(
                VehicleStatus.SX97,
                VehicleStatus.SX98,
                VehicleStatus.SX99,
                VehicleStatus.SX90,
                VehicleStatus.SX100,
            )
        val validAssetTypes = listOf(FinancialAssetType.AV, FinancialAssetType.UV)
    }
}

enum class VehiclePropertyForValidation(
    val fieldName: String,
) {
    PROFITABILITY_AUDIT_DATE("profitabilityAuditDate"),
    COST_ESTIMATION_ORDERED_DATE("costEstimationOrderedDate"),
    VEHICLE_SENT_TO_SALES_DATE("vehicleSentToSalesDate"),
    SCRAPPED_VEHICLE_OFFERED_DATE("scrappedVehicleOfferedDate"),
    APPROVED_FOR_SCRAPPING_DATE("approvedForScrappingDate"),
    SOLD_DATE("soldDate"),
    SCRAPPED_DATE("scrappedDate"),
    IS_PREPARATION_NECESSARY("isPreparationNecessary"),
    PREPARATION_DONE_DATE("preparationDoneDate"),
    SOLD_CUP_CAR_DATE("soldCupCarDate"),
    STOLEN_DATE("stolenDate"),
    ORDERED_DATE("orderedDate"),
    COMPLETED_DATE("completedDate"),
    KEY_RETURNED("keyReturned"),
}

enum class VehicleStatus(
    val description: String,
) {
    SX100("Fahrzeugbestellung storniert"),
    SX99("Fzg. verschrottet"),
    SX98("Fzg. verkauft"),
    SX97("Verkauf auserhalb FMS"),
    SX90("Fzg. gestohlen"),
    S680("für Verschrottung freigegeben"),
    S650("zur Wiederverwendung angeboten (Schrottfahrzeug)"),
    S600("Schrottfzg. zurückgenommen"),
    S420("Aufbereitung abgeschlossen"),
    S410("Aufbereitung notwendig"),
    S400("Fzg. an Verkauf übergeben"),
    S360("KVA beauftragt"),
    S350("Fahrzeug für Verkauf bestimmt"),
    S340("Restwert Börse"),
    S330("Wirtschaflichkeitsprüfung"),
    S320("Wirtschaftlichkeitsprüfung in Vorbereitung"),
    S310("Weiterverwendung prüfen"),
    S300("Fzg. zurückgenommen"),
    S290("Fahrzeug für Rückgabe eingesteuert (Pool Fzg.)"),
    S200("Fzg. ausgeliefert"),
    S140("Auslieferungsfähig"),
    S130("Aktion/Schaden nach PDI"),
    S120("Fahrzeugauslieferung geplant"),
    S100("Terminierung möglich"),
    S097("Fahrzeugverantwortlicher nicht zugeordnet"),
    S096("Fahrzeugverantwortlicher zugeordnet"),
    S095("Neufahrzeuge ohne Verwendung (VPV2)"),
    S094("Gebraucht-Fahrzeugüberlassung geplant"),
    S093("Gebrauchtfzg. Verfügbar"),
    E000("Fehler"),
}
