/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import java.time.ZonedDateTime

@Entity
@Table(name = "vehicle_option_tags", schema = "vehicle")
open class JPAVehicleOptionTag(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long? = null,
    @Column(name = "option_id", nullable = false)
    val optionId: String,
    optionDescription: String? = null,
    @Column(name = "created_at")
    @CreationTimestamp
    val createdAt: ZonedDateTime? = null,
) {
    @Column(name = "non_customer_adequate")
    var nonCustomerAdequate: Boolean = false
        private set

    @Column(name = "option_description")
    var optionDescription: String? = optionDescription
        private set

    internal fun updateNonCustomerAdequate(nonCustomerAdequate: Boolean) {
        this.nonCustomerAdequate = nonCustomerAdequate
    }

    internal fun updateOptionDescription(optionDescription: String?) {
        this.optionDescription = optionDescription
    }
}
