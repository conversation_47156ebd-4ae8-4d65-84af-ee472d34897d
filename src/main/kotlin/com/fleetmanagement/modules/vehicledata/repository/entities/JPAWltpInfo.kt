/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.*
import java.util.*

@Entity
@Table(name = "wltp_info", schema = "vehicle")
open class JPAWltpInfo(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "vehicle_weight")
    open var vehicleWeight: Int? = null,
    @Column(name = "electric_range")
    open var electricRange: Int? = null,
    @Column(name = "co2_combined")
    open var co2Combined: Int? = null,
    @Column(name = "electric_range_city")
    open var electricRangeCity: Int? = null,
) {
    internal fun updatePrivileged(
        vehicleWeight: Optional<Int>?,
        electricRange: Optional<Int>?,
        co2Combined: Optional<Int>?,
        electricRangeCity: Optional<Int>?,
    ): Boolean {
        var isUpdated = false
        vehicleWeight
            ?.ifPresentOrElse(
                { this.vehicleWeight = it },
                { this.vehicleWeight = null },
            )?.also {
                isUpdated = true
            }
        electricRange
            ?.ifPresentOrElse(
                { this.electricRange = it },
                { this.electricRange = null },
            )?.also {
                isUpdated = true
            }
        co2Combined
            ?.ifPresentOrElse(
                { this.co2Combined = it },
                { this.co2Combined = null },
            )?.also {
                isUpdated = true
            }
        electricRangeCity
            ?.ifPresentOrElse(
                { this.electricRangeCity = it },
                { this.electricRangeCity = null },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }
}
