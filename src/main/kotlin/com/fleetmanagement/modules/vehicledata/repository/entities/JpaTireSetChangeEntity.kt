/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehicledata.repository.entities

import jakarta.persistence.Column
import jakarta.persistence.Entity
import jakarta.persistence.GeneratedValue
import jakarta.persistence.GenerationType
import jakarta.persistence.Id
import jakarta.persistence.Table
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "tire_set_change", schema = "vehicle")
open class JpaTireSetChangeEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    open var id: Long? = null,
    @Column(name = "completed_date")
    open var completedDate: OffsetDateTime? = null,
    @Column(name = "ordered_date")
    open var orderedDate: OffsetDateTime? = null,
    @Column(name = "comment")
    open var comment: String? = null,
) {
    internal fun updatePrivileged(
        completedDate: Optional<OffsetDateTime>?,
        orderedDate: Optional<OffsetDateTime>?,
        comment: Optional<String>?,
    ): Boolean {
        var isUpdated = false

        completedDate
            ?.ifPresentOrElse(
                {
                    validateNotFutureDatePrecondition(
                        property = VehiclePropertyForValidation.COMPLETED_DATE,
                        date = it,
                    )
                    this.completedDate = it
                },
                { this.completedDate = null },
            )?.also {
                isUpdated = true
            }

        orderedDate
            ?.ifPresentOrElse(
                {
                    validateNotFutureDatePrecondition(
                        property = VehiclePropertyForValidation.ORDERED_DATE,
                        date = it,
                    )
                    this.orderedDate = it
                },
                { this.orderedDate = null },
            )?.also {
                isUpdated = true
            }

        comment
            ?.ifPresentOrElse(
                { this.comment = it },
                { this.comment = null },
            )?.also {
                isUpdated = true
            }

        return isUpdated
    }
}
