package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateConstraintViolation
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import java.time.LocalDate
import java.time.OffsetDateTime
import java.util.UUID

fun validateNotFutureDatePrecondition(
    property: VehiclePropertyForValidation,
    date: OffsetDateTime?,
    vehicleId: UUID? = null,
) {
    if (date == null) return
    if (date.toLocalDate().isAfter(LocalDate.now())) {
        val message =
            buildString {
                if (vehicleId != null) {
                    append("Vehicle with id [$vehicleId] ")
                } else {
                    append("Vehicle ")
                }
                append("has a date for [${property.fieldName}] not allowed in future: $date.")
            }
        throw VehicleUpdateException(
            message = message,
            constraintViolation =
                VehicleUpdateConstraintViolation(
                    vehicleId = vehicleId,
                    type = ViolationType.FUTURE_DATE_NOT_ALLOWED,
                    propertyName = property.fieldName,
                ),
        )
    }
}

fun validateBlockedForSaleAndScrappedVehiclePreconditions(
    property: VehiclePropertyForValidation,
    blockedForSale: Boolean,
    scrapVehicle: Boolean,
    vehicleId: UUID,
) {
    if (blockedForSale && saleRuleProperties.contains(property)) {
        throw VehicleUpdateException(
            message =
                "Error while setting ${property.name}. The data field ${property.name} cannot be maintained, because the vehicle is blocked for sale.",
            constraintViolation =
                VehicleUpdateConstraintViolation(
                    vehicleId,
                    ViolationType.VEHICLE_BLOCKED_FOR_SALE,
                ),
        )
    }
    if (scrapVehicle && (saleRuleProperties + profitabilityAuditRuleProperties).contains(property)) {
        throw VehicleUpdateException(
            message =
                "Error while setting ${property.name}. The data field ${property.name} cannot be maintained," +
                    " because the vehicle is marked for scrapping.",
            constraintViolation =
                VehicleUpdateConstraintViolation(
                    vehicleId,
                    ViolationType.VEHICLE_MARKED_AS_SCRAP,
                ),
        )
    }
    if (!blockedForSale && (scrappingRuleProperties + profitabilityAuditRuleProperties).contains(property)) {
        throw VehicleUpdateException(
            message =
                "Error while setting ${property.name}. The data field ${property.name} cannot be maintained, " +
                    "because the vehicle is not blocked for sale.",
            constraintViolation =
                VehicleUpdateConstraintViolation(
                    vehicleId,
                    ViolationType.VEHICLE_NOT_BLOCKED_FOR_SALE,
                ),
        )
    }
    if (!scrapVehicle && scrappingRuleProperties.contains(property)) {
        throw VehicleUpdateException(
            message =
                "Error while setting ${property.name}. The data field ${property.name} cannot be maintained," +
                    " because the vehicle is not marked for scrapping.",
            constraintViolation =
                VehicleUpdateConstraintViolation(
                    vehicleId,
                    ViolationType.VEHICLE_NOT_MARKED_AS_SCRAP,
                ),
        )
    }
}

val saleRuleProperties =
    listOf(
        VehiclePropertyForValidation.COST_ESTIMATION_ORDERED_DATE,
        VehiclePropertyForValidation.VEHICLE_SENT_TO_SALES_DATE,
        VehiclePropertyForValidation.IS_PREPARATION_NECESSARY,
        VehiclePropertyForValidation.PREPARATION_DONE_DATE,
        VehiclePropertyForValidation.SOLD_DATE,
    )
val profitabilityAuditRuleProperties =
    listOf(
        VehiclePropertyForValidation.PROFITABILITY_AUDIT_DATE,
    )
val scrappingRuleProperties =
    listOf(
        VehiclePropertyForValidation.SCRAPPED_VEHICLE_OFFERED_DATE,
        VehiclePropertyForValidation.APPROVED_FOR_SCRAPPING_DATE,
        VehiclePropertyForValidation.SCRAPPED_DATE,
    )
