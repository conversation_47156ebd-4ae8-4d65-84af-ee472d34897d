/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicledata.repository.mappers

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.vehicledata.api.domain.Department
import com.fleetmanagement.modules.vehicledata.api.domain.LeasingType
import com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto.PVHVehicle
import com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto.PVHVehicleConsumption
import com.fleetmanagement.modules.vehicledata.integrations.pvh.api.dto.PVHVehicleDataOrderTypeGroupDTO
import com.fleetmanagement.modules.vehicledata.repository.entities.JPATechnicalInfo
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.MappingTarget
import org.mapstruct.Mappings
import org.mapstruct.Named
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface PVHVehicleMapper {
    companion object {
        val INSTANCE: PVHVehicleMapper = Mappers.getMapper(PVHVehicleMapper::class.java)
        private val objectMapper = jacksonObjectMapper()
        private const val MANUFACTURER_PORSCHE = "Porsche"

        @JvmStatic
        @Named("mapReferenceIdToDepartment")
        fun mapReferenceIdToDepartment(referenceId: String?): String? = referenceId?.let { Department(it).asCommaSeparatedList() }

        @JvmStatic
        @Named("mapReferenceIdToLeasingType")
        fun mapReferenceIdToLeasingType(referenceId: String?): String? = referenceId?.let { LeasingType(it).asCommaSeparatedList() }

        @Named("objectToString")
        @JvmStatic
        fun jsonNodeToString(options: JsonNode?): String? = options?.let { objectMapper.writeValueAsString(it) }

        @Named("mapTechnicalData")
        @JvmStatic
        fun mapTechnicalData(
            source: PVHVehicle,
            @MappingTarget technicalInfo: JPATechnicalInfo,
        ) {
            if (source.countryInfo?.CNR?.value != "C00") return
            val dataItems = source.technicalData?.values?.flatten()

            fun findTechnicalAttribute(uniqueAttributeId: Int): String? =
                dataItems
                    ?.firstOrNull { it.uniqueAttributeId == uniqueAttributeId.toString() }
                    ?.technicalData
                    ?.valueSingle
                    ?.description

            technicalInfo.apply {
                this.cargoVolume = findTechnicalAttribute(109)?.toInt()
                this.length = findTechnicalAttribute(91)?.toInt()
                this.widthMirrorsFolded = findTechnicalAttribute(92)?.toInt()
                this.height = findTechnicalAttribute(93)?.toInt()
                this.topSpeed = findTechnicalAttribute(56)?.toInt()
                this.acceleration0100Kmh = findTechnicalAttribute(57)?.toFloat()
                this.powerkw = findTechnicalAttribute(19)?.toInt()
                this.maximumPayload = findTechnicalAttribute(101)?.toInt()
                this.curbWeightdin = findTechnicalAttribute(102)?.toInt()
                this.curbWeightEu = findTechnicalAttribute(103)?.toInt()
                this.grossVehicleWeight = findTechnicalAttribute(104)?.toInt()
                this.engineCapacity = findTechnicalAttribute(6)?.toFloat()
                this.vehicleWidthmirrorsExtended = findTechnicalAttribute(107)?.toInt()
                this.maximumChargingPowerdc = findTechnicalAttribute(205)?.toInt()
                this.chargingTimeac22Kw = findTechnicalAttribute(189)?.toFloat()
                this.chargingTimeac11kw0100 = findTechnicalAttribute(187)?.toFloat()
                this.chargingTimeac96kw0100 = findTechnicalAttribute(186)?.toFloat()
                this.netBatteryCapacity = findTechnicalAttribute(185)?.toFloat()
                this.grossBatteryCapacity = findTechnicalAttribute(184)?.toFloat()
                this.acceleration0100KmhlaunchControl = findTechnicalAttribute(180)?.toFloat()
                this.acceleration80120Kmh = findTechnicalAttribute(178)?.toFloat()
                this.maxRoofLoadwithPorscheRoofTransportSystem = findTechnicalAttribute(173)?.toInt()
                this.chargingTimedcMaxPower580 = findTechnicalAttribute(193)?.toFloat()
                this.totalPowerkw = findTechnicalAttribute(167)?.toInt()
            }
        }

        @Named("mapElectricRange")
        @JvmStatic
        fun mapElectricRange(consumptions: List<PVHVehicleConsumption>?): Int? =
            consumptions
                ?.firstOrNull {
                    it.dataType == "INTERPOLATIONS" &&
                        it.fuelType == "ELECTRICAL" &&
                        it.valueType == "RANGE" &&
                        it.energyManagementType == "PURE" &&
                        it.key == "COMBINED"
                }?.value
                ?.toInt()

        @Named("mapElectricRangeCity")
        @JvmStatic
        fun mapElectricRangeCity(consumptions: List<PVHVehicleConsumption>?): Int? =
            consumptions
                ?.firstOrNull {
                    it.dataType == "INTERPOLATIONS" &&
                        it.fuelType == "ELECTRICAL" &&
                        it.valueType == "RANGE" &&
                        it.energyManagementType == "PURE" &&
                        it.key == "CITY"
                }?.value
                ?.toInt()

        @Named("mapCo2Combined")
        @JvmStatic
        fun mapCo2Combined(consumptions: List<PVHVehicleConsumption>?): Int? =
            consumptions
                ?.firstOrNull {
                    it.dataType == "INTERPOLATIONS" && it.valueType == "CO2" && it.key == "COMBINED"
                }?.value
                ?.toInt()

        @Named("mapVehicleWeight")
        @JvmStatic
        fun mapVehicleWeight(consumptions: List<PVHVehicleConsumption>?): Int? =
            consumptions
                ?.firstOrNull {
                    it.dataType == "GENERAL_DATA" && it.key == "MASS_VEHICLE"
                }?.value
                ?.toInt()

        @Named("mapOrderTypeGroupToDriveType")
        @JvmStatic
        fun mapOrderTypeGroupToDriveType(orderTypeGroups: List<PVHVehicleDataOrderTypeGroupDTO>?): String? =
            orderTypeGroups
                ?.firstOrNull {
                    it.category == "DRIVE"
                }?.orderTypeGroup
    }

    @Mappings(
        Mapping(target = "modelInfo.modelDescription", source = "modelInfo.modelDescription.description"),
        Mapping(target = "modelInfo.modelDescriptionLanguage", source = "modelInfo.modelDescription.language"),
        Mapping(target = "orderInfo", source = "orderInfo"),
        Mapping(
            target = "orderInfo.department",
            source = "orderInfo.consigneeNumber",
            qualifiedByName = ["mapReferenceIdToDepartment"],
        ),
        Mapping(
            target = "orderInfo.leasingType",
            source = "orderInfo.consigneeNumber",
            qualifiedByName = ["mapReferenceIdToLeasingType"],
        ),
        Mapping(
            target = "orderInfo.customerDeliveryDate",
            source = "orderInfo.customerDeliveryDate",
            dateFormat = "yyyy-MM-dd",
        ),
        Mapping(target = "productionInfo.quoteMonth", source = "productionInfo.quoteMonth.month"),
        Mapping(target = "productionInfo.quoteYear", source = "productionInfo.quoteMonth.year"),
        Mapping(
            target = "consumptionInfo.driveType",
            source = "modelInfo.extendedModelInfo.orderTypeGroups",
            qualifiedByName = ["mapOrderTypeGroupToDriveType"],
        ),
        Mapping(target = "consumptionInfo.typification", source = "consumptionData.typification"),
        Mapping(target = "embargoInfo.inEmbargo", source = "metaInformation.embargoInformation.inEmbargo"),
        Mapping(target = "pmpData", source = "pmpData.header"),
        Mapping(target = "countryInfo.bnrValue", source = "countryInfo.BNR.value"),
        Mapping(target = "countryInfo.cnrValue", source = "countryInfo.CNR.value"),
        Mapping(
            target = "countryInfo.cnrCountryDescription",
            source = "countryInfo.CNR.countryDescription.description",
        ),
        Mapping(
            target = "countryInfo.cnrCountryDescriptionLanguage",
            source = "countryInfo.CNR.countryDescription.language",
        ),
        Mapping(target = "colorInfo.interior", source = "colorInfo.colorInterior"),
        Mapping(target = "colorInfo.interiorDescription", source = "colorInfo.colorInteriorDescription.description"),
        Mapping(
            target = "colorInfo.interiorDescriptionLanguage",
            source = "colorInfo.colorInteriorDescription.language",
        ),
        Mapping(target = "colorInfo.exterior", source = "colorInfo.colorExterior"),
        Mapping(target = "colorInfo.exteriorDescription", source = "colorInfo.colorExteriorDescription.description"),
        Mapping(
            target = "colorInfo.exteriorDescriptionLanguage",
            source = "colorInfo.colorExteriorDescription.language",
        ),
        Mapping(target = "options", source = "options", qualifiedByName = ["objectToString"]),
        Mapping(target = "source", constant = "PVH"),
        Mapping(target = "referenceId", source = "orderInfo.consigneeNumber"),
        Mapping(
            target = "technicalInfo",
            source = ".",
            qualifiedByName = ["mapTechnicalData"],
        ),
        Mapping(
            target = "wltpInfo.vehicleWeight",
            source = "consumptionData.consumptions",
            qualifiedByName = ["mapVehicleWeight"],
        ),
        Mapping(
            target = "wltpInfo.electricRange",
            source = "consumptionData.consumptions",
            qualifiedByName = ["mapElectricRange"],
        ),
        Mapping(
            target = "wltpInfo.co2Combined",
            source = "consumptionData.consumptions",
            qualifiedByName = ["mapCo2Combined"],
        ),
        Mapping(
            target = "wltpInfo.electricRangeCity",
            source = "consumptionData.consumptions",
            qualifiedByName = ["mapElectricRangeCity"],
        ),
        Mapping(
            target = "modelInfo.vehicleType",
            constant = VehicleType.PKW_STRING,
        ),
        Mapping(
            target = "modelInfo.manufacturer",
            constant = MANUFACTURER_PORSCHE,
        ),
    )
    fun map(
        pvhVehicle: PVHVehicle,
        @MappingTarget jpaVehicle: JPAVehicleEntity,
    ): JPAVehicleEntity
}
