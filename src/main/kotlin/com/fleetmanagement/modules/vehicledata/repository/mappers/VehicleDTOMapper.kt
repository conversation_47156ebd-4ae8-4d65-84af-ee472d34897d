package com.fleetmanagement.modules.vehicledata.repository.mappers

import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import com.fleetmanagement.modules.vehicledata.api.domain.Department
import com.fleetmanagement.modules.vehicledata.api.domain.LeasingType
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import org.mapstruct.Mapper
import org.mapstruct.Mapping
import org.mapstruct.Mappings
import org.mapstruct.Named
import org.mapstruct.ReportingPolicy
import org.mapstruct.factory.Mappers

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
interface VehicleDTOMapper {
    companion object {
        val INSTANCE: VehicleDTOMapper = Mappers.getMapper(VehicleDTOMapper::class.java)

        private val objectMapper = jacksonObjectMapper()

        @JvmStatic
        @Named("mapReferenceIdToDepartment")
        fun mapReferenceIdToDepartment(referenceId: String?): String? = referenceId?.let { Department(it).asCommaSeparatedList() }

        @JvmStatic
        @Named("mapReferenceIdToLeasingType")
        fun mapReferenceIdToLeasingType(referenceId: String?): String? = referenceId?.let { LeasingType(it).asCommaSeparatedList() }

        @Named("objectToString")
        @JvmStatic
        fun jsonNodeToString(options: JsonNode?): String? = options?.let { objectMapper.writeValueAsString(it) }

        @Named("stringToObject")
        @JvmStatic
        fun stringToJsonNode(json: String?): JsonNode? = json?.let { objectMapper.readTree(it) }
    }

    @Mappings(
        Mapping(target = "model.description", source = "modelInfo.modelDescription"),
        Mapping(target = "model.year", source = "modelInfo.modelYear"),
        Mapping(target = "model.productId", source = "modelInfo.productId"),
        Mapping(target = "model.productCode", source = "modelInfo.productCode"),
        Mapping(target = "model.orderType", source = "modelInfo.orderType"),
        Mapping(target = "model.vehicleType", source = "modelInfo.vehicleType"),
        Mapping(target = "model.manufacturer", source = "modelInfo.manufacturer"),
        Mapping(target = "model.range", source = "modelInfo.range"),
        Mapping(target = "consumption.driveType", source = "consumptionInfo.driveType"),
        Mapping(target = "consumption.typification", source = "consumptionInfo.typification"),
        Mapping(
            target = "order.department",
            source = "orderInfo.consigneeNumber",
            qualifiedByName = ["mapReferenceIdToDepartment"],
        ),
        Mapping(target = "order.tradingPartnerNumber", source = "orderInfo.tradingPartnerNumber"),
        Mapping(target = "order.purposeOrderType", source = "orderInfo.purposeOrderType"),
        Mapping(target = "order.importerShortName", source = "orderInfo.importerShortName"),
        Mapping(target = "order.portCode", source = "orderInfo.portCode"),
        Mapping(target = "order.commissionNumber", source = "orderInfo.commissionNumber"),
        Mapping(target = "order.invoiceNumber", source = "orderInfo.invoiceNumber"),
        Mapping(target = "order.invoiceDate", source = "orderInfo.invoiceDate", dateFormat = "yyyy-MM-dd"),
        Mapping(target = "order.purchaseOrderDate", source = "orderInfo.purchaseOrderDate", dateFormat = "yyyy-MM-dd"),
        Mapping(
            target = "order.requestedDeliveryDate",
            source = "orderInfo.requestedDeliveryDate",
            dateFormat = "yyyy-MM-dd",
        ),
        Mapping(
            target = "order.customerDeliveryDate",
            source = "orderInfo.customerDeliveryDate",
            dateFormat = "yyyy-MM-dd",
        ),
        Mapping(target = "order.deliveryType", source = "orderInfo.deliveryType"),
        Mapping(target = "order.primaryStatus", source = "orderInfo.primaryStatus"),
        Mapping(
            target = "order.leasingType",
            source = "orderInfo.consigneeNumber",
            qualifiedByName = ["mapReferenceIdToLeasingType"],
        ),
        Mapping(target = "order.preproductionVehicle", source = "orderInfo.preproductionVehicle"),
        Mapping(target = "order.blockedForSale", source = "orderInfo.blockedForSale"),
        Mapping(target = "production.number", source = "productionInfo.productionNumber"),
        Mapping(target = "production.numberVW", source = "productionInfo.productionNumberVw"),
        Mapping(target = "production.endDate", source = "productionInfo.productionEndDate"),
        Mapping(target = "production.technicalModelYear", source = "productionInfo.technicalModelYear"),
        Mapping(target = "production.factory", source = "productionInfo.factory"),
        Mapping(target = "production.factoryVW", source = "productionInfo.factoryVw"),
        Mapping(target = "production.quoteMonth", source = "productionInfo.quoteMonth"),
        Mapping(target = "production.quoteYear", source = "productionInfo.quoteYear"),
        Mapping(target = "production.plannedEndDate", source = "productionInfo.plannedProductionEndDate"),
        Mapping(target = "production.gearBoxClass", source = "productionInfo.gearBoxClass"),
        Mapping(target = "country.bnrValue", source = "countryInfo.bnrValue"),
        Mapping(target = "country.cnrValue", source = "countryInfo.cnrValue"),
        Mapping(target = "country.cnrCountryDescription", source = "countryInfo.cnrCountryDescription"),
        Mapping(target = "country.cnrCountryDescriptionLanguage", source = "countryInfo.cnrCountryDescriptionLanguage"),
        Mapping(target = "pmp.odometer", source = "pmpData.odometer"),
        Mapping(target = "pmp.timestamp", source = "pmpData.timestamp", dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSS"),
        Mapping(target = "embargo.inEmbargo", source = "embargoInfo.inEmbargo"),
        Mapping(target = "color.exterior", source = "colorInfo.exterior"),
        Mapping(target = "color.exteriorDescription", source = "colorInfo.exteriorDescription"),
        Mapping(target = "color.exteriorDescriptionLanguage", source = "colorInfo.exteriorDescriptionLanguage"),
        Mapping(target = "color.interiorDescriptionLanguage", source = "colorInfo.interiorDescriptionLanguage"),
        Mapping(target = "color.interior", source = "colorInfo.interior"),
        Mapping(target = "color.interiorDescription", source = "colorInfo.interiorDescription"),
        Mapping(target = "price.grossPrice", source = "priceInfo.grossPrice"),
        Mapping(target = "price.grossPriceWithExtras", source = "priceInfo.grossPriceWithExtras"),
        Mapping(target = "price.grossPricePlan", source = "priceInfo.grossPricePlan"),
        Mapping(target = "price.grossPriceNewCar", source = "priceInfo.grossPriceNewCar"),
        Mapping(target = "price.netPrice", source = "priceInfo.netPrice"),
        Mapping(target = "price.netPriceWithExtras", source = "priceInfo.netPriceWithExtras"),
        Mapping(target = "price.netPriceNewCar", source = "priceInfo.netPriceNewCar"),
        Mapping(target = "price.valueAddedTax", source = "priceInfo.valueAddedTax"),
        Mapping(target = "price.piaEvent", source = "priceInfo.piaEvent", qualifiedByName = ["stringToObject"]),
        Mapping(target = "technical.amountSeats", source = "technicalInfo.amountSeats"),
        Mapping(target = "technical.engineCapacity", source = "technicalInfo.engineCapacity"),
        Mapping(target = "technical.vehicleWidthMirrorsExtended", source = "technicalInfo.vehicleWidthmirrorsExtended"),
        Mapping(target = "technical.maximumChargingPowerDc", source = "technicalInfo.maximumChargingPowerdc"),
        Mapping(target = "technical.grossVehicleWeight", source = "technicalInfo.grossVehicleWeight"),
        Mapping(target = "technical.curbWeightEu", source = "technicalInfo.curbWeightEu"),
        Mapping(target = "technical.totalPowerKw", source = "technicalInfo.totalPowerkw"),
        Mapping(target = "technical.curbWeightDin", source = "technicalInfo.curbWeightdin"),
        Mapping(target = "technical.maximumPayload", source = "technicalInfo.maximumPayload"),
        Mapping(target = "technical.chargingTimeAc22Kw", source = "technicalInfo.chargingTimeac22Kw"),
        Mapping(target = "technical.chargingTimeAc11kw0100", source = "technicalInfo.chargingTimeac11kw0100"),
        Mapping(target = "technical.chargingTimeAc96kw0100", source = "technicalInfo.chargingTimeac96kw0100"),
        Mapping(target = "technical.netBatteryCapacity", source = "technicalInfo.netBatteryCapacity"),
        Mapping(target = "technical.grossBatteryCapacity", source = "technicalInfo.grossBatteryCapacity"),
        Mapping(target = "technical.acceleration0100Kmh", source = "technicalInfo.acceleration0100Kmh"),
        Mapping(
            target = "technical.acceleration0100KmhLaunchControl",
            source = "technicalInfo.acceleration0100KmhlaunchControl",
        ),
        Mapping(target = "technical.topSpeed", source = "technicalInfo.topSpeed"),
        Mapping(target = "technical.height", source = "technicalInfo.height"),
        Mapping(target = "technical.widthMirrorsFolded", source = "technicalInfo.widthMirrorsFolded"),
        Mapping(target = "technical.length", source = "technicalInfo.length"),
        Mapping(target = "technical.acceleration80120Kmh", source = "technicalInfo.acceleration80120Kmh"),
        Mapping(
            target = "technical.maxRoofLoadWithPorscheRoofTransportSystem",
            source = "technicalInfo.maxRoofLoadwithPorscheRoofTransportSystem",
        ),
        Mapping(target = "technical.chargingTimeDcMaxPower580", source = "technicalInfo.chargingTimedcMaxPower580"),
        Mapping(target = "technical.powerKw", source = "technicalInfo.powerkw"),
        Mapping(target = "options", source = "options", qualifiedByName = ["stringToObject"]),
        Mapping(target = "returnInfo.isUsedCar", source = "returnInfo.usedCar"),
        Mapping(target = "fleet.soldDate", source = "fleetInfo.soldDate"),
        Mapping(target = "fleet.scrappedDate", source = "fleetInfo.scrappedDate"),
        Mapping(target = "fleet.stolenDate", source = "fleetInfo.stolenDate"),
        Mapping(target = "fleet.soldCupCarDate", source = "fleetInfo.soldCupCarDate"),
        Mapping(target = "fleet.approvedForScrappingDate", source = "fleetInfo.approvedForScrappingDate"),
        Mapping(target = "fleet.scrappedVehicleOfferedDate", source = "fleetInfo.scrappedVehicleOfferedDate"),
        Mapping(target = "fleet.vehicleSentToSalesDate", source = "fleetInfo.vehicleSentToSalesDate"),
        Mapping(target = "fleet.costEstimationOrderedDate", source = "fleetInfo.costEstimationOrderedDate"),
        Mapping(target = "fleet.isResidualValueMarket", source = "fleetInfo.residualValueMarket"),
        Mapping(target = "fleet.profitabilityAuditDate", source = "fleetInfo.profitabilityAuditDate"),
        Mapping(target = "fleet.scrapVehicle", source = "fleetInfo.scrapVehicle"),
        Mapping(target = "fleet.comment", source = "fleetInfo.comment"),
        Mapping(target = "fleet.isClassic", source = "fleetInfo.classic"),
        Mapping(target = "fleet.raceCar", source = "fleetInfo.raceCar"),
        Mapping(target = "wltpInfo", source = "wltpInfo"),
        Mapping(target = "evaluation", source = "evaluationInfo"),
        Mapping(target = "vtstamm", source = "vtstammInfo"),
        Mapping(target = "delivery.preparationDoneDate", source = "deliveryInfo.preparationDoneDate"),
        Mapping(target = "delivery.isPreparationNecessary", source = "deliveryInfo.preparationNecessary"),
    )
    fun map(vehicle: JPAVehicleEntity): VehicleDTO

    fun map(vehicles: List<JPAVehicleEntity>): List<VehicleDTO>
}
