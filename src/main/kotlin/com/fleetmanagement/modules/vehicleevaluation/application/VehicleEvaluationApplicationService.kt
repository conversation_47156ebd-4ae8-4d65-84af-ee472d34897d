/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicleevaluation.application

import com.aspose.email.Attachment
import com.aspose.email.MailAddress
import com.aspose.words.HtmlSaveOptions
import com.aspose.words.SaveFormat
import com.fleetmanagement.integrations.mailclient.application.port.EmailDto
import com.fleetmanagement.integrations.mailclient.application.port.EmailException
import com.fleetmanagement.integrations.mailclient.application.port.EmailOutPort
import com.fleetmanagement.modules.documentgeneration.api.GenerateDocument
import com.fleetmanagement.modules.documentgeneration.api.GenerateDocumentException
import com.fleetmanagement.modules.documentgeneration.api.GenerateTUVDocument
import com.fleetmanagement.modules.documentgeneration.api.GenerateTUVDocumentException
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.UpdateCostEstimationOrderedDate
import com.fleetmanagement.modules.vehicleevaluation.application.port.CommissionTUVAppraisal
import com.fleetmanagement.modules.vehicleevaluation.application.port.ErrorDetail
import org.apache.commons.io.output.ByteArrayOutputStream
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Component
import java.time.OffsetDateTime
import java.util.UUID

@Component
class VehicleEvaluationApplicationService(
    private val generateDocument: GenerateDocument,
    private val emailOutPort: EmailOutPort,
    private val generateTUVDocument: GenerateTUVDocument,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val updateCostEstimationOrderedDate: UpdateCostEstimationOrderedDate,
    @Value("\${vehicle-evaluation.tuv-commission.tuv-email.sender-email-address}") val tuvEmailSenderEmailAddress: String,
    @Value("\${vehicle-evaluation.tuv-commission.tuv-email.recipient-to-email-address}") val tuvEmailRecipientEmailAddressInTo: String,
    @Value(
        "\${vehicle-evaluation.tuv-commission.logistics-provider-email.sender-email-address}",
    ) val logisticsProviderEmailSenderEmailAddress: String,
    @Value(
        "\${vehicle-evaluation.tuv-commission.logistics-provider-email.recipient-to-email-address}",
    ) val logisticsProviderEmailRecipientEmailAddressInTo: String,
) : CommissionTUVAppraisal {
    override fun commissionTUVAppraisal(vehicleIds: List<UUID>): List<ErrorDetail> {
        val listOfTuvTeamEmailData = mutableListOf<TUVTeamEmailData>()
        val listOfLogisticsProviderTeamEmailData = mutableListOf<LogisticsProviderTeamEmailData>()
        val errorList = mutableListOf<ErrorDetail>()

        vehicleIds.forEach {
            val vin =
                try {
                    val vehicleData = readVehicleByVehicleId.readVehicleById(it)
                    val vin = requireNotNull(vehicleData?.vin) { "Vin is missing" }
                    listOfLogisticsProviderTeamEmailData.add(LogisticsProviderTeamEmailData(vin = vin, vehicleId = it))
                    vin
                } catch (exception: IllegalArgumentException) {
                    errorList.add(
                        ErrorDetail(
                            vin = null,
                            vehicleId = it,
                            message = "Error sending Email to TÜV team ${exception.message}",
                            type = ErrorDetail.VehicleEvaluationError.TUV_EMAIL_ERROR,
                        ),
                    )
                    errorList.add(
                        ErrorDetail(
                            vin = null,
                            vehicleId = it,
                            message = "Error sending Email to logistics provider team ${exception.message}",
                            type = ErrorDetail.VehicleEvaluationError.LOGISTICS_EMAIL_ERROR,
                        ),
                    )
                    return@forEach
                }

            try {
                val pdf = generateTUVDocument.generateTUVDocument(it)
                listOfTuvTeamEmailData.add(
                    TUVTeamEmailData(
                        attachment = Attachment(pdf.inputStream, "$vin.pdf"),
                        vin = vin,
                        vehicleId = it,
                    ),
                )
            } catch (exception: GenerateTUVDocumentException) {
                errorList.add(
                    ErrorDetail(
                        vin = null,
                        vehicleId = it,
                        message = "Error creating PDF attachment for TÜV email ${exception.message}",
                        type = ErrorDetail.VehicleEvaluationError.PDF_GENERATION_ERROR,
                    ),
                )
            }
        }

        if (listOfTuvTeamEmailData.isNotEmpty()) {
            val tuvEmailVins = listOfTuvTeamEmailData.map { it.vin }
            try {
                sendEmailToTUVTeam(tuvEmailVins, listOfTuvTeamEmailData)
                updateCostEstimationOrderedDate.updateCostEstimationOrderedDate(
                    listOfTuvTeamEmailData.map { it.vehicleId },
                    OffsetDateTime.now(),
                )
            } catch (exception: EmailException) {
                val errors =
                    listOfTuvTeamEmailData.map {
                        ErrorDetail(
                            vehicleId = it.vehicleId,
                            vin = it.vin,
                            message = "Could not send email to TÜV Team ${exception.message}",
                            type = ErrorDetail.VehicleEvaluationError.TUV_EMAIL_ERROR,
                        )
                    }
                errorList.addAll(errors)
            } catch (exception: GenerateDocumentException) {
                val errors =
                    listOfTuvTeamEmailData.map {
                        ErrorDetail(
                            vehicleId = it.vehicleId,
                            vin = it.vin,
                            message = "Could not send email to TÜV Team ${exception.message}",
                            type = ErrorDetail.VehicleEvaluationError.TUV_EMAIL_ERROR,
                        )
                    }
                errorList.addAll(errors)
            }
        }

        if (listOfLogisticsProviderTeamEmailData.isNotEmpty()) {
            val logisticsEmailVin = listOfLogisticsProviderTeamEmailData.map { it.vin }
            try {
                sendEmailToLogisticsProviderTeam(logisticsEmailVin)
            } catch (exception: EmailException) {
                val errors =
                    listOfLogisticsProviderTeamEmailData.map {
                        ErrorDetail(
                            vehicleId = it.vehicleId,
                            vin = it.vin,
                            message = "Could not send email to logistics provider team ${exception.message}",
                            type = ErrorDetail.VehicleEvaluationError.LOGISTICS_EMAIL_ERROR,
                        )
                    }
                errorList.addAll(errors)
            } catch (exception: GenerateDocumentException) {
                val errors =
                    listOfLogisticsProviderTeamEmailData.map {
                        ErrorDetail(
                            vehicleId = it.vehicleId,
                            vin = it.vin,
                            message = "Could not send email to logistics provider team ${exception.message}",
                            type = ErrorDetail.VehicleEvaluationError.LOGISTICS_EMAIL_ERROR,
                        )
                    }
                errorList.addAll(errors)
            }
        }

        return errorList.toList()
    }

    private fun sendEmailToTUVTeam(
        vins: List<String>,
        listOfTuvTeamEmailData: List<TUVTeamEmailData>,
    ) {
        val emailBody = createEmailBody(vins, TUV_TEAM_EMAIL_TEMPLATE)
        val attachments = listOfTuvTeamEmailData.map { it.attachment }

        /**
         * aws ecs cannot accept list of string as environment variables, so we use comma separated email address
         * and split them into list here
         */
        val listOfRecipientEmailAddressInTo = tuvEmailRecipientEmailAddressInTo.split(",").map { it.trim() }

        val emailDto =
            EmailDto(
                subject = TUV_EMAIL_SUBJECT,
                htmlBody = emailBody,
                recipientsMailAddressInTo = listOfRecipientEmailAddressInTo.map { MailAddress(it) },
                recipientsMailAddressInCC = emptyList(),
                senderMailAddress = MailAddress(tuvEmailSenderEmailAddress),
                attachment = attachments,
            )

        emailOutPort.sendEmail(emailDto)
    }

    private fun sendEmailToLogisticsProviderTeam(vins: List<String>) {
        val emailBody = createEmailBody(vins, LOGISTICS_PROVIDER_TEAM_EMAIL_TEMPLATE)

        /**
         * aws ecs cannot accept list of string as environment variables, so we use comma separated email address
         * and split them into list here
         */
        val listOfRecipientEmailAddressInTo = logisticsProviderEmailRecipientEmailAddressInTo.split(",").map { it.trim() }

        val emailDto =
            EmailDto(
                subject = TUV_EMAIL_SUBJECT,
                htmlBody = emailBody,
                recipientsMailAddressInTo = listOfRecipientEmailAddressInTo.map { MailAddress(it) },
                recipientsMailAddressInCC = emptyList(),
                senderMailAddress = MailAddress(logisticsProviderEmailSenderEmailAddress),
                attachment = emptyList(),
            )

        emailOutPort.sendEmail(emailDto)
    }

    private fun createEmailBody(
        vins: List<String>,
        template: String,
    ): String {
        val templateVins = vins.joinToString(",")
        val templateData = mapOf(LIST_OF_VINS to templateVins)
        return generateDocument
            .generateDocument(template, templateData)
            .apply {
                range.fields.forEach { it.unlink() }
            }.let {
                val outputStream = ByteArrayOutputStream()
                it.save(outputStream, HtmlSaveOptions(SaveFormat.HTML))
                outputStream.toString(Charsets.UTF_8.name())
            }
    }

    companion object {
        private const val LIST_OF_VINS = "vins"
        private const val TUV_TEAM_EMAIL_TEMPLATE = "TUVEmail.docx"
        private const val LOGISTICS_PROVIDER_TEAM_EMAIL_TEMPLATE = "TUVLogisticsProviderEmail.docx"
        private const val TUV_EMAIL_SUBJECT = "TÜV SÜD Siegelsbach"
    }
}

data class TUVTeamEmailData(
    val attachment: Attachment,
    val vin: String,
    val vehicleId: UUID,
)

data class LogisticsProviderTeamEmailData(
    val vin: String,
    val vehicleId: UUID,
)
