/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehicleevaluation.application.port

import java.util.UUID

fun interface CommissionTUVAppraisal {
    fun commissionTUVAppraisal(vehicleIds: List<UUID>): List<ErrorDetail>
}

data class ErrorDetail(
    val vehicleId: UUID,
    val vin: String?,
    val message: String,
    val type: VehicleEvaluationError,
) {
    enum class VehicleEvaluationError {
        PDF_GENERATION_ERROR,
        TUV_EMAIL_ERROR,
        LOGISTICS_EMAIL_ERROR,
    }
}
