package com.fleetmanagement.modules.vehiclehistory

import liquibase.integration.spring.SpringLiquibase
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import javax.sql.DataSource

@Configuration
class VehicleHistoryDBConfiguration {
    @Bean
    fun vehicleHistoryLiquibase(datasource: DataSource): SpringLiquibase {
        val liquibase = SpringLiquibase()
        liquibase.changeLog = "classpath:/db/changelog/db.changelog-vehiclehistory.yml"
        liquibase.liquibaseSchema = "vehiclehistory"
        liquibase.dataSource = datasource
        return liquibase
    }
}
