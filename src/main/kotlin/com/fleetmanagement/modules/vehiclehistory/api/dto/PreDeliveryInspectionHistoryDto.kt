package com.fleetmanagement.modules.vehiclehistory.api.dto

import java.time.OffsetDateTime
import java.util.UUID

data class PreDeliveryInspectionHistoryDto(
    val id: UUID,
    val vehicleId: UUID,
    val tireSet: String?,
    val isRelevant: Boolean?,
    val foiling: Boolean?,
    val refuel: Boolean?,
    val charge: Boolean?,
    val digitalLogbook: Boolean?,
    val licencePlateMounting: Boolean?,
    val orderedDate: OffsetDateTime?,
    val completedDate: OffsetDateTime?,
    val comment: String?,
    val plannedDate: OffsetDateTime?,
    val createdBy: String,
    val created: OffsetDateTime,
    val lastModifiedBy: String,
)
