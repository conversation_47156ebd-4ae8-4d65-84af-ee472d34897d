package com.fleetmanagement.modules.vehiclehistory.api.dto

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehiclehistory.repository.entities.JPAChangeEventEntity
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDTO
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSalesDto
import java.time.OffsetDateTime
import java.util.*

data class VehicleChangeEventPayload(
    val vehicle: VehicleDTO? = null,
    val vehicleLocation: VehicleLocation? = null,
    val vehicleRegistrationOrder: VehicleRegistrationOrder? = null,
    val vehiclePerson: VehiclePersonDTO? = null,
    val vehicleTransfer: VehicleTransferHistoryDto? = null,
    val preDeliveryInspection: PreDeliveryInspectionHistoryDto? = null,
    val vehicleSales: VehicleSalesDto? = null,
)

data class VehicleChangeEvent(
    val vehicleId: UUID,
    val modifiedAt: OffsetDateTime,
    val modifiedBy: String,
    val payload: VehicleChangeEventPayload,
) {
    fun toJPAEntities(objectMapper: ObjectMapper): List<JPAChangeEventEntity> =
        listOfNotNull(
            createVehicleDataEntity(objectMapper),
            createRegistrationEntity(objectMapper),
            createLocationEntity(objectMapper),
            createVehiclePersonEntity(objectMapper),
            createTransferEntity(objectMapper),
            createPreDeliveryInspectionEntity(objectMapper),
            createSalesEntity(objectMapper),
        )

    private fun createVehiclePersonEntity(objectMapper: ObjectMapper): JPAChangeEventEntity? =
        payload.vehiclePerson?.let {
            createEntity(Domain.VEHICLE_PERSON, null, VehicleChangeEventPayload(vehiclePerson = it), objectMapper)
        }

    private fun createVehicleDataEntity(objectMapper: ObjectMapper): JPAChangeEventEntity? =
        payload.vehicle?.let {
            createEntity(Domain.VEHICLE, null, VehicleChangeEventPayload(vehicle = it), objectMapper)
        }

    private fun createRegistrationEntity(objectMapper: ObjectMapper): JPAChangeEventEntity? =
        payload.vehicleRegistrationOrder?.let {
            createEntity(Domain.REGISTRATION, it.id.toString(), VehicleChangeEventPayload(vehicleRegistrationOrder = it), objectMapper)
        }

    private fun createLocationEntity(objectMapper: ObjectMapper): JPAChangeEventEntity? =
        payload.vehicleLocation?.let {
            createEntity(Domain.LOCATION, null, VehicleChangeEventPayload(vehicleLocation = it), objectMapper)
        }

    private fun createPreDeliveryInspectionEntity(objectMapper: ObjectMapper): JPAChangeEventEntity? =
        payload.preDeliveryInspection?.let {
            createEntity(Domain.PREDELIVERY_INSPECTION, null, VehicleChangeEventPayload(preDeliveryInspection = it), objectMapper)
        }

    private fun createTransferEntity(objectMapper: ObjectMapper): JPAChangeEventEntity? =
        payload.vehicleTransfer?.let {
            createEntity(Domain.VEHICLE_TRANSFER, it.key.toString(), VehicleChangeEventPayload(vehicleTransfer = it), objectMapper)
        }

    private fun createSalesEntity(objectMapper: ObjectMapper): JPAChangeEventEntity? =
        payload.vehicleSales?.let {
            createEntity(Domain.VEHICLE_SALES, null, VehicleChangeEventPayload(vehicleSales = it), objectMapper)
        }

    private fun createEntity(
        domain: Domain,
        domainId: String?,
        payload: VehicleChangeEventPayload,
        objectMapper: ObjectMapper,
    ): JPAChangeEventEntity =
        JPAChangeEventEntity(
            vehicleId = vehicleId,
            domain = domain,
            domainId = domainId,
            jsonString = objectMapper.writeValueAsString(payload),
            modifiedBy = modifiedBy,
            modifiedAt = modifiedAt,
        )
}
