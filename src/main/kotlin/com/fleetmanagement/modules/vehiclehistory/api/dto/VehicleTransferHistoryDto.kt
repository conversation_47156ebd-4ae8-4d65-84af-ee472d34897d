package com.fleetmanagement.modules.vehiclehistory.api.dto

import java.time.OffsetDateTime
import java.util.*

data class VehicleTransferHistoryDto(
    val id: UUID,
    val vehicleId: UUID,
    val consignee: String?,
    val businessPartnerId: String?,
    val maximumServiceLifeInMonths: Int?,
    val usingCostCenter: String?,
    val internalOrderNumber: String?,
    val key: Long,
    val vehicleResponsiblePerson: String?,
    val internalContactPerson: String?,
    val plannedDeliveryDate: String?,
    val plannedReturnDate: String?,
    val remark: String?,
    val licensePlate: String?,
    val status: String?,
    val initialized: Boolean?,
    val deliveryDate: OffsetDateTime?,
    val returnDate: OffsetDateTime?,
    val latestReturnDate: OffsetDateTime?,
    val mileageAtDelivery: Int?,
    val mileageAtReturn: Int?,
    val usageGroup: UUID?,
    val vehicleUsage: UUID?,
    val depreciationRelevantCostCenterId: UUID?,
    val deliveryComment: String?,
    val deliveryLeipzig: Boolean?,
    val desiredDeliveryDate: OffsetDateTime?,
    val predecessor: UUID?,
    val provisionForDeliveryComment: String?,
    val utilizationArea: String?,
    val predecessorLatestReturnDate: OffsetDateTime?,
    val desiredTireSet: String?,
    val tiresComment: String?,
    val leasingPrivilege: String?,
    val msbookingAppointmentId: String?,
    val leasingPrivilegeValidationSuccessful: Boolean?,
    val maintenanceOrderNumber: String?,
    val successorKey: Long?,
    val successorOrderDate: OffsetDateTime?,
    val returnComment: String?,
    val serviceCards: List<String>?,
    val registrationNeeded: Boolean?,
    val isCurrent: Boolean,
    val deliveryIndex: String?,
    val usageMhp: Boolean,
    val usageVdw: Boolean,
    val privateMonthlyKilometers: Int?,
)
