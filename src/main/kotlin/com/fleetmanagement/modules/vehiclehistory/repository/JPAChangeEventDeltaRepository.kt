/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclehistory.repository

import com.fleetmanagement.modules.vehiclehistory.repository.entities.JPAChangeEventDeltaEntity
import org.springframework.data.jpa.repository.JpaRepository
import java.util.*

interface JPAChangeEventDeltaRepository : JpaRepository<JPAChangeEventDeltaEntity, UUID> {
    fun findAllByVehicleIdOrderByModifiedAtDesc(vehicleId: UUID): List<JPAChangeEventDeltaEntity>

    fun deleteAllByVehicleId(vehicleId: UUID)

    fun existsByVehicleId(vehicleId: UUID): Boolean
}
