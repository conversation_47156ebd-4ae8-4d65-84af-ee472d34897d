/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclehistory.repository

import com.fleetmanagement.modules.vehiclehistory.api.dto.Domain
import com.fleetmanagement.modules.vehiclehistory.repository.entities.JPAChangeEventEntity
import org.springframework.data.jpa.repository.JpaRepository
import java.util.*

interface JPAChangeEventRepository : JpaRepository<JPAChangeEventEntity, UUID> {
    fun findAllByVehicleIdOrderByModifiedAt(vehicleId: UUID): List<JPAChangeEventEntity>

    fun findAllByVehicleId(vehicleId: UUID): List<JPAChangeEventEntity>

    fun deleteAllByVehicleId(vehicleId: UUID)

    fun findTopByVehicleIdAndDomainAndDomainIdOrderByModifiedAtDesc(
        vehicleId: UUID,
        domain: Domain,
        domainId: String?,
    ): Optional<JPAChangeEventEntity?>
}
