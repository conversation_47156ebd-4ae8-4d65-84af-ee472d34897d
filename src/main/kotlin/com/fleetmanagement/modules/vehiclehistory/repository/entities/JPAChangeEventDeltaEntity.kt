package com.fleetmanagement.modules.vehiclehistory.repository.entities

import com.fleetmanagement.modules.vehiclehistory.api.dto.VehicleDelta
import jakarta.persistence.*
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "change_event_delta", schema = "vehiclehistory")
class JPAChangeEventDeltaEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long? = null,
    @Column(name = "current_vehicle_change_event_id")
    val currentVehicleChangeEventId: Long? = null,
    @Column(name = "vehicle_id", nullable = false)
    val vehicleId: UUID,
    @Column(name = "field")
    val field: String,
    @Column(name = "old_value")
    val oldValue: String?,
    @Column(name = "new_value")
    val newValue: String?,
    @Column(name = "modified_by")
    val modifiedBy: String,
    @Column(name = "modified_at")
    val modifiedAt: OffsetDateTime,
) {
    fun toVehicleDelta() =
        VehicleDelta(
            vehicleId = vehicleId,
            field = field,
            oldValue = oldValue,
            newValue = newValue,
            modifiedAt = modifiedAt,
            modifiedBy = modifiedBy,
        )
}
