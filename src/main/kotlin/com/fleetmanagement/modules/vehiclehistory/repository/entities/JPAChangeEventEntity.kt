/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclehistory.repository.entities

import com.fleetmanagement.modules.vehiclehistory.api.dto.Domain
import jakarta.persistence.*
import org.hibernate.annotations.CreationTimestamp
import java.time.OffsetDateTime
import java.util.*

@Entity
@Table(name = "vehicle_change_event", schema = "vehiclehistory")
class JPAChangeEventEntity(
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    val id: Long? = null,
    @Column(name = "vehicle_id", nullable = false)
    val vehicleId: UUID,
    @Enumerated(EnumType.STRING)
    @Column(name = "domain")
    val domain: Domain,
    @Column(name = "domain_id")
    val domainId: String? = null,
    @Column(name = "vehicle_change_event")
    val jsonString: String,
    @Column(name = "modified_by")
    val modifiedBy: String,
    @Column(name = "modified_at")
    val modifiedAt: OffsetDateTime,
    @CreationTimestamp
    @Column(name = "created_at", updatable = false)
    val createdAt: OffsetDateTime? = null,
)
