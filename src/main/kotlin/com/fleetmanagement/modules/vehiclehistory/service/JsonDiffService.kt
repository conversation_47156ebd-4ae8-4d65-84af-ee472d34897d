/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclehistory.service

import com.fasterxml.jackson.databind.JsonNode
import org.springframework.stereotype.Service

data class JsonDelta(
    val field: String,
    val oldValue: String?,
    val newValue: String?,
)

@Service
class JsonDiffService {
    private fun flattenObject(
        node: JsonNode,
        currentPath: String,
    ): Map<String, Any?> =
        node
            .fields()
            .asSequence()
            .flatMap { (key, value) ->
                val fieldPath = if (currentPath.isEmpty()) key else "$currentPath.$key"
                flatten(value, fieldPath).asSequence().map { it.toPair() }
            }.toMap()

    private fun flattenArray(
        node: JsonNode,
        currentPath: String,
    ): Map<String, Any?> =
        node
            .withIndex()
            .flatMap { (index, jsonNode) ->
                flatten(jsonNode, "$currentPath.$index").asSequence().map { it.toPair() }
            }.toMap()

    private fun flattenPrimitive(
        node: JsonNode,
        currentPath: String,
    ): Map<String, Any?> =
        mapOf(
            currentPath to
                node
                    .takeIf {
                        !it.isNull
                    }?.asText(),
        )

    private fun flatten(
        node: JsonNode?,
        currentPath: String = "",
    ): Map<String, Any?> {
        if (node == null) return emptyMap()

        return when {
            node.isObject -> flattenObject(node, currentPath)
            node.isArray -> flattenArray(node, currentPath)
            else -> flattenPrimitive(node, currentPath)
        }
    }

    fun compareJsonNodes(
        jsonLeft: JsonNode?,
        jsonRight: JsonNode?,
        keysToIgnore: List<String> = emptyList(),
    ): List<JsonDelta> {
        val leftFlatMap = flatten(node = jsonLeft)
        val rightFlatMap = flatten(node = jsonRight)

        val leftKeys = leftFlatMap.keys
        val rightKeys = rightFlatMap.keys

        val onlyOnLeft =
            leftKeys.subtract(rightKeys).mapNotNull { key ->
                if (ignoreKey(key, keysToIgnore)) {
                    leftFlatMap[key]?.toString()?.let { value ->
                        JsonDelta(key, value, null)
                    }
                } else {
                    null
                }
            }

        val onlyOnRight =
            rightKeys.subtract(leftKeys).mapNotNull { key ->
                if (ignoreKey(key, keysToIgnore)) {
                    rightFlatMap[key]?.toString()?.let { value ->
                        JsonDelta(key, null, value)
                    }
                } else {
                    null
                }
            }

        val different =
            leftKeys
                .intersect(rightKeys)
                .filter { leftFlatMap[it] != rightFlatMap[it] }
                .mapNotNull { key ->
                    if (ignoreKey(key, keysToIgnore)) {
                        val leftValue = leftFlatMap[key]?.toString()
                        val rightValue = rightFlatMap[key]?.toString()
                        JsonDelta(key, leftValue, rightValue).takeIf { leftValue != null || rightValue != null }
                    } else {
                        null
                    }
                }

        return onlyOnLeft + onlyOnRight + different
    }

    private fun ignoreKey(
        key: String,
        keysToIgnore: List<String>,
    ): Boolean = key.split(".").none { it in keysToIgnore }
}
