/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclehistory.service

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehiclehistory.api.VehicleChangeDeltas
import com.fleetmanagement.modules.vehiclehistory.api.VehicleChanges
import com.fleetmanagement.modules.vehiclehistory.api.dto.VehicleChangeEvent
import com.fleetmanagement.modules.vehiclehistory.api.dto.VehicleDelta
import com.fleetmanagement.modules.vehiclehistory.repository.JPAChangeEventDeltaRepository
import com.fleetmanagement.modules.vehiclehistory.repository.JPAChangeEventRepository
import com.fleetmanagement.modules.vehiclehistory.repository.entities.JPAChangeEventDeltaEntity
import com.fleetmanagement.modules.vehiclehistory.repository.entities.JPAChangeEventEntity
import jakarta.persistence.EntityManager
import org.slf4j.LoggerFactory
import org.springframework.orm.ObjectOptimisticLockingFailureException
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Service
class VehicleChangesService(
    private val jsonDiffService: JsonDiffService,
    private val changeEventRepository: JPAChangeEventRepository,
    private val changeEventDeltaRepository: JPAChangeEventDeltaRepository,
    private val objectMapper: ObjectMapper,
    private val entityManager: EntityManager,
) : VehicleChanges,
    VehicleChangeDeltas {
    companion object {
        private val logger = LoggerFactory.getLogger(VehicleChangesService::class.java)
        private val KEYS_TO_IGNORE = listOf("version", "updatedAt", "options", "piaEvent", "vehicleId")
    }

    override fun getChangeDeltas(vehicleId: String): List<VehicleDelta> =
        findOrCreateDeltas(UUID.fromString(vehicleId))
            .map { it.toVehicleDelta() }

    @Transactional
    override fun addVehicleChange(vehicleChangeEvent: VehicleChangeEvent): List<JPAChangeEventEntity> {
        logger.debug("Creating vehicle change history for vehicle: {}", vehicleChangeEvent.vehicleId)

        ensureDeltasExist(vehicleChangeEvent.vehicleId)

        val changeEventEntities = vehicleChangeEvent.toJPAEntities(objectMapper)
        val changeEventDeltas = createDeltasForEvents(changeEventEntities)

        val savedEntities = changeEventRepository.saveAll(changeEventEntities)
        changeEventDeltaRepository.saveAll(changeEventDeltas)

        deleteNonLatestEventsPerDomain(vehicleChangeEvent.vehicleId)

        logger.debug("Vehicle history created for vehicle: {}", vehicleChangeEvent.vehicleId)
        return savedEntities
    }

    private fun deleteNonLatestEventsPerDomain(vehicleId: UUID) {
        logger.info("Finding non-latest events for vehicle: {} to delete", vehicleId)

        val vehicleDomainEvents = changeEventRepository.findAllByVehicleId(vehicleId)

        val latestEntitiesByDomain =
            vehicleDomainEvents
                .groupBy { Pair(it.domain, it.domainId) }
                .mapValues { (_, entities) ->
                    entities.maxByOrNull { it.modifiedAt }
                }

        val latestIds =
            latestEntitiesByDomain.values
                .filterNotNull()
                .map { it.id }
                .toSet()

        val entitiesToDelete = vehicleDomainEvents.filterNot { it.id in latestIds }

        if (entitiesToDelete.isNotEmpty()) {
            try {
                changeEventRepository.deleteAll(entitiesToDelete)
                // force flush to trigger staleState exceptions here
                entityManager.flush()
            } catch (exception: ObjectOptimisticLockingFailureException) {
                // catch it here to avoid rollback for new events, in case delete fails when accessing this function from multiple threads in parallel
                logger.warn("Failed to delete all events for vehicle: {}", exception.message)
            }
        }

        logger.info("Deleted {} non-latest events across all domains for vehicle $vehicleId", entitiesToDelete.size)
    }

    private fun findOrCreateDeltas(vehicleId: UUID): List<JPAChangeEventDeltaEntity> =
        changeEventDeltaRepository
            .findAllByVehicleIdOrderByModifiedAtDesc(vehicleId)
            .ifEmpty {
                createDeltasForAllExistingEvents(vehicleId)
            }

    private fun ensureDeltasExist(vehicleId: UUID) {
        if (!changeEventDeltaRepository.existsByVehicleId(vehicleId)) {
            createDeltasForAllExistingEvents(vehicleId)
        }
    }

    private fun createDeltasForAllExistingEvents(vehicleId: UUID): List<JPAChangeEventDeltaEntity> {
        val changeEvents =
            changeEventRepository
                .findAllByVehicleIdOrderByModifiedAt(vehicleId)
                .ifEmpty { return emptyList() }

        val deltas =
            changeEvents
                .groupBy { it.domain to it.domainId }
                .flatMap { (_, events) ->
                    val initialDelta = calculateDeltasBetween(null, events.first())
                    val subsequentDeltas =
                        events.zipWithNext { previous, current -> calculateDeltasBetween(previous, current) }.flatten()
                    initialDelta + subsequentDeltas
                }

        return changeEventDeltaRepository.saveAll(deltas)
    }

    private fun createDeltasForEvents(changeEventEntities: Iterable<JPAChangeEventEntity>): List<JPAChangeEventDeltaEntity> =
        changeEventEntities.flatMap {
            createDeltasForEvent(it)
        }

    private fun createDeltasForEvent(currentEvent: JPAChangeEventEntity): List<JPAChangeEventDeltaEntity> {
        val latestEvent = findLatestEvent(currentEvent)
        return calculateDeltasBetween(latestEvent, currentEvent)
    }

    private fun findLatestEvent(event: JPAChangeEventEntity): JPAChangeEventEntity? =
        changeEventRepository
            .findTopByVehicleIdAndDomainAndDomainIdOrderByModifiedAtDesc(
                event.vehicleId,
                event.domain,
                event.domainId,
            ).orElse(null)

    private fun calculateDeltasBetween(
        previous: JPAChangeEventEntity?,
        current: JPAChangeEventEntity,
    ): List<JPAChangeEventDeltaEntity> {
        val previousEvent = previous?.let { objectMapper.readTree(it.jsonString) }
        val currentEvent = objectMapper.readTree(current.jsonString)
        return jsonDiffService
            .compareJsonNodes(previousEvent, currentEvent, KEYS_TO_IGNORE)
            .map { result ->
                JPAChangeEventDeltaEntity(
                    currentVehicleChangeEventId = current.id,
                    vehicleId = current.vehicleId,
                    modifiedAt = current.modifiedAt,
                    modifiedBy = current.modifiedBy,
                    field = formatFieldWithDomainId(result.field, current.domainId),
                    oldValue = result.oldValue,
                    newValue = result.newValue,
                )
            }
    }

    private fun formatFieldWithDomainId(
        field: String,
        domainId: String?,
    ): String {
        val parts = field.split(".")
        return when {
            domainId == null -> field
            parts.isEmpty() -> field
            else -> "${parts.first()}[$domainId]" + parts.drop(1).joinToString(".", prefix = ".")
        }
    }
}
