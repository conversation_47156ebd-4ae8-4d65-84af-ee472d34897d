/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclehistory.service

import com.emh.shared.s3.client.adapter.S3StorageClient
import com.emh.shared.s3.client.adapter.S3StorageClientException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.archival.job.configurations.VehicleArchiveStorageProperties
import com.fleetmanagement.modules.archival.job.service.StorageException
import com.fleetmanagement.modules.vehiclehistory.api.VehicleHistoryArchival
import com.fleetmanagement.modules.vehiclehistory.repository.JPAChangeEventDeltaRepository
import com.fleetmanagement.modules.vehiclehistory.repository.JPAChangeEventRepository
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.core.io.ByteArrayResource
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
class VehicleHistoryArchivalService(
    private val changeEventRepository: JPAChangeEventRepository,
    private val changeEventDeltaRepository: JPAChangeEventDeltaRepository,
    @Qualifier("vehicleHistoryArchiveStorageProperties") private val storageProperties: VehicleArchiveStorageProperties,
    @Qualifier("archivalObjectMapper") private val objectMapper: ObjectMapper,
    @Qualifier("vehicleHistoryArchiveStorageClient") private val s3StorageClient: S3StorageClient,
) : VehicleHistoryArchival {
    companion object {
        private val logger = LoggerFactory.getLogger(VehicleHistoryArchivalService::class.java)
    }

    private fun validateBucketConfig() {
        val bucketExists = s3StorageClient.bucketExists(storageProperties.bucket)
        if (!bucketExists) {
            throw StorageException("Bucket [${storageProperties.bucket}] does not exist.")
        }
    }

    fun deleteVehicleHistoryBy(vehicleId: UUID) {
        logger.info("Deleting vehicle_change_delta with id: $vehicleId")
        changeEventDeltaRepository.deleteAllByVehicleId(vehicleId)
        logger.info("Deleting vehicle_change_event with id: $vehicleId")
        changeEventRepository.deleteAllByVehicleId(vehicleId)
    }

    /**
     * TODO: stream events
     * Change events can be higher in numbers (more than thousands)
     * We need to be careful when we are fetch and convert the events into bytes,
     * It may overload the memory. This can probably be streamed.
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    override fun archiveVehicleHistory(vehicleId: UUID) {
        try {
            validateBucketConfig()
            val changeEventDeltas =
                changeEventDeltaRepository.findAllByVehicleIdOrderByModifiedAtDesc(vehicleId).ifEmpty {
                    throw IllegalArgumentException("Vehicle history for vehicle id $vehicleId not found")
                }

            val resource = ByteArrayResource(objectMapper.writeValueAsBytes(changeEventDeltas))
            logger.info("Uploading vehicle history data with key [$vehicleId] to S3")
            s3StorageClient.uploadResource(vehicleId.toString(), resource, storageProperties.bucket)
            logger.info("Upload vehicle history successful")

            deleteVehicleHistoryBy(vehicleId)
        } catch (exception: S3StorageClientException) {
            logger.error("Uploading vehicle history to S3 failed for vehicleId $vehicleId with error", exception)
        } catch (exception: Exception) {
            logger.error("An unexpected error encountered while archiving history for : $vehicleId", exception)
        }
    }
}
