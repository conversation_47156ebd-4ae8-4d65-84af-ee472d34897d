/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.api.dtos

data class Building(
    val name: String,
    val levels: Set<String> = emptySet(),
    val parkingLots: Set<String> = emptySet(),
)

data class Compound(
    val name: String,
    val buildings: Set<Building> = emptySet(),
    val parkingLots: Set<String> = emptySet(),
)
