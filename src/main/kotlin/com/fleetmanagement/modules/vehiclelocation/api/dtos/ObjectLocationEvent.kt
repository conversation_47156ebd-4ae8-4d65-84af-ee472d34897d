/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.api.dtos

import java.util.*

data class ObjectLocationEvent(
    val eventId: UUID,
    val occurredOn: java.time.OffsetDateTime,
    val eventSource: String,
    val eventType: String,
    val geoLocation: GeoLocation,
    val vin: String?,
    val vGuid: String?,
    val equipmentNumber: String?,
    val locationKey: String?,
    val description: String?,
    val locationName: String?,
    val building: String?,
    val level: String?,
    val parkingLot: String?,
)

data class GeoLocation(
    val latitude: Double,
    val longitude: Double,
)
