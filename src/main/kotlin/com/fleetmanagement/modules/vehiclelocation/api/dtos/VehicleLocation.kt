/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.api.dtos

import java.time.OffsetDateTime
import java.util.*

data class VehicleLocation(
    val vehicleId: UUID,
    val eventType: String,
    val compoundName: String? = null,
    val building: String? = null,
    val level: String? = null,
    val parkingLot: String? = null,
    val source: String?,
    val occurredOn: OffsetDateTime,
    val comment: String? = null,
)
