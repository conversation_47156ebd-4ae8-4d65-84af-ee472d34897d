/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.location

import com.fasterxml.jackson.databind.ObjectMapper
import jakarta.persistence.Embeddable
import com.fleetmanagement.modules.vehiclelocation.api.dtos.Building as CompoundBuilding

data class LocationNewOrUpdate(
    val locationKey: String,
    val name: String,
    val building: List<Building> = emptyList(),
    val parkingLot: List<String> = emptyList(),
    val geoLocation: GeoLocation,
)

data class Building(
    val name: String,
    val level: List<String> = emptyList(),
    val parkingLot: List<String> = emptyList(),
)

data class Location(
    val locationKey: String,
    val name: String,
    val building: List<Building> = emptyList(),
    val parkingLot: List<String> = emptyList(),
)

fun Building.toCompoundBuilding() =
    CompoundBuilding(
        name = this.name,
        levels = this.level.toSet(),
        parkingLots = this.parkingLot.toSet(),
    )

fun CompoundBuilding.toBuilding() =
    Building(
        name = this.name,
        level = this.levels.toList(),
        parkingLot = this.parkingLots.toList(),
    )

fun List<Building>.toJsonString(): String = ObjectMapper().writeValueAsString(this)

@Embeddable
data class GeoLocation(
    val latitude: Double,
    val longitude: Double,
)
