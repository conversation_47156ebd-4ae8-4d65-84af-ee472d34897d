/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.location.job

import com.fleetmanagement.modules.vehiclelocation.features.location.services.LocationService
import com.fleetmanagement.modules.vehiclelocation.features.location.services.LocationUpdateException
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.boot.context.event.ApplicationReadyEvent
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty("vehicle-location.sync-strategy", havingValue = "startup", matchIfMissing = true)
class LocationSynchronizationStartupListener(
    private val locationService: LocationService,
) {
    private val logger = LoggerFactory.getLogger(LocationSynchronizationStartupListener::class.java)

    @EventListener(ApplicationReadyEvent::class)
    fun onApplicationReadyEvent() {
        try {
            locationService.synchronizeLocations()
            logger.info("Location synchronization completed successfully.")
        } catch (e: LocationUpdateException) {
            logger.error("Failed to synchronize locations on startup", e)
        } catch (e: RuntimeException) {
            logger.error("Unexpected error occurred while synchronization locations at startup", e)
        }
    }
}
