/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclelocation.features.location.services

import com.fleetmanagement.modules.vehiclelocation.features.location.LocationNewOrUpdate
import com.fleetmanagement.modules.vehiclelocation.repository.JPALocationRepository
import com.fleetmanagement.modules.vehiclelocation.repository.entities.JPALocationEntity
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional
class CreateOrUpdateLocationService(
    private val locationRepository: JPALocationRepository,
) {
    private val logger = LoggerFactory.getLogger(CreateOrUpdateLocationService::class.java)

    fun createOrUpdateLocation(locationNewOrUpdate: LocationNewOrUpdate) {
        val existingLocation = locationRepository.findByLocationKey(locationNewOrUpdate.locationKey)

        if (existingLocation != null) {
            existingLocation.update(locationNewOrUpdate)
            logger.debug("Updated location with key ${existingLocation.locationKey}")
        } else {
            createLocation(locationNewOrUpdate)
        }
    }

    fun createLocation(locationNewOrUpdate: LocationNewOrUpdate) {
        val location =
            JPALocationEntity(
                locationKey = locationNewOrUpdate.locationKey,
                name = locationNewOrUpdate.name,
                building = locationNewOrUpdate.building,
                parkingLot = locationNewOrUpdate.parkingLot,
                geoLocation = locationNewOrUpdate.geoLocation,
            )

        locationRepository.save(location)
        logger.debug("Created location with key ${locationNewOrUpdate.locationKey}")
    }
}
