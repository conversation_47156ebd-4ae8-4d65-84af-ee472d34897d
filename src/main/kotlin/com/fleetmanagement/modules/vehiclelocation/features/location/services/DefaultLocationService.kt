/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclelocation.features.location.services

import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationException
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.client.LocationsWebClient
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.LocationDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.LocationPageDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.LocationSortByDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.SortOrderDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.exception.VehicleLocationServiceUnreachableException
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.LocationUpdatedEventDto
import org.slf4j.LoggerFactory
import org.springframework.dao.DataAccessException
import org.springframework.stereotype.Component

@Component
class DefaultLocationService(
    private val locationsWebClient: LocationsWebClient,
    private val createOrUpdateLocationService: CreateOrUpdateLocationService,
    private val deleteLocationService: DeleteLocationService,
) : LocationService {
    private val logger = LoggerFactory.getLogger(DefaultLocationService::class.java)

    override fun updateLocation(locationUpdatedEventDto: LocationUpdatedEventDto) {
        createOrUpdateLocationService.createOrUpdateLocation(
            locationUpdatedEventDto.toLocationNewOrUpdate(),
        )
    }

    override fun deleteLocation(key: String) {
        deleteLocationService.deleteLocation(key)
    }

    override fun synchronizeLocations() {
        val allKeys = mutableSetOf<String>()
        val firstResponse = getLocationsFromVehicleLocationService(DEFAULT_START_PAGE)

        allKeys.addAll(firstResponse.elements.map { it.key })
        createOrUpdateLocationsForSyncProcess(firstResponse.elements)

        val totalPages = firstResponse.totalPageCount

        for (page in 1 until totalPages) {
            try {
                // exceptions in subsequent calls to vehicle location should not stop the process
                val locationResponse = getLocationsFromVehicleLocationService(page)
                allKeys.addAll(locationResponse.elements.map { it.key })
                createOrUpdateLocationsForSyncProcess(locationResponse.elements)
            } catch (e: LocationSynchronizationException) {
                logger.error("Synchronization error on page $page", e)
            }
        }
        deleteLocationService.deleteIfNotFoundInSetOf(allKeys)
    }

    fun createOrUpdateLocationsForSyncProcess(locations: List<LocationDto>) {
        if (locations.isEmpty()) {
            return
        }
        locations.forEach { locationDto ->
            try {
                createOrUpdateLocationService.createOrUpdateLocation(locationDto.toLocationNewOrUpdate())
            } catch (e: DataAccessException) {
                logger.error("Error creating or updating location for key ${locationDto.key}", e)
            }
        }
    }

    private fun getLocationsFromVehicleLocationService(page: Int): LocationPageDto {
        try {
            return locationsWebClient.getLocationPageDto(
                page = page,
                pageSize = PAGE_SIZE,
                sortOrder = SortOrderDto.ASC,
                sortBy = listOf(LocationSortByDto.NAME),
            )
        } catch (e: VehicleLocationServiceUnreachableException) {
            val message =
                "Locations synchronization failed because vehicle location not reachable, " +
                    "error while processing page $page"
            logger.error(message, e)
            throw LocationSynchronizationException(message, e)
        } catch (e: VehicleLocationException) {
            logger.error("Locations synchronization failed on page $page", e)
            throw LocationSynchronizationException("Locations synchronization failed on page $page", e)
        }
    }

    companion object {
        private const val PAGE_SIZE = 100
        private const val DEFAULT_START_PAGE = 0
    }
}
