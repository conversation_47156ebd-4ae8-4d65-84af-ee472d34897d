/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclelocation.features.location.services

import com.fleetmanagement.modules.vehiclelocation.repository.JPALocationRepository
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional

@Component
@Transactional
class DeleteLocationService(
    private val locationRepository: JPALocationRepository,
) {
    private val logger = LoggerFactory.getLogger(DeleteLocationService::class.java)

    fun deleteIfNotFoundInSetOf(keys: Set<String>) {
        val locationsForDeleting = locationRepository.findByLocationKeyNotIn(keys)
        locationsForDeleting.forEach { deleteLocation(it.locationKey) }
    }

    fun deleteLocation(key: String) {
        locationRepository.deleteByLocationKey(key)
        logger.debug("Location with locationKey: $key has been deleted")
    }
}
