/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.location.services

import com.fleetmanagement.modules.vehiclelocation.features.location.Building
import com.fleetmanagement.modules.vehiclelocation.features.location.GeoLocation
import com.fleetmanagement.modules.vehiclelocation.features.location.LocationNewOrUpdate
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.LocationDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.LocationUpdatedEventDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.BuildingDto as BuildingDtoApi
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.GeoLocationDto as GeoLocationDtoApi
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.BuildingDto as BuildingDtoStreamzilla
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.GeoLocationDto as GeoLocationDtoStreamzilla

fun LocationUpdatedEventDto.toLocationNewOrUpdate(): LocationNewOrUpdate =
    LocationNewOrUpdate(
        locationKey = key,
        name = this.name,
        building = this.building?.map { it.toBuilding() } ?: emptyList(),
        parkingLot = this.parkingLot ?: emptyList(),
        geoLocation = this.geoLocation.toGeolocation(),
    )

fun BuildingDtoStreamzilla.toBuilding() =
    Building(
        name = this.name,
        level = this.level ?: emptyList(),
        parkingLot = this.parkingLot ?: emptyList(),
    )

fun GeoLocationDtoStreamzilla.toGeolocation() =
    GeoLocation(
        longitude = this.longitude,
        latitude = this.latitude,
    )

fun LocationDto.toLocationNewOrUpdate(): LocationNewOrUpdate =
    LocationNewOrUpdate(
        locationKey = this.key,
        name = this.name,
        building = this.building?.map { it.toBuilding() } ?: emptyList(),
        parkingLot = this.parkingLot ?: emptyList(),
        geoLocation = this.geoLocation.toGeolocation(),
    )

fun BuildingDtoApi.toBuilding() =
    Building(
        name = this.name,
        level = this.level ?: emptyList(),
        parkingLot = this.parkingLot ?: emptyList(),
    )

fun GeoLocationDtoApi.toGeolocation() =
    GeoLocation(
        longitude = this.longitude,
        latitude = this.latitude,
    )
