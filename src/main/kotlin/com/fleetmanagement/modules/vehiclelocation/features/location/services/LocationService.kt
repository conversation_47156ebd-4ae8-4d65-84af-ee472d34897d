/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.location.services

import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.LocationUpdatedEventDto

interface LocationService {
    fun deleteLocation(key: String)

    fun updateLocation(locationUpdatedEventDto: LocationUpdatedEventDto)

    fun synchronizeLocations()
}
