/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.location.services

open class LocationUpdateException(
    message: String?,
    cause: Throwable?,
) : RuntimeException(message, cause)

class LocationSynchronizationException(
    message: String?,
    cause: Throwable?,
) : LocationUpdateException(message = message, cause = cause)

class LocationNotFoundException(
    message: String?,
) : LocationUpdateException(message = message, cause = null)
