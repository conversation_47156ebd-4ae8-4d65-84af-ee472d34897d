package com.fleetmanagement.modules.vehiclelocation.features.location.services

import com.fleetmanagement.modules.vehiclelocation.api.FVMCompounds
import com.fleetmanagement.modules.vehiclelocation.api.dtos.Compound
import com.fleetmanagement.modules.vehiclelocation.features.location.toCompoundBuilding
import com.fleetmanagement.modules.vehiclelocation.repository.JPALocationRepository
import org.springframework.stereotype.Component

@Component
class RetrieveLocationService(
    private val jpaLocationRepository: JPALocationRepository,
) : FVMCompounds {
    override fun getKnownCompounds(): List<Compound> {
        val locationOfInterest =
            jpaLocationRepository.findAllByIsLocationOfInterestTrue()
        return locationOfInterest.map { jpaLocationEntity ->
            Compound(
                name = jpaLocationEntity.name,
                buildings =
                    jpaLocationEntity.building.map { it.toCompoundBuilding() }.toSet(),
                parkingLots = jpaLocationEntity.parkingLot.toSet(),
            )
        }
    }
}
