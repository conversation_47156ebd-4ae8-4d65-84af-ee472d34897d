/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.locationhistory

import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.ObjectLocationEventDto
import java.util.*

fun ObjectLocationEventDto.toVehicleLocation(
    vehicleId: UUID,
    name: String,
): VehicleLocation =
    VehicleLocation(
        vehicleId = vehicleId,
        source = this.eventSource,
        occurredOn = this.occurredOn,
        eventType = this.eventType,
        compoundName = name,
        building = this.building,
        level = this.level,
        parkingLot = this.parkingLot,
        comment = this.description,
    )
