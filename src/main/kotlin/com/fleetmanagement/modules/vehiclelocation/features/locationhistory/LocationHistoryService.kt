/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.locationhistory

import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehiclelocation.api.LocationHistory
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehiclelocation.api.exception.LocationHistoryException
import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationException
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.client.ObjectLocationEventsWebClient
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.ObjectLocationEventDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.SortOrderDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.exception.VehicleLocationServiceUnreachableException
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.stereotype.Component
import java.util.*

@Component
class LocationHistoryService(
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val objectLocationEventsWebClient: ObjectLocationEventsWebClient,
    @Qualifier("locationHistoryLocationService") private val locationService: LocationService,
) : LocationHistory {
    companion object {
        private val logger = LoggerFactory.getLogger(LocationHistoryService::class.java)
        const val PAGE_SIZE = 1000
        const val PAGE = 0
    }

    override fun getLocationHistoryBy(vehicleId: UUID): List<VehicleLocation> {
        val vehicleDTO =
            readVehicleByVehicleId.readVehicleById(vehicleId)
                ?: throw IllegalArgumentException("Vehicle by vehicleId $vehicleId not found")
        val equipmentNumber = vehicleDTO.equipmentNumber
        val vin = vehicleDTO.vin
        val vGuid = vehicleDTO.vguid

        val listObjectLocationEvents = getObjectLocationsEvents(equipmentNumber, vin, vGuid)

        val locationKeys = listObjectLocationEvents.mapNotNull { it.locationKey }.toSet()
        val locationKeyMap = locationService.getLocationKeyMap(locationKeys)

        return listObjectLocationEvents.mapNotNull { objectLocationEventDto ->
            locationKeyMap[objectLocationEventDto.locationKey]?.let { jpaLocationEntity ->
                objectLocationEventDto.toVehicleLocation(
                    vehicleId,
                    jpaLocationEntity.name,
                )
            }
        }
    }

    fun getObjectLocationsEvents(
        equipmentNumber: Long?,
        vin: String?,
        vGuid: String?,
    ): List<ObjectLocationEventDto> {
        val response =
            try {
                objectLocationEventsWebClient.getObjectLocationEvents(
                    page = PAGE,
                    pageSize = PAGE_SIZE,
                    sortOrder = SortOrderDto.DESC,
                    equipmentNumber = equipmentNumber?.toString(),
                    vin = vin,
                    vGuid = vGuid,
                )
            } catch (e: VehicleLocationServiceUnreachableException) {
                val message =
                    "Fetching object location events failed because vehicle location not reachable, " +
                        "error while processing request"
                logger.error(message, e)
                throw LocationHistoryException(message, e)
            } catch (e: VehicleLocationException) {
                logger.error("Fetching object location events failed", e)
                throw LocationHistoryException("Fetching object location events failed", e)
            }
        return response.elements
    }
}
