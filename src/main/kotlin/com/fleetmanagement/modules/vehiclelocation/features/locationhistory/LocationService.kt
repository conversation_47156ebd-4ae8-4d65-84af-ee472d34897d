/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.locationhistory

import com.fleetmanagement.modules.vehiclelocation.repository.JPALocationRepository
import com.fleetmanagement.modules.vehiclelocation.repository.entities.JPALocationEntity
import org.springframework.stereotype.Component

@Component(value = "locationHistoryLocationService")
class LocationService(
    private val locationRepository: JPALocationRepository,
) {
    fun getLocationKeyMap(locationKeys: Set<String>): Map<String, JPALocationEntity> =
        locationRepository.findByLocationKeyIn(locationKeys).associateBy { it.locationKey }
}
