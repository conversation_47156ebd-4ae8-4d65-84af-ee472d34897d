/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.readlastknownlocation

import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehiclelocation.repository.entities.JPALastKnownLocationEntity

fun JPALastKnownLocationEntity.toVehicleLocation() =
    VehicleLocation(
        vehicleId = this.vehicleId,
        eventType = this.eventType,
        compoundName = this.locationName,
        building = this.building,
        level = this.level,
        parkingLot = this.parkingLot,
        source = this.eventSource,
        occurredOn = this.occurredOn,
        comment = this.comment,
    )
