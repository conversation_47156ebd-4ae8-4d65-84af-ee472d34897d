/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.readlastknownlocation

import com.fleetmanagement.modules.vehiclelocation.api.LastKnownLocation
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehiclelocation.repository.JPALastKnownLocationRepository
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Transactional(readOnly = true)
@Component
class ReadLastKnownLocationService(
    private val lastKnownLocationRepository: JPALastKnownLocationRepository,
) : LastKnownLocation {
    override fun findLastKnownVehicleLocationBy(vehicleId: UUID): VehicleLocation? =
        lastKnownLocationRepository.findByVehicleId(vehicleId)?.toVehicleLocation()
}
