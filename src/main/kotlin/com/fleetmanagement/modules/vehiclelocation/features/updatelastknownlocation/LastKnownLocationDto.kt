/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation

import com.fleetmanagement.modules.vehiclelocation.api.dtos.GeoLocation
import jakarta.persistence.Embeddable

data class LastKnownLocationNewOrUpdate(
    val locationKey: String?,
    val locationName: String?,
    val occurredOn: java.time.OffsetDateTime,
    val eventSource: String,
    val eventType: String,
    val building: String?,
    val level: String?,
    val parkingLot: String?,
    val description: String?,
    val geoLocation: GeoLocation,
)

@Embeddable
data class GeoLocation(
    val latitude: Double,
    val longitude: Double,
) {
    init {
        require(latitude in -90.0..90.0) { "Invalid latitude: $latitude. Must be between -90.0 and 90.0." }
        require(longitude in -180.0..180.0) { "Invalid longitude: $longitude. Must be between -180.0 and 180.0." }
    }
}
