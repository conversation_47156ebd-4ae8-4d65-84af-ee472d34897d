/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation
import com.fleetmanagement.modules.vehiclelocation.api.dtos.ObjectLocationEvent

fun interface ObjectLocationUpdated {
    fun updateObjectLocation(objectLocationEvent: ObjectLocationEvent)
}
