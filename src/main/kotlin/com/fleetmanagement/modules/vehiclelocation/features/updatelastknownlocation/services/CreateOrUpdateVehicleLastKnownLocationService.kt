/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.services

import com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.GeoLocation
import com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.LastKnownLocationNewOrUpdate
import com.fleetmanagement.modules.vehiclelocation.repository.JPALastKnownLocationRepository
import com.fleetmanagement.modules.vehiclelocation.repository.entities.JPALastKnownLocationEntity
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Transactional
import java.util.*

@Component
@Transactional
class CreateOrUpdateVehicleLastKnownLocationService(
    private val lastKnownLocationRepository: JPALastKnownLocationRepository,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(CreateOrUpdateVehicleLastKnownLocationService::class.java)
    }

    fun updateLastKnownLocation(
        vehicleId: UUID,
        lastKnownLocationNewOrUpdate: LastKnownLocationNewOrUpdate,
    ) {
        val actualLastKnownLocation = lastKnownLocationRepository.findByVehicleId(vehicleId)
        if (actualLastKnownLocation == null) {
            save(vehicleId, lastKnownLocationNewOrUpdate)
            logger.info("LastKnownLocation does not yet exist in for vehicle, creating")
            return
        }

        lastKnownLocationRepository
            .findFirstByVehicleIdAndOccurredOnIsBefore(
                vehicleId,
                lastKnownLocationNewOrUpdate.occurredOn,
            )?.let {
                it.update(lastKnownLocationNewOrUpdate)
                logger.info("updating last know location for ${it.vehicleId}")
            }
    }

    private fun save(
        vehicleId: UUID,
        lastKnownLocationNewOrUpdate: LastKnownLocationNewOrUpdate,
    ) {
        val updatedLastKnownLocation =
            JPALastKnownLocationEntity(
                vehicleId = vehicleId,
                occurredOn = lastKnownLocationNewOrUpdate.occurredOn,
                eventSource = lastKnownLocationNewOrUpdate.eventSource,
                eventType = lastKnownLocationNewOrUpdate.eventType,
                locationKey = lastKnownLocationNewOrUpdate.locationKey,
                locationName = lastKnownLocationNewOrUpdate.locationName,
                building = lastKnownLocationNewOrUpdate.building,
                level = lastKnownLocationNewOrUpdate.level,
                parkingLot = lastKnownLocationNewOrUpdate.parkingLot,
                comment = lastKnownLocationNewOrUpdate.description,
                geoLocation = lastKnownLocationNewOrUpdate.geoLocation.toGeoLocation(),
            )
        lastKnownLocationRepository.save(updatedLastKnownLocation)
    }
}

fun com.fleetmanagement.modules.vehiclelocation.api.dtos.GeoLocation.toGeoLocation(): GeoLocation =
    GeoLocation(
        longitude = this.longitude,
        latitude = this.latitude,
    )
