/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.services

import com.fleetmanagement.modules.vehiclelocation.features.location.services.LocationNotFoundException
import com.fleetmanagement.modules.vehiclelocation.repository.JPALocationRepository
import com.fleetmanagement.modules.vehiclelocation.repository.entities.JPALocationEntity
import org.springframework.stereotype.Component

@Component
class LocationService(
    private val locationRepository: JPALocationRepository,
) {
    fun getMatchingLocationByCompoundName(compoundName: String): JPALocationEntity =
        locationRepository.findByName(compoundName)
            ?: throw LocationNotFoundException("location name:$compoundName does not exist")
}
