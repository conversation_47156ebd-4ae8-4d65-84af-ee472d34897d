/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.services

import com.fleetmanagement.modules.vehiclelocation.api.dtos.ObjectLocationEvent
import com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.LastKnownLocationNewOrUpdate
import com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.ObjectLocationUpdated
import org.springframework.stereotype.Component
import java.util.*

@Component
class UpdateLastKnownLocation(
    private val vehicleResolutionService: VehicleResolutionService,
    private val createOrUpdateVehicleLastKnownLocationService: CreateOrUpdateVehicleLastKnownLocationService,
) : ObjectLocationUpdated {
    override fun updateObjectLocation(objectLocationEvent: ObjectLocationEvent) {
        val vehicle = vehicleResolutionService.resolveFrom(objectLocationEvent)

        createOrUpdateVehicleLastKnownLocationService.updateLastKnownLocation(
            vehicleId = vehicle.id,
            lastKnownLocationNewOrUpdate = objectLocationEvent.toLastKnownLocationNewOrUpdate(),
        )
    }
}

private fun ObjectLocationEvent.toLastKnownLocationNewOrUpdate() =
    LastKnownLocationNewOrUpdate(
        locationKey = locationKey,
        locationName = locationName,
        occurredOn = occurredOn,
        eventSource = eventSource,
        eventType = eventType,
        building = building,
        level = level,
        parkingLot = parkingLot,
        geoLocation = geoLocation,
        description = description,
    )
