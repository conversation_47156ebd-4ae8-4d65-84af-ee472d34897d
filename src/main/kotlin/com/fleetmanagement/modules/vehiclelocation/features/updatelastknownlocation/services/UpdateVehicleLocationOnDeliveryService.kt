/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.services

import com.fleetmanagement.modules.vehiclelocation.api.LastKnownLocation
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationValidationException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferDeliveredEvent
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Component
import org.springframework.transaction.annotation.Propagation
import org.springframework.transaction.annotation.Transactional
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener
import java.time.OffsetDateTime

/**
 * This service will handle [VehicleTransferDeliveredEvent] and create 'out'-events for each delivered vehicle
 */
@Component
class UpdateVehicleLocationOnDeliveryService(
    private val vehicleLocationEventService: VehicleLocationEventService,
    private val lastKnownLocationService: LastKnownLocation,
) {
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun handle(event: VehicleTransferDeliveredEvent) {
        val currentLocation = lastKnownLocationService.findLastKnownVehicleLocationBy(vehicleId = event.vehicleId)

        if (null == currentLocation) {
            log.warn("Vehicle with id [$event.vehicleId] does not have a current location set.")
            return
        }
        if (OUT == currentLocation.eventType) {
            log.warn("Vehicle with id [$event.vehicleId] already has an OUT event for its current location.")
            return
        }

        val vehicleOutLocation =
            VehicleLocation(
                vehicleId = event.vehicleId,
                eventType = OUT,
                compoundName = currentLocation.compoundName,
                parkingLot = currentLocation.parkingLot,
                level = currentLocation.level,
                building = currentLocation.building,
                occurredOn = OffsetDateTime.now(),
                source = null, // set to MANUAL in VLS
                comment = currentLocation.comment,
            )
        try {
            vehicleLocationEventService.addLocationEvent(vehicleOutLocation)
        } catch (exception: VehicleLocationValidationException) {
            log.warn(
                "Error while trying to create location OUT event for delivery of vehicle with id [${event.vehicleId}].",
                exception,
            )
        }
    }

    companion object {
        private const val OUT = "OUT"
        private val log = LoggerFactory.getLogger(UpdateVehicleLocationOnDeliveryService::class.java)
    }
}
