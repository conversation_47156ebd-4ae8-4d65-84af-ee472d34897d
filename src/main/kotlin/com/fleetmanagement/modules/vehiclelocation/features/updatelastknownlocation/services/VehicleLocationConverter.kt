/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.services

import com.fleetmanagement.modules.vehiclelocation.api.dtos.GeoLocation
import com.fleetmanagement.modules.vehiclelocation.api.dtos.ObjectLocationEvent
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.EventTypeDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.GeoLocationDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.ObjectLocationEventDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.ObjectLocationEventNewDto

fun VehicleLocation.toObjectLocationEventNewDto(
    vin: String?,
    vGuid: String?,
    equipmentNumber: String?,
    locationKey: String,
    geoLocation: GeoLocationDto,
) = ObjectLocationEventNewDto(
    vin = vin,
    vGuid = vGuid,
    equipmentNumber = equipmentNumber,
    locationName = this.compoundName,
    eventType = EventTypeDto.forValue(this.eventType),
    description = this.comment,
    geoLocation = geoLocation,
    locationKey = locationKey,
    occurredOn = this.occurredOn,
    building = this.building,
    level = this.level,
    parkingLot = this.parkingLot,
)

fun ObjectLocationEventDto.toObjectLocationEvent(): ObjectLocationEvent =
    ObjectLocationEvent(
        eventId = this.eventId,
        occurredOn = this.occurredOn,
        eventSource = this.eventSource,
        eventType = this.eventType,
        geoLocation = this.geoLocation.toGeoLocation(),
        vin = this.vin,
        vGuid = this.vGuid,
        equipmentNumber = this.equipmentNumber,
        locationKey = this.locationKey,
        description = this.description,
        locationName = this.locationName,
        building = this.building,
        level = this.level,
        parkingLot = this.parkingLot,
    )

fun GeoLocationDto.toGeoLocation(): GeoLocation =
    GeoLocation(
        latitude = this.latitude,
        longitude = this.longitude,
    )

fun com.fleetmanagement.modules.vehiclelocation.features.location.GeoLocation.toGeoLocationDto(): GeoLocationDto =
    GeoLocationDto(
        latitude = latitude,
        longitude = longitude,
    )
