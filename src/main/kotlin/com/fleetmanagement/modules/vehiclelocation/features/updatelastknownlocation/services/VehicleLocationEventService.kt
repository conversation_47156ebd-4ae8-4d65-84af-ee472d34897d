/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.services

import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehiclelocation.api.VehicleLocationEvent
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationException
import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationValidationException
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.client.ObjectLocationEventWebClient
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.ObjectLocationEventNewDto
import com.fleetmanagement.modules.vehiclelocation.repository.entities.JPALocationEntity
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.WebClientResponseException

@Component
class VehicleLocationEventService(
    private val locationService: LocationService,
    private val objectLocationEventWebClient: ObjectLocationEventWebClient,
    private val readVehicleByVehicleId: ReadVehicleByVehicleId,
    private val updateLastKnownLocation: UpdateLastKnownLocation,
) : VehicleLocationEvent {
    override fun addLocationEvent(vehicleLocation: VehicleLocation) {
        if (vehicleLocation.compoundName == null) {
            throw VehicleLocationException("Compound name cannot be null or empty", null)
        }
        val location = locationService.getMatchingLocationByCompoundName(vehicleLocation.compoundName)

        val vehicle = readVehicleByVehicleId.readVehicleById(vehicleLocation.vehicleId)
        checkNotNull(vehicle) { "give vehicle id ${vehicleLocation.vehicleId} is invalid" }

        val locationEventDto = createLocationEventDto(vehicleLocation, vehicle, location)

        try {
            val response =
                objectLocationEventWebClient.createVehicleLocationEvent(locationEventDto).body
                    ?: throw VehicleLocationException("Could not get a response", null)

            updateLastKnownLocation.updateObjectLocation(response.toObjectLocationEvent())
        } catch (e: WebClientResponseException.UnprocessableEntity) {
            val errorMessage = e.responseBodyAsString
            throw VehicleLocationValidationException(errorMessage, e)
        }
    }

    private fun createLocationEventDto(
        vehicleLocation: VehicleLocation,
        vehicle: VehicleDTO,
        location: JPALocationEntity,
    ): ObjectLocationEventNewDto =
        vehicleLocation.toObjectLocationEventNewDto(
            vin = vehicle.vin,
            vGuid = vehicle.vguid,
            equipmentNumber = vehicle.equipmentNumber.toString(),
            locationKey = location.locationKey,
            geoLocation = location.geoLocation.toGeoLocationDto(),
        )
}
