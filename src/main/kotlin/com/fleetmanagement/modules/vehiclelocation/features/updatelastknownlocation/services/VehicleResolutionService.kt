/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.services

import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVGUID
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.api.dtos.VehicleDTO
import com.fleetmanagement.modules.vehiclelocation.api.dtos.ObjectLocationEvent
import org.springframework.stereotype.Component

@Component
class VehicleResolutionService(
    private val readVehicleByVGUID: ReadVehicleByVGUID,
    private val readVehicleByVIN: ReadVehicleByVIN,
) {
    fun resolveFrom(event: ObjectLocationEvent): VehicleDTO {
        val vin = event.vin
        val vGuid = event.vGuid
        try {
            if (vin != null) {
                return readVehicleByVIN.readVehicleByVIN(vin)
            }
            if (vGuid != null) {
                return readVehicleByVGUID.readVehicleByVGUID(vGuid)
            }
            throw NoValidIDInLocationEvent()
        } catch (e: Exception) {
            throw VehicleCannotBeResolved("Cannot resolve vehicle-id from vehicle-data module", e)
        }
    }
}

class VehicleCannotBeResolved(
    message: String,
    cause: Throwable? = null,
) : RuntimeException(message, cause)

class NoValidIDInLocationEvent : RuntimeException()
