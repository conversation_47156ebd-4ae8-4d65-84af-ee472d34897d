package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.client

import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.LocationPageDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.LocationSortByDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.SortOrderDto
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange

interface LocationsWebClient {
    @GetExchange(
        url = "/api/locations",
        accept = ["application/json"],
    )
    fun getLocationPageDto(
        @RequestParam(value = "page", required = false, defaultValue = "0") page: Int = 0,
        @RequestParam(value = "pageSize", required = false) pageSize: Int? = null,
        @RequestParam(value = "sortOrder", required = false) sortOrder: SortOrderDto? = null,
        @RequestParam(value = "sortBy", required = false) sortBy: List<LocationSortByDto>? = null,
    ): LocationPageDto
}
