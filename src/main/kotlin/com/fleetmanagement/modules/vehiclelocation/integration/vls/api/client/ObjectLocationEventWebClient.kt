package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.client

import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.ObjectLocationEventDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.ObjectLocationEventNewDto
import org.springframework.http.ResponseEntity
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.service.annotation.PostExchange

interface ObjectLocationEventWebClient {
    @PostExchange(
        url = "api/object-location-events",
        accept = ["application/json"],
    )
    fun createVehicleLocationEvent(
        @RequestBody body: ObjectLocationEventNewDto,
    ): ResponseEntity<ObjectLocationEventDto>
}
