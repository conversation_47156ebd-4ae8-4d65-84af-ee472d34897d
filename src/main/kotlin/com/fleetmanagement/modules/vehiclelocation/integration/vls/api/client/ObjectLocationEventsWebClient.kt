/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.client

import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.ObjectLocationEventPageDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto.SortOrderDto
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.service.annotation.GetExchange

interface ObjectLocationEventsWebClient {
    @GetExchange(
        url = "api/object-location-events",
        accept = ["application/json"],
    )
    fun getObjectLocationEvents(
        @RequestParam(value = "page", required = false, defaultValue = "0") page: Int = 0,
        @RequestParam(value = "pageSize", required = false) pageSize: Int? = null,
        @RequestParam(value = "sortOrder", required = false) sortOrder: SortOrderDto? = null,
        @RequestParam(value = "equipmentNumber", required = false) equipmentNumber: String? = null,
        @RequestParam(value = "vin", required = false) vin: String? = null,
        @RequestParam(value = "vGuid", required = false) vGuid: String? = null,
    ): ObjectLocationEventPageDto
}
