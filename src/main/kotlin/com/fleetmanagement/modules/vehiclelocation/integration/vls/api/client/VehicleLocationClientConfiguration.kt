package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.client

import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.security.oauth2.client.*
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.InMemoryReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.registration.ReactiveClientRegistrationRepository
import org.springframework.security.oauth2.client.web.reactive.function.client.ServerOAuth2AuthorizedClientExchangeFilterFunction
import org.springframework.security.oauth2.client.web.server.AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository
import org.springframework.security.oauth2.client.web.server.ServerOAuth2AuthorizedClientRepository
import org.springframework.web.reactive.function.client.WebClient
import org.springframework.web.reactive.function.client.support.WebClientAdapter
import org.springframework.web.service.invoker.HttpServiceProxyFactory

@Configuration
class VehicleLocationClientConfiguration {
    @Bean
    @Qualifier("vehicleLocation")
    fun reactiveClientRegistrationRepository(
        clientRegistrationRepository: ClientRegistrationRepository,
    ): ReactiveClientRegistrationRepository =
        InMemoryReactiveClientRegistrationRepository(
            clientRegistrationRepository.findByRegistrationId(CLIENT_REGISTRATION_ID),
        )

    @Bean
    @Qualifier("vehicleLocation")
    fun reactiveOAuth2AuthorizedClientService(
        @Qualifier("vehicleLocation") clientRegistrationRepository: ReactiveClientRegistrationRepository,
    ): ReactiveOAuth2AuthorizedClientService = InMemoryReactiveOAuth2AuthorizedClientService(clientRegistrationRepository)

    @Bean
    @Qualifier("vehicleLocation")
    fun reactiveAuthorizedClientRepository(
        @Qualifier("vehicleLocation") authorizedClientService: ReactiveOAuth2AuthorizedClientService,
    ): ServerOAuth2AuthorizedClientRepository = AuthenticatedPrincipalServerOAuth2AuthorizedClientRepository(authorizedClientService)

    @Bean
    @Qualifier("vehicleLocation")
    fun reactiveOAuth2AuthorizedClientManager(
        @Qualifier("vehicleLocation") clientRegistrationRepository: ReactiveClientRegistrationRepository,
        @Qualifier("vehicleLocation") authorizedClientService: ReactiveOAuth2AuthorizedClientService,
    ): ReactiveOAuth2AuthorizedClientManager =
        AuthorizedClientServiceReactiveOAuth2AuthorizedClientManager(
            clientRegistrationRepository,
            authorizedClientService,
        ).apply {
            setAuthorizedClientProvider(
                ClientCredentialsReactiveOAuth2AuthorizedClientProvider(),
            )
        }

    @Bean
    @Qualifier("vehicleLocation")
    fun vehicleServiceLocationHttpServiceProxyFactory(
        @Value("\${vehicle-location.base-url}") baseUrl: String,
        @Qualifier("vehicleLocation") authorizedClientManager: ReactiveOAuth2AuthorizedClientManager,
        vehicleLocationExceptionHandler: VehicleLocationExceptionHandler,
    ): HttpServiceProxyFactory {
        val oauth2Filter = ServerOAuth2AuthorizedClientExchangeFilterFunction(authorizedClientManager)
        oauth2Filter.setDefaultClientRegistrationId(CLIENT_REGISTRATION_ID)

        val webClient =
            WebClient
                .builder()
                .baseUrl(baseUrl)
                .filter(vehicleLocationExceptionHandler::clientErrorRequestProcessor)
                .filter(oauth2Filter)
                .build()

        val adapter = WebClientAdapter.create(webClient)

        return HttpServiceProxyFactory.builderFor(adapter).build()
    }

    @Bean
    @Qualifier("locationsWebClient")
    fun locationsWebClient(
        @Qualifier("vehicleLocation") vehicleServiceLocationHttpServiceProxyFactory: HttpServiceProxyFactory,
    ): LocationsWebClient = vehicleServiceLocationHttpServiceProxyFactory.createClient(LocationsWebClient::class.java)

    @Bean
    @Qualifier("objectLocationEvent")
    fun objectLocationEventWebClient(
        @Qualifier("vehicleLocation") vehicleServiceLocationHttpServiceProxyFactory: HttpServiceProxyFactory,
    ): ObjectLocationEventWebClient = vehicleServiceLocationHttpServiceProxyFactory.createClient(ObjectLocationEventWebClient::class.java)

    @Bean
    @Qualifier("objectLocationEventsWebClient")
    fun objectLocationEventsWebClient(
        vehicleServiceLocationHttpServiceProxyFactory: HttpServiceProxyFactory,
    ): ObjectLocationEventsWebClient = vehicleServiceLocationHttpServiceProxyFactory.createClient(ObjectLocationEventsWebClient::class.java)

    companion object {
        const val CLIENT_REGISTRATION_ID = "vehicle-location"
    }
}
