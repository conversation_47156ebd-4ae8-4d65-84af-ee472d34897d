package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.client

import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationException
import com.fleetmanagement.modules.vehiclelocation.integration.vls.api.exception.VehicleLocationServiceUnreachableException
import org.slf4j.LoggerFactory
import org.springframework.security.oauth2.client.ClientAuthorizationException
import org.springframework.security.oauth2.core.OAuth2AuthorizationException
import org.springframework.stereotype.Component
import org.springframework.web.reactive.function.client.ClientRequest
import org.springframework.web.reactive.function.client.ClientResponse
import org.springframework.web.reactive.function.client.ExchangeFunction
import org.springframework.web.reactive.function.client.WebClientResponseException
import reactor.core.publisher.Mono
import java.net.ConnectException

@Component
class VehicleLocationExceptionHandler {
    /** A request error processor. Deals with connection issues nad may handle automatic retry in the future. */
    fun clientErrorRequestProcessor(
        request: ClientRequest,
        next: ExchangeFunction,
    ): Mono<ClientResponse> =
        next.exchange(request).onErrorMap {
            when (it.cause) {
                is ConnectException -> {
                    log.error("Error during request. Target not reachable.", it.cause)
                    VehicleLocationServiceUnreachableException("Error during request. Target not reachable.", it.cause)
                }
                is ClientAuthorizationException -> {
                    log.error("Error during authorization.", it.cause)
                    VehicleLocationException("Error during authorization.", it.cause)
                }
                is OAuth2AuthorizationException -> {
                    log.error("Error during authorization.${it.message}", it.cause)
                    VehicleLocationException("Error during authorization.${it.message}", it.cause)
                }
                is WebClientResponseException -> {
                    log.error("Error during vehicle location request. ${it.message}", it.cause)
                    VehicleLocationException("Error during vehicle location request. ${it.message}", it.cause)
                }
                else -> it
            }
        }

    companion object {
        private val log = LoggerFactory.getLogger(VehicleLocationExceptionHandler::class.java)
    }
}
