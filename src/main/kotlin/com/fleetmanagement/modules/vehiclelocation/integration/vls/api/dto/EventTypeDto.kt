/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto

import com.fasterxml.jackson.annotation.JsonCreator
import com.fasterxml.jackson.annotation.JsonValue

/**
 * the type of an event IN, OUT will for example supplied by PVTM (incoming and outgoing objects from a compound) use DIRECT when declaring a location like \"my vehicle is there right now\"
 * Values: IN,OUT,DIRECT
 */
enum class EventTypeDto(
    @get:JsonValue val value: String,
) {
    IN("IN"),
    OUT("OUT"),
    DIRECT("DIRECT"),
    ;

    companion object {
        @JvmStatic
        @JsonCreator
        fun forValue(value: String): EventTypeDto = values().first { it -> it.value == value }
    }
}
