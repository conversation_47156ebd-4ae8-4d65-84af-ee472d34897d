/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * a geolocation, represented by using longitude and latitude
 * @param longitude
 * @param latitude
 */
data class GeoLocationDto(
    @get:JsonProperty("longitude", required = true) val longitude: kotlin.Double,
    @get:JsonProperty("latitude", required = true) val latitude: kotlin.Double,
)
