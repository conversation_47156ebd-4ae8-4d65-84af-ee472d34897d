/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * a single named location
 *
 * @param key a unique identifier for this location
 * @param name a human readable name for this location
 * @param geoLocation
 * @param etag The referenced etag of the resource. Has to match the current etag/version of the resource when used
 *   within PUT or PATCH.
 * @param building
 * @param parkingLot optional information identifying parking lot range, if this location refers to one
 * @param pvtmCompoundId dedicated compoundId property for PVTM supplied compounds.
 */
data class LocationDto(
    @get:JsonProperty("key", required = true) val key: String,
    @get:JsonProperty("name", required = true) val name: String,
    @get:JsonProperty("geoLocation", required = true) val geoLocation: GeoLocationDto,
    @get:JsonProperty("etag", required = true) val etag: String,
    @get:JsonProperty("building") val building: kotlin.collections.List<BuildingDto>? = null,
    @get:JsonProperty("parkingLot") val parkingLot: kotlin.collections.List<String>? = null,
    @get:JsonProperty("pvtmCompoundId") val pvtmCompoundId: String? = null,
)
