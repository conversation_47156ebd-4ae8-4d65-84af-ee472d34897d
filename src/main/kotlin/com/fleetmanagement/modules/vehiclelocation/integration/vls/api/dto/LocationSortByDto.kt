/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * supported sortBy parameters when querying locations
 * Values: KEY,NAME
 */
enum class LocationSortByDto(
    val value: String,
) {
    @JsonProperty("KEY")
    KEY("KEY"),

    @JsonProperty("NAME")
    NAME("NAME"),
}
