/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * a single object location event
 * @param eventId a unique identifier for this object location event
 * @param occurredOn the time this event occurred
 * @param eventSource the source of an event
 * @param eventType the type of an event IN, OUT will for example supplied by PVTM (incoming and outgoing objects from a compound) use DIRECT when declaring a location like \"my vehicle is there right now\"
 * @param geoLocation
 * @param vin optional vin identifying the object
 * @param vGuid optional vGuid identifying the object
 * @param equipmentNumber optional equipment number identifying the object
 * @param description an optional description for this event
 * @param locationKey a reference to a location
 * @param locationName human readable reference to a location
 * @param building name of the building
 * @param level level of the building
 * @param parkingLot reference to parking lot
 */
data class ObjectLocationEventDto(
    @get:JsonProperty("eventId", required = true) val eventId: java.util.UUID,
    @get:JsonProperty("occurredOn", required = true) val occurredOn: java.time.OffsetDateTime,
    @get:JsonProperty("eventSource", required = true) val eventSource: String,
    @get:JsonProperty("eventType", required = true) val eventType: String,
    @get:JsonProperty("geoLocation", required = true) val geoLocation: GeoLocationDto,
    @get:JsonProperty("vin") val vin: String? = null,
    @get:JsonProperty("vGuid") val vGuid: String? = null,
    @get:JsonProperty("equipmentNumber") val equipmentNumber: String? = null,
    @get:JsonProperty("description") val description: String? = null,
    @get:JsonProperty("locationKey") val locationKey: String? = null,
    @get:JsonProperty("locationName") val locationName: String? = null,
    @get:JsonProperty("building") val building: String? = null,
    @get:JsonProperty("level") val level: String? = null,
    @get:JsonProperty("parkingLot") val parkingLot: String? = null,
)
