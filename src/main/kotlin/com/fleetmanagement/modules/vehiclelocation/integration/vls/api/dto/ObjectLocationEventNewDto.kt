/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * Payload for a new object location event. Event type will default to DIRECT if not set. Either equipmentNumber, VIN or vGUID must be supplied. Geo location must be supplied. Location key is optional.
 * @param geoLocation
 * @param vin optional vin identifying the object
 * @param vGuid optional vGuid identifying the object
 * @param equipmentNumber optional equipment number identifying the object
 * @param eventType
 * @param description optional event additional text or description for this event
 * @param locationKey optional reference to a location
 * @param locationName human readable name of the location
 * @param building optional reference to a building at the location
 * @param level optional reference to a building level. If it is provided, building must be provided in request
 * @param parkingLot optional reference to a parking lot at the location
 * @param occurredOn optional time of event occurrence
 */
data class ObjectLocationEventNewDto(
    @get:JsonProperty("geoLocation", required = true) val geoLocation: GeoLocationDto,
    @get:JsonProperty("vin") val vin: String? = null,
    @get:JsonProperty("vGuid") val vGuid: String? = null,
    @get:JsonProperty("equipmentNumber") val equipmentNumber: String? = null,
    @get:JsonProperty("eventType") val eventType: EventTypeDto? = null,
    @get:JsonProperty("description") val description: String? = null,
    @get:JsonProperty("locationKey") val locationKey: String? = null,
    @get:JsonProperty("locationName") val locationName: String? = null,
    @get:JsonProperty("building") val building: String? = null,
    @get:JsonProperty("level") val level: String? = null,
    @get:JsonProperty("parkingLot") val parkingLot: String? = null,
    @get:JsonProperty("occurredOn") val occurredOn: java.time.OffsetDateTime? = null,
)
