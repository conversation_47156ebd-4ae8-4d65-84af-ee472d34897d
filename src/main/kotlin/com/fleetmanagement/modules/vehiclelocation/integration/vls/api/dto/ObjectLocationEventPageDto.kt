/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.dto

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * A page of object location events
 * @param maxElementCount The total amount of available items
 * @param totalPageCount The total amount of available pages
 * @param elements The elements for this request, matching filter- and page-constraints
 */
data class ObjectLocationEventPageDto(
    @get:JsonProperty("maxElementCount", required = true) val maxElementCount: kotlin.Int,
    @get:JsonProperty("totalPageCount", required = true) val totalPageCount: kotlin.Int,
    @get:JsonProperty("elements", required = true) val elements: kotlin.collections.List<ObjectLocationEventDto>,
)
