/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.integration.vls.api.exception

import com.fleetmanagement.modules.vehiclelocation.api.exception.VehicleLocationException

class VehicleLocationServiceUnreachableException(
    message: String?,
    cause: Throwable?,
) : VehicleLocationException(message = message, cause = cause)
