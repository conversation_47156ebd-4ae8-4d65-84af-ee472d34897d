/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * information identifying a concrete building
 *
 * @param name name of the building, must be unique within a given location
 * @param level
 * @param parkingLot
 */
data class BuildingDto(
    @get:JsonProperty("name", required = true) val name: String,
    @get:JsonProperty("level") val level: List<String>? = null,
    @get:JsonProperty("parkingLot") val parkingLot: List<String>? = null,
)
