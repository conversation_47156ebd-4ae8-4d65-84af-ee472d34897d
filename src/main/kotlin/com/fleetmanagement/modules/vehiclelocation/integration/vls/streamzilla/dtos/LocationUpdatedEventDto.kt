package com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos

import com.fasterxml.jackson.annotation.JsonProperty

/**
 * LocationUpdated event.
 *
 * @param key a unique identifier for this location
 * @param name a human readable name for this location
 * @param geoLocation
 * @param building
 * @param parkingLot optional information identifying parking lot range, if this location refers to one
 * @param pvtmCompoundId dedicated compoundId property for PVTM supplied compounds.
 */
data class LocationUpdatedEventDto(
    @get:JsonProperty("key", required = true) val key: String,
    @get:JsonProperty("name", required = true) val name: String,
    @get:JsonProperty("geoLocation", required = true) val geoLocation: GeoLocationDto,
    @get:JsonProperty("building") val building: kotlin.collections.List<BuildingDto>? = null,
    @get:JsonProperty("parkingLot") val parkingLot: kotlin.collections.List<String>? = null,
    @get:JsonProperty("pvtmCompoundId") val pvtmCompoundId: String? = null,
)
