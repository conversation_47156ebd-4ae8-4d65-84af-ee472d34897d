/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.locationevents

import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehiclelocation.features.location.services.LocationService
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.VLSKafkaIntegrationConfiguration
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.LocationDeletedEventDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.LocationUpdatedEventDto
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(VLSKafkaIntegrationConfiguration::class)
class LocationEventConsumer(
    private val objectMapper: ObjectMapper,
    private val locationService: LocationService,
) {
    private val log = LoggerFactory.getLogger(LocationEventConsumer::class.java)

    @KafkaListener(
        topics = ["\${vehicle-location.location-event.updated.kafka.consumer.topic}"],
        groupId = "\${vehicle-location.location-event.updated.kafka.consumer.group-id}",
    )
    fun listenForUpdated(message: String) {
        log.debug("Received location updated event : $message")
        val locationUpdated: LocationUpdatedEventDto

        try {
            locationUpdated = parseLocationUpdatedMessage(message)
            locationService.updateLocation(locationUpdated)
            log.debug("Successfully processed location updated event for key: ${locationUpdated.key}", message)
        } catch (e: JsonProcessingException) {
            log.error("Problem parsing update location message: $message", e)
        }
    }

    @KafkaListener(
        topics = ["\${vehicle-location.location-event.deleted.kafka.consumer.topic}"],
        groupId = "\${vehicle-location.location-event.deleted.kafka.consumer.group-id}",
    )
    fun listenForDeleted(message: String) {
        log.debug("Received location deleted event : $message")

        try {
            val locationDeleted = parseLocationDeletedMessage(message)
            locationService.deleteLocation(locationDeleted.key)
            log.debug("Successfully processed location deleted event for key: ${locationDeleted.key}", message)
        } catch (e: JsonProcessingException) {
            log.error("Problem parsing delete location message: $message", e)
        }
    }

    private fun parseLocationUpdatedMessage(message: String): LocationUpdatedEventDto =
        objectMapper.readValue(message, LocationUpdatedEventDto::class.java)

    private fun parseLocationDeletedMessage(message: String): LocationDeletedEventDto =
        objectMapper.readValue(message, LocationDeletedEventDto::class.java)
}
