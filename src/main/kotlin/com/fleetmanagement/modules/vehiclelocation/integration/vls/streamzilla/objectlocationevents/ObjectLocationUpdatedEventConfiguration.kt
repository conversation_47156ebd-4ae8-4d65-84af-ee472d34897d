/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.objectlocationevents

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.VLSKafkaIntegrationConfiguration
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.ObjectLocationEventDto
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.listener.adapter.RecordFilterStrategy

@Configuration
@ConditionalOnBean(VLSKafkaIntegrationConfiguration::class)
class ObjectLocationUpdatedEventConfiguration {
    companion object {
        private val logger = LoggerFactory.getLogger(ObjectLocationUpdatedEventConfiguration::class.java)
    }

    @Bean("objectLocationEventFilter")
    fun objectLocationEventFilter(objectMapper: ObjectMapper): RecordFilterStrategy<Any, Any> =
        RecordFilterStrategy { consumerRecord ->
            try {
                val objectLocationEvent: ObjectLocationEventDto =
                    objectMapper.readValue(
                        consumerRecord.value()?.toString() ?: "{}",
                        ObjectLocationEventDto::class.java,
                    )

                val vin = objectLocationEvent.vin
                val vguid = objectLocationEvent.vGuid
                val equipmentNumber = objectLocationEvent.equipmentNumber
                if (vin == null && vguid == null && equipmentNumber == null) {
                    logger.warn("received a payload without a valid identifier")
                    true
                } else {
                    false
                }
            } catch (e: Exception) {
                // In case of parsing error, consider the message invalid and filter it out
                logger.warn("encountered an error while parsing the msg, due msg will be filter out ${e.cause}")
                true
            }
        }
}
