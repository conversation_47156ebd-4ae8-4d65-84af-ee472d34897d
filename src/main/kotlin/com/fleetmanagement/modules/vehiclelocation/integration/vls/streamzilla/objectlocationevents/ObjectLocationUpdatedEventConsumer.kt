/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */
package com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.objectlocationevents

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.ObjectLocationUpdated
import com.fleetmanagement.modules.vehiclelocation.features.updatelastknownlocation.services.VehicleCannotBeResolved
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.VLSKafkaIntegrationConfiguration
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.ObjectLocationEventDto
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(VLSKafkaIntegrationConfiguration::class)
class ObjectLocationUpdatedEventConsumer(
    private val objectMapper: ObjectMapper,
    private val objectLocationUpdated: ObjectLocationUpdated,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(ObjectLocationUpdatedEventConsumer::class.java)
    }

    @KafkaListener(
        topics = ["\${vehicle-location.object-location-event.updated.kafka.consumer.topic}"],
        groupId = "\${vehicle-location.object-location-event.updated.kafka.consumer.group-id}",
        filter = "objectLocationEventFilter",
    )
    fun listen(message: String) {
        logger.debug("Received message from Kafka $message")
        try {
            val objectLocationEvent = parseMessage(message)
            objectLocationUpdated.updateObjectLocation(objectLocationEvent.toObjectLocationEvent())
        } catch (e: VehicleCannotBeResolved) {
            logger.warn("Could not resolve vehicle from ObjectLocationEvent", e)
        } catch (e: Exception) {
            logger.error("Failed to process message $message", e)
        }
    }

    private fun parseMessage(message: String): ObjectLocationEventDto = objectMapper.readValue(message, ObjectLocationEventDto::class.java)
}
