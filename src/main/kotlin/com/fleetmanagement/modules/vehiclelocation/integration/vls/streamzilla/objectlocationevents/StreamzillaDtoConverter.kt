package com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.objectlocationevents

import com.fleetmanagement.modules.vehiclelocation.api.dtos.GeoLocation
import com.fleetmanagement.modules.vehiclelocation.api.dtos.ObjectLocationEvent
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.GeoLocationDto
import com.fleetmanagement.modules.vehiclelocation.integration.vls.streamzilla.dtos.ObjectLocationEventDto

fun ObjectLocationEventDto.toObjectLocationEvent(): ObjectLocationEvent =
    ObjectLocationEvent(
        eventId = this.eventId,
        occurredOn = this.occurredOn,
        eventSource = this.eventSource,
        eventType = this.eventType,
        geoLocation = this.geoLocation.toGeoLocation(),
        vin = this.vin,
        vGuid = this.vGuid,
        equipmentNumber = this.equipmentNumber,
        locationKey = this.locationKey,
        description = this.description,
        locationName = this.locationName,
        building = this.building,
        level = this.level,
        parkingLot = this.parkingLot,
    )

fun GeoLocationDto.toGeoLocation(): GeoLocation =
    GeoLocation(
        latitude = this.latitude,
        longitude = this.longitude,
    )
