package com.fleetmanagement.modules.vehicleregistration.api.dto

import java.time.ZonedDateTime

data class CreateVehicleRegistration(
    val vin: String?,
    val briefNumber: String? = null,
    val licencePlate: String? = null,
    val registrationType: Int? = null,
    val registrationDate: ZonedDateTime? = null,
    val sfme: Boolean? = null,
    val tsn: String? = null,
    val hsn: String? = null,
    val remark: String? = null,
    val registrationArea: RegistrationArea = RegistrationArea.GERMANY,
)
