package com.fleetmanagement.modules.vehiclesales.features

import com.fleetmanagement.modules.vehiclelocation.api.LastKnownLocation
import com.fleetmanagement.modules.vehiclelocation.api.VehicleLocationEvent
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import org.slf4j.LoggerFactory
import org.springframework.stereotype.Service
import java.time.OffsetDateTime
import java.util.UUID

@Service
class VehicleLocationUpdateService(
    private val lastKnownLocation: LastKnownLocation,
    private val vehicleLocationEvent: VehicleLocationEvent,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(VehicleLocationUpdateService::class.java)
    }

    /**
     * Updates the vehicle location events based on the last known location event type.
     *
     * If the last event type is "IN":
     *   - Adds a new "OUT" event with the provided loadingCompletedDate.
     *
     * If the last event type is "OUT":
     *   - Adds a new "IN" event (copying all fields except eventType and source).
     *   - Adds a new "OUT" event with the provided loadingCompletedDate.
     *
     * @param vehicleId The unique identifier of the vehicle.
     * @param loadingCompletedDate The date and time when loading was completed.
     */
    fun handleVehicleLocationUpdate(
        vehicleId: UUID,
        loadingCompletedDate: OffsetDateTime,
    ) {
        val lastLocation = lastKnownLocation.findLastKnownVehicleLocationBy(vehicleId)
        if (lastLocation == null) {
            logger.info("No last known location found for vehicle with ID: $vehicleId, no location event will be added.")
            return
        }

        when (lastLocation.eventType) {
            "IN" -> addLocationOutEvent(lastLocation, loadingCompletedDate)
            "OUT" -> addLocationInAndOutEvents(lastLocation, loadingCompletedDate)
        }
    }

    private fun addLocationOutEvent(
        lastLocation: VehicleLocation,
        loadingCompletedDate: OffsetDateTime,
    ) {
        val newLocation =
            lastLocation.copy(
                eventType = "OUT",
                source = "FVM",
                occurredOn = loadingCompletedDate,
                comment = "Verladung abgeschlossen",
            )
        vehicleLocationEvent.addLocationEvent(newLocation)
        logger.info("Added OUT event for vehicle ${lastLocation.vehicleId} on vehicle loading completed")
    }

    /**
     * This method is used to avoid data inconsistency when the last event type is "OUT".
     * It first adds an "IN" event (copying all fields except eventType and source),
     * then adds an "OUT" event with the provided loadingCompletedDate.
     */
    private fun addLocationInAndOutEvents(
        lastLocation: VehicleLocation,
        loadingCompletedDate: OffsetDateTime,
    ) {
        val inLocation =
            lastLocation.copy(
                eventType = "IN",
                source = "FVM",
                comment = "Verladung abgeschlossen",
            )
        vehicleLocationEvent.addLocationEvent(inLocation)

        val outLocation =
            lastLocation.copy(
                eventType = "OUT",
                source = "FVM",
                occurredOn = loadingCompletedDate,
                comment = "Verladung abgeschlossen",
            )
        vehicleLocationEvent.addLocationEvent(outLocation)

        logger.info("Added IN and OUT events for vehicle ${lastLocation.vehicleId} on vehicle loading completed")
    }
}
