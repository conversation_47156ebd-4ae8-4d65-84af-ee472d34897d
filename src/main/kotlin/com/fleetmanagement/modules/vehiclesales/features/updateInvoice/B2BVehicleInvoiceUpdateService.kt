/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.features.updateInvoice

import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceNotFoundException
import com.fleetmanagement.modules.vehiclesales.features.toVehicleInvoiceDto
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleInvoiceRepository
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.util.*

@Service
@Transactional
class B2BVehicleInvoiceUpdateService(
    private val jPAVehicleInvoiceRepository: JPAVehicleInvoiceRepository,
) {
    /**
     * Updates the customer delivery date for the current B2B vehicle invoice associated with the given vehicle ID. If no current invoice is found, the function returns without making changes.
     * Params:
     * vehicleId - The unique identifier of the vehicle.
     * customerDeliveryDate - The new customer delivery date to set.
     */
    fun updateCustomerDeliveryDateForB2B(
        vehicleId: UUID,
        customerDeliveryDate: OffsetDateTime,
    ): VehicleInvoice {
        val invoice =
            jPAVehicleInvoiceRepository.findByVehicleIdAndIsCurrentTrue(vehicleId)
                ?: throw VehicleInvoiceNotFoundException("Current invoice not found for vehicle $vehicleId")
        invoice.updateCustomerDeliveryDate(customerDeliveryDate = customerDeliveryDate)
        return invoice.toVehicleInvoiceDto()
    }
}
