/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.features.updateInvoice

import com.fleetmanagement.modules.vehicledata.api.UpdateVehicleSoldDate
import com.fleetmanagement.modules.vehiclesales.api.CreateVehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.UpdateVehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.domain.InvoiceStatus
import com.fleetmanagement.modules.vehiclesales.api.domain.TransactionType
import com.fleetmanagement.modules.vehiclesales.api.dto.CancelInvoiceResult
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoiceUpdateDto
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceNotFoundException
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceUpdateException
import com.fleetmanagement.modules.vehiclesales.features.UpsertVehicleSaleService
import com.fleetmanagement.modules.vehiclesales.features.toVehicleInvoiceCreateDto
import com.fleetmanagement.modules.vehiclesales.features.toVehicleInvoiceDto
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleInvoiceRepository
import com.fleetmanagement.modules.vehiclesales.repository.entities.JPAVehicleInvoiceEntity
import com.fleetmanagement.modules.vehiclesales.repository.entities.TransactionId
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.Optional
import java.util.UUID

@Service
@Transactional
class B2CVehicleInvoiceUpdateService(
    private val jPAVehicleInvoiceRepository: JPAVehicleInvoiceRepository,
    private val createVehicleInvoiceService: CreateVehicleInvoice,
    private val vehicleInvoiceUpdateRuleService: List<VehicleInvoiceUpdateRule>,
    private val vehicleUpdateService: UpdateVehicleSoldDate,
    private val vehicleSalesUpdateService: UpsertVehicleSaleService,
) : UpdateVehicleInvoice {
    override fun approveInvoiceBy(invoiceId: UUID): VehicleInvoice {
        val invoice = getInvoiceBy(invoiceId)
        invoice.updateInvoiceStatus(InvoiceStatus.INVOICE_REQUEST_APPROVED)
        return invoice.toVehicleInvoiceDto()
    }

    override fun deleteInvoiceBy(invoiceId: UUID): UUID {
        if (!jPAVehicleInvoiceRepository.existsById(invoiceId)) {
            throw VehicleInvoiceNotFoundException("Invoice $invoiceId not found")
        }
        jPAVehicleInvoiceRepository.deleteById(invoiceId)
        return invoiceId
    }

    override fun cancelInvoiceBy(
        invoiceId: UUID,
        modifier: String,
    ): CancelInvoiceResult {
        val invoice = getInvoiceBy(invoiceId)
        invoice.updateInvoiceStatus(InvoiceStatus.CANCELED)

        vehicleUpdateService.updateVehicleSoldDate(
            vehicleId = invoice.vehicleId,
            modifier = modifier,
            soldDate = null, // Reset sold date on refund
        )
        vehicleSalesUpdateService.resetVehicleSales(
            vehicleId = invoice.vehicleId,
            currentUser = modifier,
        )

        val creditInvoiceDto =
            invoice.toVehicleInvoiceCreateDto(
                transactionType = TransactionType.CREDIT_NOTE,
                referenceTransactionId = TransactionId(invoice.transactionId),
            )
        val creditInvoice = createVehicleInvoiceService.createInvoice(creditInvoiceDto)

        return CancelInvoiceResult(
            canceledInvoice = invoice.toVehicleInvoiceDto(),
            creditInvoice = creditInvoice,
        )
    }

    override fun updateInvoiceBy(
        invoiceId: UUID,
        vehicleInvoiceUpdateDto: VehicleInvoiceUpdateDto,
        modifier: String,
    ): VehicleInvoice {
        val invoice = getInvoiceBy(invoiceId)
        val rule =
            vehicleInvoiceUpdateRuleService.firstOrNull { it.matches(invoice.invoiceStatus) }
                ?: throw VehicleInvoiceUpdateException(
                    message = "Vehicle with invoice status: ${invoice.invoiceStatus} cannot be updated",
                )

        when (rule) {
            is VehicleInvoiceUpdateRuleWithUserContext -> {
                val requiredFields = rule.requiredFields()
                checkRequiredFields(requiredFields, vehicleInvoiceUpdateDto)
                rule.apply(invoice, vehicleInvoiceUpdateDto, modifier)
            }

            is VehicleInvoiceUpdateRuleWithoutUserContext -> {
                val requiredFields = rule.requiredFields()
                checkRequiredFields(requiredFields, vehicleInvoiceUpdateDto)
                rule.apply(invoice, vehicleInvoiceUpdateDto)
            }
        }
        return invoice.toVehicleInvoiceDto()
    }

    private fun checkRequiredFields(
        requiredFields: Map<String, (VehicleInvoiceUpdateDto) -> Optional<*>?>,
        vehicleInvoiceUpdateDto: VehicleInvoiceUpdateDto,
    ) {
        val missingFields =
            requiredFields.filterValues { extractor -> extractor(vehicleInvoiceUpdateDto)?.isPresent != true }.keys.toList()

        if (missingFields.isNotEmpty()) {
            throw VehicleInvoiceUpdateException(message = "Missing required fields", properties = missingFields)
        }
    }

    private fun getInvoiceBy(invoiceId: UUID): JPAVehicleInvoiceEntity =
        jPAVehicleInvoiceRepository
            .findById(invoiceId)
            .orElseThrow { VehicleInvoiceNotFoundException("Invoice $invoiceId not found") }
}
