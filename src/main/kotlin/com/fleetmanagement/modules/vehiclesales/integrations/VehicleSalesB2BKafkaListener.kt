/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.integrations

import com.fasterxml.jackson.databind.ObjectMapper
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehiclesales.integrations.configurations.VehicleSalesB2BConfiguration
import com.fleetmanagement.modules.vehiclesales.integrations.service.VehicleSalesB2BInvoiceCreateService
import com.porsche.b2b.entities.KafkaCompletedAuction
import org.apache.avro.generic.GenericRecord
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.kafka.annotation.KafkaListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnBean(VehicleSalesB2BConfiguration::class)
class VehicleSalesB2BKafkaListener(
    private val objectMapper: ObjectMapper,
    private val readVehicleByVinService: ReadVehicleByVIN,
    private val vehicleSalesB2BInvoiceCreateService: VehicleSalesB2BInvoiceCreateService,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(VehicleSalesB2BKafkaListener::class.java)
        private const val B2B_KAFKA_TOPIC = "FRA_Honeypotters_completed_auctions"
        private const val LISTENER_ID = "vehicle_sales_B2B_kafka_listener"
    }

    /**
     * Listens for completed auction events from Kafka and processes them.
     */
    @KafkaListener(
        topics = [B2B_KAFKA_TOPIC],
        id = LISTENER_ID,
        groupId = "\${vehicle-sales.b2b-integration.consumer.group-id}",
        containerFactory = "vehicleSalesB2BKafkaListenerContainerFactory",
    )
    fun listenCompletedAuction(record: GenericRecord) {
        val completedAuction = parseRecord(record) ?: return
        val vin = completedAuction.vin?.toString().orEmpty()
        if (vin.isBlank()) return

        val vehicle =
            runCatching { readVehicleByVinService.readVehicleByVIN(vin) }
                .onFailure { logger.warn("B2B vehicle doesn't exist in FVM for vin: $vin", it) }
                .getOrNull() ?: return

        vehicleSalesB2BInvoiceCreateService.createInvoiceForAuction(
            vehicleId = vehicle.id,
            completedAuction = completedAuction,
        )
    }

    private fun parseRecord(record: GenericRecord): KafkaCompletedAuction? =
        runCatching { objectMapper.readValue(record.toString(), KafkaCompletedAuction::class.java) }
            .onFailure {
                logger.warn("Failed to parse KafkaCompletedAuction from message: $record", it)
            }.getOrNull()
}
