/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.integrations.configurations

import org.springframework.boot.context.properties.ConfigurationProperties

@ConfigurationProperties(prefix = "vehicle-sales.b2b-integration")
class VehicleSalesB2BIntegrationConfigurationProperties(
    val enabled: Boolean,
    val consumer: ConsumerProperties,
)

class ConsumerProperties(
    val keyDeserializer: String,
    val valueDeserializer: String,
    val schemaRegistryUrl: String,
    val schemaRegistryUser: String,
    val schemaRegistryPassword: String,
)
