/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.integrations.configurations

import com.porsche.b2b.entities.KafkaCompletedAuction
import org.apache.kafka.clients.consumer.ConsumerConfig
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.boot.autoconfigure.kafka.KafkaProperties
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.kafka.config.ConcurrentKafkaListenerContainerFactory
import org.springframework.kafka.core.ConsumerFactory
import org.springframework.kafka.core.DefaultKafkaConsumerFactory

@Configuration
@ConditionalOnBean(VehicleSalesB2BConfiguration::class)
class VehicleSalesB2BKafkaConsumerConfig(
    private val kafkaProperties: KafkaProperties,
    private val customProperties: VehicleSalesB2BIntegrationConfigurationProperties,
) {
    fun vehicleSalesB2BKafkaConsumerFactory(): ConsumerFactory<String, KafkaCompletedAuction> {
        val config = kafkaProperties.buildConsumerProperties()

        config[ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG] = customProperties.consumer.keyDeserializer
        config[ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG] = customProperties.consumer.valueDeserializer

        config["schema.registry.url"] = customProperties.consumer.schemaRegistryUrl
        config["basic.auth.credentials.source"] = "USER_INFO"
        config["basic.auth.user.info"] =
            "${customProperties.consumer.schemaRegistryUser}:${customProperties.consumer.schemaRegistryPassword}"
        config["specific.avro.reader"] = true

        return DefaultKafkaConsumerFactory(config)
    }

    @Bean(name = ["vehicleSalesB2BKafkaListenerContainerFactory"])
    fun vehicleSalesB2BKafkaListenerContainerFactory(): ConcurrentKafkaListenerContainerFactory<String, KafkaCompletedAuction> {
        val factory = ConcurrentKafkaListenerContainerFactory<String, KafkaCompletedAuction>()
        factory.consumerFactory = vehicleSalesB2BKafkaConsumerFactory()
        factory.setConcurrency(1)
        factory.containerProperties.pollTimeout = 3000
        return factory
    }
}
