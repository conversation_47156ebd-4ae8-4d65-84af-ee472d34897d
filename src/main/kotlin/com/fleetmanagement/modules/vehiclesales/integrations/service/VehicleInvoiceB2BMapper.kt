/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.integrations.service

import com.fleetmanagement.modules.vehiclesales.api.domain.InvoiceStatus
import com.fleetmanagement.modules.vehiclesales.api.domain.PaymentType
import com.fleetmanagement.modules.vehiclesales.api.domain.TransactionType
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoiceCreateDto
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleSaleUpdateDto
import com.porsche.b2b.entities.KafkaCompletedAuction
import java.time.LocalTime
import java.time.ZoneOffset
import java.util.*

fun KafkaCompletedAuction.toVehicleInvoiceCreateDto(vehicleId: UUID) =
    VehicleInvoiceCreateDto(
        vehicleId = vehicleId,
        salesNetPriceEUR = customerVehiclePrice.toBigDecimal(),
        customerInvoiceRecipient = false,
        paymentType = PaymentType.DIRECT_DEBIT,
        customerPartnerNumber =
            checkNotNull(customer.debtorId?.toString()) {
                "Customer debtor ID must not be null"
            },
        invoiceRecipientNumber =
            checkNotNull(customer.debtorId?.toString()) {
                "Customer debtor ID must not be null"
            },
        transactionType = TransactionType.INVOICE,
        salesPersonNumber = "332392",
        auctionId = auctionId?.toString(),
        invoiceStatus = InvoiceStatus.INVOICE_SENT_TO_CUSTOMER,
        customerDeliveryDate = saleDate?.atTime(LocalTime.MIDNIGHT)?.atOffset(ZoneOffset.UTC),
        invoiceDate = invoiceDate?.atTime(LocalTime.MIDNIGHT)?.atOffset(ZoneOffset.UTC),
        invoiceNumber = null, // KafkaEvent does not provide invoice number
        receiptNumber = null, // KafkaEvent does not provide receipt number
    )

fun KafkaCompletedAuction.toVehicleSaleUpdateDto(vehicleId: UUID) =
    VehicleSaleUpdateDto(
        vehicleId = vehicleId,
        comment = customer.companyName?.toString()?.let { Optional.of(it) },
        contractSigned = purchaseContractDate?.let { Optional.of(true) },
        reservedForB2B = Optional.of(true),
        reservedForB2C = null,
        plannedDeliveryDate = null,
    )
