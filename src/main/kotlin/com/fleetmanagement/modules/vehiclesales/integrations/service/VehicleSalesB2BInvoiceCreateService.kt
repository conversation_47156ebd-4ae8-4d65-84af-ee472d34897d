/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.integrations.service

import com.fleetmanagement.modules.vehiclesales.api.CreateVehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.UpsertVehicleSale
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoice
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceAlreadyExistsException
import com.fleetmanagement.modules.vehiclesales.integrations.configurations.VehicleSalesB2BConfiguration
import com.porsche.b2b.entities.KafkaCompletedAuction
import org.slf4j.LoggerFactory
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.util.UUID

@Service
@ConditionalOnBean(VehicleSalesB2BConfiguration::class)
@Transactional
class VehicleSalesB2BInvoiceCreateService(
    private val createVehicleInvoice: CreateVehicleInvoice,
    private val upsertVehicleSalesService: UpsertVehicleSale,
) {
    companion object {
        private val logger = LoggerFactory.getLogger(VehicleSalesB2BInvoiceCreateService::class.java)
    }

    fun createInvoiceForAuction(
        vehicleId: UUID,
        completedAuction: KafkaCompletedAuction,
    ): VehicleInvoice? =
        runCatching {
            upsertVehicleSalesService.upsert(
                vehicleSale = completedAuction.toVehicleSaleUpdateDto(vehicleId),
                currentUser = "B2BKafkaEvent",
            )
            createVehicleInvoice.createInvoice(
                vehicleInvoiceCreateDto = completedAuction.toVehicleInvoiceCreateDto(vehicleId),
            )
        }.onFailure { ex ->
            when (ex) {
                is VehicleInvoiceAlreadyExistsException ->
                    logger.warn(
                        "Vehicle invoice for vehicle ID {} already exists, skipping creation.",
                        vehicleId,
                        ex,
                    )

                else -> logger.error("Failed to create invoice for vehicle ID $vehicleId: ${ex.message}", ex)
            }
        }.getOrNull()
}
