/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.dmsvehiclemigration.application

import com.fleetmanagement.TestAsyncConfiguration
import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.emhshared.LeasingArt
import com.fleetmanagement.emhshared.domain.VehicleType
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterFinder
import com.fleetmanagement.modules.consigneedatasheet.application.CostCenterNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageFinder
import com.fleetmanagement.modules.consigneedatasheet.application.VehicleUsageNotFoundException
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterDescription
import com.fleetmanagement.modules.consigneedatasheet.domain.CostCenterId
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.consigneedatasheet.domain.VehicleUsageId
import com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka.model.VehicleMigrationDto
import com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka.model.VehicleMigrationVehicleDto
import com.fleetmanagement.modules.dmsvehiclemigration.adapter.out.kafka.model.VehicleMigrationVehicleResponsiblePersonDto
import com.fleetmanagement.modules.dmsvehiclemigration.application.port.PublishVehicleUseCase
import com.fleetmanagement.modules.dmsvehiclemigration.objectmothers.VehicleMigrationObjectMother
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.events.VehicleStatusChangedEvent
import com.fleetmanagement.modules.vehicledata.features.mileagereading.MileageReadingService
import com.fleetmanagement.modules.vehicledata.features.updatevehicle.UpdateVehicleStatusService
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicletransfer.application.CurrentVehicleTransferService
import com.fleetmanagement.modules.vehicletransfer.application.HistoryCaptureService
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferApplicationService
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferLeasingPrivilegeResolverService
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferApplicationService
import com.fleetmanagement.modules.vehicletransfer.application.VehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.domain.DomainEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferAutomaticCreatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferInitializedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferLicensePlateEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferManualCreatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferNotFoundException
import com.fleetmanagement.modules.vehicletransfer.domain.PlannedVehicleTransferUpdatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferDeliveredEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferLicensePlateEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferNotFoundException
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferReturnedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.VehicleTransferUpdatedEvent
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import com.ninjasquad.springmockk.MockkBean
import io.mockk.Runs
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.just
import io.mockk.mockkObject
import io.mockk.runs
import io.mockk.unmockkObject
import io.mockk.verify
import org.awaitility.Durations
import org.awaitility.kotlin.atMost
import org.awaitility.kotlin.await
import org.awaitility.kotlin.untilAsserted
import org.awaitility.kotlin.withPollDelay
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.ApplicationContext
import org.springframework.context.ApplicationEventPublisher
import org.springframework.context.annotation.Import
import org.springframework.test.context.transaction.TestTransaction
import org.springframework.transaction.annotation.Transactional
import java.time.OffsetDateTime
import java.time.ZonedDateTime
import java.util.UUID
import java.util.stream.Stream
import com.fleetmanagement.modules.vehicletransfer.domain.entities.CostCenterId as VTCostCenterId
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleUsageId as VTVehicleUsageId

@SpringBootTest
@Transactional
@Import(TestcontainersConfiguration::class, TestAsyncConfiguration::class)
class DMSVehicleMigrationApplicationServiceTest {
    @Autowired
    private lateinit var dmsVehicleMigrationApplicationService: DMSVehicleMigrationApplicationService

    @Autowired
    private lateinit var applicationEventPublisher: ApplicationEventPublisher

    @MockkBean
    private lateinit var plannedVehicleTransferFinder: PlannedVehicleTransferFinder

    @MockkBean
    private lateinit var vehicleTransferFinder: VehicleTransferFinder

    @MockkBean
    private lateinit var readVehicleByVehicleId: ReadVehicleByVehicleId

    @MockkBean
    private lateinit var costCenterFinder: CostCenterFinder

    @MockkBean
    private lateinit var vehicleUsageFinder: VehicleUsageFinder

    @MockkBean
    private lateinit var readRegistrationOrder: ReadRegistrationOrder

    @MockkBean
    private lateinit var readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber

    @MockkBean
    private lateinit var publishVehicleUseCase: PublishVehicleUseCase

    @MockkBean(relaxed = true)
    private lateinit var updateVehicleStatusService: UpdateVehicleStatusService

    @MockkBean(relaxed = true)
    private lateinit var vehicleTransferApplicationService: VehicleTransferApplicationService

    @MockkBean(relaxed = true)
    private lateinit var plannedVehicleTransferApplicationService: PlannedVehicleTransferApplicationService

    @MockkBean(relaxed = true)
    private lateinit var currentVehicleTransferService: CurrentVehicleTransferService

    @MockkBean(relaxed = true)
    private lateinit var historyCaptureService: HistoryCaptureService

    @MockkBean(relaxed = true)
    private lateinit var mileageReadingService: MileageReadingService

    @MockkBean(relaxed = true)
    private lateinit var plannedVehicleTransferLeasingPrivilegeResolverService: PlannedVehicleTransferLeasingPrivilegeResolverService

    val leasingArt = LeasingArt.L1_1

    @BeforeEach
    fun setup() {
        mockkObject(LeasingArt.Companion)
        every { LeasingArt.of(any(), any(), any()) } returns leasingArt
    }

    @AfterEach
    fun resetMocks() {
        clearMocks(plannedVehicleTransferFinder)
        clearMocks(vehicleTransferFinder)
        clearMocks(readVehicleByVehicleId)
        clearMocks(costCenterFinder)
        clearMocks(vehicleUsageFinder)
        clearMocks(readRegistrationOrder)
        clearMocks(readVehiclePersonDetailByEmployeeNumber)
        clearMocks(publishVehicleUseCase)
        clearMocks(currentVehicleTransferService)
        unmockkObject(LeasingArt.Companion)
    }

    @ParameterizedTest
    @MethodSource("eventsProvider")
    fun `should handle vehicle transfer events and publish dms vehicle migration`(event: DomainEvent) {
        val vin = "1HGCM82633A123456"
        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val modelDescription = "Taycan"
        val vehicleType = "PKW"
        val manufacturer = "Porsche"
        val status = VehicleStatus.S096
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = status,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO
        every { plannedVehicleTransferLeasingPrivilegeResolverService.handlePlannedVehicleTransferManualCreatedEvent(any()) } just runs

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenterDescription = "00H0014444"
        val usingCostCenter = CostCenterDescription(usingCostCenterDescription)
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns plannedVehicleTransfer

        val vehicleTransfer =
            VehicleMigrationObjectMother.vehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )

        every { vehicleTransferFinder.getVehicleTransfer(any()) } returns vehicleTransfer

        val depreciationRelevantCostCenterDescriptionValue = "00H0014444"
        val depreciationRelevantCostCenterDescription =
            CostCenterDescription(depreciationRelevantCostCenterDescriptionValue)
        val depreciationRelevantCostCenter =
            VehicleMigrationObjectMother.depreciationRelevantCostCenter(description = depreciationRelevantCostCenterDescription)
        every { costCenterFinder.getCostCenter(any()) } returns depreciationRelevantCostCenter

        val usage = "usage"
        val vehicleUsage = VehicleMigrationObjectMother.vehicleUsage(usage)

        every { vehicleUsageFinder.getVehicleUsage(any()) } returns vehicleUsage

        val licensePlate = "SW 1234"
        val dateTimeString = "2025-05-22T14:10:10+02:00[Europe/Berlin]"
        val firstRegistrationDate = ZonedDateTime.parse(dateTimeString)
        val registrationOrder =
            VehicleRegistrationAPIResponse(
                data =
                    VehicleMigrationObjectMother.vehicleRegistrationOrder(
                        firstRegistrationDateTime = firstRegistrationDate,
                        licensePlate = licensePlate,
                    ),
            )
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns registrationOrder

        val employeeNumber = "01234567"
        val firstName = "Max"
        val lastName = "Mustermann"
        val companyEmail = "<EMAIL>"
        val vehiclePersonDetail =
            VehicleMigrationObjectMother.vehicleResponsiblePersonDetails(
                employeeNumber,
                firstName,
                lastName,
                companyEmail,
            )
        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns vehiclePersonDetail

        every { publishVehicleUseCase.publishVehicle(any()) } just Runs

        applicationEventPublisher.publishEvent(
            event,
        )

        val expectedVehicleMigrationDto =
            VehicleMigrationDto(
                vehicle =
                    VehicleMigrationVehicleDto(
                        vin = vin,
                        vguid = vguid,
                        model = modelDescription,
                        depreciationRelevantCostCenter = depreciationRelevantCostCenterDescriptionValue,
                        usingCostCenter = usingCostCenterDescription,
                        vehicleUsage = usage,
                        fleetInformation = VehicleMigrationVehicleDto.FleetInformation.PRE_USAGE,
                        isActive = true,
                        firstRegistrationDate = OffsetDateTime.parse("2025-05-22T14:10:10+02:00"),
                        licensePlate = licensePlate,
                        vehicleType = vehicleType,
                        manufacturer = manufacturer,
                        leasingArt = leasingArt.id,
                    ),
                vehicleResponsiblePerson =
                    VehicleMigrationVehicleResponsiblePersonDto(
                        employeeNumber = employeeNumber,
                        firstName = firstName,
                        lastName = lastName,
                        email = companyEmail,
                    ),
            )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    when (event) {
                        is PlannedVehicleTransferEvent ->
                            plannedVehicleTransferFinder.getPlannedVehicleTransfer(
                                vehicleTransferKey,
                            )

                        is VehicleTransferEvent -> vehicleTransferFinder.getVehicleTransfer(vehicleTransferKey)
                    }
                }

                verify(exactly = 1) {
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                    readRegistrationOrder.getLatestOrderBy(vehicleId)
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        vehicleResponsiblePerson.value,
                    )
                    publishVehicleUseCase.publishVehicle(expectedVehicleMigrationDto)
                }
            }
    }

    @Test
    fun `should handle VehicleStatusChangedEvent and publish dms vehicle migration`() {
        val vin = "1HGCM82633A123456"
        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val vehicleType = "PKW"
        val manufacturer = "Porsche"
        val modelDescription = "Taycan"
        val status = VehicleStatus.S096
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = status,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenterDescription = "00H0014444"
        val usingCostCenter = CostCenterDescription(usingCostCenterDescription)
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.findPlannedVehicleTransfer(any()) } returns plannedVehicleTransfer

        every { currentVehicleTransferService.retrieveCurrentVehicleTransferKey(any()) } returns vehicleTransferKey

        val depreciationRelevantCostCenterDescriptionValue = "00H0014444"
        val depreciationRelevantCostCenterDescription =
            CostCenterDescription(depreciationRelevantCostCenterDescriptionValue)
        val depreciationRelevantCostCenter =
            VehicleMigrationObjectMother.depreciationRelevantCostCenter(description = depreciationRelevantCostCenterDescription)
        every { costCenterFinder.getCostCenter(any()) } returns depreciationRelevantCostCenter

        val usage = "usage"
        val vehicleUsage = VehicleMigrationObjectMother.vehicleUsage(usage)
        every { vehicleUsageFinder.getVehicleUsage(any()) } returns vehicleUsage

        val licensePlate = "SW 1234"
        val dateTimeString = "2025-05-22T14:10:10+02:00[Europe/Berlin]"
        val firstRegistrationDate = ZonedDateTime.parse(dateTimeString)
        val registrationOrder =
            VehicleRegistrationAPIResponse(
                data =
                    VehicleMigrationObjectMother.vehicleRegistrationOrder(
                        firstRegistrationDateTime = firstRegistrationDate,
                        licensePlate = licensePlate,
                    ),
            )
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns registrationOrder

        val employeeNumber = "01234567"
        val firstName = "Max"
        val lastName = "Mustermann"
        val companyEmail = "<EMAIL>"
        val vehiclePersonDetail =
            VehicleMigrationObjectMother.vehicleResponsiblePersonDetails(
                employeeNumber,
                firstName,
                lastName,
                companyEmail,
            )
        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns vehiclePersonDetail

        every { publishVehicleUseCase.publishVehicle(any()) } just Runs

        applicationEventPublisher.publishEvent(
            VehicleStatusChangedEvent(
                vehicleId = vehicleId,
                status = VehicleStatus.S096,
            ),
        )

        val expectedVehicleMigrationDto =
            VehicleMigrationDto(
                vehicle =
                    VehicleMigrationVehicleDto(
                        vin = vin,
                        vguid = vguid,
                        model = modelDescription,
                        depreciationRelevantCostCenter = depreciationRelevantCostCenterDescriptionValue,
                        usingCostCenter = usingCostCenterDescription,
                        vehicleUsage = usage,
                        fleetInformation = VehicleMigrationVehicleDto.FleetInformation.PRE_USAGE,
                        isActive = true,
                        firstRegistrationDate = OffsetDateTime.parse("2025-05-22T14:10:10+02:00"),
                        licensePlate = licensePlate,
                        vehicleType = vehicleType,
                        manufacturer = manufacturer,
                        leasingArt = leasingArt.id,
                    ),
                vehicleResponsiblePerson =
                    VehicleMigrationVehicleResponsiblePersonDto(
                        employeeNumber = employeeNumber,
                        firstName = firstName,
                        lastName = lastName,
                        email = companyEmail,
                    ),
            )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    currentVehicleTransferService.retrieveCurrentVehicleTransferKey(vehicleId)
                    plannedVehicleTransferFinder.findPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                    readRegistrationOrder.getLatestOrderBy(vehicleId)
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        vehicleResponsiblePerson.value,
                    )
                    publishVehicleUseCase.publishVehicle(expectedVehicleMigrationDto)
                }

                verify(exactly = 0) { vehicleTransferFinder.findVehicleTransferByKey(any()) }
            }
    }

    @Test
    fun `should publish vehicle even when vguid is null`() {
        val vin = "1HGCM82633A123456"
        val vguid = null
        val modelDescription = "Taycan"
        val vehicleType = "PKW"
        val manufacturer = "Porsche"
        val status = VehicleStatus.S096
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = status,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO
        every {
            plannedVehicleTransferLeasingPrivilegeResolverService
                .handlePlannedVehicleTransferManualCreatedEvent(any())
        } just runs

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenterDescription = "00H0014444"
        val usingCostCenter = CostCenterDescription(usingCostCenterDescription)
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns
            plannedVehicleTransfer

        val depreciationRelevantCostCenterDescriptionValue = "00H0014444"
        val depreciationRelevantCostCenterDescription =
            CostCenterDescription(depreciationRelevantCostCenterDescriptionValue)
        val depreciationRelevantCostCenter =
            VehicleMigrationObjectMother.depreciationRelevantCostCenter(
                description = depreciationRelevantCostCenterDescription,
            )
        every { costCenterFinder.getCostCenter(any()) } returns depreciationRelevantCostCenter

        val usage = "usage"
        val vehicleUsage = VehicleMigrationObjectMother.vehicleUsage(usage)

        every { vehicleUsageFinder.getVehicleUsage(any()) } returns vehicleUsage

        val licensePlate = "SW 1234"
        val dateTimeString = "2025-05-22T14:10:10+02:00[Europe/Berlin]"
        val firstRegistrationDate = ZonedDateTime.parse(dateTimeString)
        val registrationOrder =
            VehicleRegistrationAPIResponse(
                data =
                    VehicleMigrationObjectMother.vehicleRegistrationOrder(
                        firstRegistrationDateTime = firstRegistrationDate,
                        licensePlate = licensePlate,
                    ),
            )
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns registrationOrder

        val employeeNumber = "01234567"
        val firstName = "Max"
        val lastName = "Mustermann"
        val companyEmail = "<EMAIL>"
        val vehiclePersonDetail =
            VehicleMigrationObjectMother.vehicleResponsiblePersonDetails(
                employeeNumber,
                firstName,
                lastName,
                companyEmail,
            )
        every {
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any())
        } returns vehiclePersonDetail

        every { publishVehicleUseCase.publishVehicle(any()) } just Runs

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferUpdatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
            ),
        )

        val expectedVehicleMigrationDto =
            VehicleMigrationDto(
                vehicle =
                    VehicleMigrationVehicleDto(
                        vin = vin,
                        vguid = vguid,
                        model = modelDescription,
                        depreciationRelevantCostCenter =
                        depreciationRelevantCostCenterDescriptionValue,
                        usingCostCenter = usingCostCenterDescription,
                        vehicleUsage = usage,
                        fleetInformation = VehicleMigrationVehicleDto.FleetInformation.PRE_USAGE,
                        isActive = true,
                        firstRegistrationDate = firstRegistrationDate.toOffsetDateTime(),
                        licensePlate = licensePlate,
                        vehicleType = vehicleType,
                        manufacturer = manufacturer,
                        leasingArt = leasingArt.id,
                    ),
                vehicleResponsiblePerson =
                    VehicleMigrationVehicleResponsiblePersonDto(
                        employeeNumber = employeeNumber,
                        firstName = firstName,
                        lastName = lastName,
                        email = companyEmail,
                    ),
            )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(
                        CostCenterId(depreciationRelevantCostCenterId.value),
                    )
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                    readRegistrationOrder.getLatestOrderBy(vehicleId)
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        vehicleResponsiblePerson.value,
                    )
                    publishVehicleUseCase.publishVehicle(expectedVehicleMigrationDto)
                }
            }
    }

    @Test
    fun `should not publish if planned vehicle transfer is missing`() {
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } throws
            PlannedVehicleTransferNotFoundException(
                vehicleTransferKey.value,
            )

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferLicensePlateEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) { plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey) }
                verify(exactly = 0) {
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(any())
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should not publish if  vehicle transfer is missing`() {
        every { vehicleTransferFinder.getVehicleTransfer(any()) } throws
            VehicleTransferNotFoundException(
                vehicleTransferKey.value,
            )

        applicationEventPublisher.publishEvent(
            VehicleTransferDeliveredEvent(
                vehicleId = vehicleId,
                vehicleTransferKey = vehicleTransferKey,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) { vehicleTransferFinder.getVehicleTransfer(vehicleTransferKey) }
                verify(exactly = 0) {
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(any())
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should not publish if vehicle vin is null`() {
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns
            VehicleMigrationObjectMother.plannedVehicleTransfer()

        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val modelDescription = "Taycan"
        val status = VehicleStatus.S096
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = null,
                vguid = vguid,
                model = modelDescription,
                status = status,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferUpdatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                }
                verify(exactly = 0) {
                    costCenterFinder.getCostCenter(any())
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should not publish if vehicle type is null`() {
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns
            VehicleMigrationObjectMother.plannedVehicleTransfer()
        val vin = "1HGCM82633A123456"
        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val manufacturer = "Porsche"
        val modelDescription = "Taycan"
        val status = VehicleStatus.S096
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = status,
                manufacturer = manufacturer,
                vehicleType = null,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferUpdatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                }
                verify(exactly = 0) {
                    costCenterFinder.getCostCenter(any())
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should not publish if manufacturer is null`() {
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns
            VehicleMigrationObjectMother.plannedVehicleTransfer()
        val vin = "1HGCM82633A123456"
        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val vehicleType = VehicleType.PKW
        val modelDescription = "Taycan"
        val status = VehicleStatus.S096
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = status,
                manufacturer = null,
                vehicleType = vehicleType,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferUpdatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                }
                verify(exactly = 0) {
                    costCenterFinder.getCostCenter(any())
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should not publish if vehicle transfer depreciationRelevantCostCenterId is null`() {
        every { readVehicleByVehicleId.readVehicleById(any()) } returns VehicleMigrationObjectMother.vehicleDTO(id = vehicleId)

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenter = CostCenterDescription("00H0014444")
        val depreciationRelevantCostCenterId = null
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns
            plannedVehicleTransfer

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferAutomaticCreatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
                consignee = null,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                }
                verify(exactly = 0) {
                    costCenterFinder.getCostCenter(any())
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should not publish if vehicle transfer vehicleUsageId is null`() {
        every { readVehicleByVehicleId.readVehicleById(any()) } returns VehicleMigrationObjectMother.vehicleDTO(id = vehicleId)

        val vehicleUsageId = null
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenter = CostCenterDescription("00H0014444")
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns
            plannedVehicleTransfer

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferUpdatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                }
                verify(exactly = 0) {
                    costCenterFinder.getCostCenter(any())
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should not publish if depreciationRelevantCostCenter is not found`() {
        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenter = CostCenterDescription("00H0014444")
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns plannedVehicleTransfer

        every { readVehicleByVehicleId.readVehicleById(any()) } returns VehicleMigrationObjectMother.vehicleDTO(id = vehicleId)

        every { costCenterFinder.getCostCenter(any()) } throws
            CostCenterNotFoundException(
                depreciationRelevantCostCenterId.value,
                "Could not find cost center",
                null,
            )

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferInitializedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
                consignee = null,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                }
                verify(exactly = 0) {
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should not publish if vehicleUsage is not found`() {
        every { readVehicleByVehicleId.readVehicleById(any()) } returns VehicleMigrationObjectMother.vehicleDTO(id = vehicleId)

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenter = CostCenterDescription("00H0014444")
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns plannedVehicleTransfer

        every { costCenterFinder.getCostCenter(any()) } returns VehicleMigrationObjectMother.depreciationRelevantCostCenter()

        every { vehicleUsageFinder.getVehicleUsage(any()) } throws
            VehicleUsageNotFoundException(
                vehicleUsageId.value,
                "Could not find vehicle usage",
            )

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferLicensePlateEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                }
                verify(exactly = 0) {
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should still publish if vehicle is in active state and license plate is missing`() {
        every { readVehicleByVehicleId.readVehicleById(any()) } returns VehicleMigrationObjectMother.vehicleDTO(id = vehicleId)

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenter = CostCenterDescription("00H0014444")
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns plannedVehicleTransfer

        every { costCenterFinder.getCostCenter(any()) } returns VehicleMigrationObjectMother.depreciationRelevantCostCenter()

        every { vehicleUsageFinder.getVehicleUsage(any()) } returns VehicleMigrationObjectMother.vehicleUsage()

        val licensePlate = null
        val firstRegistrationDate = ZonedDateTime.now()
        val registrationOrder =
            VehicleRegistrationAPIResponse(
                data =
                    VehicleMigrationObjectMother.vehicleRegistrationOrder(
                        firstRegistrationDateTime = firstRegistrationDate,
                        licensePlate = licensePlate,
                    ),
            )
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns registrationOrder

        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns
            VehicleMigrationObjectMother.vehicleResponsiblePersonDetails()

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferAutomaticCreatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
                consignee = null,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(
                    exactly = 1,
                ) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                    readRegistrationOrder.getLatestOrderBy(vehicleId)
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        vehicleResponsiblePerson.value,
                    )
                }
                verify(exactly = 1) {
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should publish vehicle migration with isActive as false and fleetInformation None if vehicle status is invalid`() {
        val vin = "1HGCM82633A123456"
        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val vehicleType = "PKW"
        val manufacturer = "Porsche"
        val modelDescription = "Taycan"
        val invalidStatusForMigration = VehicleStatus.SX99
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = invalidStatusForMigration,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenter = CostCenterDescription("00H0014444")
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns plannedVehicleTransfer

        val depreciationRelevantCostCenter = VehicleMigrationObjectMother.depreciationRelevantCostCenter()
        every { costCenterFinder.getCostCenter(any()) } returns depreciationRelevantCostCenter

        val vehicleUsage = VehicleMigrationObjectMother.vehicleUsage()
        every { vehicleUsageFinder.getVehicleUsage(any()) } returns vehicleUsage

        val licensePlate = null
        val firstRegistrationDate = ZonedDateTime.now()
        val registrationOrder =
            VehicleRegistrationAPIResponse(
                data =
                    VehicleMigrationObjectMother.vehicleRegistrationOrder(
                        firstRegistrationDateTime = firstRegistrationDate,
                        licensePlate = licensePlate,
                    ),
            )
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns registrationOrder

        val employeeNumber = "01234567"
        val firstName = "Max"
        val lastName = "Mustermann"
        val companyEmail = "<EMAIL>"
        val vehiclePersonDetail =
            VehicleMigrationObjectMother.vehicleResponsiblePersonDetails(
                employeeNumber,
                firstName,
                lastName,
                companyEmail,
            )
        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns vehiclePersonDetail

        every { publishVehicleUseCase.publishVehicle(any()) } just Runs

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferAutomaticCreatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
                consignee = null,
            ),
        )

        val expectedVehicleMigrationDto =
            VehicleMigrationDto(
                vehicle =
                    VehicleMigrationVehicleDto(
                        vin = vin,
                        vguid = vguid,
                        model = modelDescription,
                        depreciationRelevantCostCenter = depreciationRelevantCostCenter.description.value,
                        usingCostCenter = usingCostCenter.value,
                        vehicleUsage = vehicleUsage.usage,
                        fleetInformation = VehicleMigrationVehicleDto.FleetInformation.NONE,
                        isActive = false,
                        firstRegistrationDate = firstRegistrationDate.toOffsetDateTime(),
                        licensePlate = licensePlate,
                        vehicleType = vehicleType,
                        manufacturer = manufacturer,
                        leasingArt = leasingArt.id,
                    ),
                vehicleResponsiblePerson =
                    VehicleMigrationVehicleResponsiblePersonDto(
                        employeeNumber = employeeNumber,
                        firstName = firstName,
                        lastName = lastName,
                        email = companyEmail,
                    ),
            )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(
                    exactly = 1,
                ) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                    readRegistrationOrder.getLatestOrderBy(vehicleId)
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        vehicleResponsiblePerson.value,
                    )
                    publishVehicleUseCase.publishVehicle(expectedVehicleMigrationDto)
                }
            }
    }

    @Test
    fun `should publish vehicle migration with isActive as false and without vehicle responsible person if missing in vehicle transfer`() {
        val vin = "1HGCM82633A123456"
        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val vehicleType = "PKW"
        val manufacturer = "Porsche"
        val modelDescription = "Taycan"
        val status = VehicleStatus.S290
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = status,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = null
        val usingCostCenter = CostCenterDescription("00H0014444")
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns plannedVehicleTransfer

        val depreciationRelevantCostCenter = VehicleMigrationObjectMother.depreciationRelevantCostCenter()
        every { costCenterFinder.getCostCenter(any()) } returns depreciationRelevantCostCenter

        val vehicleUsage = VehicleMigrationObjectMother.vehicleUsage()
        every { vehicleUsageFinder.getVehicleUsage(any()) } returns vehicleUsage

        val licensePlate = null
        val firstRegistrationDate = ZonedDateTime.now()
        val registrationOrder =
            VehicleRegistrationAPIResponse(
                data =
                    VehicleMigrationObjectMother.vehicleRegistrationOrder(
                        firstRegistrationDateTime = firstRegistrationDate,
                        licensePlate = licensePlate,
                    ),
            )
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns registrationOrder

        every { publishVehicleUseCase.publishVehicle(any()) } just Runs

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferAutomaticCreatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
                consignee = null,
            ),
        )

        val expectedVehicleMigrationDto =
            VehicleMigrationDto(
                vehicle =
                    VehicleMigrationVehicleDto(
                        vin = vin,
                        vguid = vguid,
                        model = modelDescription,
                        depreciationRelevantCostCenter = depreciationRelevantCostCenter.description.value,
                        usingCostCenter = usingCostCenter.value,
                        vehicleUsage = vehicleUsage.usage,
                        fleetInformation = VehicleMigrationVehicleDto.FleetInformation.POST_USAGE,
                        isActive = false,
                        firstRegistrationDate = firstRegistrationDate.toOffsetDateTime(),
                        licensePlate = licensePlate,
                        vehicleType = vehicleType,
                        manufacturer = manufacturer,
                        leasingArt = leasingArt.id,
                    ),
                vehicleResponsiblePerson = null,
            )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(
                    exactly = 1,
                ) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                    readRegistrationOrder.getLatestOrderBy(vehicleId)
                    publishVehicleUseCase.publishVehicle(expectedVehicleMigrationDto)
                }
                verify(exactly = 0) {
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                }
            }
    }

    @Test
    fun `should publish vehicle migration with isActive as false and without vehicle responsible person if not found`() {
        val vin = "1HGCM82633A123456"
        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val vehicleType = "PKW"
        val manufacturer = "Porsche"
        val modelDescription = "Taycan"
        val status = VehicleStatus.S200
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = status,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenter = CostCenterDescription("00H0014444")
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns plannedVehicleTransfer

        val depreciationRelevantCostCenter = VehicleMigrationObjectMother.depreciationRelevantCostCenter()
        every { costCenterFinder.getCostCenter(any()) } returns depreciationRelevantCostCenter

        val vehicleUsage = VehicleMigrationObjectMother.vehicleUsage()
        every { vehicleUsageFinder.getVehicleUsage(any()) } returns vehicleUsage

        val licensePlate = null
        val firstRegistrationDate = ZonedDateTime.now()
        val registrationOrder =
            VehicleRegistrationAPIResponse(
                data =
                    VehicleMigrationObjectMother.vehicleRegistrationOrder(
                        firstRegistrationDateTime = firstRegistrationDate,
                        licensePlate = licensePlate,
                    ),
            )
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns registrationOrder

        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns null

        every { publishVehicleUseCase.publishVehicle(any()) } just Runs

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferAutomaticCreatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
                consignee = null,
            ),
        )

        val expectedVehicleMigrationDto =
            VehicleMigrationDto(
                vehicle =
                    VehicleMigrationVehicleDto(
                        vin = vin,
                        vguid = vguid,
                        model = modelDescription,
                        depreciationRelevantCostCenter = depreciationRelevantCostCenter.description.value,
                        usingCostCenter = usingCostCenter.value,
                        vehicleUsage = vehicleUsage.usage,
                        fleetInformation = VehicleMigrationVehicleDto.FleetInformation.USAGE,
                        isActive = false,
                        firstRegistrationDate = firstRegistrationDate.toOffsetDateTime(),
                        licensePlate = licensePlate,
                        vehicleType = vehicleType,
                        manufacturer = manufacturer,
                        leasingArt = leasingArt.id,
                    ),
                vehicleResponsiblePerson = null,
            )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(
                    exactly = 1,
                ) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                    readRegistrationOrder.getLatestOrderBy(vehicleId)
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        vehicleResponsiblePerson.value,
                    )
                    publishVehicleUseCase.publishVehicle(expectedVehicleMigrationDto)
                }
            }
    }

    @Suppress("ktlint:standard:max-line-length")
    @Test
    fun `should publish vehicle migration with isActive as false and without vehicle responsible person if ReadVehiclePersonException is thrown`() {
        val vin = "1HGCM82633A123456"
        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val vehicleType = "PKW"
        val manufacturer = "Porsche"
        val modelDescription = "Taycan"
        val status = VehicleStatus.S200
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = status,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenter = CostCenterDescription("00H0014444")
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        val plannedVehicleTransfer =
            VehicleMigrationObjectMother.plannedVehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )
        every { plannedVehicleTransferFinder.getPlannedVehicleTransfer(any()) } returns plannedVehicleTransfer

        val depreciationRelevantCostCenter = VehicleMigrationObjectMother.depreciationRelevantCostCenter()
        every { costCenterFinder.getCostCenter(any()) } returns depreciationRelevantCostCenter

        val vehicleUsage = VehicleMigrationObjectMother.vehicleUsage()
        every { vehicleUsageFinder.getVehicleUsage(any()) } returns vehicleUsage

        val licensePlate = null
        val firstRegistrationDate = ZonedDateTime.now()
        val registrationOrder =
            VehicleRegistrationAPIResponse(
                data =
                    VehicleMigrationObjectMother.vehicleRegistrationOrder(
                        firstRegistrationDateTime = firstRegistrationDate,
                        licensePlate = licensePlate,
                    ),
            )
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns registrationOrder

        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } throws
            ReadVehiclePersonException(
                message = "Cannot connect to service",
                null,
            )

        every { publishVehicleUseCase.publishVehicle(any()) } just Runs

        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferAutomaticCreatedEvent(
                vehicleId = vehicleId,
                plannedVehicleTransferKey = vehicleTransferKey,
                consignee = null,
            ),
        )

        val expectedVehicleMigrationDto =
            VehicleMigrationDto(
                vehicle =
                    VehicleMigrationVehicleDto(
                        vin = vin,
                        vguid = vguid,
                        model = modelDescription,
                        depreciationRelevantCostCenter = depreciationRelevantCostCenter.description.value,
                        usingCostCenter = usingCostCenter.value,
                        vehicleUsage = vehicleUsage.usage,
                        fleetInformation = VehicleMigrationVehicleDto.FleetInformation.USAGE,
                        isActive = false,
                        firstRegistrationDate = firstRegistrationDate.toOffsetDateTime(),
                        licensePlate = licensePlate,
                        vehicleType = vehicleType,
                        manufacturer = manufacturer,
                        leasingArt = leasingArt.id,
                    ),
                vehicleResponsiblePerson = null,
            )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(
                    exactly = 1,
                ) {
                    plannedVehicleTransferFinder.getPlannedVehicleTransfer(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                    readRegistrationOrder.getLatestOrderBy(vehicleId)
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        vehicleResponsiblePerson.value,
                    )
                    publishVehicleUseCase.publishVehicle(expectedVehicleMigrationDto)
                }
            }
    }

    @Test
    fun `should not publish vehicle migration if current vehicle transfer is missing`() {
        every { currentVehicleTransferService.retrieveCurrentVehicleTransferKey(any()) } returns null

        applicationEventPublisher.publishEvent(
            VehicleStatusChangedEvent(
                vehicleId = vehicleId,
                status = VehicleStatus.S200,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    currentVehicleTransferService.retrieveCurrentVehicleTransferKey(vehicleId)
                }

                verify(exactly = 0) {
                    plannedVehicleTransferFinder.findPlannedVehicleTransfer(any())
                    vehicleTransferFinder.findVehicleTransferByKey(any())
                    readVehicleByVehicleId.readVehicleById(any())
                    costCenterFinder.getCostCenter(any())
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should not publish vehicle migration if both planned vehicle transfer  and vehicle transfer is missing`() {
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        every { currentVehicleTransferService.retrieveCurrentVehicleTransferKey(any()) } returns vehicleTransferKey

        every { plannedVehicleTransferFinder.findPlannedVehicleTransfer(any()) } returns null

        every { vehicleTransferFinder.findVehicleTransferByKey(any()) } returns null

        applicationEventPublisher.publishEvent(
            VehicleStatusChangedEvent(
                vehicleId = vehicleId,
                status = VehicleStatus.S096,
            ),
        )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    currentVehicleTransferService.retrieveCurrentVehicleTransferKey(vehicleId)
                    plannedVehicleTransferFinder.findPlannedVehicleTransfer(vehicleTransferKey)
                    vehicleTransferFinder.findVehicleTransferByKey(vehicleTransferKey)
                }

                verify(exactly = 0) {
                    readVehicleByVehicleId.readVehicleById(any())
                    costCenterFinder.getCostCenter(any())
                    vehicleUsageFinder.getVehicleUsage(any())
                    readRegistrationOrder.getLatestOrderBy(any())
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        any(),
                    )
                    publishVehicleUseCase.publishVehicle(any())
                }
            }
    }

    @Test
    fun `should publish dms vehicle migration on vehicle status change with vehicle transfer`() {
        val vin = "1HGCM82633A123456"
        val vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0"
        val vehicleType = "PKW"
        val manufacturer = "Porsche"
        val modelDescription = "Taycan"
        val status = VehicleStatus.S096
        val vehicleDTO =
            VehicleMigrationObjectMother.vehicleDTO(
                id = vehicleId,
                vin = vin,
                vguid = vguid,
                model = modelDescription,
                status = status,
            )
        every { readVehicleByVehicleId.readVehicleById(any()) } returns vehicleDTO

        val vehicleUsageId = VTVehicleUsageId(UUID.randomUUID())
        val vehicleResponsiblePerson = EmployeeNumber("01234567")
        val usingCostCenterDescription = "00H0014444"
        val usingCostCenter = CostCenterDescription(usingCostCenterDescription)
        val depreciationRelevantCostCenterId = VTCostCenterId(UUID.randomUUID())
        every { plannedVehicleTransferFinder.findPlannedVehicleTransfer(any()) } returns null

        every { currentVehicleTransferService.retrieveCurrentVehicleTransferKey(any()) } returns vehicleTransferKey

        val vehicleTransfer =
            VehicleMigrationObjectMother.vehicleTransfer(
                vehicleId = vehicleId,
                vehicleUsageId = vehicleUsageId,
                depreciationRelevantCostCenterId = depreciationRelevantCostCenterId,
                usingCostCenter = usingCostCenter,
                vehicleResponsiblePerson = vehicleResponsiblePerson,
            )

        every { vehicleTransferFinder.findVehicleTransferByKey(any()) } returns vehicleTransfer

        val depreciationRelevantCostCenterDescriptionValue = "00H0014444"
        val depreciationRelevantCostCenterDescription =
            CostCenterDescription(depreciationRelevantCostCenterDescriptionValue)
        val depreciationRelevantCostCenter =
            VehicleMigrationObjectMother.depreciationRelevantCostCenter(description = depreciationRelevantCostCenterDescription)
        every { costCenterFinder.getCostCenter(any()) } returns depreciationRelevantCostCenter

        val usage = "usage"
        val vehicleUsage = VehicleMigrationObjectMother.vehicleUsage(usage)
        every { vehicleUsageFinder.getVehicleUsage(any()) } returns vehicleUsage

        val licensePlate = "SW 1234"
        val dateTimeString = "2025-05-22T14:10:10+02:00[Europe/Berlin]"
        val firstRegistrationDate = ZonedDateTime.parse(dateTimeString)
        val registrationOrder =
            VehicleRegistrationAPIResponse(
                data =
                    VehicleMigrationObjectMother.vehicleRegistrationOrder(
                        firstRegistrationDateTime = firstRegistrationDate,
                        licensePlate = licensePlate,
                    ),
            )
        every { readRegistrationOrder.getLatestOrderBy(any()) } returns registrationOrder

        val employeeNumber = "01234567"
        val firstName = "Max"
        val lastName = "Mustermann"
        val companyEmail = "<EMAIL>"
        val vehiclePersonDetail =
            VehicleMigrationObjectMother.vehicleResponsiblePersonDetails(
                employeeNumber,
                firstName,
                lastName,
                companyEmail,
            )
        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns vehiclePersonDetail

        every { publishVehicleUseCase.publishVehicle(any()) } just Runs

        dmsVehicleMigrationApplicationService.handleVehicleStatusChangedEvent(
            VehicleStatusChangedEvent(
                vehicleId = vehicleId,
                status = VehicleStatus.S096,
            ),
        )

        val expectedVehicleMigrationDto =
            VehicleMigrationDto(
                vehicle =
                    VehicleMigrationVehicleDto(
                        vin = vin,
                        vguid = vguid,
                        model = modelDescription,
                        depreciationRelevantCostCenter = depreciationRelevantCostCenterDescriptionValue,
                        usingCostCenter = usingCostCenterDescription,
                        vehicleUsage = usage,
                        fleetInformation = VehicleMigrationVehicleDto.FleetInformation.PRE_USAGE,
                        isActive = true,
                        firstRegistrationDate = OffsetDateTime.parse("2025-05-22T14:10:10+02:00"),
                        licensePlate = licensePlate,
                        vehicleType = vehicleType,
                        manufacturer = manufacturer,
                        leasingArt = leasingArt.id,
                    ),
                vehicleResponsiblePerson =
                    VehicleMigrationVehicleResponsiblePersonDto(
                        employeeNumber = employeeNumber,
                        firstName = firstName,
                        lastName = lastName,
                        email = companyEmail,
                    ),
            )

        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                verify(exactly = 1) {
                    currentVehicleTransferService.retrieveCurrentVehicleTransferKey(vehicleId)
                    plannedVehicleTransferFinder.findPlannedVehicleTransfer(vehicleTransferKey)
                    vehicleTransferFinder.findVehicleTransferByKey(vehicleTransferKey)
                    readVehicleByVehicleId.readVehicleById(vehicleId)
                    costCenterFinder.getCostCenter(CostCenterId(depreciationRelevantCostCenterId.value))
                    vehicleUsageFinder.getVehicleUsage(VehicleUsageId(vehicleUsageId.value))
                    readRegistrationOrder.getLatestOrderBy(vehicleId)
                    readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(
                        vehicleResponsiblePerson.value,
                    )
                    publishVehicleUseCase.publishVehicle(expectedVehicleMigrationDto)
                }
            }
    }

    companion object {
        val vehicleId: UUID = UUID.randomUUID()
        val vehicleTransferKey = VehicleTransferKey(1)

        @JvmStatic
        fun eventsProvider(): Stream<Arguments> =
            listOf(
                PlannedVehicleTransferManualCreatedEvent(
                    vehicleId = vehicleId,
                    consignee = null,
                    plannedVehicleTransferKey = vehicleTransferKey,
                ),
                PlannedVehicleTransferAutomaticCreatedEvent(
                    vehicleId = vehicleId,
                    consignee = null,
                    plannedVehicleTransferKey = vehicleTransferKey,
                ),
                PlannedVehicleTransferInitializedEvent(
                    vehicleId = vehicleId,
                    consignee = null,
                    plannedVehicleTransferKey = vehicleTransferKey,
                ),
                PlannedVehicleTransferUpdatedEvent(
                    vehicleId = vehicleId,
                    plannedVehicleTransferKey = vehicleTransferKey,
                ),
                PlannedVehicleTransferLicensePlateEvent(
                    vehicleId = vehicleId,
                    plannedVehicleTransferKey = vehicleTransferKey,
                ),
                VehicleTransferDeliveredEvent(
                    vehicleId = vehicleId,
                    vehicleTransferKey = vehicleTransferKey,
                ),
                VehicleTransferReturnedEvent(
                    vehicleId = vehicleId,
                    vehicleTransferKey = vehicleTransferKey,
                ),
                VehicleTransferUpdatedEvent(
                    vehicleId = vehicleId,
                    vehicleTransferKey = vehicleTransferKey,
                ),
                VehicleTransferLicensePlateEvent(
                    vehicleId = vehicleId,
                    vehicleTransferKey = vehicleTransferKey,
                ),
            ).map { Arguments.of(it) }.stream()
    }
}

@SpringBootTest(properties = ["dms-vehicle-migration.enabled=false"])
@Transactional
@Import(TestcontainersConfiguration::class)
class DMSVehicleMigrationApplicationServiceFeatureFlagTest {
    @Autowired
    private lateinit var applicationEventPublisher: ApplicationEventPublisher

    @Autowired
    private lateinit var applicationContext: ApplicationContext

    @MockkBean(relaxed = true)
    private lateinit var updateVehicleStatusService: UpdateVehicleStatusService

    @MockkBean(relaxed = true)
    private lateinit var vehicleTransferApplicationService: VehicleTransferApplicationService

    @MockkBean(relaxed = true)
    private lateinit var plannedVehicleTransferApplicationService: PlannedVehicleTransferApplicationService

    @MockkBean(relaxed = true)
    private lateinit var currentVehicleTransferService: CurrentVehicleTransferService

    @MockkBean(relaxed = true)
    private lateinit var historyCaptureService: HistoryCaptureService

    @MockkBean(relaxed = true)
    private lateinit var mileageReadingService: MileageReadingService

    @Test
    fun `should not handle event if feature flag is disabled`() {
        applicationEventPublisher.publishEvent(
            PlannedVehicleTransferManualCreatedEvent(
                plannedVehicleTransferKey = VehicleTransferKey(1),
                consignee = null,
                vehicleId = UUID.randomUUID(),
            ),
        )
        TestTransaction.flagForCommit()
        TestTransaction.end()

        TestTransaction.start()

        await atMost
            Durations.ONE_SECOND withPollDelay
            Durations.TWO_HUNDRED_MILLISECONDS untilAsserted
            {
                val listenerBeans =
                    applicationContext.getBeansOfType(DMSVehicleMigrationApplicationService::class.java)
                assert(listenerBeans.isEmpty())
            }
    }
}
