/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generateprovisionofdeliverydocument

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.documentgeneration.objectmothers.ProvisionOfDeliveryObjectMother
import com.fleetmanagement.modules.documentgeneration.objectmothers.VehicleDataObjectMother
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehicletransfer.domain.entities.TireSet
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UsageGroupId
import io.mockk.every
import io.mockk.mockkStatic
import io.mockk.unmockkAll
import org.junit.jupiter.api.AfterEach
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.LocalDateTime
import java.time.OffsetDateTime
import java.time.ZoneId
import java.time.ZoneOffset
import java.util.UUID

class GenerateProvisionOfDeliveryDocumentConverterTest {
    @BeforeEach
    fun setUp() {
        mockkStatic(LocalDateTime::class)
    }

    @AfterEach
    fun tearDown() {
        unmockkAll()
    }

    @Test
    fun `should convert ProvisionOfDeliveryTableRowData to ProvisionOfDeliveryTemplateTableRowData`() {
        val vehicleId = UUID.randomUUID()
        val vin = "vin"
        val status = VehicleStatus.entries.random().name
        val plannedDeliveryDate = OffsetDateTime.of(2025, 7, 22, 12, 10, 10, 10, ZoneOffset.UTC)
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val employeeNumber = "00123456"
        val firstName = "John"
        val lastName = "Doe"
        val vehicleResponsiblePerson = EmployeeNumber(employeeNumber)
        val licensePlate = "SW-1234"
        val desiredTireSet = TireSet.SR
        val provisionForDeliveryComment = "comment"
        val compoundName = "compoundName"
        val parkingLot = "parkingLot"

        val vehicleData =
            VehicleDataObjectMother.vehicleData(
                vehicleId,
                vin = vin,
                status = status,
            )

        val locationData = ProvisionOfDeliveryObjectMother.vehicleLocation(vehicleId, compoundName, parkingLot)

        val vehiclePersonDetail =
            ProvisionOfDeliveryObjectMother.vehiclePersonDetails(
                employeeNumber,
                firstName,
                lastName,
            )

        val transferData =
            ProvisionOfDeliveryObjectMother.plannedVehicleTransfer(
                vehicleId,
                usageGroupId,
                vehicleResponsiblePerson,
                licensePlate,
                desiredTireSet,
                provisionForDeliveryComment,
                plannedDeliveryDate,
            )

        val provisionOfDeliveryTemplateTableRowData =
            ProvisionOfDeliveryTableRowData(
                vehicleData = vehicleData,
                locationData = locationData,
                transferData = transferData,
                vehicleResponsiblePersonDetails = vehiclePersonDetail,
            ).toProvisionOfDeliveryTemplateTableRowData()

        val tableData = provisionOfDeliveryTemplateTableRowData.data

        assertEquals("22.07.2025", tableData[0])
        assertEquals(status, tableData[1])
        assertEquals(lastName, tableData[2])
        assertEquals(vin, tableData[3])
        assertEquals(licensePlate, tableData[4])
        assertEquals("Nein", tableData[5])
        assertEquals(compoundName, tableData[6])
        assertEquals(parkingLot, tableData[7])
        assertEquals(desiredTireSet.name, tableData[8])
        assertEquals(provisionForDeliveryComment, tableData[9])
    }

    @Test
    fun `should return empty strings if some data is missing`() {
        val vehicleId = UUID.randomUUID()
        val vin = "vin"
        val plannedDeliveryDate = OffsetDateTime.of(2025, 7, 22, 12, 10, 10, 10, ZoneOffset.UTC)
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val employeeNumber = "00123456"
        val vehicleResponsiblePerson = EmployeeNumber(employeeNumber)
        val licensePlate = "SW-1234"
        val provisionForDeliveryComment = "comment"
        val compoundName = "compoundName"
        val parkingLot = "parkingLot"

        val vehicleData =
            VehicleDataObjectMother.vehicleData(
                vehicleId,
                vin = vin,
                status = null,
            )

        val locationData = ProvisionOfDeliveryObjectMother.vehicleLocation(vehicleId, compoundName, parkingLot)

        val vehiclePersonDetail =
            null

        val transferData =
            ProvisionOfDeliveryObjectMother.plannedVehicleTransfer(
                vehicleId,
                usageGroupId,
                vehicleResponsiblePerson,
                licensePlate,
                null,
                provisionForDeliveryComment,
                plannedDeliveryDate,
            )

        val provisionOfDeliveryTemplateTableRowData =
            ProvisionOfDeliveryTableRowData(
                vehicleData = vehicleData,
                locationData = locationData,
                transferData = transferData,
                vehicleResponsiblePersonDetails = vehiclePersonDetail,
            ).toProvisionOfDeliveryTemplateTableRowData()

        val tableData = provisionOfDeliveryTemplateTableRowData.data

        assertEquals("22.07.2025", tableData[0])
        assertEquals("", tableData[1])
        assertEquals("", tableData[2])
        assertEquals(vin, tableData[3])
        assertEquals(licensePlate, tableData[4])
        assertEquals("Nein", tableData[5])
        assertEquals(compoundName, tableData[6])
        assertEquals(parkingLot, tableData[7])
        assertEquals("", tableData[8])
        assertEquals(provisionForDeliveryComment, tableData[9])
    }

    @Test
    fun `should convert list of ProvisionOfDeliveryTemplateTableRowData to ProvisionForDeliveryTemplateData`() {
        val berlinZone = ZoneId.of("Europe/Berlin")
        val mockedTime = LocalDateTime.of(2024, 1, 1, 10, 20, 30)

        every { LocalDateTime.now(berlinZone) } returns mockedTime

        val listOfProvisionOfDeliveryTemplateTableRowData =
            listOf(
                ProvisionOfDeliveryTemplateTableRowData(
                    data = listOf("a", "b", "c"),
                ),
                ProvisionOfDeliveryTemplateTableRowData(
                    data = listOf("d", "e", "f"),
                ),
            )

        val provisionForDeliveryTemplateData =
            listOfProvisionOfDeliveryTemplateTableRowData.toProvisionForDeliveryTemplateData()
        assertEquals("01.01.2024 10:20", provisionForDeliveryTemplateData.creationDateTime)
        val deliveryList = provisionForDeliveryTemplateData.deliveryListTableData[0]
        val anotherDeliveryList = provisionForDeliveryTemplateData.deliveryListTableData[1]

        provisionForDeliveryTemplateData.deliveryListTableData.any {
            it == deliveryList
        }

        provisionForDeliveryTemplateData.deliveryListTableData.any {
            it == anotherDeliveryList
        }
    }

    @Test
    fun `should convert plannedDeliveryDate to LocalDate`() {
        val vehicleId = UUID.randomUUID()
        val vin = "vin"
        val status = VehicleStatus.entries.random().name
        val plannedDeliveryDate = OffsetDateTime.of(2025, 7, 22, 22, 0, 0, 0, ZoneOffset.UTC)
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val employeeNumber = "00123456"
        val firstName = "John"
        val lastName = "Doe"
        val vehicleResponsiblePerson = EmployeeNumber(employeeNumber)
        val licensePlate = "SW-1234"
        val desiredTireSet = TireSet.SR
        val provisionForDeliveryComment = "comment"
        val compoundName = "compoundName"
        val parkingLot = "parkingLot"

        val vehicleData =
            VehicleDataObjectMother.vehicleData(
                vehicleId,
                vin = vin,
                status = status,
            )

        val locationData = ProvisionOfDeliveryObjectMother.vehicleLocation(vehicleId, compoundName, parkingLot)

        val vehiclePersonDetail =
            ProvisionOfDeliveryObjectMother.vehiclePersonDetails(
                employeeNumber,
                firstName,
                lastName,
            )

        val transferData =
            ProvisionOfDeliveryObjectMother.plannedVehicleTransfer(
                vehicleId,
                usageGroupId,
                vehicleResponsiblePerson,
                licensePlate,
                desiredTireSet,
                provisionForDeliveryComment,
                plannedDeliveryDate,
            )

        val provisionOfDeliveryTemplateTableRowData =
            ProvisionOfDeliveryTableRowData(
                vehicleData = vehicleData,
                locationData = locationData,
                transferData = transferData,
                vehicleResponsiblePersonDetails = vehiclePersonDetail,
            ).toProvisionOfDeliveryTemplateTableRowData()

        val tableData = provisionOfDeliveryTemplateTableRowData.data

        assertEquals("23.07.2025", tableData[0])
    }
}
