/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.features.generateprovisionofdeliverydocument

import com.fleetmanagement.emhshared.USAGE_GROUP_DESCRIPTION_DEVELOPMENT
import com.fleetmanagement.emhshared.USAGE_GROUP_DESCRIPTION_PERSON
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupDto
import com.fleetmanagement.modules.consigneedatasheet.application.UsageGroupFinder
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.documentgeneration.api.GenerateProvisionOfDeliveryDocumentException
import com.fleetmanagement.modules.documentgeneration.api.NoScheduledTransfersException
import com.fleetmanagement.modules.documentgeneration.features.FillTemplateService
import com.fleetmanagement.modules.documentgeneration.features.FormFieldNotFoundException
import com.fleetmanagement.modules.documentgeneration.objectmothers.DocumentObjectMother
import com.fleetmanagement.modules.documentgeneration.objectmothers.ProvisionOfDeliveryObjectMother
import com.fleetmanagement.modules.documentgeneration.objectmothers.VehicleDataObjectMother
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.repository.entities.VehicleStatus
import com.fleetmanagement.modules.vehiclelocation.api.LastKnownLocation
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonDetailByEmployeeNumber
import com.fleetmanagement.modules.vehicleperson.api.readvehicleperson.ReadVehiclePersonException
import com.fleetmanagement.modules.vehicletransfer.application.PlannedVehicleTransferFinder
import com.fleetmanagement.modules.vehicletransfer.domain.entities.TireSet
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UsageGroupId
import io.mockk.*
import org.hamcrest.MatcherAssert.assertThat
import org.hamcrest.Matchers
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertInstanceOf
import org.junit.jupiter.api.assertThrows
import java.time.OffsetDateTime
import java.util.*

class GenerateProvisionOfDeliveryDocumentServiceTest {
    private val plannedVehicleTransferFinder: PlannedVehicleTransferFinder = mockk()
    private val usageGroupFinder: UsageGroupFinder = mockk()
    private val readVehicleByVehicleId: ReadVehicleByVehicleId = mockk()
    private val fillTemplateService: FillTemplateService = mockk()
    private val lastKnownLocation: LastKnownLocation = mockk()
    private val readVehiclePersonDetailByEmployeeNumber: ReadVehiclePersonDetailByEmployeeNumber = mockk()

    @Test
    fun `should generate provision of delivery document`() {
        val date = OffsetDateTime.now()
        val vehicleId = UUID.randomUUID()
        val vin = "vin"
        val status = VehicleStatus.entries.random().name
        val plannedDeliveryDate = OffsetDateTime.now()
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val employeeNumber = "00123456"
        val anotherEmployeeNumber = "00222222"
        val firstName = "John"
        val lastName = "Doe"
        val vehicleResponsiblePerson = EmployeeNumber(employeeNumber)
        val anotherVehicleResponsiblePerson = EmployeeNumber(anotherEmployeeNumber)
        val licensePlate = "SW-1234"
        val desiredTireSet = TireSet.SR
        val provisionForDeliveryComment = "comment"
        val compoundName = "compoundName"
        val parkingLot = "parkingLot"

        val anotherVehicleId = UUID.randomUUID()
        val anotherUsageGroupId = UsageGroupId(UUID.randomUUID())

        every { plannedVehicleTransferFinder.findPlannedTransfersForProvisionForDelivery(any()) } returns
            listOf(
                ProvisionOfDeliveryObjectMother.plannedVehicleTransfer(
                    vehicleId,
                    usageGroupId,
                    vehicleResponsiblePerson,
                    licensePlate,
                    desiredTireSet,
                    provisionForDeliveryComment,
                    plannedDeliveryDate,
                ),
                ProvisionOfDeliveryObjectMother.plannedVehicleTransfer(
                    anotherVehicleId,
                    usageGroupId,
                    anotherVehicleResponsiblePerson,
                    licensePlate,
                    desiredTireSet,
                    provisionForDeliveryComment,
                    plannedDeliveryDate,
                ),
            )

        every { usageGroupFinder.readAllUsageGroups() } returns
            listOf(
                UsageGroupDto(
                    id = usageGroupId.value,
                    description = USAGE_GROUP_DESCRIPTION_PERSON,
                    version = 0,
                    usageGroupId = 4,
                ),
                UsageGroupDto(
                    id = anotherUsageGroupId.value,
                    description = USAGE_GROUP_DESCRIPTION_DEVELOPMENT,
                    version = 0,
                    usageGroupId = 1,
                ),
            )

        every { readVehicleByVehicleId.readVehicleById(any()) } returns
            VehicleDataObjectMother.vehicleData(
                vehicleId,
                vin = vin,
                status = status,
            )

        every {
            lastKnownLocation.findLastKnownVehicleLocationBy(any())
        } returns ProvisionOfDeliveryObjectMother.vehicleLocation(vehicleId, compoundName, parkingLot)

        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns
            ProvisionOfDeliveryObjectMother.vehiclePersonDetails(
                employeeNumber,
                firstName,
                lastName,
            )

        every { fillTemplateService.fillTemplate(any(), any()) } returns
            DocumentObjectMother.getDocumentWithTable(
                numberOfTables = 1,
            )

        every { fillTemplateService.fillTable(any(), any(), any()) } just Runs

        val result =
            GenerateProvisionOfDeliveryDocumentService(
                plannedVehicleTransferFinder,
                usageGroupFinder,
                readVehicleByVehicleId,
                fillTemplateService,
                lastKnownLocation,
                readVehiclePersonDetailByEmployeeNumber,
            ).generateProvisionOfDelivery(date)

        assertThat(result, Matchers.notNullValue())
        verify(exactly = 1) {
            plannedVehicleTransferFinder.findPlannedTransfersForProvisionForDelivery(date)
            fillTemplateService.fillTemplate("ProvisionOfDeliveryDocument.docx", any())
            fillTemplateService.fillTable(any(), any(), any())
            usageGroupFinder.readAllUsageGroups()
            readVehicleByVehicleId.readVehicleById(vehicleId)
            lastKnownLocation.findLastKnownVehicleLocationBy(vehicleId)
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(employeeNumber)
            readVehicleByVehicleId.readVehicleById(anotherVehicleId)
            lastKnownLocation.findLastKnownVehicleLocationBy(anotherVehicleId)
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(anotherEmployeeNumber)
        }
    }

    @Test
    fun `should generate provision of delivery document only for transfers where usage group is PERSON`() {
        val date = OffsetDateTime.now()
        val vehicleId = UUID.randomUUID()
        val vin = "vin"
        val status = VehicleStatus.entries.random().name
        val plannedDeliveryDate = OffsetDateTime.now()
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val employeeNumber = "00123456"
        val anotherEmployeeNumber = "00222222"
        val firstName = "John"
        val lastName = "Doe"
        val vehicleResponsiblePerson = EmployeeNumber(employeeNumber)
        val anotherVehicleResponsiblePerson = EmployeeNumber(anotherEmployeeNumber)
        val licensePlate = "SW-1234"
        val desiredTireSet = TireSet.SR
        val provisionForDeliveryComment = "comment"
        val compoundName = "compoundName"
        val parkingLot = "parkingLot"

        val anotherVehicleId = UUID.randomUUID()
        val anotherUsageGroupId = UsageGroupId(UUID.randomUUID())

        every { plannedVehicleTransferFinder.findPlannedTransfersForProvisionForDelivery(any()) } returns
            listOf(
                ProvisionOfDeliveryObjectMother.plannedVehicleTransfer(
                    vehicleId,
                    usageGroupId,
                    vehicleResponsiblePerson,
                    licensePlate,
                    desiredTireSet,
                    provisionForDeliveryComment,
                    plannedDeliveryDate,
                ),
                ProvisionOfDeliveryObjectMother.plannedVehicleTransfer(
                    anotherVehicleId,
                    anotherUsageGroupId,
                    anotherVehicleResponsiblePerson,
                    licensePlate,
                    desiredTireSet,
                    provisionForDeliveryComment,
                    plannedDeliveryDate,
                ),
            )

        every { usageGroupFinder.readAllUsageGroups() } returns
            listOf(
                UsageGroupDto(
                    id = usageGroupId.value,
                    description = USAGE_GROUP_DESCRIPTION_PERSON,
                    version = 0,
                    usageGroupId = 4,
                ),
                UsageGroupDto(
                    id = anotherUsageGroupId.value,
                    description = USAGE_GROUP_DESCRIPTION_DEVELOPMENT,
                    version = 0,
                    usageGroupId = 1,
                ),
            )

        every { readVehicleByVehicleId.readVehicleById(any()) } returns
            VehicleDataObjectMother.vehicleData(
                vehicleId,
                vin = vin,
                status = status,
            )

        every {
            lastKnownLocation.findLastKnownVehicleLocationBy(any())
        } returns ProvisionOfDeliveryObjectMother.vehicleLocation(vehicleId, compoundName, parkingLot)

        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns
            ProvisionOfDeliveryObjectMother.vehiclePersonDetails(
                employeeNumber,
                firstName,
                lastName,
            )

        every { fillTemplateService.fillTemplate(any(), any()) } returns
            DocumentObjectMother.getDocumentWithTable(
                numberOfTables = 1,
            )

        every { fillTemplateService.fillTable(any(), any(), any()) } just Runs

        val result =
            GenerateProvisionOfDeliveryDocumentService(
                plannedVehicleTransferFinder,
                usageGroupFinder,
                readVehicleByVehicleId,
                fillTemplateService,
                lastKnownLocation,
                readVehiclePersonDetailByEmployeeNumber,
            ).generateProvisionOfDelivery(date)

        assertThat(result, Matchers.notNullValue())
        verify(exactly = 1) {
            plannedVehicleTransferFinder.findPlannedTransfersForProvisionForDelivery(date)
            usageGroupFinder.readAllUsageGroups()
            readVehicleByVehicleId.readVehicleById(vehicleId)
            fillTemplateService.fillTemplate("ProvisionOfDeliveryDocument.docx", any())
            fillTemplateService.fillTable(any(), any(), any())
            lastKnownLocation.findLastKnownVehicleLocationBy(vehicleId)
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(employeeNumber)
        }
    }

    @Test
    fun `should throw NoScheduledTransfersException in case there are no planned vehicle transfer for given date`() {
        val date = OffsetDateTime.now()
        val vehicleId = UUID.randomUUID()
        val vin = "vin"
        val status = VehicleStatus.entries.random().name
        val employeeNumber = "00123456"
        val firstName = "John"
        val lastName = "Doe"
        val compoundName = "compoundName"
        val parkingLot = "parkingLot"

        every { plannedVehicleTransferFinder.findPlannedTransfersForProvisionForDelivery(any()) } returns
            emptyList()

        every { usageGroupFinder.readAllUsageGroups() } returns
            listOf(
                UsageGroupDto(
                    id = UUID.randomUUID(),
                    description = USAGE_GROUP_DESCRIPTION_PERSON,
                    version = 0,
                    usageGroupId = 4,
                ),
                UsageGroupDto(
                    id = UUID.randomUUID(),
                    description = USAGE_GROUP_DESCRIPTION_DEVELOPMENT,
                    version = 0,
                    usageGroupId = 1,
                ),
            )

        every { readVehicleByVehicleId.readVehicleById(any()) } returns
            VehicleDataObjectMother.vehicleData(
                vehicleId,
                vin = vin,
                status = status,
            )

        every {
            lastKnownLocation.findLastKnownVehicleLocationBy(any())
        } returns ProvisionOfDeliveryObjectMother.vehicleLocation(vehicleId, compoundName, parkingLot)

        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns
            ProvisionOfDeliveryObjectMother.vehiclePersonDetails(
                employeeNumber,
                firstName,
                lastName,
            )

        every { fillTemplateService.fillTemplate(any(), any()) } returns
            DocumentObjectMother.getDocumentWithTable(
                numberOfTables = 1,
            )

        every { fillTemplateService.fillTable(any(), any(), any()) } just Runs

        val error =
            assertThrows<NoScheduledTransfersException> {
                GenerateProvisionOfDeliveryDocumentService(
                    plannedVehicleTransferFinder,
                    usageGroupFinder,
                    readVehicleByVehicleId,
                    fillTemplateService,
                    lastKnownLocation,
                    readVehiclePersonDetailByEmployeeNumber,
                ).generateProvisionOfDelivery(date)
            }

        assertEquals("Could not generate provision of delivery document. No planned vehicle transfer found.", error.message)
    }

    @Test
    fun `should throw GenerateProvisionOfDeliveryDocumentException when template is missing form field`() {
        val date = OffsetDateTime.now()
        val vehicleId = UUID.randomUUID()
        val vin = "vin"
        val status = VehicleStatus.entries.random().name
        val plannedDeliveryDate = OffsetDateTime.now()
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val employeeNumber = "00123456"
        val firstName = "John"
        val lastName = "Doe"
        val vehicleResponsiblePerson = EmployeeNumber(employeeNumber)
        val licensePlate = "SW-1234"
        val desiredTireSet = TireSet.SR
        val provisionForDeliveryComment = "comment"
        val compoundName = "compoundName"
        val parkingLot = "parkingLot"

        every { plannedVehicleTransferFinder.findPlannedTransfersForProvisionForDelivery(any()) } returns
            listOf(
                ProvisionOfDeliveryObjectMother.plannedVehicleTransfer(
                    vehicleId,
                    usageGroupId,
                    vehicleResponsiblePerson,
                    licensePlate,
                    desiredTireSet,
                    provisionForDeliveryComment,
                    plannedDeliveryDate,
                ),
            )

        every { usageGroupFinder.readAllUsageGroups() } returns
            listOf(
                UsageGroupDto(
                    id = usageGroupId.value,
                    description = USAGE_GROUP_DESCRIPTION_PERSON,
                    version = 0,
                    usageGroupId = 4,
                ),
                UsageGroupDto(
                    id = UUID.randomUUID(),
                    description = USAGE_GROUP_DESCRIPTION_DEVELOPMENT,
                    version = 0,
                    usageGroupId = 1,
                ),
            )

        every { readVehicleByVehicleId.readVehicleById(any()) } returns
            VehicleDataObjectMother.vehicleData(
                vehicleId,
                vin = vin,
                status = status,
            )

        every {
            lastKnownLocation.findLastKnownVehicleLocationBy(any())
        } returns ProvisionOfDeliveryObjectMother.vehicleLocation(vehicleId, compoundName, parkingLot)

        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } returns
            ProvisionOfDeliveryObjectMother.vehiclePersonDetails(
                employeeNumber,
                firstName,
                lastName,
            )

        every { fillTemplateService.fillTemplate(any(), any()) } throws FormFieldNotFoundException("Cannot find form field")

        every { fillTemplateService.fillTable(any(), any(), any()) } just Runs

        val error =
            assertThrows<GenerateProvisionOfDeliveryDocumentException> {
                GenerateProvisionOfDeliveryDocumentService(
                    plannedVehicleTransferFinder,
                    usageGroupFinder,
                    readVehicleByVehicleId,
                    fillTemplateService,
                    lastKnownLocation,
                    readVehiclePersonDetailByEmployeeNumber,
                ).generateProvisionOfDelivery(date)
            }

        assertInstanceOf<FormFieldNotFoundException>(error.cause)
        assertEquals("Error while generating provision of delivery document. Cannot find form field", error.message)
    }

    @Test
    fun `should handle ReadVehiclePersonException`() {
        val date = OffsetDateTime.now()
        val vehicleId = UUID.randomUUID()
        val vin = "vin"
        val status = VehicleStatus.entries.random().name
        val plannedDeliveryDate = OffsetDateTime.now()
        val usageGroupId = UsageGroupId(UUID.randomUUID())
        val employeeNumber = "00123456"
        val vehicleResponsiblePerson = EmployeeNumber(employeeNumber)
        val licensePlate = "SW-1234"
        val desiredTireSet = TireSet.SR
        val provisionForDeliveryComment = "comment"
        val compoundName = "compoundName"
        val parkingLot = "parkingLot"

        every { plannedVehicleTransferFinder.findPlannedTransfersForProvisionForDelivery(any()) } returns
            listOf(
                ProvisionOfDeliveryObjectMother.plannedVehicleTransfer(
                    vehicleId,
                    usageGroupId,
                    vehicleResponsiblePerson,
                    licensePlate,
                    desiredTireSet,
                    provisionForDeliveryComment,
                    plannedDeliveryDate,
                ),
            )

        every { usageGroupFinder.readAllUsageGroups() } returns
            listOf(
                UsageGroupDto(
                    id = usageGroupId.value,
                    description = USAGE_GROUP_DESCRIPTION_PERSON,
                    version = 0,
                    usageGroupId = 4,
                ),
                UsageGroupDto(
                    id = UUID.randomUUID(),
                    description = USAGE_GROUP_DESCRIPTION_DEVELOPMENT,
                    version = 0,
                    usageGroupId = 1,
                ),
            )

        every { readVehicleByVehicleId.readVehicleById(any()) } returns
            VehicleDataObjectMother.vehicleData(
                vehicleId,
                vin = vin,
                status = status,
            )

        every {
            lastKnownLocation.findLastKnownVehicleLocationBy(any())
        } returns ProvisionOfDeliveryObjectMother.vehicleLocation(vehicleId, compoundName, parkingLot)

        every { readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(any()) } throws
            ReadVehiclePersonException(
                "Could not find person",
                null,
            )

        every { fillTemplateService.fillTemplate(any(), any()) } returns
            DocumentObjectMother.getDocumentWithTable(
                numberOfTables = 1,
            )

        every { fillTemplateService.fillTable(any(), any(), any()) } just Runs

        val result =
            GenerateProvisionOfDeliveryDocumentService(
                plannedVehicleTransferFinder,
                usageGroupFinder,
                readVehicleByVehicleId,
                fillTemplateService,
                lastKnownLocation,
                readVehiclePersonDetailByEmployeeNumber,
            ).generateProvisionOfDelivery(date)

        assertThat(result, Matchers.notNullValue())
        verify(exactly = 1) {
            plannedVehicleTransferFinder.findPlannedTransfersForProvisionForDelivery(date)
            usageGroupFinder.readAllUsageGroups()
            readVehicleByVehicleId.readVehicleById(vehicleId)
            fillTemplateService.fillTemplate("ProvisionOfDeliveryDocument.docx", any())
            fillTemplateService.fillTable(any(), any(), any())
            lastKnownLocation.findLastKnownVehicleLocationBy(vehicleId)
            readVehiclePersonDetailByEmployeeNumber.readVehiclePersonDetailByEmployeeNumber(employeeNumber)
        }
    }
}
