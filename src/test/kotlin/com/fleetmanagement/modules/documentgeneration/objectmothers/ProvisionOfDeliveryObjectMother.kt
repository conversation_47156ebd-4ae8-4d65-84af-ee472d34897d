/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2024 Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.documentgeneration.objectmothers

import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import com.fleetmanagement.modules.vehicleperson.api.dtos.VehiclePersonDetail
import com.fleetmanagement.modules.vehicletransfer.domain.entities.PlannedVehicleTransfer
import com.fleetmanagement.modules.vehicletransfer.domain.entities.TireSet
import com.fleetmanagement.modules.vehicletransfer.domain.entities.UsageGroupId
import com.fleetmanagement.modules.vehicletransfer.domain.entities.VehicleTransferKey
import java.time.OffsetDateTime
import java.util.UUID

object ProvisionOfDeliveryObjectMother {
    fun plannedVehicleTransfer(
        vehicleId: UUID,
        usageGroupId: UsageGroupId? = null,
        vehicleResponsiblePerson: EmployeeNumber? = null,
        licensePlate: String? = null,
        desiredTireSet: TireSet? = null,
        provisionForDeliveryComment: String? = null,
        plannedDeliveryDate: OffsetDateTime? = null,
    ): PlannedVehicleTransfer =
        PlannedVehicleTransfer(
            vehicleId = vehicleId,
            consignee = null,
            vehicleUsage = null,
            vehicleResponsiblePerson = vehicleResponsiblePerson,
            internalContactPerson = null,
            depreciationRelevantCostCenterId = null,
            usingCostCenter = null,
            internalOrderNumber = null,
            usageGroupId = usageGroupId,
            maximumServiceLifeInMonths = null,
            businessPartnerId = null,
            vehicleTransferKey = VehicleTransferKey(1),
            licensePlate = licensePlate,
            desiredDeliveryDate = null,
            deliveryLeipzig = null,
            initialized = true,
            desiredTireSet = desiredTireSet,
            serviceCards = emptyList(),
            registrationNeeded = null,
            provisionForDeliveryComment = provisionForDeliveryComment,
            plannedDeliveryDate = plannedDeliveryDate,
            plannedReturnDate = null,
            remark = null,
            deliveryIndex = null,
            utilizationArea = null,
            usageMhp = false,
            usageVdw = false,
            privateMonthlyKilometers = null,
        )

    fun vehicleLocation(
        vehicleId: UUID,
        compoundName: String? = null,
        parkingLot: String? = null,
    ): VehicleLocation =
        VehicleLocation(
            vehicleId = vehicleId,
            eventType = "IN",
            compoundName = compoundName,
            building = null,
            level = null,
            parkingLot = parkingLot,
            source = null,
            occurredOn = OffsetDateTime.now(),
            comment = null,
        )

    fun vehiclePersonDetails(
        employeeNumber: String,
        firstName: String,
        lastName: String,
        companyEmail: String = "<EMAIL>",
        accountingArea: String = "accounting-area",
        businessPartnerId: String = "business-partner-id",
    ): VehiclePersonDetail =
        VehiclePersonDetail(
            employeeNumber = employeeNumber,
            firstName = firstName,
            lastName = lastName,
            companyEmail = companyEmail,
            accountingArea = accountingArea,
            businessPartnerId = businessPartnerId,
        )
}
