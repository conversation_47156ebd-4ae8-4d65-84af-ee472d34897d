@file:Suppress("ktlint:standard:no-wildcard-imports")

package com.fleetmanagement.modules.fvm.features.manager.vehicle

import app.getxray.xray.junit.customjunitxml.annotations.XrayTest
import com.fleetmanagement.modules.fvm.dto.accesscontrol.ApiLabelConstant
import com.fleetmanagement.modules.fvm.dto.accesscontrol.Resource
import com.fleetmanagement.modules.vehicledata.api.domain.FinancialAssetType
import com.fleetmanagement.modules.vehicledata.api.domain.FuelType
import com.fleetmanagement.modules.vehicledata.api.domain.TireSet
import com.fleetmanagement.modules.vehicledata.api.dtos.CreateUIVehicleDTO
import com.fleetmanagement.modules.vehicledata.builders.vehicledto.VehicleDTOBuilder
import com.fleetmanagement.modules.vehicledata.features.createvehicle.VehicleCreationService
import com.fleetmanagement.modules.vehicledata.repository.entities.JPAVehicleEntity
import com.fleetmanagement.modules.vehicledata.repository.mappers.VehicleDTOMapper
import com.fleetmanagement.modules.vehicleregistration.api.WriteRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateVehicleRegistration
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationArea
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferDto
import com.fleetmanagement.modules.vehicletransfer.application.port.CreatePlannedVehicleTransferUseCase
import com.fleetmanagement.modules.vehicletransfer.application.port.VehicleTransferKey
import com.fleetmanagement.security.api.dto.PrivilegeFilter
import com.fleetmanagement.security.api.dto.PrivilegePermission.WRITE
import com.fleetmanagement.security.configurations.SecurityConfiguration
import com.fleetmanagement.security.features.accesscontrol.domain.Privilege
import com.fleetmanagement.security.features.accesscontrol.services.PrivilegeService
import com.fleetmanagement.security.features.tokenvalidation.JwtValidator
import com.fleetmanagement.security.utils.TestObjectMothers.testJWTToken
import com.fleetmanagement.security.utils.WithMockALBUser
import com.ninjasquad.springmockk.MockkBean
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.context.annotation.Import
import org.springframework.http.MediaType
import org.springframework.security.web.context.RequestAttributeSecurityContextRepository
import org.springframework.test.context.TestPropertySource
import org.springframework.test.json.JsonCompareMode
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.content
import org.springframework.test.web.servlet.result.MockMvcResultMatchers.status
import java.math.BigDecimal
import java.time.ZoneOffset
import java.time.ZonedDateTime
import java.util.Optional
import java.util.UUID

private const val VALID_LICENSE_PLATE = "B-MX 1234E"

@WebMvcTest(FVMVehicleCreationController::class)
@TestPropertySource(properties = ["security.enabled=true"])
@Import(SecurityConfiguration::class, ApiLabelConstant::class)
class FVMVehicleCreationControllerTest {
    @Autowired
    private lateinit var mockMvc: MockMvc

    @MockkBean(name = "albTokenValidator")
    private lateinit var albTokenValidator: JwtValidator

    @MockkBean(name = "apiGatewayTokenValidator")
    private lateinit var apiGatewayTokenValidator: JwtValidator

    @MockkBean(name = "requestAttributeSecurityContextRepository")
    private lateinit var requestAttributeSecurityContextRepository: RequestAttributeSecurityContextRepository

    @MockkBean
    private lateinit var privilegeService: PrivilegeService

    @MockkBean
    private lateinit var creationService: VehicleCreationService

    @MockkBean
    private lateinit var writeRegistrationOrder: WriteRegistrationOrder

    @MockkBean
    private lateinit var createPlannedVehicleTransferUseCase: CreatePlannedVehicleTransferUseCase

    @BeforeEach
    fun setup() {
        `disable token validation`()
        `setup privileges`()
    }

    private fun `setup privileges`() {
        every { privilegeService.findAllByGroupIdWithFilters(any()) } returns
            listOf(
                Privilege(id = 1000, resource = "vehicle", permission = WRITE, filter = null),
                Privilege(
                    id = 1001,
                    resource = Resource.APIS.label,
                    permission = WRITE,
                    filter = PrivilegeFilter(listOf(Resource.VEHICLE.label)),
                ),
            )
    }

    private fun `disable token validation`() {
        every { albTokenValidator.validate(any()) } returns Unit
        every { albTokenValidator.canValidate(any()) } returns true

        every { apiGatewayTokenValidator.validate(any()) } returns Unit
        every { apiGatewayTokenValidator.canValidate(any()) } returns true

        every { requestAttributeSecurityContextRepository.saveContext(any(), any(), any()) } just Runs
    }

    @Test
    @WithMockALBUser(authorities = [ApiLabelConstant.API_VEHICLE_WRITE])
    @XrayTest(key = "FP20-3260")
    @DisplayName("Derivation of usageGroup when vehicle is created manually")
    fun `user can successfully create vehicle with registration order and planned vehicle transfer`() {
        val vin = "WP0ZZZ99ZKS131390"
        val licencePlate = "BB-PS 1111"
        val registrationDate = ZonedDateTime.of(2025, 2, 11, 0, 0, 0, 0, ZoneOffset.UTC)

        val createVehicleRequestJson = getCreateVehicleRequestJson(vin, licencePlate)
        val createVehicleRequest = createVehicleDTO(vin, licencePlate)

        val vehicleId = UUID.randomUUID()

        val vehicleEntity =
            JPAVehicleEntity(
                id = vehicleId,
                vin = vin,
                vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0",
                modelInfo = null,
                consumptionInfo = null,
                orderInfo = null,
                productionInfo = null,
                countryInfo = null,
                embargoInfo = null,
                options = null,
            )
        val vehicleDTO = VehicleDTOMapper.INSTANCE.map(vehicleEntity)

        val createPlannedVehicleTransferDto =
            CreatePlannedVehicleTransferDto(
                vehicleId = vehicleId,
                vehicleUsageId = createVehicleRequest.vehicleUsageId,
                vehicleResponsiblePerson = createVehicleRequest.vehicleResponsiblePerson,
                internalContactPerson = createVehicleRequest.internalContactPerson,
                internalOrderNumber = createVehicleRequest.internalOrderNumber,
                licensePlate = createVehicleRequest.licencePlate,
                usingCostCenter = createVehicleRequest.usingCostCenter,
            )
        val createRegistrationOrderRequest =
            CreateVehicleRegistration(
                vin = createVehicleRequest.vin,
                licencePlate = createVehicleRequest.licencePlate,
                registrationType = 1,
                registrationDate = createVehicleRequest.registrationDate,
                registrationArea = RegistrationArea.GERMANY,
            )

        every { creationService.createOrUpdateVehicle(createVehicleRequest, "Mustermann, Max (FDC3)") } returns vehicleDTO

        every { writeRegistrationOrder.createRegistrationOrders(listOf(createRegistrationOrderRequest)) } returns
            VehicleRegistrationAPIResponse(
                listOf(
                    VehicleRegistrationOrder(
                        id = 1,
                        vin = Optional.of(vin),
                        licencePlate = Optional.of(licencePlate),
                        version = 1,
                        registrationDate = Optional.of(registrationDate),
                        registrationArea = Optional.of("GERMANY"),
                    ),
                ),
            )

        every { createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(any()) } returns
            VehicleTransferKey(23)

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/ui/vehicles")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(createVehicleRequestJson)
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken()),
            ).andExpectAll(
                status().isCreated,
                content().json(
                    """
                    {
                      "data": {
                        "vehicle": {
                          "id": "$vehicleId",
                          "vin": "WP0ZZZ99ZKS131390",
                          "vguid": "13e4a669-2ba8-4a7e-be98-27173133b6f0",
                          "equiId": null,
                          "source": "UNKNOWN",
                          "referenceId": null,
                          "equipmentNumber": null,
                          "financialAssetType": null,
                          "options": null,
                          "currentTires": "SR",
                          "externalLeaseStart": null,
                          "externalLeaseEnd": null,
                          "externalLeaseRate": null,
                          "externalLeaseLessee": null,
                          "createdAt": null,
                          "numberOfDamages": null,
                          "repairfixCarId": null,
                          "tuevAppointment": null,
                          "status": "E000",
                          "version": 0,
                          "model": {
                            "description": null,
                            "year": null,
                            "productId": null,
                            "productCode": null,
                            "orderType": null,
                            "vehicleType": null,
                            "manufacturer": null,
                            "range": null,
                            "modelDescriptionDevelopment": null,
                            "rangeDevelopment": null
                          },
                          "consumption": {
                            "driveType": null,
                            "typification": null,
                            "primaryFuelType": null,
                            "secondaryFuelType": null
                          },
                          "order": {
                            "department": null,
                            "tradingPartnerNumber": null,
                            "purposeOrderType": null,
                            "importerShortName": null,
                            "portCode": null,
                            "commissionNumber": null,
                            "invoiceNumber": null,
                            "invoiceDate": null,
                            "purchaseOrderDate": null,
                            "requestedDeliveryDate": null,
                            "customerDeliveryDate": null,
                            "deliveryType": null,
                            "primaryStatus": null,
                            "leasingType": null,
                            "preproductionVehicle": null,
                            "blockedForSale": null
                          },
                          "production": {
                            "number": null,
                            "numberVW": null,
                            "endDate": null,
                            "technicalModelYear": null,
                            "factory": null,
                            "factoryVW": null,
                            "quoteMonth": null,
                            "quoteYear": null,
                            "plannedEndDate": null,
                            "gearBoxClass": null
                          },
                          "country": {
                            "bnrValue": null,
                            "cnrValue": null,
                            "cnrCountryDescription": null
                          },
                          "pmp": {
                            "odometer": null,
                            "timestamp": null
                          },
                          "embargo": {
                            "inEmbargo": null
                          },
                          "color": {
                            "exterior": null,
                            "exteriorDescription": null,
                            "interior": null,
                            "interiorDescription": null
                          },
                          "price": {
                            "grossPrice": null,
                            "grossPriceWithExtras": null,
                            "grossPricePlan": null,
                            "grossPriceNewCar": null,
                            "netPrice": null,
                            "netPriceWithExtras": null,
                            "netPriceNewCar": null,
                            "valueAddedTax": null,
                            "factoryNetPriceEUR": null,
                            "factoryGrossPriceEUR": null,
                            "piaEvent": null
                          },
                          "technical": {
                            "amountSeats": null,
                            "engineCapacity": null,
                            "cargoVolume": null,
                            "vehicleWidthMirrorsExtended": null,
                            "maximumChargingPowerDc": null,
                            "grossVehicleWeight": null,
                            "curbWeightEu": null,
                            "totalPowerKw": null,
                            "curbWeightDin": null,
                            "maximumPayload": null,
                            "chargingTimeAc22Kw": null,
                            "chargingTimeAc11Kw0100": null,
                            "chargingTimeAc96Kw0100": null,
                            "netBatteryCapacity": null,
                            "grossBatteryCapacity": null,
                            "acceleration0100Kmh": null,
                            "acceleration0100KmhLaunchControl": null,
                            "topSpeed": null,
                            "height": null,
                            "widthMirrorsFolded": null,
                            "length": null,
                            "acceleration80120Kmh": null,
                            "maxRoofLoadWithPorscheRoofTransportSystem": null,
                            "chargingTimeDcMaxPower580": null,
                            "powerKw": null
                          },
                          "fleet": {
                            "scrapVehicle": null,
                            "soldDate": null,
                            "scrappedDate": null,
                            "stolenDate": null,
                            "soldCupCarDate": null,
                            "approvedForScrappingDate": null,
                            "scrappedVehicleOfferedDate": null,
                            "vehicleSentToSalesDate": null,
                            "costEstimationOrderedDate": null,
                            "isResidualValueMarket": null,
                            "profitabilityAuditDate": null,
                            "comment": null,
                            "raceCar": false,
                            "classic": false
                          },
                          "delivery": {
                            "preparationDoneDate": null,
                            "isPreparationNecessary": null
                          },
                          "returnInfo": {
                            "nextProcess": null,
                            "isUsedCar": null,
                            "keyReturned": null,
                            "factoryCarPreparationOrderNumber": null
                          },
                          "tireSetChange": {
                            "comment": null,
                            "orderedDate": null,
                            "completedDate": null
                          },
                          "wltp": null,
                          "evaluation": {
                            "appraisalNetPrice": null,
                            "vehicleEvaluationComment": null,
                            "pcComplaintCheckComment": null
                          },
                          "currentMileage": {
                            "mileage": null,
                            "readDate": null
                          },
                          "vtstamm": {
                            "subjectToConfidentiality": null,
                            "confidentialityClassification": null,
                            "subjectToConfidentialityStartDate": null,
                            "subjectToConfidentialityEndDate": null,
                            "recordFactoryExit": null,
                            "camouflageRequired": null,
                            "internalDesignation": null,
                            "typeOfUseVTS": null,
                            "statusVTS": null
                          }
                        }
                      },
                      "errors": null,
                      "warnings": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            creationService.createOrUpdateVehicle(createVehicleRequest, "Mustermann, Max (FDC3)")
            writeRegistrationOrder.createRegistrationOrders(listOf(createRegistrationOrderRequest))
            createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(createPlannedVehicleTransferDto)
        }
    }

    @Test
    @WithMockALBUser(authorities = [ApiLabelConstant.API_VEHICLE_WRITE])
    fun `vehicle is created and error is reported when registration creation fails`() {
        val vin = "WP0ZZZ99ZKS131390"
        val licencePlate = "BB-PS 1111"

        val createVehicleRequestJson = getCreateVehicleRequestJson(vin, licencePlate)
        val createVehicleRequest = createVehicleDTO(vin, licencePlate)

        val vehicleId = UUID.randomUUID()

        val vehicleEntity =
            JPAVehicleEntity(
                id = vehicleId,
                vin = vin,
                vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0",
                modelInfo = null,
                consumptionInfo = null,
                orderInfo = null,
                productionInfo = null,
                countryInfo = null,
                embargoInfo = null,
                options = null,
                vtstammInfo = null,
            )
        val vehicleDTO = VehicleDTOMapper.INSTANCE.map(vehicleEntity)

        val createRegistrationOrderRequest =
            CreateVehicleRegistration(
                vin = createVehicleRequest.vin,
                licencePlate = createVehicleRequest.licencePlate,
                registrationType = 1,
                registrationDate = createVehicleRequest.registrationDate,
            )

        val createPlannedVehicleTransferDto =
            CreatePlannedVehicleTransferDto(
                vehicleId = vehicleId,
                vehicleUsageId = createVehicleRequest.vehicleUsageId,
                vehicleResponsiblePerson = createVehicleRequest.vehicleResponsiblePerson,
                internalContactPerson = createVehicleRequest.internalContactPerson,
                internalOrderNumber = createVehicleRequest.internalOrderNumber,
                licensePlate = createVehicleRequest.licencePlate,
                usingCostCenter = createVehicleRequest.usingCostCenter,
            )

        every {
            creationService.createOrUpdateVehicle(
                createVehicleRequest,
                "Mustermann, Max (FDC3)",
            )
        } returns vehicleDTO

        every { writeRegistrationOrder.createRegistrationOrders(listOf(createRegistrationOrderRequest)) } throws
            RuntimeException("Registration service is unavailable")

        every { createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(any()) } returns
            VehicleTransferKey(23)

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/ui/vehicles")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(createVehicleRequestJson)
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken()),
            ).andExpectAll(
                status().isConflict,
                content().json(
                    """
                    {
                      "data": {
                        "vehicle": {
                          "id": $vehicleId,
                          "vin": "WP0ZZZ99ZKS131390",
                          "vguid": "13e4a669-2ba8-4a7e-be98-27173133b6f0",
                          "equiId": null,
                          "source": "UNKNOWN",
                          "referenceId": null,
                          "equipmentNumber": null,
                          "financialAssetType": null,
                          "options": null,
                          "currentTires": "SR",
                          "externalLeaseStart": null,
                          "externalLeaseEnd": null,
                          "externalLeaseRate": null,
                          "externalLeaseLessee": null,
                          "createdAt": null,
                          "numberOfDamages": null,
                          "repairfixCarId": null,
                          "tuevAppointment": null,
                          "status": "E000",
                          "version": 0,
                          "model": {
                            "description": null,
                            "year": null,
                            "productId": null,
                            "productCode": null,
                            "orderType": null,
                            "vehicleType": null,
                            "manufacturer": null,
                            "range": null,
                            "modelDescriptionDevelopment": null,
                            "rangeDevelopment": null
                          },
                          "consumption": {
                            "driveType": null,
                            "typification": null,
                            "primaryFuelType": null,
                            "secondaryFuelType": null
                          },
                          "order": {
                            "department": null,
                            "tradingPartnerNumber": null,
                            "purposeOrderType": null,
                            "importerShortName": null,
                            "portCode": null,
                            "commissionNumber": null,
                            "invoiceNumber": null,
                            "invoiceDate": null,
                            "purchaseOrderDate": null,
                            "requestedDeliveryDate": null,
                            "customerDeliveryDate": null,
                            "deliveryType": null,
                            "primaryStatus": null,
                            "leasingType": null,
                            "preproductionVehicle": null,
                            "blockedForSale": null
                          },
                          "production": {
                            "number": null,
                            "numberVW": null,
                            "endDate": null,
                            "technicalModelYear": null,
                            "factory": null,
                            "factoryVW": null,
                            "quoteMonth": null,
                            "quoteYear": null,
                            "plannedEndDate": null,
                            "gearBoxClass": null
                          },
                          "country": {
                            "bnrValue": null,
                            "cnrValue": null,
                            "cnrCountryDescription": null
                          },
                          "pmp": {
                            "odometer": null,
                            "timestamp": null
                          },
                          "embargo": {
                            "inEmbargo": null
                          },
                          "color": {
                            "exterior": null,
                            "exteriorDescription": null,
                            "interior": null,
                            "interiorDescription": null
                          },
                          "price": {
                            "grossPrice": null,
                            "grossPriceWithExtras": null,
                            "grossPricePlan": null,
                            "grossPriceNewCar": null,
                            "netPrice": null,
                            "netPriceWithExtras": null,
                            "netPriceNewCar": null,
                            "valueAddedTax": null,
                            "factoryNetPriceEUR": null,
                            "factoryGrossPriceEUR": null,
                            "piaEvent": null
                          },
                          "technical": {
                            "amountSeats": null,
                            "engineCapacity": null,
                            "cargoVolume": null,
                            "vehicleWidthMirrorsExtended": null,
                            "maximumChargingPowerDc": null,
                            "grossVehicleWeight": null,
                            "curbWeightEu": null,
                            "totalPowerKw": null,
                            "curbWeightDin": null,
                            "maximumPayload": null,
                            "chargingTimeAc22Kw": null,
                            "chargingTimeAc11Kw0100": null,
                            "chargingTimeAc96Kw0100": null,
                            "netBatteryCapacity": null,
                            "grossBatteryCapacity": null,
                            "acceleration0100Kmh": null,
                            "acceleration0100KmhLaunchControl": null,
                            "topSpeed": null,
                            "height": null,
                            "widthMirrorsFolded": null,
                            "length": null,
                            "acceleration80120Kmh": null,
                            "maxRoofLoadWithPorscheRoofTransportSystem": null,
                            "chargingTimeDcMaxPower580": null,
                            "powerKw": null
                          },
                          "fleet": {
                            "scrapVehicle": null,
                            "soldDate": null,
                            "scrappedDate": null,
                            "stolenDate": null,
                            "soldCupCarDate": null,
                            "approvedForScrappingDate": null,
                            "scrappedVehicleOfferedDate": null,
                            "vehicleSentToSalesDate": null,
                            "costEstimationOrderedDate": null,
                            "isResidualValueMarket": null,
                            "profitabilityAuditDate": null,
                            "comment": null,
                            "raceCar": false,
                            "classic": false
                          },
                          "delivery": {
                            "preparationDoneDate": null,
                            "isPreparationNecessary": null
                          },
                          "returnInfo": {
                            "nextProcess": null,
                            "isUsedCar": null,
                            "keyReturned": null,
                            "factoryCarPreparationOrderNumber": null
                          },
                          "tireSetChange": {
                            "comment": null,
                            "orderedDate": null,
                            "completedDate": null
                          },
                          "wltp": null,
                          "evaluation": {
                            "appraisalNetPrice": null,
                            "vehicleEvaluationComment": null,
                            "pcComplaintCheckComment": null
                          },
                          "currentMileage": {
                            "mileage": null,
                            "readDate": null
                          },
                          "vtstamm": {
                            "subjectToConfidentiality": null,
                            "confidentialityClassification": null,
                            "subjectToConfidentialityStartDate": null,
                            "subjectToConfidentialityEndDate": null,
                            "recordFactoryExit": null,
                            "camouflageRequired": null,
                            "internalDesignation": null,
                            "typeOfUseVTS": null,
                            "statusVTS": null
                          }
                        }
                      },
                      "errors": [
                        {
                          "type": "error.fvm.vehicleCreationPartialSuccess",
                          "title": "Manual Action Required",
                          "status": 409,
                          "detail": "Vehicle created successfully. Please create vehicle registration manually.",
                          "instance": "/ui/vehicles",
                          "failedProcesses":["vehicle registration"]
                        }
                      ],
                      "warnings": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            creationService.createOrUpdateVehicle(createVehicleRequest, "Mustermann, Max (FDC3)")
            writeRegistrationOrder.createRegistrationOrders(listOf(createRegistrationOrderRequest))
            createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(createPlannedVehicleTransferDto)
        }
    }

    @Test
    @WithMockALBUser(authorities = [ApiLabelConstant.API_VEHICLE_WRITE])
    fun `vehicle is created and error is reported when transfer creation fails`() {
        val vin = "WP0ZZZ99ZKS131390"
        val licencePlate = "BB-PS 1111"
        val registrationDate = ZonedDateTime.of(2025, 2, 11, 0, 0, 0, 0, ZoneOffset.UTC)

        val createVehicleRequestJson = getCreateVehicleRequestJson(vin, licencePlate)
        val createVehicleRequest = createVehicleDTO(vin, licencePlate)

        val vehicleId = UUID.randomUUID()

        val vehicleEntity =
            JPAVehicleEntity(
                id = vehicleId,
                vin = vin,
                vguid = "13e4a669-2ba8-4a7e-be98-27173133b6f0",
                modelInfo = null,
                consumptionInfo = null,
                orderInfo = null,
                productionInfo = null,
                countryInfo = null,
                embargoInfo = null,
                options = null,
            )
        val vehicleDTO = VehicleDTOMapper.INSTANCE.map(vehicleEntity)

        val createPlannedVehicleTransferDto =
            CreatePlannedVehicleTransferDto(
                vehicleId = vehicleId,
                vehicleUsageId = createVehicleRequest.vehicleUsageId,
                vehicleResponsiblePerson = createVehicleRequest.vehicleResponsiblePerson,
                internalContactPerson = createVehicleRequest.internalContactPerson,
                internalOrderNumber = createVehicleRequest.internalOrderNumber,
                licensePlate = createVehicleRequest.licencePlate,
                usingCostCenter = createVehicleRequest.usingCostCenter,
            )
        val createRegistrationOrderRequest =
            CreateVehicleRegistration(
                vin = createVehicleRequest.vin,
                licencePlate = createVehicleRequest.licencePlate,
                registrationType = 1,
                registrationDate = createVehicleRequest.registrationDate,
            )

        every {
            creationService.createOrUpdateVehicle(
                createVehicleRequest,
                "Mustermann, Max (FDC3)",
            )
        } returns vehicleDTO

        every { writeRegistrationOrder.createRegistrationOrders(listOf(createRegistrationOrderRequest)) } returns
            VehicleRegistrationAPIResponse(
                listOf(
                    VehicleRegistrationOrder(
                        id = 1,
                        vin = Optional.of(vin),
                        licencePlate = Optional.of(licencePlate),
                        version = 1,
                        registrationDate = Optional.of(registrationDate),
                    ),
                ),
            )

        every { createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(any()) } throws
            RuntimeException(
                "Transfer creation failed",
            )

        mockMvc
            .perform(
                MockMvcRequestBuilders
                    .post("/ui/vehicles")
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(createVehicleRequestJson)
                    .header("Authorization", "Bearer testAccessToken")
                    .header("X-Amzn-Oidc-Accesstoken", testJWTToken()),
            ).andExpectAll(
                status().isConflict,
                content().json(
                    """
                    {
                      "data": {
                        "vehicle": {
                          "id": $vehicleId,
                          "vin": "WP0ZZZ99ZKS131390",
                          "vguid": "13e4a669-2ba8-4a7e-be98-27173133b6f0",
                          "equiId": null,
                          "source": "UNKNOWN",
                          "referenceId": null,
                          "equipmentNumber": null,
                          "financialAssetType": null,
                          "options": null,
                          "currentTires": "SR",
                          "externalLeaseStart": null,
                          "externalLeaseEnd": null,
                          "externalLeaseRate": null,
                          "externalLeaseLessee": null,
                          "createdAt": null,
                          "numberOfDamages": null,
                          "repairfixCarId": null,
                          "tuevAppointment": null,
                          "status": "E000",
                          "version": 0,
                          "model": {
                            "description": null,
                            "year": null,
                            "productId": null,
                            "productCode": null,
                            "orderType": null,
                            "vehicleType": null,
                            "manufacturer": null,
                            "range": null,
                            "modelDescriptionDevelopment": null,
                            "rangeDevelopment": null
                          },
                          "consumption": {
                            "driveType": null,
                            "typification": null,
                            "primaryFuelType": null,
                            "secondaryFuelType": null
                          },
                          "order": {
                            "department": null,
                            "tradingPartnerNumber": null,
                            "purposeOrderType": null,
                            "importerShortName": null,
                            "portCode": null,
                            "commissionNumber": null,
                            "invoiceNumber": null,
                            "invoiceDate": null,
                            "purchaseOrderDate": null,
                            "requestedDeliveryDate": null,
                            "customerDeliveryDate": null,
                            "deliveryType": null,
                            "primaryStatus": null,
                            "leasingType": null,
                            "preproductionVehicle": null,
                            "blockedForSale": null
                          },
                          "production": {
                            "number": null,
                            "numberVW": null,
                            "endDate": null,
                            "technicalModelYear": null,
                            "factory": null,
                            "factoryVW": null,
                            "quoteMonth": null,
                            "quoteYear": null,
                            "plannedEndDate": null,
                            "gearBoxClass": null
                          },
                          "country": {
                            "bnrValue": null,
                            "cnrValue": null,
                            "cnrCountryDescription": null
                          },
                          "pmp": {
                            "odometer": null,
                            "timestamp": null
                          },
                          "embargo": {
                            "inEmbargo": null
                          },
                          "color": {
                            "exterior": null,
                            "exteriorDescription": null,
                            "interior": null,
                            "interiorDescription": null
                          },
                          "price": {
                            "grossPrice": null,
                            "grossPriceWithExtras": null,
                            "grossPricePlan": null,
                            "grossPriceNewCar": null,
                            "netPrice": null,
                            "netPriceWithExtras": null,
                            "netPriceNewCar": null,
                            "valueAddedTax": null,
                            "factoryNetPriceEUR": null,
                            "factoryGrossPriceEUR": null,
                            "piaEvent": null
                          },
                          "technical": {
                            "amountSeats": null,
                            "engineCapacity": null,
                            "cargoVolume": null,
                            "vehicleWidthMirrorsExtended": null,
                            "maximumChargingPowerDc": null,
                            "grossVehicleWeight": null,
                            "curbWeightEu": null,
                            "totalPowerKw": null,
                            "curbWeightDin": null,
                            "maximumPayload": null,
                            "chargingTimeAc22Kw": null,
                            "chargingTimeAc11Kw0100": null,
                            "chargingTimeAc96Kw0100": null,
                            "netBatteryCapacity": null,
                            "grossBatteryCapacity": null,
                            "acceleration0100Kmh": null,
                            "acceleration0100KmhLaunchControl": null,
                            "topSpeed": null,
                            "height": null,
                            "widthMirrorsFolded": null,
                            "length": null,
                            "acceleration80120Kmh": null,
                            "maxRoofLoadWithPorscheRoofTransportSystem": null,
                            "chargingTimeDcMaxPower580": null,
                            "powerKw": null
                          },
                          "fleet": {
                            "scrapVehicle": null,
                            "soldDate": null,
                            "scrappedDate": null,
                            "stolenDate": null,
                            "soldCupCarDate": null,
                            "approvedForScrappingDate": null,
                            "scrappedVehicleOfferedDate": null,
                            "vehicleSentToSalesDate": null,
                            "costEstimationOrderedDate": null,
                            "isResidualValueMarket": null,
                            "profitabilityAuditDate": null,
                            "comment": null,
                            "raceCar": false,
                            "classic": false
                          },
                          "delivery": {
                            "preparationDoneDate": null,
                            "isPreparationNecessary": null
                          },
                          "returnInfo": {
                            "nextProcess": null,
                            "isUsedCar": null,
                            "keyReturned": null,
                            "factoryCarPreparationOrderNumber": null
                          },
                          "tireSetChange": {
                            "comment": null,
                            "orderedDate": null,
                            "completedDate": null
                          },
                          "wltp": null,
                          "evaluation": {
                            "appraisalNetPrice": null,
                            "vehicleEvaluationComment": null,
                            "pcComplaintCheckComment": null
                          },
                          "currentMileage": {
                            "mileage": null,
                            "readDate": null
                          },
                          "vtstamm": {
                            "subjectToConfidentiality": null,
                            "confidentialityClassification": null,
                            "subjectToConfidentialityStartDate": null,
                            "subjectToConfidentialityEndDate": null,
                            "recordFactoryExit": null,
                            "camouflageRequired": null,
                            "internalDesignation": null,
                            "typeOfUseVTS": null,
                            "statusVTS": null
                          }
                        }
                      },
                      "errors": [
                        {
                          "type": "error.fvm.vehicleCreationPartialSuccess",
                          "title": "Manual Action Required",
                          "status": 409,
                          "detail": "Vehicle created successfully. Please create vehicle transfer manually.",
                          "instance": "/ui/vehicles",
                          "failedProcesses":["vehicle transfer"]
                        }
                      ],
                      "warnings": null
                    }
                    """.trimIndent(),
                ),
            )

        verify(exactly = 1) {
            creationService.createOrUpdateVehicle(createVehicleRequest, "Mustermann, Max (FDC3)")
            writeRegistrationOrder.createRegistrationOrders(listOf(createRegistrationOrderRequest))
            createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(createPlannedVehicleTransferDto)
        }
    }

    @Nested
    inner class ValidationTests {
        @Test
        @WithMockALBUser(authorities = [ApiLabelConstant.API_VEHICLE_WRITE])
        fun `should accept request with VIN for a PKW vehicle that has 17 characters`() {
            `given vehicle can be created in our system`()
            `given vehicle-registration can be created in our system`()
            `given vehicle transfer can be created in our system`()

            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/ui/vehicles")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(getCreateVehicleRequestJson(vin = "WP0ZZZ99ZKS131390", licencePlate = VALID_LICENSE_PLATE))
                        .header("Authorization", "Bearer testAccessToken")
                        .header("X-Amzn-Oidc-Accesstoken", testJWTToken()),
                ).andExpectAll(
                    status().isCreated,
                )
        }

        @Test
        @WithMockALBUser(authorities = [ApiLabelConstant.API_VEHICLE_WRITE])
        fun `pre-1981 (museum) vehicles can have a vin number that is less than 17 characters`() {
            `given vehicle can be created in our system`()
            `given vehicle-registration can be created in our system`()
            `given vehicle transfer can be created in our system`()

            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/ui/vehicles")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(
                            getCreateVehicleRequestJson(
                                vin = "WP0ZZ",
                                licencePlate = VALID_LICENSE_PLATE,
                                vehicleType = "PKW",
                            ),
                        ).header("Authorization", "Bearer testAccessToken")
                        .header("X-Amzn-Oidc-Accesstoken", testJWTToken()),
                ).andExpectAll(
                    status().isCreated,
                )
        }

        @Test
        @WithMockALBUser(authorities = [ApiLabelConstant.API_VEHICLE_WRITE])
        fun `any vehicle with a VIN number greater than 17 characters results in validation error`() {
            `given vehicle can be created in our system`()
            `given vehicle-registration can be created in our system`()
            `given vehicle transfer can be created in our system`()

            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/ui/vehicles")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(
                            getCreateVehicleRequestJson(
                                vin = "MR5725123MR5725123MR5725123",
                                licencePlate = VALID_LICENSE_PLATE,
                                vehicleType = "TRAILER",
                            ),
                        ).header("Authorization", "Bearer testAccessToken")
                        .header("X-Amzn-Oidc-Accesstoken", testJWTToken()),
                ).andExpectAll(
                    status().isUnprocessableEntity,
                    content().json(
                        """
                        {
                          "data": null,
                          "errors": [{
                            "type": "error.validation.pattern",
                            "field": "vin"
                          }]
                        }
                        """.trimIndent(),
                        JsonCompareMode.LENIENT,
                    ),
                )
        }

        @Test
        @WithMockALBUser(authorities = [ApiLabelConstant.API_VEHICLE_WRITE])
        fun `any vehicle with an empty VIN results in an validation error`() {
            `given vehicle can be created in our system`()
            `given vehicle-registration can be created in our system`()
            `given vehicle transfer can be created in our system`()

            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/ui/vehicles")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(
                            getCreateVehicleRequestJson(
                                vin = "",
                                licencePlate = VALID_LICENSE_PLATE,
                                vehicleType = "TRAILER",
                            ),
                        ).header("Authorization", "Bearer testAccessToken")
                        .header("X-Amzn-Oidc-Accesstoken", testJWTToken()),
                ).andExpectAll(
                    status().isUnprocessableEntity,
                    content().json(
                        """
                        {
                          "data": null,
                          "errors": [{
                            "type": "error.validation.pattern",
                            "field": "vin"
                          }]
                        }
                        """.trimIndent(),
                        JsonCompareMode.LENIENT,
                    ),
                )
        }

        @Test
        @WithMockALBUser(authorities = [ApiLabelConstant.API_VEHICLE_WRITE])
        fun `any vehicle with with invalid Cost Center results in a validation error`() {
            `given vehicle can be created in our system`()
            `given vehicle-registration can be created in our system`()
            `given vehicle transfer can be created in our system`()

            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/ui/vehicles")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(
                            getCreateVehicleRequestJson(
                                vin = "WP0ZZZ99ZKS131390",
                                licencePlate = VALID_LICENSE_PLATE,
                                vehicleType = "TRAILER",
                                usingCostCenter = "invalidCostCenter",
                            ),
                        ).header("Authorization", "Bearer testAccessToken")
                        .header("X-Amzn-Oidc-Accesstoken", testJWTToken()),
                ).andExpectAll(
                    status().isUnprocessableEntity,
                    content().json(
                        """
                        {
                          "data": null,
                          "errors": [{
                            "type": "error.validation.pattern",
                            "field": "usingCostCenter"
                          }]
                        }
                        """.trimIndent(),
                        JsonCompareMode.LENIENT,
                    ),
                )
        }

        @Test
        @WithMockALBUser(authorities = [ApiLabelConstant.API_VEHICLE_WRITE])
        fun `any licensePlate that does not comfort to the format is rejected`() {
            `given vehicle can be created in our system`()
            `given vehicle-registration can be created in our system`()
            `given vehicle transfer can be created in our system`()

            mockMvc
                .perform(
                    MockMvcRequestBuilders
                        .post("/ui/vehicles")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(
                            getCreateVehicleRequestJson(
                                vin = "WP0ZZZ99ZKS131390",
                                licencePlate = "INVALID_LICENSE_PLATE",
                            ),
                        ).header("Authorization", "Bearer testAccessToken")
                        .header("X-Amzn-Oidc-Accesstoken", testJWTToken()),
                ).andExpectAll(
                    status().isUnprocessableEntity,
                    content().json(
                        """
                        {       
                          "data": null,
                          "errors": [{
                            "type": "error.validation.invalid.licence-plate",
                            "field": "licencePlate"
                          }]
                        }
                        """.trimIndent(),
                        JsonCompareMode.LENIENT,
                    ),
                )
        }

        fun `given vehicle can be created in our system`() =
            every {
                creationService.createOrUpdateVehicle(any(), any())
            } returns VehicleDTOBuilder().build() // don't care about other fields due to nature of test

        fun `given vehicle-registration can be created in our system`() =
            every {
                writeRegistrationOrder.createRegistrationOrders(any<List<CreateVehicleRegistration>>())
            } returns
                VehicleRegistrationAPIResponse(
                    listOf(
                        VehicleRegistrationOrder(
                            id = 1,
                            version = 1,
                        ), // don't care about other fields due to nature of test
                    ),
                )

        fun `given vehicle transfer can be created in our system`() =
            every {
                createPlannedVehicleTransferUseCase.manualCreatePlannedVehicleTransfer(any())
            } returns VehicleTransferKey(value = 1) // don't care about other fields due to nature of test
    }

    private fun createVehicleDTO(
        vin: String,
        licencePlate: String,
    ) = CreateUIVehicleDTO(
        vin = vin,
        manufacturer = "TestManufacturer",
        modelDescription = "Macan",
        amountSeats = 5,
        engineCapacity = 2.0f,
        licencePlate = licencePlate,
        technicalModelYear = 2022,
        primaryFuelType = "${FuelType.DIESEL}",
        currentTires = "${TireSet.WR}",
        orderType = "95BAU1",
        secondaryFuelType = "${FuelType.ELECTRIC}",
        netPriceWithExtras = BigDecimal("8800"),
        vehicleType = "PKW",
        pmpDataOdometer = 123,
        enginePower = 150,
        vehicleResponsiblePerson = "0012345",
        internalContactPerson = "0012345",
        externalLeaseStart = ZonedDateTime.of(2024, 10, 1, 0, 0, 0, 0, ZoneOffset.UTC),
        externalLeaseEnd = ZonedDateTime.of(2024, 10, 1, 0, 0, 0, 0, ZoneOffset.UTC),
        externalLeaseLessee = "John Doe",
        externalLeaseRate = 300.0f,
        vehicleUsageId = UUID.fromString("a2de1023-3b08-4e38-b287-594904adf9f1"),
        internalOrderNumber = "ts4WaENv64dazuC9i9Te",
        financialAssetType = "${FinancialAssetType.AV}",
        registrationDate = ZonedDateTime.of(2025, 2, 11, 0, 0, 0, 0, ZoneOffset.UTC),
        usingCostCenter = "00H0015130",
    )

    private fun getCreateVehicleRequestJson(
        vin: String,
        licencePlate: String,
        vehicleType: String = "PKW",
        usingCostCenter: String = "00H0015130",
    ) = """
        {
            "vin": "$vin",
            "manufacturer": "TestManufacturer",
            "modelDescription": "Macan",
            "amountSeats": 5,
            "engineCapacity": 2.0,
            "licencePlate": "$licencePlate",
            "technicalModelYear": 2022,
            "primaryFuelType": "${FuelType.DIESEL}",
            "currentTires": "${TireSet.WR}",
            "orderType": "95BAU1",
            "secondaryFuelType": "${FuelType.ELECTRIC}",
            "netPriceWithExtras": 8800,
            "vehicleType": "$vehicleType",
            "pmpDataOdometer": 123,
            "enginePower": 150.0,
            "grossPrice": 10000.0,
            "vehicleResponsiblePerson": "0012345",
            "internalContactPerson": "0012345",
            "externalLeaseStart": "2024-10-01T00:00:00Z",
            "externalLeaseEnd": "2024-10-01T00:00:00Z",
            "externalLeaseLessee": "John Doe",
            "externalLeaseRate": 300.0,
            "vehicleUsageId": "a2de1023-3b08-4e38-b287-594904adf9f1",
            "depreciationRelevantCostCenterId": "8276a6eb-6f45-42b1-9452-56bb61be4676",
            "internalOrderNumber": "ts4WaENv64dazuC9i9Te",
            "financialAssetType": "${FinancialAssetType.AV}",
            "registrationDate": "2025-02-11T00:00:00Z",
            "usingCostCenter": "$usingCostCenter"
            }
        """.trimIndent()
}
