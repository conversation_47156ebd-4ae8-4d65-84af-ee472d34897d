package com.fleetmanagement.modules.migration

import com.fleetmanagement.modules.migration.job.FMSMigrationJobService
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.quartz.JobDataMap
import org.quartz.JobExecutionContext
import org.quartz.Trigger

class FMSMigrationJobTest {
    private lateinit var fmsMigrationJobService: FMSMigrationJobService
    private lateinit var jobExecutionContext: JobExecutionContext
    private lateinit var trigger: Trigger
    private lateinit var jobDataMap: JobDataMap
    private lateinit var fmsMigrationJob: FMSMigrationJob

    @BeforeEach
    fun setUp() {
        fmsMigrationJobService = mockk(relaxed = true)
        jobExecutionContext = mockk(relaxed = true)
        trigger = mockk(relaxed = true)
        jobDataMap = JobDataMap()

        every { jobExecutionContext.trigger } returns trigger
        every { trigger.jobDataMap } returns jobDataMap

        fmsMigrationJob = FMSMigrationJob(fmsMigrationJobService)
    }

    @Test
    fun `should call executeMigration with VIN filter when provided`() {
        // Given
        jobDataMap.put("vinFilter", "VIN1,VIN2,VIN3")

        // When
        fmsMigrationJob.execute(jobExecutionContext)

        // Then
        verify { fmsMigrationJobService.executeMigration(any(), listOf("VIN1", "VIN2", "VIN3")) }
    }

    @Test
    fun `should call executeMigration with null VIN filter when not provided`() {
        // When
        fmsMigrationJob.execute(jobExecutionContext)

        // Then
        verify { fmsMigrationJobService.executeMigration(any(), null) }
    }

    @Test
    fun `should call executeMigration with null VIN filter when provided but empty`() {
        // Given
        jobDataMap.put("vinFilter", "")

        // When
        fmsMigrationJob.execute(jobExecutionContext)

        // Then
        verify { fmsMigrationJobService.executeMigration(any(), null) }
    }

    @Test
    fun `should call executeMigration with VIN filter when provided with whitespace`() {
        // Given
        jobDataMap.put("vinFilter", " VIN1 , VIN2,  VIN3 ")

        // When
        fmsMigrationJob.execute(jobExecutionContext)

        // Then
        verify { fmsMigrationJobService.executeMigration(any(), listOf("VIN1", "VIN2", "VIN3")) }
    }
}
