package com.fleetmanagement.modules.migration.postprocessors

import com.fleetmanagement.modules.migration.job.FMSExtendedRegistrationAggregate
import com.fleetmanagement.modules.migration.job.FMSRegistrationAggregate
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregate
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVIN
import com.fleetmanagement.modules.vehicledata.objectmothers.VehicleDataDTOObjectMother
import com.fleetmanagement.modules.vehicleregistration.api.ReadRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.WriteRegistrationOrder
import com.fleetmanagement.modules.vehicleregistration.api.dto.CreateVehicleRegistration
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationArea
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationAPIResponse
import com.fleetmanagement.modules.vehicleregistration.api.dto.VehicleRegistrationOrder
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.OffsetDateTime
import java.time.ZonedDateTime
import java.util.Optional

class VehicleRegistrationCreatorTest {
    private val readVehicleByVIN = mockk<ReadVehicleByVIN>(relaxed = true)
    private val readRegistrationOrder = mockk<ReadRegistrationOrder>(relaxed = true)
    private val writeRegistrationOrder = mockk<WriteRegistrationOrder>(relaxed = true)
    private val vehicleRegistrationCreator =
        VehicleRegistrationCreator(
            readVehicleByVIN,
            readRegistrationOrder,
            writeRegistrationOrder,
        )

    @Test
    fun `create returns immediately when registrations are empty`() {
        val aggregate =
            FMSVehicleAggregate(
                productGuid = "product-guid",
            ).apply {
                vin = "vin123"
            }

        vehicleRegistrationCreator.create(aggregate)

        verify { writeRegistrationOrder wasNot Called }
    }

    @Test
    fun `create throws when vin is null`() {
        val aggregate =
            FMSVehicleAggregate(
                productGuid = "product-guid",
            ).apply {
                vin = null
                this.registrations.add(
                    FMSRegistrationAggregate(
                        licencePlate = "ABC123",
                        registrationType = 1,
                        registrationDate = OffsetDateTime.now(),
                        remark = "remark1",
                    ),
                )
            }

        val exception =
            assertThrows<IllegalStateException> {
                vehicleRegistrationCreator.create(aggregate)
            }
        assertEquals("VIN cannot be null", exception.message)
    }

    @Test
    fun `create calls writeRegistrationOrder with merged registrations`() {
        val testVin = "vin123"
        val vehicle = VehicleDataDTOObjectMother.vehicleDTOWithRequiredFieldsPopulated(vin = testVin)
        val registration1 =
            FMSRegistrationAggregate(
                licencePlate = "B-AB 1234E",
                registrationType = 1,
                registrationDate = OffsetDateTime.now().minusDays(1),
                remark = "remark1",
                registrationArea = RegistrationArea.OTHER,
            )
        val registration2 =
            FMSRegistrationAggregate(
                licencePlate = null,
                registrationType = 4,
                registrationDate = OffsetDateTime.now(),
                remark = "remark2",
            )
        val extendedData =
            FMSExtendedRegistrationAggregate(
                tsnForAllRegistrations = "tsn",
                hsnForAllRegistrations = "hsn",
                briefNumberOnlyForLatestRegistration = "brief",
                sfmeOnlyForLatestRegistration = true,
            )
        val aggregate =
            FMSVehicleAggregate(
                productGuid = "product-guid",
            ).apply {
                vin = testVin
                this.registrations.addAll(mutableListOf(registration1, registration2))
                extendedRegistrationData = extendedData
            }

        every { readVehicleByVIN.readVehicleByVIN(testVin) } returns vehicle
        every { readRegistrationOrder.getCompletedOrdersBy(vehicle.id) } returns VehicleRegistrationAPIResponse(data = emptyList())

        // WHEN
        vehicleRegistrationCreator.create(aggregate)

        val slot = slot<List<CreateVehicleRegistration>>()
        verify(exactly = 1) { writeRegistrationOrder.createRegistrationOrders(capture(slot)) }
        // THEN
        val reg1 = slot.captured[0]
        assertEquals(testVin, reg1.vin)
        assertEquals("B-AB 1234E", reg1.licencePlate)
        assertEquals(RegistrationArea.OTHER, reg1.registrationArea)
        assertEquals(null, reg1.briefNumber)
        assertEquals("tsn", reg1.tsn)

        val reg2 = slot.captured[1]
        assertEquals(testVin, reg2.vin)
        assertEquals(null, reg2.licencePlate)
        assertEquals("brief", reg2.briefNumber)
        assertEquals("tsn", reg2.tsn)
        assertEquals(RegistrationArea.GERMANY, reg2.registrationArea)
    }

    @Test
    fun `create throws VehicleRegistrationCreationFailed on error`() {
        val registration =
            FMSRegistrationAggregate(
                licencePlate = "ABC123",
                registrationType = 1,
                registrationDate = OffsetDateTime.now(),
                remark = "remark1",
            )
        val extendedData = FMSExtendedRegistrationAggregate()
        val aggregate =
            FMSVehicleAggregate(productGuid = "product-guid").apply {
                vin = "vin123"
                this.registrations.add(registration)
                extendedRegistrationData = extendedData
            }

        every { writeRegistrationOrder.createRegistrationOrders(any<List<CreateVehicleRegistration>>()) } throws
            RuntimeException(
                "fail",
            )

        val exception =
            assertThrows<VehicleRegistrationCreator.VehicleRegistrationCreationFailed> {
                vehicleRegistrationCreator.create(aggregate)
            }
        assertTrue(exception.message!!.contains("Vehicle registration creation failed"))
        assertTrue(exception.cause is RuntimeException)
        assertEquals("product-guid", exception.productGuid)
    }

    @Test
    fun `filters out registrations that already exist`() {
        val testVin = "vin123"
        val vehicle = VehicleDataDTOObjectMother.vehicleDTOWithRequiredFieldsPopulated(vin = testVin)
        val existingDate = ZonedDateTime.now().minusDays(1)

        val existingRegistration =
            VehicleRegistrationOrder(
                id = 1,
                licencePlate = Optional.of("ABC123"),
                registrationType = Optional.of(1),
                registrationDate = Optional.of(existingDate),
                version = 1,
            )

        val duplicate =
            FMSRegistrationAggregate(
                licencePlate = "ABC123",
                registrationType = 1,
                registrationDate = existingDate.toOffsetDateTime(),
                remark = "duplicate",
            )

        val unique =
            FMSRegistrationAggregate(
                licencePlate = "NEW456",
                registrationType = 2,
                registrationDate = ZonedDateTime.now().toOffsetDateTime(),
                remark = "unique",
            )

        every { readVehicleByVIN.readVehicleByVIN(testVin) } returns vehicle
        every { readRegistrationOrder.getCompletedOrdersBy(vehicle.id) } returns
            VehicleRegistrationAPIResponse(listOf(existingRegistration))

        val aggregate =
            FMSVehicleAggregate(productGuid = "guid").apply {
                vin = testVin
                registrations.addAll(listOf(duplicate, unique))
            }

        vehicleRegistrationCreator.create(aggregate)

        verify(exactly = 1) {
            writeRegistrationOrder.createRegistrationOrders(
                match<List<CreateVehicleRegistration>> {
                    it.size == 1 &&
                        it.first().licencePlate == "NEW456"
                },
            )
        }
    }

    @Test
    fun `converts type 3 to type 2 when type 1 is present`() {
        val testVin = "vin123"
        val vehicle = VehicleDataDTOObjectMother.vehicleDTOWithRequiredFieldsPopulated(vin = testVin)

        val type1 =
            FMSRegistrationAggregate(
                licencePlate = "ABC123",
                registrationType = 1,
                registrationDate = OffsetDateTime.now(),
                remark = "type1",
            )
        val type3 =
            FMSRegistrationAggregate(
                licencePlate = "XYZ123",
                registrationType = 3,
                registrationDate = OffsetDateTime.now().plusDays(1),
                remark = "type3",
            )

        every { readVehicleByVIN.readVehicleByVIN(testVin) } returns vehicle
        every { readRegistrationOrder.getCompletedOrdersBy(vehicle.id) } returns VehicleRegistrationAPIResponse(data = emptyList())

        val aggregate =
            FMSVehicleAggregate(productGuid = "guid").apply {
                vin = testVin
                registrations.addAll(listOf(type1, type3))
            }

        vehicleRegistrationCreator.create(aggregate)

        verify {
            writeRegistrationOrder.createRegistrationOrders(
                match<List<CreateVehicleRegistration>> {
                    it.any { reg -> reg.licencePlate == "XYZ123" && reg.registrationType == 2 }
                },
            )
        }
    }

    @Test
    fun `converts type 3 to type 1 when type 1 is absent`() {
        val testVin = "vin123"
        val vehicle = VehicleDataDTOObjectMother.vehicleDTOWithRequiredFieldsPopulated(vin = testVin)

        val type3 =
            FMSRegistrationAggregate(
                licencePlate = "ABC123",
                registrationType = 3,
                registrationDate = OffsetDateTime.now(),
                remark = "type3 only",
            )

        every { readVehicleByVIN.readVehicleByVIN(testVin) } returns vehicle
        every { readRegistrationOrder.getCompletedOrdersBy(vehicle.id) } returns
            VehicleRegistrationAPIResponse(
                emptyList(),
            )

        val aggregate =
            FMSVehicleAggregate(productGuid = "guid").apply {
                vin = testVin
                registrations.add(type3)
            }

        vehicleRegistrationCreator.create(aggregate)

        verify {
            writeRegistrationOrder.createRegistrationOrders(
                match<List<CreateVehicleRegistration>> {
                    it.size == 1 && it.first().registrationType == 1
                },
            )
        }
    }
}
