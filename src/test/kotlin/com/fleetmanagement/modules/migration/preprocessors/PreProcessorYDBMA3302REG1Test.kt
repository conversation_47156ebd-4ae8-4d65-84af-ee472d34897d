package com.fleetmanagement.modules.migration.preprocessors

import com.fleetmanagement.modules.migration.LocalFileProvider
import com.fleetmanagement.modules.migration.job.FMSRegistrationAggregate
import com.fleetmanagement.modules.migration.job.FMSVehicleAggregateCache
import com.fleetmanagement.modules.migration.preprocessors.DateUtil.dateFrom
import com.fleetmanagement.modules.migration.preprocessors.excelparsing.ExcelPreprocessor
import com.fleetmanagement.modules.vehicleregistration.api.dto.RegistrationArea
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertAll

class PreProcessorYDBMA3302REG1Test {
    private lateinit var cache: FMSVehicleAggregateCache
    private lateinit var serviceUnderTest: PreProcessorYDBMA3302REG1

    @BeforeEach
    fun setup() {
        cache = FMSVehicleAggregateCache()
        serviceUnderTest =
            PreProcessorYDBMA3302REG1(
                ExcelPreprocessor(
                    LocalFileProvider("/migration/testdata/YDBMA3302_REG1.XLSX"),
                ),
            )
    }

    @Test
    fun `should validate the file`() {
        val result = serviceUnderTest.validate()
        assertTrue(result, "Expected validation to succeed when all headers are present")
    }

    @Test
    fun `should correctly map multiple entries for same vehicle`() {
        serviceUnderTest.process(cache)

        val multiEntryVehicle = cache.getAggregate("005056BBC54B1EEDBBE72CB8B5857198")
        assertEquals(2, multiEntryVehicle.registrations.size) // 3rd entry is discarded because of null reg date
        assertEquals(
            FMSRegistrationAggregate(
                registrationType = 1,
                registrationDate = dateFrom("2023-08-02"),
                licencePlate = "S-PR 5257H",
                remark = "H-Kenn.",
            ),
            multiEntryVehicle.registrations[0],
        )
        assertEquals(
            FMSRegistrationAggregate(
                registrationType = 4,
                registrationDate = dateFrom("2024-11-12"),
                licencePlate = null,
                registrationArea = RegistrationArea.GERMANY,
                remark = null,
            ),
            multiEntryVehicle.registrations[1],
        )

        // 2 records in csv, one entry is discarded because of null reg date
        val vehicleWithNullRegDate = cache.getAggregate("005056BB7E3A1EEE81A52AA994367924")
        assertEquals(1, vehicleWithNullRegDate.registrations.size)
    }

    @Test
    fun `should sanitize licence plate`() {
        serviceUnderTest.process(cache)

        val vehicleWithSanitizedPlate = cache.getAggregate("005056BBCE341EEE81A52766C6663FD8")
        assertEquals(2, vehicleWithSanitizedPlate.registrations.size)
        assertEquals("BB-VF 7126", vehicleWithSanitizedPlate.registrations.first().licencePlate)
        assertEquals("BB-VF 7126", vehicleWithSanitizedPlate.registrations.last().licencePlate)
    }

    @Test
    fun `should detect E- or H-suffix from remark and attach to licence plate`() {
        serviceUnderTest.process(cache)

        val licensePlateWithHSuffix = cache.getAggregate("005056BBC54B1EEDBBE72CB8B5857198")
        val licensePlateWithESuffix = cache.getAggregate("B792C97A88941EDEA3A67EFDBE85E7C6")
        assertEquals("S-PR 5257H", licensePlateWithHSuffix.registrations.first().licencePlate)
        assertEquals("S-PO 6867E", licensePlateWithESuffix.registrations.first().licencePlate)
    }

    @Test
    fun `should set registration area as OTHER and not sanitize if license plate is not valid German format`() {
        serviceUnderTest.process(cache)

        val foreignLicensePlate = cache.getAggregate("005056BB7E3A1EEE81A52AA994367924")
        assertEquals("FOREIGN- 123", foreignLicensePlate.registrations.last().licencePlate)
        assertEquals(RegistrationArea.OTHER, foreignLicensePlate.registrations.last().registrationArea)
    }

    @Test
    fun `should correctly extract the license plate suffix from various remarks (real data)`() {
        assertAll(
            // E license plates
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E--KENN.")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-KEN.")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-KENN")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-KENN:.")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-Kenn.")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-KENN.")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-Kenn. / SFME")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-KENN. / SFME")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-Kenn. | SFME")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-KENN. | SFME")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-Kenn.;")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-Kenn./SFME")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-KENN./SFME")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-KENN.| SFME")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-Kennzeichen")) },
            { assertEquals("E", serviceUnderTest.extractLicensePlateSuffix("E-KENNZEICHEN")) },
            // H license plates
            { assertEquals("H", serviceUnderTest.extractLicensePlateSuffix("H")) },
            { assertEquals("H", serviceUnderTest.extractLicensePlateSuffix("H-KENNZ.")) },
            { assertEquals("H", serviceUnderTest.extractLicensePlateSuffix("H-KENN.")) },
            { assertEquals("H", serviceUnderTest.extractLicensePlateSuffix("H-KENNZEICHEN")) },
            // no suffix
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("ERSTZULASSUNG 1988")) },
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("FSME")) },
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("HÄNGER")) },
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("MUSEUM")) },
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("POL. KENNZEICHEN WAR FALSCH")) },
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("SFME")) },
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("SFME;")) },
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("SFVE")) },
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("Tageszulassung")) },
            { assertEquals("", serviceUnderTest.extractLicensePlateSuffix("TAGESZULASSUNG")) },
        )
    }
}
