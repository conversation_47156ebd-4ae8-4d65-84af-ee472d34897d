package com.fleetmanagement.modules.noncustomeradequate.features.repository.entities

import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.JPANonCustomerAdequateVehicleEntity
import com.fleetmanagement.modules.noncustomeradequate.repository.entities.NonCustomerAdequateVehicleFutureDateException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import java.util.Optional
import java.util.UUID

class JPANonCustomerAdequateVehicleEntityTest {
    val vehicleId: UUID = UUID.randomUUID()

    @Test
    fun `should throw for VehicleUpdateException when profitabilityAuditDone in future`() {
        val entity = JPANonCustomerAdequateVehicleEntity(vehicleId)
        val futureDate = OffsetDateTime.now().plusDays(1)
        val ex =
            assertThrows(NonCustomerAdequateVehicleFutureDateException::class.java) {
                entity.updatePrivileged(
                    profitabilityAuditDone = Optional.of(futureDate),
                    rebuildStarted = null,
                    rebuildDone = null,
                    comment = null,
                    plannedRebuildCost = null,
                    actualRebuildCost = null,
                    expectedRevenue = null,
                    salesPrice = null,
                    status = null,
                )
            }
        assertEquals(
            ViolationType.FUTURE_DATE_NOT_ALLOWED,
            ex.constraintViolation.type,
        )
        assertEquals(
            "profitabilityAuditDone",
            ex.constraintViolation.propertyName,
        )
    }

    @Test
    fun `should throw for VehicleUpdateException when rebuildStarted in future`() {
        val entity = JPANonCustomerAdequateVehicleEntity(vehicleId)
        val futureDate = OffsetDateTime.now().plusDays(1)
        val ex =
            assertThrows(NonCustomerAdequateVehicleFutureDateException::class.java) {
                entity.updatePrivileged(
                    profitabilityAuditDone = null,
                    rebuildStarted = Optional.of(futureDate),
                    rebuildDone = null,
                    comment = null,
                    plannedRebuildCost = null,
                    actualRebuildCost = null,
                    expectedRevenue = null,
                    salesPrice = null,
                    status = null,
                )
            }
        assertEquals(
            ViolationType.FUTURE_DATE_NOT_ALLOWED,
            ex.constraintViolation.type,
        )
        assertEquals(
            "rebuildStarted",
            ex.constraintViolation.propertyName,
        )
    }

    @Test
    fun `should throw for VehicleUpdateException when rebuildDone in future`() {
        val entity = JPANonCustomerAdequateVehicleEntity(vehicleId)
        val futureDate = OffsetDateTime.now().plusDays(1)
        val ex =
            assertThrows(NonCustomerAdequateVehicleFutureDateException::class.java) {
                entity.updatePrivileged(
                    profitabilityAuditDone = null,
                    rebuildStarted = null,
                    rebuildDone = Optional.of(futureDate),
                    comment = null,
                    plannedRebuildCost = null,
                    actualRebuildCost = null,
                    expectedRevenue = null,
                    salesPrice = null,
                    status = null,
                )
            }
        assertEquals(
            ViolationType.FUTURE_DATE_NOT_ALLOWED,
            ex.constraintViolation.type,
        )
        assertEquals(
            "rebuildDone",
            ex.constraintViolation.propertyName,
        )
    }
}
