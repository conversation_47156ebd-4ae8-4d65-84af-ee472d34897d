package com.fleetmanagement.modules.rela.adapter.out.rest

import com.fleetmanagement.modules.rela.adapter.out.rest.model.CreateAppointmentResponseDto
import com.fleetmanagement.modules.rela.application.*
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import java.time.LocalDate
import java.time.OffsetDateTime
import kotlin.test.assertEquals

class RelaClientTest {
    
    private val relaWebClient = mockk<RelaWebClient>()
    private val relaClient = RelaClient(relaWebClient)
    
    @Test
    fun `should create appointment successfully`() {
        // Given
        val request = RelaAppointmentRequest(
            appointmentDate = OffsetDateTime.now().plusDays(7),
            appointmentTime = OffsetDateTime.now().plusDays(7).withHour(10),
            vehicleLicensePlate = "S-PL 1234",
            vehicleVin = "WP0ZZZ999SS123456",
            customerLastName = "Müller",
            customerFirstName = "Hans",
            serviceBayNumber = 1,
            serviceTypeId = 2
        )
        
        val expectedResponse = CreateAppointmentResponseDto(auftragsnummer = "14274000023")
        every { relaWebClient.createAppointment(any()) } returns expectedResponse
        
        // When
        val result = relaClient.createAppointment(request)
        
        // Then
        assertEquals("14274000023", result.orderNumber)
        verify { relaWebClient.createAppointment(any()) }
    }
    
    @Test
    fun `should throw exception when appointment creation fails`() {
        // Given
        val request = RelaAppointmentRequest(
            appointmentDate = OffsetDateTime.now().plusDays(7),
            appointmentTime = OffsetDateTime.now().plusDays(7).withHour(10),
            vehicleLicensePlate = "S-PL 1234",
            vehicleVin = "WP0ZZZ999SS123456",
            customerLastName = "Müller",
            customerFirstName = "Hans"
        )
        
        every { relaWebClient.createAppointment(any()) } throws RuntimeException("API Error")
        
        // When & Then
        assertThrows<RelaAppointmentCreationException> {
            relaClient.createAppointment(request)
        }
    }
    
    @Test
    fun `should cancel appointment successfully`() {
        // Given
        val orderNumber = "14274000023"
        val cancellationRequest = RelaCancellationRequest(
            cancelledBy = "Hans Müller",
            cancellationDate = LocalDate.now()
        )
        
        every { relaWebClient.cancelAppointment(any(), any()) } returns Unit
        
        // When
        relaClient.cancelAppointment(orderNumber, cancellationRequest)
        
        // Then
        verify { relaWebClient.cancelAppointment(orderNumber, any()) }
    }
    
    @Test
    fun `should throw exception when appointment cancellation fails`() {
        // Given
        val orderNumber = "14274000023"
        val cancellationRequest = RelaCancellationRequest(
            cancelledBy = "Hans Müller",
            cancellationDate = LocalDate.now()
        )
        
        every { relaWebClient.cancelAppointment(any(), any()) } throws RuntimeException("API Error")
        
        // When & Then
        assertThrows<RelaAppointmentCancellationException> {
            relaClient.cancelAppointment(orderNumber, cancellationRequest)
        }
    }
}
