package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import java.util.Optional

class JPAReturnInfoTest {
    @Test
    fun `should throw for VehicleUpdateException when keyReturned in future`() {
        val entity = JPAReturnInfo()
        val futureDate = OffsetDateTime.now().plusDays(1)
        val ex =
            assertThrows(VehicleUpdateException::class.java) {
                entity.updatePrivileged(
                    nextProcess = null,
                    scrapVehicle = true,
                    blockedForSale = true,
                    keyReturned = Optional.of(futureDate),
                )
            }
        assertEquals(
            ViolationType.FUTURE_DATE_NOT_ALLOWED,
            ex.constraintViolation!!.type,
        )
        assertEquals(
            "keyReturned",
            ex.constraintViolation!!.propertyName,
        )
    }
}
