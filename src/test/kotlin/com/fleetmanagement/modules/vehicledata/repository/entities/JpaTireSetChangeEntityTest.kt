package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import java.util.Optional

class JpaTireSetChangeEntityTest {
    @Test
    fun `should throw for VehicleUpdateException when orderedDate in future`() {
        val entity = JpaTireSetChangeEntity()
        val futureDate = OffsetDateTime.now().plusDays(1)
        val ex =
            assertThrows(VehicleUpdateException::class.java) {
                entity.updatePrivileged(
                    completedDate = null,
                    orderedDate = Optional.of(futureDate),
                    comment = null,
                )
            }
        assertEquals(
            ViolationType.FUTURE_DATE_NOT_ALLOWED,
            ex.constraintViolation!!.type,
        )
        assertEquals(
            "orderedDate",
            ex.constraintViolation!!.propertyName,
        )
    }

    @Test
    fun `should throw for VehicleUpdateException when completedDate in future`() {
        val entity = JpaTireSetChangeEntity()
        val futureDate = OffsetDateTime.now().plusDays(1)
        val ex =
            assertThrows(VehicleUpdateException::class.java) {
                entity.updatePrivileged(
                    completedDate = Optional.of(futureDate),
                    orderedDate = null,
                    comment = null,
                )
            }
        assertEquals(
            ViolationType.FUTURE_DATE_NOT_ALLOWED,
            ex.constraintViolation!!.type,
        )
        assertEquals(
            "completedDate",
            ex.constraintViolation!!.propertyName,
        )
    }
}
