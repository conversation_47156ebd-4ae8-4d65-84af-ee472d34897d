package com.fleetmanagement.modules.vehicledata.repository.entities

import com.fleetmanagement.emhshared.domain.ViolationType
import com.fleetmanagement.modules.vehicledata.api.exceptions.VehicleUpdateException
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import java.time.OffsetDateTime
import java.util.UUID

class PropertyValidatorTest {
    private val testProperty = VehiclePropertyForValidation.PROFITABILITY_AUDIT_DATE
    private val testVehicleId = UUID.randomUUID()

    @Test
    fun `does nothing when date is null`() {
        assertDoesNotThrow {
            validateNotFutureDatePrecondition(testProperty, null, testVehicleId)
        }
    }

    @Test
    fun `does nothing when date is today`() {
        val today = OffsetDateTime.now()
        assertDoesNotThrow {
            validateNotFutureDatePrecondition(testProperty, today, testVehicleId)
        }
    }

    @Test
    fun `throws when date is in future`() {
        val futureDate = OffsetDateTime.now().plusDays(1)
        val ex =
            assertThrows(VehicleUpdateException::class.java) {
                validateNotFutureDatePrecondition(testProperty, futureDate, testVehicleId)
            }
        assertEquals(testVehicleId, ex.constraintViolation!!.vehicleId)
        assertEquals(ViolationType.FUTURE_DATE_NOT_ALLOWED, ex.constraintViolation!!.type)
        assertEquals(testProperty.fieldName, ex.constraintViolation!!.propertyName)
    }
}
