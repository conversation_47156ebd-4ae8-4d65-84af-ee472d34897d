package com.fleetmanagement.modules.vehiclesales.features

import com.fleetmanagement.modules.vehiclelocation.api.LastKnownLocation
import com.fleetmanagement.modules.vehiclelocation.api.VehicleLocationEvent
import com.fleetmanagement.modules.vehiclelocation.api.dtos.VehicleLocation
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import java.time.OffsetDateTime
import java.util.UUID

class VehicleLocationUpdateServiceTest {
    private val lastKnownLocation: LastKnownLocation = mockk()
    private val vehicleLocationEvent: VehicleLocationEvent = mockk()
    private lateinit var service: VehicleLocationUpdateService

    @BeforeEach
    fun setup() {
        every { vehicleLocationEvent.addLocationEvent(any()) } returns Unit
        service = VehicleLocationUpdateService(lastKnownLocation, vehicleLocationEvent)
    }

    @Test
    fun `should create OUT event when last location event is IN`() {
        val vehicleId = UUID.randomUUID()
        val loadingCompletedDate = OffsetDateTime.now()

        val lastLocation = `given last known location event is available for a vehicle`(vehicleId, "IN")

        service.handleVehicleLocationUpdate(vehicleId, loadingCompletedDate)

        // Verify that an OUT event was created
        val expectedLocation =
            VehicleLocation(
                vehicleId = vehicleId,
                eventType = "OUT",
                source = "FVM",
                occurredOn = loadingCompletedDate,
                level = lastLocation.level,
                building = lastLocation.building,
                parkingLot = lastLocation.parkingLot,
                comment = "Verladung abgeschlossen",
            )
        verify(exactly = 1) { vehicleLocationEvent.addLocationEvent(expectedLocation) }
    }

    @Test
    fun `should create IN and OUT events when last location event is OUT`() {
        val vehicleId = UUID.randomUUID()
        val loadingCompletedDate = OffsetDateTime.now()

        val lastLocation = `given last known location event is available for a vehicle`(vehicleId, "OUT")

        service.handleVehicleLocationUpdate(vehicleId, loadingCompletedDate)

        // Verify that an IN event was created
        val expectedInLocation =
            lastLocation.copy(
                eventType = "IN",
                source = "FVM",
                comment = "Verladung abgeschlossen",
            )
        verify(exactly = 1) { vehicleLocationEvent.addLocationEvent(expectedInLocation) }

        // Verify that an OUT event was created
        val expectedOutLocation =
            lastLocation.copy(
                eventType = "OUT",
                source = "FVM",
                occurredOn = loadingCompletedDate,
                comment = "Verladung abgeschlossen",
            )
        verify(exactly = 1) { vehicleLocationEvent.addLocationEvent(expectedOutLocation) }
    }

    @Test
    fun `should not add any event when last known location is null`() {
        val vehicleId = UUID.randomUUID()
        val loadingCompletedDate = OffsetDateTime.now()

        // Mock the case where no last known location is found
        every { lastKnownLocation.findLastKnownVehicleLocationBy(vehicleId) } returns null

        service.handleVehicleLocationUpdate(vehicleId, loadingCompletedDate)

        // Verify that no events were added
        verify(exactly = 0) { vehicleLocationEvent.addLocationEvent(any()) }
    }

    private fun `given last known location event is available for a vehicle`(
        vehicleId: UUID,
        eventType: String,
    ): VehicleLocation {
        // Mock the last known location retrieval
        val lastLocationEvent =
            VehicleLocation(
                vehicleId = vehicleId,
                eventType = eventType,
                source = "FVM",
                occurredOn = OffsetDateTime.now(),
                level = "Level 1",
                building = "Building A",
                parkingLot = "Parking Lot 1",
            )
        every { lastKnownLocation.findLastKnownVehicleLocationBy(vehicleId) } returns lastLocationEvent
        return lastLocationEvent
    }
}
