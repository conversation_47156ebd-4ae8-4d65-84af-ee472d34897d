/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.features.updateInvoice

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.consigneedatasheet.domain.EmployeeNumber
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.objectmothers.VehicleDataDTOObjectMother
import com.fleetmanagement.modules.vehiclesales.VehicleInvoiceBuilder
import com.fleetmanagement.modules.vehiclesales.VehicleSalesDBConfiguration
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleInvoiceRepository
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleSaleRepository
import com.fleetmanagement.modules.vehiclesales.repository.entities.JPAVehicleSaleEntity
import com.fleetmanagement.modules.vehiclesales.repository.entities.PartnerNumber
import com.fleetmanagement.modules.vehiclesales.repository.entities.TransactionId
import com.ninjasquad.springmockk.MockkBean
import io.mockk.every
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.EnableTransactionManagement
import java.time.OffsetDateTime
import java.util.Optional
import java.util.UUID

@DataJpaTest
@Import(
    value = [
        VehicleSalesDBConfiguration::class,
        TestcontainersConfiguration::class,
        B2BVehicleInvoiceUpdateService::class,
    ],
)
@EnableTransactionManagement(proxyTargetClass = true)
class B2BVehicleInvoiceUpdateServiceTest {
    @Autowired
    private lateinit var jpaVehicleSaleRepository: JPAVehicleSaleRepository

    @Autowired
    private lateinit var jpaVehicleInvoiceRepository: JPAVehicleInvoiceRepository

    @MockkBean
    private lateinit var readVehicleService: ReadVehicleByVehicleId

    @Autowired
    private lateinit var b2BVehicleInvoiceUpdateService: B2BVehicleInvoiceUpdateService

    @BeforeEach
    fun setUp() {
        jpaVehicleInvoiceRepository.deleteAll()
        jpaVehicleSaleRepository.deleteAll()
    }

    @Test
    fun `should update customer delivery date for B2B vehicle`() {
        // GIVEN
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        val vehicleSales = createVehicleSaleEntity(vehicleId)
        val customerPartnerNumber = PartnerNumber("654321")
        val vehicleInvoice =
            VehicleInvoiceBuilder()
                .vehicleId(vehicleId)
                .customerInvoiceRecipient(false)
                .customerPartnerNumber(customerPartnerNumber)
                .invoiceRecipientNumber(customerPartnerNumber)
                .vehicleSale(vehicleSales)
                .salesNetPriceEUR(75000.00.toBigDecimal())
                .salesPersonNumber(EmployeeNumber("P0100"))
                .transactionId(TransactionId("VIN_2025_1"))
                .asCurrent()
                .build()
        jpaVehicleInvoiceRepository.save(vehicleInvoice)

        // WHEN
        val newCustomerDeliveryDate = OffsetDateTime.parse("2025-07-14T10:00:00Z")
        val updatedInvoice =
            b2BVehicleInvoiceUpdateService.updateCustomerDeliveryDateForB2B(
                vehicleId = vehicleId,
                customerDeliveryDate = newCustomerDeliveryDate,
            )

        // THEN
        assertEquals(newCustomerDeliveryDate, updatedInvoice.customerDeliveryDate)
    }

    private fun createVehicleSaleEntity(vehicleId: UUID): JPAVehicleSaleEntity {
        val saleEntityToCreate =
            JPAVehicleSaleEntity(vehicleId = vehicleId).apply {
                updatePrivileged(
                    comment = Optional.of("Test comment for invoice"),
                    reservedForB2C = Optional.empty(),
                    reservedForB2B = Optional.of(true),
                    contractSigned = Optional.of(true),
                    plannedDeliveryDate = Optional.empty(),
                    loadingCompletedDate = Optional.of(OffsetDateTime.parse("2025-07-01T10:00:00Z")),
                )
            }
        return jpaVehicleSaleRepository.save(saleEntityToCreate)
    }
}
