/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.features.updateInvoice

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.vehicledata.api.ReadVehicleByVehicleId
import com.fleetmanagement.modules.vehicledata.api.UpdateVehicleSoldDate
import com.fleetmanagement.modules.vehicledata.objectmothers.VehicleDataDTOObjectMother
import com.fleetmanagement.modules.vehiclesales.VehicleInvoiceBuilder
import com.fleetmanagement.modules.vehiclesales.VehicleSalesDBConfiguration
import com.fleetmanagement.modules.vehiclesales.api.domain.InvoiceStatus
import com.fleetmanagement.modules.vehiclesales.api.dto.VehicleInvoiceUpdateDto
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceNotFoundException
import com.fleetmanagement.modules.vehiclesales.api.exception.VehicleInvoiceUpdateException
import com.fleetmanagement.modules.vehiclesales.features.UpsertVehicleSaleService
import com.fleetmanagement.modules.vehiclesales.features.createinvoice.CurrentVehicleInvoiceService
import com.fleetmanagement.modules.vehiclesales.features.createinvoice.TransactionIdGenerator
import com.fleetmanagement.modules.vehiclesales.features.createinvoice.VehicleInvoiceCreateDtoBuilder
import com.fleetmanagement.modules.vehiclesales.features.createinvoice.VehicleInvoiceCreateService
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleInvoiceRepository
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleSaleRepository
import com.fleetmanagement.modules.vehiclesales.repository.entities.JPAVehicleSaleEntity
import com.ninjasquad.springmockk.MockkBean
import io.mockk.*
import org.junit.jupiter.api.Assertions.*
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.orm.jpa.DataJpaTest
import org.springframework.context.annotation.Import
import org.springframework.transaction.annotation.EnableTransactionManagement
import java.time.OffsetDateTime
import java.util.*

@DataJpaTest
@Import(
    value = [
        VehicleSalesDBConfiguration::class,
        TestcontainersConfiguration::class,
        InvoiceRequestApprovedUpdateRule::class,
        RefundCreatedUpdateRule::class,
        InvoiceRequestCreatedUpdateRule::class,
        CustomerDeliveryDateConfirmedUpdateRule::class,
        TransactionIdGenerator::class,
        VehicleInvoiceCreateService::class,
        B2CVehicleInvoiceUpdateService::class,
        CurrentVehicleInvoiceService::class,
    ],
)
@EnableTransactionManagement(proxyTargetClass = true)
class B2CVehicleInvoiceUpdateServiceTest {
    @Autowired
    private lateinit var jpaVehicleSaleRepository: JPAVehicleSaleRepository

    @Autowired
    private lateinit var jpaVehicleInvoiceRepository: JPAVehicleInvoiceRepository

    @MockkBean
    private lateinit var readVehicleService: ReadVehicleByVehicleId

    @MockkBean(relaxed = true)
    private lateinit var updateVehicleService: UpdateVehicleSoldDate

    @MockkBean(relaxed = true)
    private lateinit var upsertVehicleSaleService: UpsertVehicleSaleService

    @Autowired
    private lateinit var vehicleInvoiceCreateService: VehicleInvoiceCreateService

    @Autowired
    private lateinit var b2CVehicleInvoiceUpdateService: B2CVehicleInvoiceUpdateService

    @BeforeEach
    fun setUp() {
        jpaVehicleInvoiceRepository.deleteAll()
        jpaVehicleSaleRepository.deleteAll()
    }

    @Test
    fun `should update invoice status to INVOICE_REQUEST_APPROVED`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        createVehicleSaleEntity(vehicleId)
        val vehicleInvoiceCreateDto = VehicleInvoiceCreateDtoBuilder().vehicleId(vehicleId).build()

        val createdInvoice = vehicleInvoiceCreateService.createInvoice(vehicleInvoiceCreateDto)
        checkNotNull(createdInvoice)
        assertEquals(InvoiceStatus.WAITING_FOR_APPROVAL, createdInvoice.invoiceStatus)

        val updatedInvoice = b2CVehicleInvoiceUpdateService.approveInvoiceBy(createdInvoice.invoiceId)
        assertEquals(InvoiceStatus.INVOICE_REQUEST_APPROVED, updatedInvoice.invoiceStatus)
    }

    @Test
    fun `should throw exception when trying to approve non-existing invoice`() {
        val nonExistingInvoiceId = UUID.randomUUID()
        assertThrows<VehicleInvoiceNotFoundException> {
            b2CVehicleInvoiceUpdateService.approveInvoiceBy(nonExistingInvoiceId)
        }
    }

    @Test
    fun `should update invoice status to CANCELED and create credit invoice, reset sales`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        createVehicleSaleEntity(vehicleId)
        val vehicleInvoiceCreateDto = VehicleInvoiceCreateDtoBuilder().vehicleId(vehicleId).build()

        val createdInvoice = vehicleInvoiceCreateService.createInvoice(vehicleInvoiceCreateDto)
        checkNotNull(createdInvoice)
        assertEquals(InvoiceStatus.WAITING_FOR_APPROVAL, createdInvoice.invoiceStatus)
        assertTrue(createdInvoice.isCurrent)

        val updatedInvoice = b2CVehicleInvoiceUpdateService.cancelInvoiceBy(createdInvoice.invoiceId, "test-modifier")
        assertTrue(updatedInvoice.creditInvoice.isCurrent)
        assertFalse(updatedInvoice.canceledInvoice.isCurrent)
        assertEquals(InvoiceStatus.CANCELED, updatedInvoice.canceledInvoice.invoiceStatus)
        assertEquals(
            InvoiceStatus.REFUND_CREATED,
            updatedInvoice.creditInvoice.invoiceStatus,
        )
        assertNotNull(updatedInvoice.creditInvoice.referenceTransactionId)
        assertEquals(
            updatedInvoice.creditInvoice.referenceTransactionId,
            updatedInvoice.canceledInvoice.transactionId,
        )
        verify(exactly = 1) {
            updateVehicleService.updateVehicleSoldDate(
                vehicleId = vehicleId,
                modifier = "test-modifier",
                soldDate = null,
            )
            upsertVehicleSaleService.resetVehicleSales(
                vehicleId = vehicleId,
                currentUser = "test-modifier",
            )
        }
    }

    @Test
    fun `should throw exception when trying to cancel non-existing invoice`() {
        val nonExistingInvoiceId = UUID.randomUUID()
        assertThrows<VehicleInvoiceNotFoundException> {
            b2CVehicleInvoiceUpdateService.cancelInvoiceBy(nonExistingInvoiceId, "random-user")
        }
    }

    @Test
    fun `should delete an invoice`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        createVehicleSaleEntity(vehicleId)
        val vehicleInvoiceCreateDto = VehicleInvoiceCreateDtoBuilder().vehicleId(vehicleId).build()

        val createdInvoice = vehicleInvoiceCreateService.createInvoice(vehicleInvoiceCreateDto)
        checkNotNull(createdInvoice)

        val deletedInvoice = b2CVehicleInvoiceUpdateService.deleteInvoiceBy(createdInvoice.invoiceId)
        assertEquals(createdInvoice.invoiceId, deletedInvoice)
        assertFalse(jpaVehicleInvoiceRepository.existsById(createdInvoice.invoiceId))
    }

    @Test
    fun `should throw exception when trying to delete non-existing invoice`() {
        val nonExistingInvoiceId = UUID.randomUUID()
        assertThrows<VehicleInvoiceNotFoundException> {
            b2CVehicleInvoiceUpdateService.deleteInvoiceBy(nonExistingInvoiceId)
        }
    }

    @Test
    fun `should update invoice status to INVOICE_REQUEST_CREATED when receiptNumber and invoiceNumber are set`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        val vehicleSale = createVehicleSaleEntity(vehicleId)
        val vehicleInvoice =
            VehicleInvoiceBuilder()
                .vehicleId(vehicleId)
                .vehicleSale(vehicleSale)
                .invoiceStatus(InvoiceStatus.INVOICE_REQUEST_APPROVED)
                .build()
        val createdInvoice = jpaVehicleInvoiceRepository.save(vehicleInvoice)
        checkNotNull(createdInvoice)
        assertEquals(InvoiceStatus.INVOICE_REQUEST_APPROVED, createdInvoice.invoiceStatus)

        val invoiceUpdateDto =
            VehicleInvoiceUpdateDto(
                receiptNumber = Optional.of("12345"),
                invoiceNumber = Optional.of("INV-67890"),
                customerDeliveryDate = null,
                paymentReceived = null,
                finalInvoiceNumber = null,
                invoiceDate = null,
            )
        val updatedInvoice =
            b2CVehicleInvoiceUpdateService.updateInvoiceBy(
                invoiceId = createdInvoice.id!!,
                vehicleInvoiceUpdateDto = invoiceUpdateDto,
                modifier = "test-modifier",
            )
        assertEquals("12345", updatedInvoice.receiptNumber)
        assertEquals("INV-67890", updatedInvoice.invoiceNumber)
        assertEquals(InvoiceStatus.INVOICE_REQUEST_CREATED, updatedInvoice.invoiceStatus)
    }

    @Test
    fun `should update invoice status to CUSTOMER_DELIVERY_DATE_CONFIRMED when customerDeliveryDate and paymentReceived are set`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        val vehicleSale = createVehicleSaleEntity(vehicleId)
        val vehicleInvoice =
            VehicleInvoiceBuilder()
                .vehicleId(vehicleId)
                .vehicleSale(vehicleSale)
                .invoiceStatus(InvoiceStatus.INVOICE_REQUEST_CREATED)
                .build()
        val createdInvoice = jpaVehicleInvoiceRepository.save(vehicleInvoice)
        checkNotNull(createdInvoice)
        assertEquals(InvoiceStatus.INVOICE_REQUEST_CREATED, createdInvoice.invoiceStatus)

        val invoiceUpdateDto =
            VehicleInvoiceUpdateDto(
                receiptNumber = Optional.of("12345"),
                invoiceNumber = Optional.of("INV-67890"),
                customerDeliveryDate = Optional.of(OffsetDateTime.parse("2023-10-01T10:00:00Z")),
                paymentReceived = Optional.of(true),
                finalInvoiceNumber = null,
                invoiceDate = null,
            )
        val updatedInvoice =
            b2CVehicleInvoiceUpdateService.updateInvoiceBy(
                invoiceId = createdInvoice.id!!,
                vehicleInvoiceUpdateDto = invoiceUpdateDto,
                modifier = "test-modifier",
            )
        assertEquals("12345", updatedInvoice.receiptNumber)
        assertEquals("INV-67890", updatedInvoice.invoiceNumber)
        assertEquals(OffsetDateTime.parse("2023-10-01T10:00:00Z"), updatedInvoice.customerDeliveryDate)
        assertTrue(updatedInvoice.paymentReceived)
        assertEquals(InvoiceStatus.CUSTOMER_DELIVERY_DATE_CONFIRMED, updatedInvoice.invoiceStatus)
    }

    @Test
    fun `should update invoice status to INVOICE_SENT_TO_CUSTOMER when finalInvoiceNumber and invoiceDate are set`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        every {
            updateVehicleService.updateVehicleSoldDate(
                any(),
                any(),
                any(),
            )
        } just runs

        val vehicleSale = createVehicleSaleEntity(vehicleId)
        val vehicleInvoice =
            VehicleInvoiceBuilder()
                .vehicleId(vehicleId)
                .vehicleSale(vehicleSale)
                .invoiceStatus(InvoiceStatus.CUSTOMER_DELIVERY_DATE_CONFIRMED)
                .build()
        val createdInvoice = jpaVehicleInvoiceRepository.save(vehicleInvoice)
        checkNotNull(createdInvoice)
        assertEquals(InvoiceStatus.CUSTOMER_DELIVERY_DATE_CONFIRMED, createdInvoice.invoiceStatus)

        val invoiceUpdateDto =
            VehicleInvoiceUpdateDto(
                receiptNumber = Optional.of("12345"),
                invoiceNumber = Optional.of("INV-67890"),
                customerDeliveryDate = Optional.of(OffsetDateTime.parse("2023-10-01T10:00:00Z")),
                paymentReceived = Optional.of(true),
                finalInvoiceNumber = Optional.of("FIN-12345"),
                invoiceDate = Optional.of(OffsetDateTime.parse("2023-10-01T10:00:00Z")),
            )
        val updatedInvoice =
            b2CVehicleInvoiceUpdateService.updateInvoiceBy(
                invoiceId = createdInvoice.id!!,
                vehicleInvoiceUpdateDto = invoiceUpdateDto,
                modifier = "test-modifier",
            )
        assertEquals("FIN-12345", updatedInvoice.finalInvoiceNumber)
        assertEquals(OffsetDateTime.parse("2023-10-01T10:00:00Z"), updatedInvoice.invoiceDate)
        assertEquals(InvoiceStatus.INVOICE_SENT_TO_CUSTOMER, updatedInvoice.invoiceStatus)
        verify(exactly = 1) {
            updateVehicleService.updateVehicleSoldDate(
                vehicleId = vehicleId,
                modifier = "test-modifier",
                soldDate = OffsetDateTime.parse("2023-10-01T10:00:00Z"),
            )
        }
    }

    @Test
    fun `should update invoice status to REFUND_COMPLETED when receiptNumber and invoiceNumber are set`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        every { updateVehicleService.updateVehicleSoldDate(vehicleId, any(), any()) } just Runs
        every { upsertVehicleSaleService.resetVehicleSales(vehicleId, any()) } just Runs

        val vehicleSale = createVehicleSaleEntity(vehicleId)
        val vehicleInvoice =
            VehicleInvoiceBuilder()
                .vehicleId(vehicleId)
                .vehicleSale(vehicleSale)
                .invoiceStatus(InvoiceStatus.REFUND_CREATED)
                .build()
        val createdInvoice = jpaVehicleInvoiceRepository.save(vehicleInvoice)
        checkNotNull(createdInvoice)
        assertEquals(InvoiceStatus.REFUND_CREATED, createdInvoice.invoiceStatus)

        val invoiceUpdateDto =
            VehicleInvoiceUpdateDto(
                receiptNumber = Optional.of("12345"),
                invoiceNumber = Optional.of("INV-67890"),
                customerDeliveryDate = null,
                paymentReceived = null,
                finalInvoiceNumber = null,
                invoiceDate = null,
            )
        val updatedInvoice =
            b2CVehicleInvoiceUpdateService.updateInvoiceBy(
                invoiceId = createdInvoice.id!!,
                vehicleInvoiceUpdateDto = invoiceUpdateDto,
                modifier = "test-modifier",
            )
        assertEquals("12345", updatedInvoice.receiptNumber)
        assertEquals("INV-67890", updatedInvoice.invoiceNumber)
        assertEquals(InvoiceStatus.REFUND_COMPLETED, updatedInvoice.invoiceStatus)
    }

    @Test
    fun `should throw VehicleInvoiceUpdateException when trying to update invoice with invalid status`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        val vehicleSale = createVehicleSaleEntity(vehicleId)
        val vehicleInvoice =
            VehicleInvoiceBuilder()
                .vehicleId(vehicleId)
                .vehicleSale(vehicleSale)
                .invoiceStatus(InvoiceStatus.WAITING_FOR_APPROVAL) // This state cannot be updated
                .build()
        val createdInvoice = jpaVehicleInvoiceRepository.save(vehicleInvoice)
        checkNotNull(createdInvoice)

        val invoiceUpdateDto =
            VehicleInvoiceUpdateDto(
                receiptNumber = Optional.of("12345"),
                invoiceNumber = Optional.of("INV-67890"),
                customerDeliveryDate = null,
                paymentReceived = null,
                finalInvoiceNumber = null,
                invoiceDate = null,
            )
        val exception =
            assertThrows<VehicleInvoiceUpdateException> {
                b2CVehicleInvoiceUpdateService.updateInvoiceBy(
                    invoiceId = createdInvoice.id!!,
                    vehicleInvoiceUpdateDto = invoiceUpdateDto,
                    modifier = "test-modifier",
                )
            }
        assertEquals(
            "Vehicle with invoice status: WAITING_FOR_APPROVAL cannot be updated",
            exception.message,
        )
    }

    @Test
    fun `should throw VehicleInvoiceUpdateException when trying to update invoice with invalid data`() {
        val vehicleId = UUID.randomUUID()
        every { readVehicleService.readVehicleById(vehicleId) } returns
            VehicleDataDTOObjectMother.vehicleDTOWithAllFieldsPopulated(
                id = vehicleId,
                vin = "WP0CB2A92FS143285",
            )
        val vehicleSale = createVehicleSaleEntity(vehicleId)
        val vehicleInvoice =
            VehicleInvoiceBuilder()
                .vehicleId(vehicleId)
                .vehicleSale(vehicleSale)
                .invoiceStatus(InvoiceStatus.INVOICE_REQUEST_APPROVED)
                .build()
        val createdInvoice = jpaVehicleInvoiceRepository.save(vehicleInvoice)
        checkNotNull(createdInvoice)

        val invoiceUpdateDto =
            VehicleInvoiceUpdateDto(
                receiptNumber = Optional.of("12345"),
                invoiceNumber = Optional.empty(), // Invalid data: invoiceNumber is required
                customerDeliveryDate = null,
                paymentReceived = null,
                finalInvoiceNumber = null,
                invoiceDate = null,
            )
        val exception =
            assertThrows<VehicleInvoiceUpdateException> {
                b2CVehicleInvoiceUpdateService.updateInvoiceBy(
                    invoiceId = createdInvoice.id!!,
                    vehicleInvoiceUpdateDto = invoiceUpdateDto,
                    modifier = "test-modifier",
                )
            }
        assertEquals(
            "Missing required fields",
            exception.message,
        )

        assertEquals(
            listOf("invoiceNumber"),
            exception.properties,
        )
    }

    @Test
    fun `should throw VehicleInvoiceNotFoundException when trying to update non-existing invoice`() {
        val nonExistingInvoiceId = UUID.randomUUID()
        val invoiceUpdateDto =
            VehicleInvoiceUpdateDto(
                receiptNumber = null,
                invoiceNumber = null,
                customerDeliveryDate = null,
                paymentReceived = null,
                finalInvoiceNumber = null,
                invoiceDate = null,
            )
        assertThrows<VehicleInvoiceNotFoundException> {
            b2CVehicleInvoiceUpdateService.updateInvoiceBy(
                invoiceId = nonExistingInvoiceId,
                vehicleInvoiceUpdateDto = invoiceUpdateDto,
                modifier = "test-modifier",
            )
        }
    }

    private fun createVehicleSaleEntity(vehicleId: UUID): JPAVehicleSaleEntity {
        val saleEntityToCreate =
            JPAVehicleSaleEntity(vehicleId = vehicleId).apply {
                updatePrivileged(
                    comment = Optional.of("Test comment for invoice"),
                    reservedForB2C = Optional.of(true),
                    reservedForB2B = Optional.empty(),
                    contractSigned = Optional.of(true),
                    plannedDeliveryDate = Optional.empty(),
                    loadingCompletedDate = Optional.empty(),
                )
            }
        return jpaVehicleSaleRepository.save(saleEntityToCreate)
    }
}
