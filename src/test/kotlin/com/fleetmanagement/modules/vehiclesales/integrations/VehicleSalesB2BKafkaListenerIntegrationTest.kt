/*
 * This code is protected by intellectual property rights.
 * Dr. Ing. h.c. F. Porsche AG owns exclusive rights of use.
 * © 2020 - 2035, Dr. Ing. h.c. F. Porsche AG.
 */

package com.fleetmanagement.modules.vehiclesales.integrations

import com.fleetmanagement.TestcontainersConfiguration
import com.fleetmanagement.modules.vehicledata.internal.repository.objectmothers.JPAVehicleEntityObjectMother
import com.fleetmanagement.modules.vehicledata.repository.JPAVehicleRepository
import com.fleetmanagement.modules.vehiclesales.api.domain.InvoiceStatus
import com.fleetmanagement.modules.vehiclesales.api.domain.PaymentType
import com.fleetmanagement.modules.vehiclesales.api.domain.SalesDiscountType
import com.fleetmanagement.modules.vehiclesales.api.domain.TransactionType
import com.fleetmanagement.modules.vehiclesales.repository.JPAVehicleInvoiceRepository
import com.ninjasquad.springmockk.SpykBean
import com.porsche.b2b.entities.Customer
import io.confluent.kafka.serializers.KafkaAvroSerializer
import io.mockk.verify
import org.apache.avro.Schema
import org.apache.avro.generic.GenericData
import org.apache.avro.generic.GenericRecord
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.common.serialization.StringSerializer
import org.awaitility.Awaitility.await
import org.awaitility.Durations
import org.junit.jupiter.api.Assertions.assertEquals
import org.junit.jupiter.api.Assertions.assertFalse
import org.junit.jupiter.api.Assertions.assertNull
import org.junit.jupiter.api.Assertions.assertTrue
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.context.TestConfiguration
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Import
import org.springframework.core.io.ClassPathResource
import org.springframework.kafka.annotation.EnableKafka
import org.springframework.kafka.core.DefaultKafkaProducerFactory
import org.springframework.kafka.core.KafkaTemplate
import org.springframework.kafka.core.ProducerFactory
import org.springframework.kafka.test.EmbeddedKafkaBroker
import org.springframework.kafka.test.context.EmbeddedKafka
import org.springframework.test.context.TestPropertySource
import java.io.InputStream
import java.math.BigDecimal
import java.time.Duration
import java.time.LocalDate
import java.time.OffsetDateTime
import java.time.Year

private const val TOPIC_NAME = "FRA_Honeypotters_completed_auctions"

@SpringBootTest
@EmbeddedKafka(partitions = 1, topics = [TOPIC_NAME])
@Import(TestcontainersConfiguration::class, TestEmbeddedB2BKafkaConfig::class)
@TestPropertySource(properties = [$$"spring.kafka.bootstrap-servers=${spring.embedded.kafka.brokers}"])
class VehicleSalesB2BKafkaListenerIntegrationTest {
    @Autowired
    private lateinit var kafkaTemplate: KafkaTemplate<String, GenericRecord>

    @SpykBean
    private lateinit var vehicleSalesB2BKafkaListener: VehicleSalesB2BKafkaListener

    @Autowired
    private lateinit var jpaVehicleRepository: JPAVehicleRepository

    @Autowired
    private lateinit var jpaVehicleInvoiceRepository: JPAVehicleInvoiceRepository

    @BeforeEach
    fun setUp() {
        jpaVehicleRepository.deleteAll()
        jpaVehicleInvoiceRepository.deleteAll()
    }

    companion object {
        val schema: Schema
        const val TEST_VIN = "WP0CB2A92FS143285"
        const val TEST_AUCTION_ID = "auction123"
        const val TEST_CUSTOMER_VEHICLE_PRICE = 100000.0
        val TEST_PURCHASE_CONTRACT_DATE: LocalDate = LocalDate.parse("2025-10-01")
        val TEST_SALE_DATE: LocalDate = LocalDate.parse("2025-01-01")
        val TEST_INVOICE_DATE: LocalDate = LocalDate.parse("2025-01-03")
        val TEST_CUSTOMER = Customer("John", "Doe", "Porsche Dealer", "12345")

        init {
            val schemaStream: InputStream = ClassPathResource("avro/b2b/b2b.avsc").inputStream
            schema = Schema.Parser().parse(schemaStream)
        }

        fun buildTestRecord(): GenericRecord =
            GenericData.Record(schema).apply {
                put("vin", TEST_VIN)
                put("auctionId", TEST_AUCTION_ID)
                put("customerVehiclePrice", TEST_CUSTOMER_VEHICLE_PRICE)
                put("customer", TEST_CUSTOMER)
                put("purchaseContractDate", TEST_PURCHASE_CONTRACT_DATE.toEpochDay().toInt())
                put("saleDate", TEST_SALE_DATE.toEpochDay().toInt())
                put("invoiceDate", TEST_INVOICE_DATE.toEpochDay().toInt())
            }
    }

    @Test
    fun `should process completed auction event`() {
        // Given
        val vehicleToCreate = JPAVehicleEntityObjectMother.newVehicleEntityWithAllFieldsPopulated()
        val createdVehicle = jpaVehicleRepository.saveAndFlush(vehicleToCreate)
        checkNotNull(createdVehicle)
        val vehicleId = createdVehicle.id!!
        val record = buildTestRecord()

        // When
        kafkaTemplate.send(TOPIC_NAME, TEST_VIN, record).get()

        await().atMost(Duration.ofSeconds(10)).pollDelay(Durations.ONE_SECOND).untilAsserted {
            verify(exactly = 1) { vehicleSalesB2BKafkaListener.listenCompletedAuction(any()) }
            val invoices = jpaVehicleInvoiceRepository.findByVehicleId(vehicleId)
            assertEquals(1, invoices.size)
            with(invoices.first()) {
                assertNull(receiptNumber)
                assertNull(invoiceNumber)
                assertNull(finalInvoiceNumber)
                assertEquals(TEST_AUCTION_ID, auctionId)
                assertEquals(TEST_CUSTOMER.companyName, vehicleSale!!.comment)
                assertTrue(isCurrent)
                assertFalse(customerInvoiceRecipient)
                assertEquals(TEST_AUCTION_ID, auctionId)
                assertEquals(BigDecimal("100000.00"), salesNetPriceEUR)
                assertEquals(BigDecimal("100000.00"), salesNetPriceAfterDiscountEUR)
                assertEquals(OffsetDateTime.parse("2025-01-01T00:00Z"), customerDeliveryDate)
                assertEquals(OffsetDateTime.parse("2025-01-03T00:00Z"), invoiceDate)
                assertEquals("00332392", salesPersonNumber.value)
                assertEquals("${TEST_VIN}_${Year.now().value}_1", transactionId)
                assertEquals(PaymentType.DIRECT_DEBIT, paymentType)
                assertEquals("0000012345", customerPartnerNumber.value)
                assertEquals("0000012345", invoiceRecipientNumber.value)
                assertEquals(SalesDiscountType.NO_DISCOUNT, salesDiscountType)
                assertEquals(TransactionType.INVOICE, transactionType)
                assertEquals(TEST_CUSTOMER.companyName, vehicleSale?.comment)
                assertEquals(InvoiceStatus.INVOICE_SENT_TO_CUSTOMER, invoiceStatus)
            }
        }
    }
}

@TestConfiguration
@EnableKafka
class TestEmbeddedB2BKafkaConfig {
    @Bean("vehicleSalesB2BKafkaProducerContainerFactory")
    fun producerFactory(embeddedKafkaBroker: EmbeddedKafkaBroker): ProducerFactory<String, GenericRecord> {
        val configs =
            mapOf(
                ProducerConfig.BOOTSTRAP_SERVERS_CONFIG to embeddedKafkaBroker.brokersAsString,
                ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG to StringSerializer::class.java,
                ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG to KafkaAvroSerializer::class.java,
                "schema.registry.url" to "mock://schema-registry",
            )
        return DefaultKafkaProducerFactory(configs)
    }

    @Bean
    fun kafkaTemplate(
        @Qualifier("vehicleSalesB2BKafkaProducerContainerFactory") producerFactory: ProducerFactory<String, GenericRecord>,
    ): KafkaTemplate<String, GenericRecord> = KafkaTemplate(producerFactory)
}
